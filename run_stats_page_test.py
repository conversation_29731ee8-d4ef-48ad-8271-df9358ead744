#!/usr/bin/env python3
"""
Run the stats page for visual testing.
"""

import pygame
import sys
from stats_page import StatsPage

def main():
    """Run the stats page for testing"""
    print("Starting stats page test...")
    
    # Initialize pygame
    pygame.init()
    
    # Create a screen
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("WOW Bingo - Stats Page Test")
    
    try:
        # Create stats page instance
        print("Creating stats page...")
        stats_page = StatsPage(screen)
        print("Stats page created successfully")
        
        # Run for a few seconds to test
        clock = pygame.time.Clock()
        running = True
        start_time = pygame.time.get_ticks()
        
        print("Running stats page for 10 seconds...")
        
        while running:
            current_time = pygame.time.get_ticks()
            
            # Run for 10 seconds then exit
            if current_time - start_time > 10000:
                print("Test completed successfully!")
                running = False
                break
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
            
            # Update and draw
            try:
                stats_page.update()
                stats_page.draw()
                pygame.display.flip()
            except Exception as e:
                print(f"Error during update/draw: {e}")
                import traceback
                traceback.print_exc()
                running = False
            
            clock.tick(60)  # 60 FPS
        
        print("Stats page test completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error running stats page: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)