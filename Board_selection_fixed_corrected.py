import pygame
import math
import time
import sys
import os
from pygame import gfxdraw
from view_players import ViewPlayers, Player
from player_storage import save_players_to_json, load_players_from_json
import datetime
from game_state_handler import GameState
from game_ui_handler import Game<PERSON>Handler

# Constants for window dimensions and scaling
SCREEN_WIDTH, SCREEN_HEIGHT = 1024, 768
BASE_WIDTH, BASE_HEIGHT = 1024, 768

# Colors - same as main.py
DARK_BLUE = (10, 30, 45)  # Background color
LIGHT_BLUE = (20, 50, 70)  # Slightly lighter background
YELLOW = (255, 200, 50)   # For 'O' in BINGO
RED = (255, 50, 50)       # For 'B' and 'G' in BINGO
GREEN = (50, 200, 100)    # For highlighted numbers
BLUE = (50, 100, 220)     # For 'N' in BINGO
ORANGE = (255, 120, 30)   # For the "Total Callout" text
WHITE = (255, 255, 255)   # For text
GRAY = (60, 60, 70)       # For non-highlighted number circles
BLACK = (20, 20, 25)      # For number row backgrounds
GOLD = (255, 215, 0)      # For special elements
DARK_GREEN = (0, 100, 50) # Darker green for contrast
LIGHT_GREEN = (50, 200, 100) # For highlighted numbers
PURPLE = (128, 0, 128)    # Unused but available
NAV_BAR_BG = (15, 35, 55) # Dark blue for navigation bar
NAV_BAR_ACTIVE = (30, 100, 130) # Teal highlight for active nav item

# Initialize scaling factors
scale_x = 1.0
scale_y = 1.0

# Fonts will be initialized in the main function

class BoardSelectionWindow:
    def __init__(self, screen):
        self.screen = screen
        
        # Get screen dimensions
        screen_width, screen_height = screen.get_size()
        
        # Store scaling factors
        self.scale_x = screen_width / BASE_WIDTH
        self.scale_y = screen_height / BASE_HEIGHT
        
        # Initialize state variables
        self.selected_cartella_numbers = []
        self.cartella_number = 12  # Default
        self.bet_amount = 50       # Default
        self.prize_pool = 0
        self.input_active = False
        self.input_text = str(self.cartella_number)
        self.input_cursor_visible = True
        self.input_cursor_timer = 0
        self.bet_input_active = False
        self.bet_input_text = str(self.bet_amount)
        self.bet_input_cursor_visible = True
        self.bet_input_cursor_timer = 0
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"
        self.called_numbers = []   # For lucky numbers display
        self.current_number = None
        self.bingo_board = []
        self.grid_page = 0        # Track which page of numbers we're viewing (0-5)
        
        # Load bingo boards from JSON file
        self.bingo_boards = self.load_bingo_boards()
        
        # Create a bingo board based on default cartella number
        self.create_board_from_number(self.cartella_number)
        
        # Button states
        self.button_states = {
            "add_player": {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            },
            "continue": {
                "hover": False,
                "click": False,
                "click_time": 0,
                "hover_alpha": 0
            }
        }
        
        # Store hit areas for buttons
        self.hit_areas = {}
        
        # Load players
        self.players = load_players_from_json()
        if self.players is None:
            self.players = []
        
        # Calculate prize pool
        self.calculate_prize_pool()
        
        # Sound effects
        try:
            self.button_click_sound = pygame.mixer.Sound("assets/audio-effects/button-click.mp3")
        except Exception as e:
            print(f"Error loading sound effects: {e}")
            self.button_click_sound = None
    
    def load_bingo_boards(self):
        """Load bingo boards from JSON file"""
        try:
            import json
            # Ensure data directory exists
            os.makedirs('data', exist_ok=True)
            
            # Use os.path.join to create a proper path to bingo_boards.json in the data directory
            boards_path = os.path.join('data', 'bingo_boards.json')
            
            # If the file doesn't exist, return empty dict
            if not os.path.exists(boards_path):
                print(f"Bingo boards file not found at {boards_path}")
                return {}
                
            with open(boards_path, "r") as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading bingo boards: {e}")
            return {}
    
    def run(self):
        """Run the board selection window loop"""
        running = True
        clock = pygame.time.Clock()
        # Store initial window size for returning from fullscreen
        self.current_w, self.current_h = self.screen.get_size()
        
        while running:
            events = pygame.event.get()
            for event in events:
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                
                elif event.type == pygame.MOUSEMOTION:
                    # Update button hover states
                    self.update_button_hover_states(event.pos)
                
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        self.check_button_click(event.pos)
                
                elif event.type == pygame.KEYDOWN:
                    # Always check for fullscreen toggle
                    if event.key == pygame.K_f:
                        # Toggle fullscreen
                        if pygame.display.get_surface().get_flags() & pygame.FULLSCREEN:
                            # Switch back to windowed mode
                            self.screen = pygame.display.set_mode((self.current_w, self.current_h), pygame.RESIZABLE)
                        else:
                            # Save current size before going fullscreen
                            self.current_w, self.current_h = self.screen.get_size()
                            # Switch to fullscreen
                            self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                        
                        # Update scaling after changing display mode
                        self.scale_x = self.screen.get_width() / 800
                        self.scale_y = self.screen.get_height() / 600
                        
                        # Play sound if available
                        if self.button_click_sound:
                            self.button_click_sound.play()
                    else:
                        # Handle other keyboard input
                        self.handle_input(event)
            
            # Update cursor blink state for input fields
            current_time = pygame.time.get_ticks()
            if self.input_active:
                self.input_cursor_timer += 1
                if self.input_cursor_timer > 30:
                    self.input_cursor_visible = not self.input_cursor_visible
                    self.input_cursor_timer = 0
            
            if self.bet_input_active:
                self.bet_input_cursor_timer += 1
                if self.bet_input_cursor_timer > 30:
                    self.bet_input_cursor_visible = not self.bet_input_cursor_visible
                    self.bet_input_cursor_timer = 0
            
            # Update message timer
            if self.message_timer > 0:
                self.message_timer -= 1
            
            # Draw the screen
            self.draw()
            
            # Check for "Continue" button - exits the selection screen
            if "continue_clicked" in self.hit_areas:
                del self.hit_areas["continue_clicked"]
                running = False
            
            pygame.display.flip()
            clock.tick(30)
        
        # Return the list of selected cartella numbers
        return self.selected_cartella_numbers
    
    def draw(self):
        """Draw the board selection screen"""
        # Clear the screen with dark background color
        self.screen.fill(DARK_BLUE)
        
        screen_width, screen_height = self.screen.get_size()
        
        # Calculate responsive section heights and spacing
        # Adjust section heights to be more proportional to screen height
        header_height = int(min(110 * self.scale_y, screen_height * 0.15))
        
        # Calculate available space after header
        available_height = screen_height - header_height
        
        # Allocate space proportionally to ensure elements fit
        grid_height = int(available_height * 0.65)  # Grid takes 65% of available space
        bottom_section_height = int(available_height * 0.30)  # Bottom section takes 30% of available space
        padding = int(min(15 * min(self.scale_x, self.scale_y), screen_height * 0.02))  # Ensure padding isn't too large
        
        # Draw the WOW BINGO header (in a slightly smaller area)
        self.draw_wow_bingo_header()
        
        # Draw subtle separator line between header and content
        line_y = header_height
        pygame.draw.line(
            self.screen,
            (40, 60, 80),
            (padding, line_y),
            (screen_width - padding, line_y),
            2
        )
        
        # Draw CARTELLA SELECTION title with total count on the same line
        title_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("CARTELLA SELECTION", True, WHITE)
        title_x = padding
        title_y = header_height + padding
        self.screen.blit(title_text, (title_x, title_y))
        
        # Draw total selected count on the right side
        count_font = pygame.font.SysFont("Arial", int(28 * min(self.scale_x, self.scale_y)), bold=True)
        count_text = count_font.render(f"#Total selected: {len(self.selected_cartella_numbers)}", True, ORANGE)
        count_x = screen_width - count_text.get_width() - padding
        count_y = title_y
        self.screen.blit(count_text, (count_x, count_y))
        
        # Draw selected cartella numbers right under the title if any exist
        selected_y = title_y + title_text.get_height() + int(8 * self.scale_y)
        if self.selected_cartella_numbers:
            self.draw_selected_cartellas(padding, selected_y, screen_width - padding*2)
            selected_height = int(min(40 * self.scale_y, available_height * 0.05))  # Limit selected height
        else:
            selected_height = 0
        
        # Calculate grid starting position
        grid_y = selected_y + selected_height + padding
        
        # Ensure grid fits within available space
        max_grid_height = screen_height - grid_y - bottom_section_height - padding * 2
        grid_height = min(grid_height, max_grid_height)
        
        # Draw number grid (BINGO board) in a more compact area
        self.draw_bingo_grid()
        
        # Recalculate bottom section position to ensure it's visible
        # Make sure bottom_y doesn't go beyond the screen height
        bottom_y = min(grid_y + grid_height + padding, screen_height - bottom_section_height - padding)
        
        # Calculate column widths for 3-column layout
        col_width = int((screen_width - padding*4) / 3)
        
        # Draw PRIZE POOL section (left column)
        prize_pool_x = padding
        prize_pool_width = col_width
        self.draw_prize_pool_section(prize_pool_x, bottom_y, prize_pool_width, bottom_section_height)
        
        # Draw Add Players section (middle column)
        add_players_x = prize_pool_x + prize_pool_width + padding
        add_players_width = col_width
        self.draw_add_players_section(add_players_x, bottom_y, add_players_width, bottom_section_height)
        
        # Draw Next button (right column, centered vertically)
        next_btn_width = int(col_width * 0.8)
        next_btn_height = int(min(70 * self.scale_y, bottom_section_height * 0.5))
        next_btn_x = add_players_x + add_players_width + padding + int((col_width - next_btn_width)/2)
        next_btn_y = bottom_y + int((bottom_section_height - next_btn_height)/2)
        self.draw_next_button(next_btn_x, next_btn_y, next_btn_width, next_btn_height)
        
        # Draw toast message if active
        if self.message_timer > 0:
            self.draw_toast_message()
    
    def draw_continue_button(self, x, y, width, height):
        """Draw the continue/next button with hover effect"""
        # Get current mouse position for hover check
        mouse_pos = pygame.mouse.get_pos()
        
        # Create a rect for the button
        button_rect = pygame.Rect(x, y, width, height)
        
        # Get button animation state
        btn_state = self.button_states["continue"]
        
        # Check for hover animation
        hover_alpha = btn_state["hover_alpha"]
        
        # Base color and hover color
        base_color = (0, 120, 180)
        hover_color = (0, 150, 220)
        
        # Calculate effective color based on hover state
        if hover_alpha > 0:
            # Blend colors based on hover alpha
            effective_color = (
                int(base_color[0] + (hover_color[0] - base_color[0]) * hover_alpha / 255),
                int(base_color[1] + (hover_color[1] - base_color[1]) * hover_alpha / 255),
                int(base_color[2] + (hover_color[2] - base_color[2]) * hover_alpha / 255)
            )
        else:
            effective_color = base_color
        
        # Draw button
        self.draw_gradient_rect(button_rect, effective_color, 
                               (effective_color[0]//2, effective_color[1]//2, effective_color[2]//2), 10)
        
        # Draw text
        btn_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        btn_text = btn_font.render("Continue to Game", True, WHITE)
        text_x = button_rect.centerx - btn_text.get_width() // 2
        text_y = button_rect.centery - btn_text.get_height() // 2
        self.screen.blit(btn_text, (text_x, text_y))
        
        # Store hit area
        self.hit_areas["continue"] = button_rect
    
    def draw_next_button(self, x, y, width, height):
        """Draw the modern 'NEXT' button with arrow and hover effects"""
        # Get current mouse position for hover check
        mouse_pos = pygame.mouse.get_pos()
        
        # Create a rect for the button
        button_rect = pygame.Rect(x, y, width, height)
        
        # Check if mouse is hovering over the button
        is_hovering = button_rect.collidepoint(mouse_pos)
        
        # Draw button with modern gradient and glow effect
        if is_hovering:
            # Brighter colors for hover state
            self.draw_gradient_rect(button_rect, (0, 180, 220), (0, 140, 200), 12)
            
            # Add enhanced glow effect when hovering
            for i in range(1, 5):
                glow_rect = pygame.Rect(
                    button_rect.x - i,
                    button_rect.y - i,
                    button_rect.width + i*2,
                    button_rect.height + i*2
                )
                glow_color = (0, 220, 255, 70 - i*15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=12+i)
                
            # Add inner highlight
            inner_highlight = pygame.Rect(
                button_rect.x + 2,
                button_rect.y + 2,
                button_rect.width - 4,
                int(button_rect.height * 0.3)
            )
            pygame.draw.rect(self.screen, (100, 220, 255, 40), inner_highlight, border_radius=10)
        else:
            # Normal state colors
            self.draw_gradient_rect(button_rect, (0, 140, 180), (0, 100, 140), 12)
        
        # Draw elegant "NEXT" text
        font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)
        text = font.render("NEXT", True, (255, 255, 255))
        
        # Calculate position for text and icon
        content_width = text.get_width() + int(30 * self.scale_x)  # Text + space + arrow
        text_x = x + int((width - content_width) / 2)
        text_y = y + int((height - text.get_height()) / 2)
        
        # Draw text with slight shadow for depth
        shadow_offset = int(1 * min(self.scale_x, self.scale_y))
        text_shadow = font.render("NEXT", True, (0, 0, 0, 150))
        self.screen.blit(text_shadow, (text_x + shadow_offset, text_y + shadow_offset))
        self.screen.blit(text, (text_x, text_y))
        
        # Draw modern arrow icon instead of simple triangle
        arrow_x = text_x + text.get_width() + int(10 * self.scale_x)
        arrow_y = int(y + height / 2)
        arrow_size = int(16 * min(self.scale_x, self.scale_y))
        
        # Draw circular background for arrow
        circle_radius = int(arrow_size * 0.8)
        pygame.draw.circle(self.screen, (255, 255, 255, 90), 
                          (int(arrow_x + arrow_size/2), arrow_y), circle_radius)
        
        # Draw arrow shape
        arrow_points = [
            (arrow_x, int(arrow_y - arrow_size/2)),                    # Top point
            (arrow_x + arrow_size, arrow_y),                           # Right point
            (arrow_x, int(arrow_y + arrow_size/2)),                    # Bottom point
            (arrow_x + int(arrow_size/4), arrow_y)                     # Inner point
        ]
        
        # Draw shadow for arrow
        shadow_points = [(p[0] + shadow_offset, p[1] + shadow_offset) for p in arrow_points]
        pygame.draw.polygon(self.screen, (0, 0, 0, 100), shadow_points)
        
        # Draw arrow
        pygame.draw.polygon(self.screen, (255, 255, 255), arrow_points)
        
        # Add subtle pulsating animation effect when hovering
        if is_hovering:
            # Calculate pulse based on time
            pulse = (math.sin(time.time() * 4) + 1) / 8  # Subtle pulse value
            
            # Draw pulsating border
            pulse_rect = pygame.Rect(
                button_rect.x - int(2 * pulse * self.scale_x),
                button_rect.y - int(2 * pulse * self.scale_y),
                button_rect.width + int(4 * pulse * self.scale_x),
                button_rect.height + int(4 * pulse * self.scale_y)
            )
            pygame.draw.rect(self.screen, (100, 220, 255, int(100 * pulse)), pulse_rect, 2, border_radius=14)
        
        # Store hit area for next button
        self.hit_areas["next_button"] = button_rect
    
    def draw_selected_cartellas(self, x, y, width):
        """Draw a visually appealing list of selected cartella numbers"""
        if not self.selected_cartella_numbers:
            return
        
        # Calculate appropriate padding and sizes
        padding = int(10 * min(self.scale_x, self.scale_y))
        chip_size = int(28 * min(self.scale_x, self.scale_y))
        chip_spacing = int(6 * min(self.scale_x, self.scale_y))
        
        # Draw header with small indicator icon
        header_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True)
        header_text = header_font.render("Selected Cartella Numbers:", True, (200, 200, 220))
        
        # Create a small tag icon before the header
        icon_size = int(header_text.get_height() * 0.9)
        icon_x = x
        icon_y = y + (header_text.get_height() - icon_size) // 2
        
        # Draw tag icon
        tag_points = [
            (icon_x, icon_y),
            (icon_x + int(icon_size*0.7), icon_y),
            (icon_x + icon_size, icon_y + int(icon_size*0.5)),
            (icon_x + int(icon_size*0.7), icon_y + icon_size),
            (icon_x, icon_y + icon_size)
        ]
        pygame.draw.polygon(self.screen, (0, 160, 220), tag_points)
        pygame.draw.polygon(self.screen, (0, 120, 180), tag_points, 1)
        
        # Draw small circle in the tag
        pygame.draw.circle(self.screen, (255, 255, 255, 180), 
                          (icon_x + int(icon_size*0.3), icon_y + int(icon_size*0.5)), 
                          int(icon_size*0.2))
        
        # Position header text after the icon
        header_x = icon_x + icon_size + padding//2
        self.screen.blit(header_text, (header_x, y))
        
        # Calculate available width for chips
        available_width = width - padding*2
        
        # Determine number of chips per row based on available space
        max_chips_per_row = max(1, int(available_width / (chip_size + chip_spacing)))
        
        # Organize numbers into rows
        rows = []
        current_row = []
        
        for num in sorted(self.selected_cartella_numbers):
            if len(current_row) >= max_chips_per_row:
                rows.append(current_row)
                current_row = []
            current_row.append(num)
        
        if current_row:
            rows.append(current_row)
        
        # Start position for drawing chips
        chips_y = y + header_text.get_height() + padding
        
        # Draw each row of chips
        for row_idx, row in enumerate(rows):
            row_y = chips_y + row_idx * (chip_size + chip_spacing//2)
            
            # Center the row of chips
            row_width = len(row) * chip_size + (len(row) - 1) * chip_spacing
            row_x = x + (available_width - row_width) // 2
            
            # Draw each chip in the row
            for i, num in enumerate(row):
                chip_x = row_x + i * (chip_size + chip_spacing)
                
                # Determine color based on the row index (alternate colors)
                colors = [(60, 140, 230), (230, 100, 100), (100, 200, 100), (220, 180, 50)]
                color_idx = row_idx % len(colors)
                color = colors[color_idx]
                
                # Draw chip background
                chip_rect = pygame.Rect(chip_x, row_y, chip_size, chip_size)
                
                # Create gradient for chip
                gradient_surf = pygame.Surface((chip_size, chip_size), pygame.SRCALPHA)
                for y_pos in range(chip_size):
                    # Calculate blend factor (0.0 to 1.0)
                    t = y_pos / chip_size
                    # Linear interpolation for gradient
                    r = int(color[0] * (1-t) + max(0, color[0]-30) * t)
                    g = int(color[1] * (1-t) + max(0, color[1]-30) * t)
                    b = int(color[2] * (1-t) + max(0, color[2]-30) * t)
                    pygame.draw.line(gradient_surf, (r, g, b), (0, y_pos), (chip_size, y_pos))
                
                # Apply rounded corners
                chip_mask = pygame.Surface((chip_size, chip_size), pygame.SRCALPHA)
                pygame.draw.rect(chip_mask, (255, 255, 255), (0, 0, chip_size, chip_size), border_radius=chip_size//2)
                gradient_surf.blit(chip_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
                
                # Draw chip with shadow
                shadow_offset = int(2 * min(self.scale_x, self.scale_y))
                shadow_surf = pygame.Surface((chip_size, chip_size), pygame.SRCALPHA)
                pygame.draw.rect(shadow_surf, (0, 0, 0, 80), (0, 0, chip_size, chip_size), border_radius=chip_size//2)
                self.screen.blit(shadow_surf, (chip_x + shadow_offset, row_y + shadow_offset))
                
                # Draw the chip
                self.screen.blit(gradient_surf, chip_rect)
                
                # Draw thin highlight border
                pygame.draw.rect(self.screen, (*color, 180), chip_rect, width=1, border_radius=chip_size//2)
                
                # Draw number text
                num_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True)
                num_text = str(num)
                num_surf = num_font.render(num_text, True, (255, 255, 255))
                num_rect = num_surf.get_rect(center=(chip_x + chip_size//2, row_y + chip_size//2))
                
                # Draw shadow for better readability
                shadow_surf = num_font.render(num_text, True, (0, 0, 0, 180))
                shadow_rect = shadow_surf.get_rect(center=(num_rect.centerx + 1, num_rect.centery + 1))
                self.screen.blit(shadow_surf, shadow_rect)
                self.screen.blit(num_surf, num_rect)
    
    def update_button_hover_states(self, mouse_pos):
        """Update hover states for all buttons based on mouse position"""
        for key, area in self.hit_areas.items():
            # Skip if this button doesn't have animation state
            if key not in self.button_states:
                continue
                
            # Check if mouse is over this button
            hover = area.collidepoint(mouse_pos)
            btn_state = self.button_states[key]
            
            # Update hover state
            if hover != btn_state["hover"]:
                btn_state["hover"] = hover
                if hover:
                    # Mouse entered - start hover-in animation
                    btn_state["hover_alpha"] = 1
                else:
                    # Mouse left - start hover-out animation
                    btn_state["hover_alpha"] = 255
            
            # Animate hover alpha
            if hover and btn_state["hover_alpha"] < 255:
                # Fade in (faster)
                btn_state["hover_alpha"] = min(255, btn_state["hover_alpha"] + 25)
            elif not hover and btn_state["hover_alpha"] > 0:
                # Fade out (slower)
                btn_state["hover_alpha"] = max(0, btn_state["hover_alpha"] - 15)
    
    def draw_wow_bingo_header(self):
        """Draw the enhanced WOW BINGO colorful header with professional animations"""
        # Get current time for animations
        current_time = pygame.time.get_ticks()

        # Define base sizes with subtle animation
        size_animation = math.sin(current_time * 0.002) * 0.05 + 1.0  # Subtle breathing effect
        wow_font_size = int(80 * min(self.scale_x, self.scale_y) * size_animation)
        bingo_font_size = int(65 * min(self.scale_x, self.scale_y) * size_animation)

        wow_font = pygame.font.SysFont("Impact", wow_font_size, bold=True)
        bingo_font = pygame.font.SysFont("Impact", bingo_font_size, bold=True)

        # Enhanced colors with animation intensity
        base_intensity = abs(math.sin(current_time * 0.003)) * 0.2 + 0.8

        wow_colors = [
            (int(255 * base_intensity), int(60 * base_intensity + 40), 0),     # W - Animated Red/Orange
            (int(255 * base_intensity), int(215 * base_intensity), 0),        # O - Animated Yellow/Gold
            (0, int(100 * base_intensity + 50), int(255 * base_intensity)),   # W - Animated Blue
        ]

        bingo_colors = [
            (int(30 * base_intensity + 20), int(100 * base_intensity + 50), int(255 * base_intensity)),   # B - Animated Blue
            (0, int(180 * base_intensity + 50), int(60 * base_intensity + 40)),                          # I - Animated Green
            (int(30 * base_intensity + 20), int(100 * base_intensity + 50), int(255 * base_intensity)),   # N - Animated Blue
            (int(255 * base_intensity), int(60 * base_intensity + 40), 0),                               # G - Animated Red
            (int(255 * base_intensity), int(215 * base_intensity), 0)                                    # O - Animated Yellow/Gold
        ]

        # Enhanced positioning with proper alignment
        base_x = int(30 * self.scale_x)
        base_y = int(25 * self.scale_y)

        # Add subtle floating animation
        float_offset = math.sin(current_time * 0.004) * 3
        x_pos = base_x
        y_pos = base_y + int(float_offset)

        # Draw enhanced "WOW" part with individual letter animations
        for i, letter in enumerate("WOW"):
            # Individual letter animation phase
            letter_phase = current_time * 0.005 + (i * 0.8)
            letter_intensity = abs(math.sin(letter_phase)) * 0.3 + 0.7

            # Animated color for this letter
            base_color = wow_colors[i]
            animated_color = (
                int(base_color[0] * letter_intensity),
                int(base_color[1] * letter_intensity),
                int(base_color[2] * letter_intensity)
            )

            # Enhanced shadow with multiple layers for depth
            shadow_layers = [
                (4, 4, (10, 10, 10)),
                (3, 3, (20, 20, 20)),
                (2, 2, (30, 30, 30))
            ]

            for shadow_x, shadow_y, shadow_color in shadow_layers:
                shadow_surf = wow_font.render(letter, True, shadow_color)
                self.screen.blit(shadow_surf, (x_pos + int(shadow_x * self.scale_x),
                                             y_pos + int(shadow_y * self.scale_y)))

            # Draw main letter with glow effect
            letter_surf = wow_font.render(letter, True, animated_color)
            self.screen.blit(letter_surf, (x_pos, y_pos))

            # Add animated glow effect
            glow_intensity = abs(math.sin(letter_phase * 1.2)) * 0.4 + 0.3
            glow_surface = pygame.Surface(letter_surf.get_size(), pygame.SRCALPHA)
            glow_text = wow_font.render(letter, True, (*animated_color, int(100 * glow_intensity)))
            glow_surface.blit(glow_text, (0, 0))

            # Apply glow with blend mode
            for offset in [(0, -1), (0, 1), (-1, 0), (1, 0)]:
                self.screen.blit(glow_surface, (x_pos + offset[0], y_pos + offset[1]),
                               special_flags=pygame.BLEND_ALPHA_SDL2)

            # Move position for next letter (slightly closer together)
            x_pos += letter_surf.get_width() * 0.82

        # Enhanced hyphen with animation
        hyphen_intensity = abs(math.sin(current_time * 0.006)) * 0.4 + 0.6
        hyphen_color = (int(255 * hyphen_intensity), int(255 * hyphen_intensity), int(255 * hyphen_intensity))
        hyphen_surf = wow_font.render("-", True, hyphen_color)

        # Add glow to hyphen
        hyphen_glow = pygame.Surface(hyphen_surf.get_size(), pygame.SRCALPHA)
        hyphen_glow_text = wow_font.render("-", True, (*hyphen_color, int(80 * hyphen_intensity)))
        hyphen_glow.blit(hyphen_glow_text, (0, 0))
        self.screen.blit(hyphen_glow, (x_pos, y_pos), special_flags=pygame.BLEND_ALPHA_SDL2)
        self.screen.blit(hyphen_surf, (x_pos, y_pos))

        x_pos += hyphen_surf.get_width() * 0.8

        # Add proper spacing between "WOW-" and "BINGO"
        x_pos += int(8 * self.scale_x)

        # Draw enhanced "BINGO" part with animated racing stripes
        for i, letter in enumerate("BINGO"):
            # Individual letter animation
            letter_phase = current_time * 0.004 + (i * 0.6)
            letter_intensity = abs(math.sin(letter_phase)) * 0.3 + 0.7

            # Animated color for this letter
            base_color = bingo_colors[i]
            animated_color = (
                int(base_color[0] * letter_intensity),
                int(base_color[1] * letter_intensity),
                int(base_color[2] * letter_intensity)
            )

            letter_surf = bingo_font.render(letter, True, animated_color)

            # Create enhanced surface for letter with animated racing stripes
            stripe_width = int(letter_surf.get_width() * 1.2)
            stripe_height = letter_surf.get_height()
            letter_with_stripe = pygame.Surface((stripe_width, stripe_height), pygame.SRCALPHA)

            # Animated racing stripes
            stripe_animation = (current_time * 0.01 + i * 0.5) % (math.pi * 2)
            stripe_y_pos = letter_surf.get_height() * 0.4
            stripe_h = letter_surf.get_height() * 0.2

            # Create animated racing stripes with flowing effect
            for j in range(4):  # More stripes for better effect
                stripe_offset = math.sin(stripe_animation + j * 0.8) * 10
                stripe_x = j * (letter_surf.get_width() * 0.15) + stripe_offset
                stripe_w = letter_surf.get_width() * 0.7

                # Animated opacity
                opacity_animation = abs(math.sin(stripe_animation + j * 0.5)) * 0.4 + 0.3
                opacity = int(180 * opacity_animation)

                # Enhanced stripe color with animation
                stripe_color = (
                    int(animated_color[0] * 0.8),
                    int(animated_color[1] * 0.8),
                    int(animated_color[2] * 0.8),
                    opacity
                )

                # Draw animated racing stripe
                if stripe_x + stripe_w > 0 and stripe_x < letter_surf.get_width():
                    pygame.draw.rect(
                        letter_with_stripe,
                        stripe_color,
                        (max(0, stripe_x), stripe_y_pos,
                         min(stripe_w, letter_surf.get_width() - max(0, stripe_x)), stripe_h)
                    )

            # Enhanced shadow with multiple layers
            shadow_layers = [
                (4, 4, (10, 10, 10)),
                (3, 3, (20, 20, 20)),
                (2, 2, (30, 30, 30))
            ]

            for shadow_x, shadow_y, shadow_color in shadow_layers:
                shadow_surf = bingo_font.render(letter, True, shadow_color)
                letter_with_stripe.blit(shadow_surf, (int(shadow_x * self.scale_x), int(shadow_y * self.scale_y)))

            # Draw the main letter
            letter_with_stripe.blit(letter_surf, (0, 0))

            # Add animated glow effect to the letter
            glow_intensity = abs(math.sin(letter_phase * 1.5)) * 0.5 + 0.3
            glow_surface = pygame.Surface(letter_surf.get_size(), pygame.SRCALPHA)
            glow_text = bingo_font.render(letter, True, (*animated_color, int(120 * glow_intensity)))
            glow_surface.blit(glow_text, (0, 0))

            # Apply glow to the letter surface
            for offset in [(0, -1), (0, 1), (-1, 0), (1, 0), (-1, -1), (1, 1)]:
                letter_with_stripe.blit(glow_surface, offset, special_flags=pygame.BLEND_ALPHA_SDL2)

            # Draw the complete enhanced letter to the screen
            bingo_y_offset = int(5 * self.scale_y) + int(math.sin(letter_phase) * 2)  # Individual letter float
            self.screen.blit(letter_with_stripe, (x_pos, y_pos + bingo_y_offset))

            # Move position for next letter with proper spacing
            x_pos += stripe_width - int(5 * self.scale_x)
    
    def draw_bingo_grid(self):
        """Draw a more compact and visually appealing BINGO grid with numbers 1-75"""
        # Get current mouse position for all hover checks
        mouse_pos = pygame.mouse.get_pos()
        
        screen_width = self.screen.get_width()
        padding = int(15 * min(self.scale_x, self.scale_y))
        
        # Calculate grid position dynamically based on layout
        grid_start_x = padding
        grid_start_y = int(150 * self.scale_y)
        
        # Improved row colors with better contrast
        row_colors = {
            'B': (255, 60, 80),    # Bright red for B
            'I': (40, 220, 120),   # Vibrant green for I 
            'N': (50, 120, 255),   # Bright blue for N
            'G': (255, 60, 80),    # Same red for G (matching B)
            'O': (255, 220, 40)    # Bright yellow/gold for O
        }
        row_letters = "BINGO"
        
        # More compact row dimensions
        row_height = int(60 * self.scale_y)
        row_spacing = int(6 * self.scale_y)
        
        # Improved font sizes
        letter_font = pygame.font.SysFont("Impact", int(56 * min(self.scale_x, self.scale_y)), bold=True)
        number_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=False)
        small_number_font = pygame.font.SysFont("Arial", int(22 * min(self.scale_x, self.scale_y)), bold=False)
        
        # Larger font sizes for selected numbers (2x size)
        large_number_font = pygame.font.SysFont("Arial", int(48 * min(self.scale_x, self.scale_y)), bold=True)
        large_small_number_font = pygame.font.SysFont("Arial", int(44 * min(self.scale_x, self.scale_y)), bold=True)
        
        # Calculate max pages and max number
        max_num = 400  # Maximum number to display
        nums_per_page = 75  # Numbers per page
        max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)
        
        # Position for the first row
        current_y = grid_start_y
        
        # Calculate animation pulse effect
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1
        
        # Draw page indicator above grid
        page_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True)
        page_text = page_font.render(f"PAGE {self.grid_page + 1}/{max_pages}", True, (180, 180, 180))
        page_x = grid_start_x + int(200 * self.scale_x)  # Centered position
        page_y = grid_start_y - int(20 * self.scale_y)  # Position above grid
        self.screen.blit(page_text, (page_x, page_y))
        
        # Calculate the total height of the bingo grid
        total_grid_height = (row_height + row_spacing) * 5 - row_spacing
        
        # Draw vertical navigation buttons on the right side - more compact design
        nav_btn_width = int(15 * self.scale_x)  # Reduced width
        nav_btn_height = total_grid_height
        
        # Position next button at the right edge
        nav_btn_x = screen_width - padding - nav_btn_width
        nav_btn_y = grid_start_y
        nav_btn_rect = pygame.Rect(nav_btn_x, nav_btn_y, nav_btn_width, nav_btn_height)
        
        # Position previous button immediately to the left of the next button with minimal gap
        prev_btn_x = nav_btn_x - nav_btn_width - int(2 * self.scale_x)
        prev_btn_y = grid_start_y
        prev_btn_rect = pygame.Rect(prev_btn_x, prev_btn_y, nav_btn_width, nav_btn_height)
        
        # Check mouse hover for prev button
        prev_btn_hover = prev_btn_rect.collidepoint(mouse_pos)
        
        # Draw the previous button with gradient effect (matching the next button style)
        # Use more vibrant colors for an amazing look
        prev_start_color = (60, 140, 255) if prev_btn_hover else (40, 100, 220)  # Brighter blue
        prev_end_color = (20, 80, 200) if prev_btn_hover else (10, 60, 180)      # Deeper blue
        
        # Create gradient surface for previous button
        prev_btn_surf = pygame.Surface((nav_btn_width, nav_btn_height), pygame.SRCALPHA)
        for y in range(nav_btn_height):
            # Calculate blend factor
            t = y / nav_btn_height
            # Linear interpolation of colors
            r = int(prev_start_color[0] * (1-t) + prev_end_color[0] * t)
            g = int(prev_start_color[1] * (1-t) + prev_end_color[1] * t)
            b = int(prev_start_color[2] * (1-t) + prev_end_color[2] * t)
            pygame.draw.line(prev_btn_surf, (r, g, b), (0, y), (nav_btn_width, y))
        
        # Draw the previous button with rounded corners
        pygame.draw.rect(prev_btn_surf, (0, 0, 0, 0), (0, 0, nav_btn_width, nav_btn_height), border_radius=5)
        self.screen.blit(prev_btn_surf, prev_btn_rect)
        
        # Draw previous button border with glowing effect
        border_color = (100, 180, 255) if prev_btn_hover else (70, 130, 220)
        pygame.draw.rect(self.screen, border_color, prev_btn_rect, width=1, border_radius=5)
        
        # Add subtle glow effect for previous button when hovered
        if prev_btn_hover:
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    prev_btn_rect.x - i, prev_btn_rect.y - i,
                    prev_btn_rect.width + i*2, prev_btn_rect.height + i*2
                )
                glow_color = (100, 180, 255, 70 - i*20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=5+i)
        
        # Draw "<" arrow symbol in the center of previous button with improved visibility
        prev_arrow_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True)
        prev_arrow_text = prev_arrow_font.render("<", True, (255, 255, 255))  # Brighter white
        prev_arrow_x = prev_btn_x + (nav_btn_width - prev_arrow_text.get_width()) // 2
        prev_arrow_y = prev_btn_y + (nav_btn_height - prev_arrow_text.get_height()) // 2
        self.screen.blit(prev_arrow_text, (prev_arrow_x, prev_arrow_y))
        
        # Store prev button hit area
        self.hit_areas["prev_grid_page"] = prev_btn_rect
        
        # Check mouse hover for nav button
        nav_btn_hover = nav_btn_rect.collidepoint(mouse_pos)
        
        # Draw the navigation button with gradient effect - matching style with previous button
        start_color = (60, 140, 255) if nav_btn_hover else (40, 100, 220)  # Brighter blue
        end_color = (20, 80, 200) if nav_btn_hover else (10, 60, 180)      # Deeper blue
        
        # Create gradient surface
        nav_btn_surf = pygame.Surface((nav_btn_width, nav_btn_height), pygame.SRCALPHA)
        for y in range(nav_btn_height):
            # Calculate blend factor
            t = y / nav_btn_height
            # Linear interpolation of colors
            r = int(start_color[0] * (1-t) + end_color[0] * t)
            g = int(start_color[1] * (1-t) + end_color[1] * t)
            b = int(start_color[2] * (1-t) + end_color[2] * t)
            pygame.draw.line(nav_btn_surf, (r, g, b), (0, y), (nav_btn_width, y))
        
        # Draw the button with rounded corners
        pygame.draw.rect(nav_btn_surf, (0, 0, 0, 0), (0, 0, nav_btn_width, nav_btn_height), border_radius=5)
        self.screen.blit(nav_btn_surf, nav_btn_rect)
        
        # Draw button border with glowing effect - matching previous button
        border_color = (100, 180, 255) if nav_btn_hover else (70, 130, 220)
        pygame.draw.rect(self.screen, border_color, nav_btn_rect, width=1, border_radius=5)
        
        # Add subtle glow effect for next button when hovered
        if nav_btn_hover:
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    nav_btn_rect.x - i, nav_btn_rect.y - i,
                    nav_btn_rect.width + i*2, nav_btn_rect.height + i*2
                )
                glow_color = (100, 180, 255, 70 - i*20)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=5+i)
        
        # Draw ">" arrow symbol in the center of button with improved visibility
        arrow_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True)
        arrow_text = arrow_font.render(">", True, (255, 255, 255))  # Brighter white
        arrow_x = nav_btn_x + (nav_btn_width - arrow_text.get_width()) // 2
        arrow_y = nav_btn_y + (nav_btn_height - arrow_text.get_height()) // 2
        self.screen.blit(arrow_text, (arrow_x, arrow_y))
        
        # Store nav button hit area
        self.hit_areas["next_grid_page"] = nav_btn_rect
        
        # Draw each row (B, I, N, G, O)
        for row_idx, letter in enumerate(row_letters):
            row_color = row_colors[letter]
            
            # Draw row letter with modern styling
            letter_surf = letter_font.render(letter, True, row_color)
            letter_x = grid_start_x
            letter_y = current_y + (row_height - letter_surf.get_height()) // 2
            
            # Draw modern indicator bar next to letter
            bar_width = int(4 * min(self.scale_x, self.scale_y))
            bar_height = int(row_height * 0.6)
            bar_x = letter_x - bar_width - int(4 * self.scale_x)
            bar_y = current_y + (row_height - bar_height) // 2
            
            pygame.draw.rect(self.screen, row_color, 
                           (bar_x, bar_y, bar_width, bar_height), 
                           border_radius=int(2 * min(self.scale_x, self.scale_y)))
            
            # Draw letter with shadow effect for depth
            shadow_offset = int(3 * min(self.scale_x, self.scale_y))
            shadow_surf = letter_font.render(letter, True, (30, 30, 30, 150))
            self.screen.blit(shadow_surf, (letter_x + shadow_offset, letter_y + shadow_offset))
            self.screen.blit(letter_surf, (letter_x, letter_y))
            
            # Draw modern background for numbers
            # Add extra buffer for the navigation buttons to prevent overlap
            nav_buttons_width = nav_btn_width * 2 + int(5 * self.scale_x)  # Width of both nav buttons + gap
            row_bg_width = nav_btn_x - letter_x - letter_surf.get_width() - padding - nav_buttons_width 
            row_bg_x = letter_x + letter_surf.get_width() + int(10 * self.scale_x)
            row_bg_rect = pygame.Rect(row_bg_x, current_y, row_bg_width, row_height)
            
            # Draw rounded rectangle with subtle gradient
            bg_color_top = (25, 30, 40)
            bg_color_bottom = (20, 25, 30)
            radius = int(8 * self.scale_y)
            
            # Create gradient background
            bg_surf = pygame.Surface((int(row_bg_width), row_height), pygame.SRCALPHA)
            for y in range(row_height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / row_height
                # Linear interpolation of colors
                r = int(bg_color_top[0] * (1-t) + bg_color_bottom[0] * t)
                g = int(bg_color_top[1] * (1-t) + bg_color_bottom[1] * t)
                b = int(bg_color_top[2] * (1-t) + bg_color_bottom[2] * t)
                pygame.draw.line(bg_surf, (r, g, b), (0, y), (int(row_bg_width), y))
            
            # Create mask for rounded corners
            mask = pygame.Surface((int(row_bg_width), row_height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, int(row_bg_width), row_height), border_radius=radius)
            
            # Apply mask
            bg_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            self.screen.blit(bg_surf, row_bg_rect)
            
            # Draw subtle border
            pygame.draw.rect(self.screen, (40, 50, 60), row_bg_rect, width=1, border_radius=radius)
            
            # Calculate number of numbers in this row and spacing
            nums_per_row = 15
            num_spacing = row_bg_width / nums_per_row
            num_radius = min(int(22 * self.scale_x), int(num_spacing * 0.4))
            
            # Draw numbers for this row
            for col in range(nums_per_row):
                # Calculate actual number based on grid page
                base_num = row_idx * nums_per_row + col + 1
                num = base_num + (self.grid_page * nums_per_page)
                
                # Skip numbers beyond max_num
                if num > max_num:
                    continue
                
                # Calculate number position
                num_x = row_bg_x + int(num_spacing * (col + 0.5))
                num_y = current_y + row_height // 2
                
                # Check if number is called/selected or current
                is_selected = num in self.called_numbers
                is_current = num == self.cartella_number
                
                # Mouse hover check
                number_rect = pygame.Rect(
                    num_x - num_radius - int(2*self.scale_x),
                    num_y - num_radius - int(2*self.scale_y), 
                    (num_radius + int(2*self.scale_x)) * 2, 
                    (num_radius + int(2*self.scale_y)) * 2
                )
                is_hover = number_rect.collidepoint(mouse_pos)
                
                # Determine circle color with animation for current number
                if is_current:
                    # Pulsating highlight for current number
                    highlight_intensity = int(150 + 105 * pulse)
                    highlight_color = (
                        min(255, row_color[0] + highlight_intensity),
                        min(255, row_color[1] + highlight_intensity),
                        min(255, row_color[2] + highlight_intensity)
                    )
                    # Draw outer glow
                    for i in range(1, 4):
                        glow_radius = num_radius + int(i * 2 * self.scale_x)
                        glow_alpha = 150 - i * 40
                        glow_color = (*row_color, glow_alpha)
                        glow_surf = pygame.Surface((glow_radius*2, glow_radius*2), pygame.SRCALPHA)
                        pygame.draw.circle(glow_surf, glow_color, (glow_radius, glow_radius), glow_radius)
                        self.screen.blit(glow_surf, (num_x-glow_radius, num_y-glow_radius), special_flags=pygame.BLEND_RGBA_ADD)
                    
                    # Draw the main circle
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), 
                                      num_radius + int(2 * pulse * self.scale_x))
                    
                    # Draw inner circle for better contrast
                    pygame.draw.circle(self.screen, row_color, (num_x, num_y), 
                                      int(num_radius * 0.8))
                elif is_selected:
                    # Enhanced highlight for selected numbers
                    
                    # Draw outer glow
                    glow_color = (*row_color, 100)
                    glow_radius = num_radius + int(2 * self.scale_x)
                    glow_surf = pygame.Surface((glow_radius*2, glow_radius*2), pygame.SRCALPHA)
                    pygame.draw.circle(glow_surf, glow_color, (glow_radius, glow_radius), glow_radius)
                    self.screen.blit(glow_surf, (num_x-glow_radius, num_y-glow_radius), special_flags=pygame.BLEND_RGBA_ADD)
                    
                    # Brighter color for selected numbers with a subtle pulse
                    bright_factor = 0.7 + 0.3 * pulse
                    bright_color = (
                        min(255, int(row_color[0] * bright_factor)),
                        min(255, int(row_color[1] * bright_factor)),
                        min(255, int(row_color[2] * bright_factor))
                    )
                    pygame.draw.circle(self.screen, bright_color, (num_x, num_y), num_radius)
                    
                    # Draw a subtle ring around selected numbers
                    ring_color = (255, 255, 255, 100)
                    pygame.draw.circle(self.screen, ring_color, (num_x, num_y), num_radius, width=2)
                elif is_hover:
                    # Hover effect for non-selected numbers
                    hover_color = (100, 110, 130)
                    pygame.draw.circle(self.screen, hover_color, (num_x, num_y), num_radius)
                else:
                    # Improved gray color for non-selected numbers
                    pygame.draw.circle(self.screen, (70, 75, 85), (num_x, num_y), num_radius)
                
                # Choose font based on selection state and number size
                if is_selected or is_current:
                    # Use larger, bold font for selected/current numbers
                    current_font = large_number_font if num < 10 else large_small_number_font
                else:
                    # Use regular font for unselected numbers
                    current_font = number_font if num < 10 else small_number_font
                
                # Draw number text with improved contrast
                num_text = str(num)
                
                # Text color depends on state
                if is_selected:
                    text_color = (255, 255, 255)  # White for selected
                elif is_current:
                    text_color = (255, 255, 255)  # White for current
                elif is_hover:
                    text_color = (255, 255, 255)  # White for hover
                else:
                    text_color = (220, 220, 220)  # Light gray for normal
                
                num_surf = current_font.render(num_text, True, text_color)
                num_rect = num_surf.get_rect(center=(num_x, num_y))
                
                # Add subtle shadow for better readability
                shadow_surf = current_font.render(num_text, True, (20, 20, 20))
                shadow_rect = shadow_surf.get_rect(center=(num_x + int(1*self.scale_x), 
                                                          num_y + int(1*self.scale_y)))
                self.screen.blit(shadow_surf, shadow_rect)
                self.screen.blit(num_surf, num_rect)
                
                # Store hit area for each number with slightly larger clickable area
                self.hit_areas[f"lucky_number_{num}"] = number_rect
            
            # Move to next row with compact spacing
            current_y += row_height + row_spacing
    
    def check_button_click(self, pos):
        """Handle button click events based on cursor position"""
        # Make a copy of the hit areas dictionary to avoid modification during iteration
        hit_areas_copy = dict(self.hit_areas.items())
        
        # Check if we clicked on any of our hit areas
        for key, area in hit_areas_copy.items():
            if area.collidepoint(pos):
                # Play button click sound if available
                if self.button_click_sound:
                    self.button_click_sound.play()
                
                # Handle specific button actions
                if key == "next_button":
                    if len(self.selected_cartella_numbers) > 0:
                        self.hit_areas["continue_clicked"] = True
                    else:
                        self.show_message("Please select at least one cartella number", "warning")
                
                elif key == "continue":
                    if len(self.selected_cartella_numbers) > 0:
                        self.hit_areas["continue_clicked"] = True
                    else:
                        self.show_message("Please select at least one cartella number", "warning")
                
                elif key == "cartella_input":
                    self.input_active = True
                    self.bet_input_active = False
                
                elif key == "bet_input":
                    self.bet_input_active = True
                    self.input_active = False
                
                elif key == "set_button":
                    # Apply the bet input value
                    self._apply_bet_input()
                    self.calculate_prize_pool()
                    self.show_message(f"Bet amount set to {self.bet_amount} ETB", "success")
                
                elif key == "next_grid_page":
                    # Move to the next page of numbers
                    max_num = 400
                    nums_per_page = 75
                    max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)
                    
                    # Increment page and wrap around if needed
                    self.grid_page = (self.grid_page + 1) % max_pages
                    
                    # Show message about the current page
                    page_msg = f"Showing numbers {self.grid_page * nums_per_page + 1}-"
                    if self.grid_page == max_pages - 1:  # Last page
                        page_msg += str(max_num)
                    else:
                        page_msg += str((self.grid_page + 1) * nums_per_page)
                    
                    self.show_message(page_msg, "info")
                
                elif key == "prev_grid_page":
                    # Move to the previous page of numbers
                    max_num = 400
                    nums_per_page = 75
                    max_pages = (max_num // nums_per_page) + (1 if max_num % nums_per_page > 0 else 0)
                    
                    # Decrement page and wrap around if needed
                    self.grid_page = (self.grid_page - 1) % max_pages
                    
                    # Show message about the current page
                    page_msg = f"Showing numbers {self.grid_page * nums_per_page + 1}-"
                    if self.grid_page == max_pages - 1:  # Last page
                        page_msg += str(max_num)
                    else:
                        page_msg += str((self.grid_page + 1) * nums_per_page)
                    
                    self.show_message(page_msg, "info")
                
                elif key == "cartella_up":
                    # Increase cartella number by 1
                    new_number = self.cartella_number + 1
                    if self.validate_cartella_number(new_number):
                        self.cartella_number = new_number
                        self.input_text = str(new_number)
                        self.create_board_from_number(new_number)
                
                elif key == "cartella_down":
                    # Decrease cartella number by 1
                    new_number = self.cartella_number - 1
                    if self.validate_cartella_number(new_number):
                        self.cartella_number = new_number
                        self.input_text = str(new_number)
                        self.create_board_from_number(new_number)
                
                elif key == "add_player" or key == "add_button":
                    # If input is active, apply the input value first
                    if self.input_active:
                        is_valid, error_msg = self.validate_cartella_number(self.input_text)
                        if is_valid:
                            self.cartella_number = int(self.input_text)
                            self.create_board_from_number(self.cartella_number)
                            self.input_active = False
                        else:
                            self.show_message(error_msg, "error")
                            return
                    
                    # Check if cartella is already selected
                    if self.cartella_number in self.selected_cartella_numbers:
                        # Remove the cartella
                        self.selected_cartella_numbers.remove(self.cartella_number)
                        
                        # Remove from called_numbers for highlighting
                        if self.cartella_number in self.called_numbers:
                            self.called_numbers.remove(self.cartella_number)
                        
                        # Remove from players list
                        for i, player in enumerate(self.players):
                            if player.cartela_no == self.cartella_number:
                                self.players.pop(i)
                                break
                        
                        # Save updated players to JSON
                        save_players_to_json(self.players)
                        
                        # Update prize pool
                        self.calculate_prize_pool()
                        
                        # Show success message
                        self.show_message(f"Removed player with cartella #{self.cartella_number}", "success")
                    else:
                        # Add current cartella to the selected list
                        self.add_player()
                
                elif key.startswith("lucky_number_"):
                    # Extract number from key (format: "lucky_number_XX")
                    try:
                        number = int(key.split("_")[-1])
                        if number >= 1 and number <= 400:  # Ensure it's a valid grid number
                            # Check if number is already selected
                            if number in self.called_numbers:
                                # Remove the number from highlighting and selection
                                self.called_numbers.remove(number)
                                
                                # Remove from selected cartella numbers if it exists
                                if number in self.selected_cartella_numbers:
                                    self.selected_cartella_numbers.remove(number)
                                
                                # Remove from players list
                                for i, player in enumerate(self.players):
                                    if player.cartela_no == number:
                                        self.players.pop(i)
                                        break
                                
                                # Save updated players to JSON
                                save_players_to_json(self.players)
                                
                                # Update prize pool
                                self.calculate_prize_pool()
                                
                                # Show success message
                                self.show_message(f"Removed player with cartella #{number}", "success")
                            else:
                                # Update current cartella number to clicked number
                                self.cartella_number = number
                                self.input_text = str(number)
                                self.create_board_from_number(number)
                                
                                # Add this number to called_numbers for highlighting
                                if number not in self.called_numbers:
                                    self.called_numbers.append(number)
                                
                                # Add player with this cartella number
                                self.add_player()
                    except:
                        pass
    
    def add_player(self):
        """Add player with current cartella number"""
        # Validate cartella number
        is_valid, error_msg = self.validate_cartella_number(str(self.cartella_number))
        if not is_valid:
            self.message = error_msg
            self.message_type = "error"
            self.message_timer = 180
            return
        
        # Add cartella number to selected list if not already there
        if self.cartella_number not in self.selected_cartella_numbers:
            self.selected_cartella_numbers.append(self.cartella_number)
            
            # Add the number to called_numbers list for highlighting in the grid
            if self.cartella_number not in self.called_numbers:
                self.called_numbers.append(self.cartella_number)
            
            # Create a new player
            player = Player(
                cartela_no=self.cartella_number,
                bet_amount=self.bet_amount
            )
            
            # Add to players list
            self.players.append(player)
            
            # Save players to JSON
            save_players_to_json(self.players)
            
            # Update prize pool
            self.calculate_prize_pool()
            
            # Show success message
            self.message = f"Added player with cartella #{self.cartella_number}"
            self.message_type = "success"
            self.message_timer = 180
        else:
            # Show error message
            self.message = f"Cartella #{self.cartella_number} already selected"
            self.message_type = "error"
            self.message_timer = 180
    
    def calculate_prize_pool(self):
        """Calculate prize pool based on players and bet amount"""
        total_bets = sum(player.bet_amount for player in self.players)
        self.prize_pool = total_bets
    
    def validate_cartella_number(self, number):
        """Validate cartella number input"""
        try:
            num = int(number)
            if num < 1 or num > 100:
                return False, "Cartella number must be between 1 and 100"
            return True, ""
        except ValueError:
            return False, "Cartella number must be a valid integer"
    
    def handle_input(self, event):
        """Handle keyboard input events"""
        if not self.input_active and not self.bet_input_active:
            return False
            
        if event.type != pygame.KEYDOWN:
            return False
            
        # Flag to track if we should play a sound
        play_sound = False
            
        if event.key == pygame.K_ESCAPE:
            # Cancel input
            if self.input_active:
                self.input_active = False
                self.input_text = str(self.cartella_number)
                play_sound = True
            elif self.bet_input_active:
                self.bet_input_active = False
                self.bet_input_text = str(self.bet_amount)
                play_sound = True
            
            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True
            
        if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
            # Confirm input
            if self.input_active:
                self.input_active = False
                is_valid, error_msg = self.validate_cartella_number(self.input_text)
                if is_valid:
                    self.cartella_number = int(self.input_text)
                    # Update bingo board based on new cartella number
                    self.create_board_from_number(self.cartella_number)
                    play_sound = True
                else:
                    # Show error message
                    self.message = error_msg
                    self.message_type = "error"
                    self.message_timer = 180
                    play_sound = True
            elif self.bet_input_active:
                self._apply_bet_input()
                self.bet_input_active = False
                play_sound = True
            
            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True
            
        if event.key == pygame.K_BACKSPACE:
            # Remove last character
            if self.input_active and self.input_text:
                self.input_text = self.input_text[:-1]
                play_sound = True
            elif self.bet_input_active and self.bet_input_text:
                self.bet_input_text = self.bet_input_text[:-1]
                play_sound = True
                
            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True
        
        # Only allow numeric input
        if event.unicode.isdigit():
            if self.input_active:
                # Limit input length
                if len(self.input_text) < 2:  # Max 2 digits for cartella (1-75)
                    self.input_text += event.unicode
                    play_sound = True
            elif self.bet_input_active:
                # Limit input length
                if len(self.bet_input_text) < 4:  # Max 4 digits for bet (up to 9999)
                    self.bet_input_text += event.unicode
                    play_sound = True
                    
            if play_sound and self.button_click_sound:
                self.button_click_sound.play()
            return True
            
        return False
    
    def _apply_bet_input(self):
        """Apply the bet input value"""
        try:
            value = int(self.bet_input_text)
            if value < 10:
                value = 10  # Minimum bet amount
            elif value > 1000:
                value = 1000  # Maximum bet amount
                
            self.bet_amount = value
            self.bet_input_text = str(value)
        except ValueError:
            # Reset to previous value
            self.bet_input_text = str(self.bet_amount)
    
    def show_message(self, message, message_type):
        """Show a toast message of the specified type (success, warning, error)"""
        self.message = message
        self.message_type = message_type
        self.message_timer = 180  # Show for about 6 seconds at 30 FPS
    
    def draw_toast_message(self):
        """Draw responsive toast message for feedback"""
        if self.message_timer <= 0:
            return
            
        # Choose color based on message type
        if self.message_type == "error":
            color = (255, 70, 70)  # Red for errors
        elif self.message_type == "success":
            color = (70, 255, 70)  # Green for success
        else:
            color = (70, 70, 255)  # Blue for info
            
        # Calculate size and position
        screen_width, screen_height = self.screen.get_size()
        
        # Responsive padding based on screen size
        padding = max(int(12 * min(self.scale_x, self.scale_y)), 6)
        
        # Responsive font size based on screen size
        font_size = max(int(18 * min(self.scale_x, self.scale_y)), 12)
        font_size = min(font_size, int(screen_height * 0.03))  # Prevent too large
        
        # Calculate size based on message length
        font = pygame.font.SysFont("Arial", font_size)
        msg_surf = font.render(self.message, True, WHITE)
        
        # Ensure toast isn't too wide for the screen
        max_width = screen_width * 0.8
        if msg_surf.get_width() > max_width - padding * 2:
            # Need to wrap text
            wrapped_text = self._wrap_text(self.message, font, max_width - padding * 2)
            total_height = 0
            text_surfs = []
            
            # Create surfaces for each line
            for line in wrapped_text:
                line_surf = font.render(line, True, WHITE)
                text_surfs.append(line_surf)
                total_height += line_surf.get_height()
            
            # Calculate toast dimensions
            toast_width = max(surf.get_width() for surf in text_surfs) + padding * 2
            toast_height = total_height + padding * 2
            
            # Create toast background
            toast_x = (screen_width - toast_width) // 2
            toast_y = screen_height - toast_height - padding * 2
            toast_rect = pygame.Rect(toast_x, toast_y, toast_width, toast_height)
            
            # Draw rounded rectangle with semi-transparency
            toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
            pygame.draw.rect(toast_surface, (*color, 220), toast_surface.get_rect(), 
                        border_radius=int(10 * min(self.scale_x, self.scale_y)))
            
            # Draw border
            pygame.draw.rect(toast_surface, (*color, 255), toast_surface.get_rect(), 
                            width=int(2 * min(self.scale_x, self.scale_y)), 
                            border_radius=int(10 * min(self.scale_x, self.scale_y)))
            
            # Draw to screen
            self.screen.blit(toast_surface, toast_rect)
            
            # Draw each line of text
            y_offset = padding
            for surf in text_surfs:
                x_pos = toast_x + (toast_width - surf.get_width()) // 2
                self.screen.blit(surf, (x_pos, toast_y + y_offset))
                y_offset += surf.get_height()
                
        else:
            # Simple single-line toast
            toast_width = msg_surf.get_width() + padding * 2
            toast_height = msg_surf.get_height() + padding
            
            # Center horizontally, position at bottom
            toast_x = (screen_width - toast_width) // 2
            toast_y = screen_height - toast_height - padding * 2
            
            # Create toast background
            toast_rect = pygame.Rect(toast_x, toast_y, toast_width, toast_height)
            
            # Draw rounded rectangle with semi-transparency
            toast_surface = pygame.Surface((toast_width, toast_height), pygame.SRCALPHA)
            pygame.draw.rect(toast_surface, (*color, 220), toast_surface.get_rect(), 
                        border_radius=int(10 * min(self.scale_x, self.scale_y)))
            
            # Draw border
            pygame.draw.rect(toast_surface, (*color, 255), toast_surface.get_rect(), 
                            width=int(2 * min(self.scale_x, self.scale_y)), 
                            border_radius=int(10 * min(self.scale_x, self.scale_y)))
            
            # Draw to screen
            self.screen.blit(toast_surface, toast_rect)
            
            # Draw message text
            self.screen.blit(msg_surf, (toast_x + padding, toast_y + padding // 2))
    
    def _wrap_text(self, text, font, max_width):
        """Helper method to wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []
        
        for word in words:
            # Try adding this word to the current line
            test_line = ' '.join(current_line + [word])
            test_width = font.size(test_line)[0]
            
            if test_width <= max_width:
                # Word fits, add it to the current line
                current_line.append(word)
            else:
                # Word doesn't fit, start a new line
                if current_line:  # Only append if there are words in the line
                    lines.append(' '.join(current_line))
                current_line = [word]
        
        # Add the last line
        if current_line:
            lines.append(' '.join(current_line))
            
        return lines

    # Methods to be implemented from main.py (copied/modified)
    def draw_gradient_rect(self, rect, color1, color2, border_radius=0, shadow=True):
        """Draw a rectangle with a vertical gradient"""
        # Create a surface with alpha channel
        surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        
        # Draw a filled rounded rect on the surface
        if border_radius > 0:
            # Draw gradient by blending two colors over height
            for y in range(rect.height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / rect.height
                
                # Linear interpolation of colors
                r = int(color1[0] * (1-t) + color2[0] * t)
                g = int(color1[1] * (1-t) + color2[1] * t)
                b = int(color1[2] * (1-t) + color2[2] * t)
                
                # Handle alpha if present
                a = 255
                if len(color1) > 3 and len(color2) > 3:
                    a = int(color1[3] * (1-t) + color2[3] * t)
                
                # Draw a line of the gradient
                pygame.draw.line(surf, (r, g, b, a), (0, y), (rect.width, y))
            
            # Apply rounded corners by drawing a rounded rect "mask"
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            pygame.draw.rect(mask, (255, 255, 255), (0, 0, rect.width, rect.height), border_radius=border_radius)
            
            # Combine surfaces
            final_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            final_surf.blit(surf, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            
            # Apply mask
            final_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            
            # Apply a shadow effect for depth if requested
            if shadow:
                shadow_offset = int(3 * self.scale_x)  # Increased shadow offset
                shadow_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
                pygame.draw.rect(shadow_surf, (0, 0, 0, 100), pygame.Rect(shadow_offset, shadow_offset, 
                                                                    rect.width-shadow_offset*2, 
                                                                    rect.height-shadow_offset*2), 
                               border_radius=border_radius)
                self.screen.blit(shadow_surf, (rect.x, rect.y))
            
            self.screen.blit(final_surf, rect)
        else:
            # Without rounded corners, draw gradient directly
            for y in range(rect.height):
                # Calculate blend factor (0.0 to 1.0)
                t = y / max(1, rect.height - 1)
                
                # Linear interpolation of colors
                r = int(color1[0] * (1-t) + color2[0] * t)
                g = int(color1[1] * (1-t) + color2[1] * t)
                b = int(color1[2] * (1-t) + color2[2] * t)
                
                # Handle alpha if present
                a = 255
                if len(color1) > 3 and len(color2) > 3:
                    a = int(color1[3] * (1-t) + color2[3] * t)
                
                # Draw a line of the gradient
                pygame.draw.line(surf, (r, g, b, a), (0, y), (rect.width, y))
            
            # Apply a shadow effect for depth if requested
            if shadow:
                shadow_offset = int(3 * self.scale_x)  # Increased shadow offset
                shadow_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
                pygame.draw.rect(shadow_surf, (0, 0, 0, 100), pygame.Rect(0, 0, 
                                                                    rect.width, 
                                                                    rect.height))
                self.screen.blit(shadow_surf, (rect.x + shadow_offset, rect.y + shadow_offset))
                
            self.screen.blit(surf, rect)

    def draw_lucky_numbers(self, x_position, section_width, section_height):
        """Draw the Lucky Numbers section at the specified position with the given dimensions"""
        # Position header correctly using passed coordinates and height
        header_rect = pygame.Rect(
            x_position,
            int(40 * self.scale_y),  
            section_width,
            section_height
        )
        self.draw_gradient_rect(header_rect, DARK_BLUE, LIGHT_BLUE, 10)
        
        # Calculate responsive font sizes based on available space
        header_size = max(int(28 * min(self.scale_x, self.scale_y)), int(section_width * 0.03))
        header_size = min(header_size, int(section_height * 0.06))  # Prevent too large fonts
        
        # Draw "LUCKY NUMBERS" text
        header_font = pygame.font.SysFont("Arial", header_size, bold=True)
        lucky_surf = header_font.render("CARTELLA NUMBERS", True, WHITE)
        lucky_x = header_rect.x + int(20 * self.scale_x)
        lucky_y = int(44 * self.scale_y)  # Moved up slightly
        self.screen.blit(lucky_surf, (lucky_x, lucky_y))
        
        # Draw total callout with orange color
        total_surf = header_font.render(f"#Total players:{len(self.called_numbers)}", True, ORANGE)
        total_x = header_rect.right - total_surf.get_width() - int(20 * self.scale_x)
        total_y = int(44 * self.scale_y)  # Moved up slightly
        self.screen.blit(total_surf, (total_x, total_y))
        
        # Letters for BINGO rows with improved colors for better visibility
        bingo_letters = "BINGO"
        row_colors = {
            'B': (255, 60, 60),    # Slightly brighter red for B
            'I': (50, 220, 100),   # Brighter green for I 
            'N': (80, 120, 255),   # Brighter blue for N
            'G': (255, 60, 60),    # Same red for G (matching B)
            'O': (255, 220, 50)    # Brighter gold/yellow for O
        }
        
        # Calculate responsive row spacing based on available height - make more compact
        content_start_y = lucky_y + lucky_surf.get_height() + int(6 * self.scale_y)  # Reduced padding
        available_height = header_rect.bottom - content_start_y - int(8 * self.scale_y)  # Reduced bottom padding
        row_height = available_height / 5  # 5 rows for B, I, N, G, O
        
        # Create responsive font sizes for letters and numbers
        letter_size = max(int(54 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.04))
        letter_size = min(letter_size, int(row_height * 0.7))  # Prevent too large fonts
        bingo_letter_font = pygame.font.SysFont("Arial Black", letter_size, bold=True)
        
        # DOUBLED base number size (from 28 to 56)
        number_size = max(int(56 * min(self.scale_x, self.scale_y) * 0.8), int(section_width * 0.04))
        number_size = min(number_size, int(row_height * 0.8))  # Increased max size to prevent capping
        number_font = pygame.font.SysFont("Arial", number_size, bold=True)
        
        # Calculate pulsating effect for animations
        pulse = (math.sin(time.time() * 3) + 1) / 2  # Value between 0 and 1
        fast_pulse = (math.sin(time.time() * 6) + 1) / 2  # Faster pulsing for current number
        
        # Draw each row of the bingo board
        for row_idx, letter in enumerate(bingo_letters):
            row_color = row_colors[letter]
            
            # Use more compact layout with letters on left and numbers spread across width
            letter_x = header_rect.x + int(20 * self.scale_x)  # Moved closer to left edge
            letter_y = content_start_y + row_idx * row_height
            
            # Draw cleaner horizontal lines (reduced count for cleaner look)
            line_count = 2  # Reduced line count
            line_length = int(10 * self.scale_x)  # Shorter lines
            line_thickness = max(1, int(2 * min(self.scale_x, self.scale_y)))
            line_spacing = int(5 * self.scale_y)  # Reduced spacing
            line_start_y = letter_y + int(28 * self.scale_y)
            
            for i in range(line_count):
                line_y_pos = line_start_y + i * line_spacing
                pygame.draw.line(self.screen, row_color, 
                               (letter_x - line_length - int(4 * self.scale_x), line_y_pos), 
                               (letter_x - int(4 * self.scale_x), line_y_pos), 
                               line_thickness)
            
            # Draw the BINGO letter with reduced 3D effect for better readability
            letter_surf = bingo_letter_font.render(letter, True, row_color)
            
            # Add subtle shadow for minimal 3D effect (reduced offset)
            shadow_offset = int(1 * self.scale_x)  # Reduced shadow
            shadow_surf = bingo_letter_font.render(letter, True, (row_color[0]//2, row_color[1]//2, row_color[2]//2))
            shadow_pos = (letter_x + shadow_offset, letter_y + shadow_offset)
            self.screen.blit(shadow_surf, shadow_pos)
            
            # Draw main letter
            self.screen.blit(letter_surf, (letter_x, letter_y))
            
            # Calculate responsive row background width - use more of the available width
            letter_width = letter_surf.get_width()
            number_area_width = section_width - letter_width - int(50 * self.scale_x)  # More width for numbers
            row_bg_width = min(number_area_width, header_rect.right - letter_x - int(40 * self.scale_x))
            
            # Make row background taller to fit larger numbers
            row_bg_rect = pygame.Rect(
                letter_x + int(45 * self.scale_x),  # Reduced gap between letter and numbers
                letter_y + int(6 * self.scale_y),   # Moved closer to letter
                row_bg_width,
                min(int(54 * self.scale_y), int(row_height * 0.85))  # Taller row background for larger numbers
            )
            pygame.draw.rect(self.screen, (20, 20, 25), row_bg_rect, border_radius=int(20 * self.scale_y))
            
            # Calculate number sizes and spacing based on available width
            available_width = row_bg_width
            # Increased circle size to accommodate larger numbers
            circle_radius = min(int(22 * min(self.scale_x, self.scale_y)), int(available_width / 42))
            
            # Each row will display two sets of numbers for a total of 20 numbers per row
            # For example: B row will have numbers 1-20, I row will have 21-40, etc.
            numbers_per_row = 20
            # Adjusted spacing to account for larger circles
            num_spacing = available_width / (numbers_per_row + 0.2)  # Reduced margin to fit larger circles
            
            # Draw the numbers in a single row - with animation for called numbers
            for col in range(numbers_per_row):
                num = row_idx * numbers_per_row + col + 1  # Numbers 1-100
                
                # Skip numbers beyond 100
                if num > 100:
                    continue
                
                # Calculate number position - ensure it's within the row background
                num_x = row_bg_rect.x + int(num_spacing/2) + col * num_spacing
                num_x = min(num_x, row_bg_rect.right - circle_radius)  # Ensure it doesn't exceed right boundary
                num_y = row_bg_rect.centery
                
                # Check if number has been called (for Board Selection, we want all numbers with hover effect)
                is_called = num in self.called_numbers
                is_current = num == self.current_number
                is_recently_called = is_current
                
                if is_recently_called:
                    # Recently called number - more dramatic pulsating glow effect
                    # Create pulsating glow effect with multiple layers
                    glow_radius = circle_radius * (1.0 + 0.5 * fast_pulse)
                    
                    # Draw outer glow circles with decreasing opacity
                    for i in range(2):  # Reduced glow layers
                        alpha = int(230 - i * 70)
                        glow_color = (*row_color, alpha)
                        glow_size = int(glow_radius) + i * 2
                        pygame.draw.circle(self.screen, glow_color, (num_x, num_y), glow_size, 2)
                    
                    # Strong highlight fill with pulsating brightness
                    brightness = int(220 + 35 * fast_pulse)
                    highlight_color = (min(255, row_color[0] + brightness//3), 
                                      min(255, row_color[1] + brightness//3), 
                                      min(255, row_color[2] + brightness//3))
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)
                    
                    # Add a white center for extra pop
                    pygame.draw.circle(self.screen, WHITE, (num_x, num_y), int(circle_radius * 0.7))
                    
                elif is_called:
                    # Called but not recent - highlight with row color
                    # Subtle pulsating effect for all called numbers
                    highlight_intensity = int(170 + 40 * pulse)
                    highlight_color = (min(255, row_color[0] * highlight_intensity // 255),
                                      min(255, row_color[1] * highlight_intensity // 255),
                                      min(255, row_color[2] * highlight_intensity // 255))
                    
                    # Draw background circle with row color
                    pygame.draw.circle(self.screen, highlight_color, (num_x, num_y), circle_radius)
                    
                    # Add inner circle for better contrast
                    inner_color = (min(255, highlight_color[0] + 30),
                                  min(255, highlight_color[1] + 30),
                                  min(255, highlight_color[2] + 30))
                    pygame.draw.circle(self.screen, inner_color, (num_x, num_y), int(circle_radius * 0.75))
                else:
                    # Not called - dark background
                    pygame.draw.circle(self.screen, (60, 60, 70), (num_x, num_y), circle_radius)
                
                # Calculate responsive font size for numbers based on circle size - maximized
                # Increased font-to-circle ratio significantly for 2x larger numbers
                num_font_size = min(number_size, int(circle_radius * 1.8))
                if num_font_size < 10:  # Min font size for readability (increased)
                    continue  # Skip rendering numbers if they'd be too small
                
                current_font = pygame.font.SysFont("Arial", num_font_size, bold=True)
                
                # Two-digit numbers need special handling
                text_to_render = str(num)
                # Adjusted scaling for multi-digit numbers to better fit larger text
                if num >= 10 and num < 100:
                    current_font = pygame.font.SysFont("Arial", int(num_font_size * 0.95), bold=True)
                elif num >= 100:
                    current_font = pygame.font.SysFont("Arial", int(num_font_size * 0.85), bold=True)
                
                # Draw the number text
                if is_recently_called:
                    # Text color for recently called number - always black for contrast against white center
                    text_color = (0, 0, 0)
                    # Use larger, bolder font for the recently called number
                    num_surf = current_font.render(text_to_render, True, text_color)
                elif is_called:
                    # Text color for called numbers - white with high contrast
                    text_color = WHITE
                    num_surf = current_font.render(text_to_render, True, text_color)
                else:
                    # Text color for uncalled numbers - slightly dimmer
                    text_color = (200, 200, 200)
                    num_surf = current_font.render(text_to_render, True, text_color)
                
                num_rect = num_surf.get_rect(center=(num_x, num_y))
                self.screen.blit(num_surf, num_rect)
                
                # Store hit area for each number - each number circle is clickable
                lucky_number_rect = pygame.Rect(
                    num_x - circle_radius, 
                    num_y - circle_radius,
                    circle_radius * 2, 
                    circle_radius * 2
                )
                self.hit_areas[f"lucky_number_{num}"] = lucky_number_rect

    def draw_add_players_section(self, x, y, width, height):
        """Draw a more compact and visually appealing Add Players section"""
        # Get current mouse position for all hover checks
        mouse_pos = pygame.mouse.get_pos()
        
        # Draw section background with a smoother gradient
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (15, 25, 45), (25, 35, 65), 12)
        
        # Calculate padding based on scale
        padding = int(10 * min(self.scale_x, self.scale_y))
        
        # Draw title "ADD PLAYERS" with a modern look
        title_font = pygame.font.SysFont("Arial", int(22 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("ADD PLAYERS", True, (230, 230, 230))
        
        # Add a colored indicator bar before the title
        indicator_width = int(4 * min(self.scale_x, self.scale_y))
        indicator_height = title_text.get_height()
        indicator_x = x + padding
        indicator_y = y + padding
        indicator_rect = pygame.Rect(indicator_x, indicator_y, indicator_width, indicator_height)
        pygame.draw.rect(self.screen, (0, 160, 220), indicator_rect, border_radius=2)
        
        # Position title text after the indicator
        title_x = indicator_x + indicator_width + padding
        title_y = y + padding
        self.screen.blit(title_text, (title_x, title_y))
        
        # Draw separator line with gradient effect
        line_y = title_y + title_text.get_height() + int(padding/2)
        line_width = width - padding*2
        line_height = int(2 * min(self.scale_x, self.scale_y))
        line_rect = pygame.Rect(x + padding, line_y, line_width, line_height)
        line_gradient = pygame.Surface((line_width, line_height), pygame.SRCALPHA)
        
        # Create gradient for line
        for i in range(line_width):
            alpha = 150 if i < line_width/2 else 150 - int(150 * (i - line_width/2) / (line_width/2))
            line_color = (70, 130, 180, alpha)
            pygame.draw.line(line_gradient, line_color, (i, 0), (i, line_height))
        
        self.screen.blit(line_gradient, line_rect)
        
        # Position for input components
        components_y = line_y + padding*2
        
        # Calculate available space for components
        available_height = height - (components_y - y) - padding
        component_height = int(available_height * 0.4)
        
        # Draw cartella number label with a cleaner font
        label_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True)
        cartella_label = label_font.render("CARTELLA NUMBER", True, (180, 180, 180))
        cartella_label_x = x + padding
        cartella_label_y = components_y
        self.screen.blit(cartella_label, (cartella_label_x, cartella_label_y))
        
        # Draw compact row with input box, arrows and add button
        input_y = cartella_label_y + cartella_label.get_height() + int(padding/2)
        row_height = component_height
        
        # Calculate widths for a more balanced layout
        input_width = int(width * 0.3)
        arrow_size = int(row_height * 0.6)
        arrow_gap = int(padding/2)
        add_btn_width = int(width * 0.3)
        
        # Draw cartella number input box with a modern design
        input_x = x + padding
        input_rect = pygame.Rect(input_x, input_y, input_width, row_height)
        
        # Check mouse hover for input
        input_hover = input_rect.collidepoint(mouse_pos)
        
        # Draw stylish input box with colored border
        if self.input_active or input_hover:
            # Active style with colored border
            border_color = (0, 160, 220)
            pygame.draw.rect(self.screen, (20, 30, 50), input_rect, border_radius=6)
            pygame.draw.rect(self.screen, border_color, input_rect, width=2, border_radius=6)
            
            # Draw subtle glow effect when active
        if self.input_active:
                for i in range(1, 3):
                    glow_rect = pygame.Rect(
                        input_rect.x - i, input_rect.y - i,
                        input_rect.width + i*2, input_rect.height + i*2
                    )
                    glow_color = (*border_color, 60 - i*20)
                    pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=6+i)
        else:
            # Inactive style
            pygame.draw.rect(self.screen, (20, 30, 50), input_rect, border_radius=6)
            pygame.draw.rect(self.screen, (40, 50, 70), input_rect, width=1, border_radius=6)
        
        # Draw cartella number value
        number_font = pygame.font.SysFont("Arial", int(22 * min(self.scale_x, self.scale_y)), bold=True)
        
        # Show input text with cursor if active
        if self.input_active:
            display_text = self.input_text
            if self.input_cursor_visible:
                display_text += "|"
            number_text = number_font.render(display_text, True, (255, 255, 255))
        else:
            number_text = number_font.render(str(self.cartella_number), True, (255, 255, 255))
            
        number_x = input_x + (input_width - number_text.get_width()) // 2
        number_y = input_y + (row_height - number_text.get_height()) // 2
        self.screen.blit(number_text, (number_x, number_y))
        
        # Position arrows and add button in the same row
        controls_x = input_x + input_width + arrow_gap
        
        # Draw up/down arrows grouped in a single control
        arrows_width = arrow_size*2
        arrows_height = row_height
        arrows_rect = pygame.Rect(controls_x, input_y, arrows_width, arrows_height)
        
        # Draw up arrow
        up_arrow_rect = pygame.Rect(controls_x, input_y, arrow_size, arrow_size)
        up_hover = up_arrow_rect.collidepoint(mouse_pos)
        
        # Draw down arrow
        down_arrow_rect = pygame.Rect(controls_x + arrow_size, input_y, arrow_size, arrow_size)
        down_hover = down_arrow_rect.collidepoint(mouse_pos)
        
        # Draw arrows container
        pygame.draw.rect(self.screen, (25, 35, 55), arrows_rect, border_radius=6)
        
        # Draw divider between arrows
        pygame.draw.line(
            self.screen,
            (50, 60, 80),
            (controls_x + arrow_size, input_y + int(arrows_height*0.15)),
            (controls_x + arrow_size, input_y + int(arrows_height*0.85)),
            1
        )
        
        # Draw up arrow with hover effect
        if up_hover:
            up_color = (0, 160, 220)
            # Highlight background
            pygame.draw.rect(self.screen, (30, 45, 65), up_arrow_rect, border_radius=6)
        else:
            up_color = (150, 160, 180)
            
        # Draw up triangle with improved design
        triangle_size = int(arrow_size * 0.4)
        pygame.draw.polygon(
            self.screen,
            up_color,
            [
                (controls_x + arrow_size//2, input_y + arrows_height//4),
                (controls_x + arrow_size//2 - triangle_size//2, input_y + arrows_height//4 + triangle_size),
                (controls_x + arrow_size//2 + triangle_size//2, input_y + arrows_height//4 + triangle_size)
            ]
        )
        
        # Draw down arrow with hover effect
        if down_hover:
            down_color = (0, 160, 220)
            # Highlight background
            pygame.draw.rect(self.screen, (30, 45, 65), down_arrow_rect, border_radius=6)
        else:
            down_color = (150, 160, 180)
            
        # Draw down triangle with improved design
        pygame.draw.polygon(
            self.screen,
            down_color,
            [
                (controls_x + arrow_size + arrow_size//2, input_y + arrows_height//4 + triangle_size),
                (controls_x + arrow_size + arrow_size//2 - triangle_size//2, input_y + arrows_height//4),
                (controls_x + arrow_size + arrow_size//2 + triangle_size//2, input_y + arrows_height//4)
            ]
        )
        
        # Add button with modern design
        add_btn_x = controls_x + arrows_width + arrow_gap
        add_btn_rect = pygame.Rect(add_btn_x, input_y, add_btn_width, row_height)
        
        # Check hover for ADD button
        add_btn_hover = add_btn_rect.collidepoint(mouse_pos)
        
        # Draw ADD button with modern gradient and subtle animation
        if add_btn_hover:
            self.draw_gradient_rect(add_btn_rect, (0, 140, 220), (0, 100, 180), 8)
            
            # Add glow effect on hover
            for i in range(1, 3):
                glow_rect = pygame.Rect(
                    add_btn_rect.x - i, add_btn_rect.y - i,
                    add_btn_rect.width + i*2, add_btn_rect.height + i*2
                )
                glow_color = (0, 150, 255, 50 - i*15)
                pygame.draw.rect(self.screen, glow_color, glow_rect, 1, border_radius=8+i)
        else:
            self.draw_gradient_rect(add_btn_rect, (0, 100, 180), (0, 80, 140), 8)
        
        # Draw ADD text with a modern small icon
        add_text = label_font.render("ADD", True, (255, 255, 255))
        add_x = add_btn_x + (add_btn_width - add_text.get_width()) // 2
        add_y = input_y + (row_height - add_text.get_height()) // 2
        self.screen.blit(add_text, (add_x, add_y))
        
        # Draw plus icon before text
        plus_size = int(add_text.get_height() * 0.6)
        plus_x = add_x - plus_size - int(padding/2)
        plus_y = add_y + (add_text.get_height() - plus_size) // 2
        
        plus_circle_color = (255, 255, 255, 150)
        pygame.draw.circle(self.screen, plus_circle_color, (int(plus_x + plus_size/2), int(plus_y + plus_size/2)), int(plus_size/2))
        
        # Horizontal and vertical lines to create + symbol
        line_thickness = max(1, int(plus_size * 0.1))
        pygame.draw.line(
            self.screen, 
            (0, 0, 0), 
            (int(plus_x + plus_size/4), int(plus_y + plus_size/2)),
            (int(plus_x + plus_size*3/4), int(plus_y + plus_size/2)),
            line_thickness
        )
        pygame.draw.line(
            self.screen, 
            (0, 0, 0), 
            (int(plus_x + plus_size/2), int(plus_y + plus_size/4)),
            (int(plus_x + plus_size/2), int(plus_y + plus_size*3/4)),
            line_thickness
        )

        # Store hit areas
        self.hit_areas["cartella_input"] = input_rect
        self.hit_areas["cartella_up"] = up_arrow_rect
        self.hit_areas["cartella_down"] = down_arrow_rect
        self.hit_areas["add_player"] = add_btn_rect

    def draw_prize_pool_section(self, x, y, width, height):
        """Draw the Prize Pool section on the top right of the screen"""
        # Create a background with subtle gradient and rounded corners
        section_rect = pygame.Rect(x, y, width, height)
        self.draw_gradient_rect(section_rect, (20, 40, 60), (30, 50, 70), 10)
        
        # "Prize Pool" title text with gold gradient
        header_height = int(height * 0.15)  # 15% for header
        
        # Draw gold-gradient title bar on top
        title_bar_rect = pygame.Rect(x, y, width, header_height)
        pygame.draw.rect(self.screen, GOLD, title_bar_rect, border_radius=10)
        pygame.draw.rect(self.screen, GOLD, 
                        (x, y+int(header_height/2), width, header_height/2), 
                        border_radius=0)
        
        # Add highlight to top of title bar
        highlight_rect = pygame.Rect(x+2, y+2, width-4, int(header_height*0.3))
        highlight_color = (255, 240, 180, 90)  # Semi-transparent light gold
        pygame.draw.rect(self.screen, highlight_color, highlight_rect, border_radius=8)
        
        # Draw title text
        title_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        title_text = title_font.render("PRIZE POOL", True, (20, 20, 20))
        title_x = x + (width - title_text.get_width()) // 2
        title_y = y + (header_height - title_text.get_height()) // 2
        
        # Add text shadow for better visibility
        shadow_text = title_font.render("PRIZE POOL", True, (10, 10, 10))
        self.screen.blit(shadow_text, (title_x+2, title_y+2))
        self.screen.blit(title_text, (title_x, title_y))
        
        # Calculate remaining usable area
        content_y = y + header_height + int(10 * self.scale_y)
        content_height = height - header_height - int(20 * self.scale_y)
        
        # BET AMOUNT section
        label_font = pygame.font.SysFont("Arial", int(16 * min(self.scale_x, self.scale_y)), bold=True)
        bet_label = label_font.render("BET AMOUNT:", True, (200, 200, 200))
        bet_label_y = content_y + int(10 * self.scale_y)
        self.screen.blit(bet_label, (x + int(10 * self.scale_x), bet_label_y))
        
        # Bet amount input
        input_height = int(36 * self.scale_y)
        input_width = int(width * 0.45)
        input_x = x + int(10 * self.scale_x)
        input_y = bet_label_y + bet_label.get_height() + int(5 * self.scale_y)
        
        # Draw bet input rectangle with dark background
        input_rect = pygame.Rect(input_x, input_y, input_width, input_height)
        pygame.draw.rect(self.screen, (10, 20, 30), input_rect, border_radius=5)
        pygame.draw.rect(self.screen, (60, 100, 150), input_rect, 1, border_radius=5)
        
        # Draw inset shadow at top of input
        pygame.draw.line(self.screen, (0, 0, 0, 100), 
                        (input_x+2, input_y+2), 
                        (input_x+input_width-2, input_y+2))
        
        # Add highlight to bottom of input
        pygame.draw.line(self.screen, (100, 140, 180), 
                        (input_x+2, input_y+input_height-2), 
                        (input_x+input_width-2, input_y+input_height-2))
        
        # Draw bet amount text
        input_font = pygame.font.SysFont("Arial", int(22 * min(self.scale_x, self.scale_y)), bold=True)
        
        # If input is active, use input_text, otherwise use stored value
        if self.bet_input_active:
            display_text = self.bet_input_text
            # Add cursor if visible
            if self.bet_input_cursor_visible:
                display_text += "|"
            text_color = (255, 255, 255)  # Brighter white when active
        else:
            display_text = str(self.bet_amount)
            text_color = (200, 200, 200)  # Slightly dimmer when inactive
        
        # Calculate position to right-align text with padding
        bet_text = input_font.render(display_text, True, text_color)
        text_x = input_x + input_width - bet_text.get_width() - int(10 * self.scale_x)
        text_y = input_y + (input_height - bet_text.get_height()) // 2
        self.screen.blit(bet_text, (text_x, text_y))
        
        # Draw "ETB" label
        etb_label = label_font.render("ETB", True, (255, 160, 0))
        etb_x = input_x + input_width + int(8 * self.scale_x)
        etb_y = input_y + (input_height - etb_label.get_height()) // 2
        self.screen.blit(etb_label, (etb_x, etb_y))
        
        # Draw "SET" button
        set_btn_width = int(width * 0.25)
        set_btn_height = input_height
        set_btn_x = input_x + input_width + int(8 * self.scale_x) + etb_label.get_width() + int(5 * self.scale_x)
        set_btn_y = input_y
        
        set_btn_rect = pygame.Rect(set_btn_x, set_btn_y, set_btn_width, set_btn_height)
        
        # Check if mouse is over set button
        mouse_pos = pygame.mouse.get_pos()
        set_btn_hover = set_btn_rect.collidepoint(mouse_pos)
        
        # Draw button with hover effect
        if set_btn_hover:
            self.draw_gradient_rect(set_btn_rect, (60, 150, 255), (40, 100, 200), 5)
        else:
            self.draw_gradient_rect(set_btn_rect, (50, 100, 200), (30, 80, 150), 5)
        
        # Draw set button text
        set_btn_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)), bold=True)
        set_btn_text = set_btn_font.render("SET", True, (255, 255, 255))
        set_text_x = set_btn_x + (set_btn_width - set_btn_text.get_width()) // 2
        set_text_y = set_btn_y + (set_btn_height - set_btn_text.get_height()) // 2
        self.screen.blit(set_btn_text, (set_text_x, set_text_y))
        
        # TOTAL POT section
        total_label = label_font.render("TOTAL POT:", True, (200, 200, 200))
        total_label_y = input_y + input_height + int(15 * self.scale_y)
        self.screen.blit(total_label, (x + int(10 * self.scale_x), total_label_y))
        
        # Draw prize display with glowing gold background
        prize_display_height = int(50 * self.scale_y)  # Taller display for prize
        prize_display_width = width - int(20 * self.scale_x)
        prize_display_x = x + int(10 * self.scale_x)
        prize_display_y = total_label_y + total_label.get_height() + int(5 * self.scale_y)
        
        # Create a rectangle for the prize display with rounded corners and gradient
        prize_display_rect = pygame.Rect(
            prize_display_x, prize_display_y, 
            prize_display_width, prize_display_height
        )
        
        # Draw a more stylized background for the prize amount
        # Golden gradient background
        self.draw_gradient_rect(prize_display_rect, (30, 30, 10), (50, 40, 15), 10)
        
        # Add a subtle inner border glow
        inner_border_color = (200, 160, 30, 100)  # Semi-transparent gold
        smaller_rect = pygame.Rect(
            prize_display_rect.x + 2, prize_display_rect.y + 2,
            prize_display_rect.width - 4, prize_display_rect.height - 4
        )
        pygame.draw.rect(self.screen, inner_border_color, smaller_rect, 1, border_radius=8)
        
        # Prize amount text with large font and glow
        prize_font = pygame.font.SysFont("Arial", int(30 * min(self.scale_x, self.scale_y)), bold=True)
        prize_text = prize_font.render(str(self.prize_pool), True, (255, 220, 50))  # Gold text
        
        # Center prize text in display area
        prize_x = prize_display_x + (prize_display_width - prize_text.get_width()) // 2
        prize_y = prize_display_y + (prize_display_height - prize_text.get_height()) // 2
        
        # Draw glowing effect for prize text
        glow_surf = pygame.Surface((prize_text.get_width() + int(10 * self.scale_x), 
                                   prize_text.get_height() + int(10 * self.scale_y)), pygame.SRCALPHA)
        pygame.draw.ellipse(glow_surf, (255, 220, 50, 40), 
                           (0, 0, prize_text.get_width() + int(10 * self.scale_x), 
                           prize_text.get_height() + int(10 * self.scale_y)))
        self.screen.blit(glow_surf, (prize_x - int(5 * self.scale_x), prize_y - int(5 * self.scale_y)))
        
        # Draw the prize amount
        self.screen.blit(prize_text, (prize_x, prize_y))
        
        # Draw ETB label with modern styling
        etb_font = pygame.font.SysFont("Arial", int(20 * min(self.scale_x, self.scale_y)), bold=True)
        etb_text = etb_font.render("ETB", True, (255, 150, 50))
        
        # Position ETB at the bottom right of the prize display
        etb_x = prize_display_rect.right - etb_text.get_width() - int(12 * self.scale_x)
        etb_y = prize_display_rect.bottom - etb_text.get_height() - int(8 * self.scale_y)
        self.screen.blit(etb_text, (etb_x, etb_y))
        
        # Store hit areas
        self.hit_areas["bet_input"] = input_rect
        self.hit_areas["set_button"] = set_btn_rect

    def draw_playing_board(self, y_position, section_height, section_width):
        """Draw the Playing Board section on the left side of the screen"""
        # Scale dimensions based on section height
        title_height = int(section_height * 0.1)
        board_height = int(section_height * 0.85)
        
        # Use the left side position
        x_position = int(20 * self.scale_x)
        
        # "Playing Board" title text with glow
        header_font = pygame.font.SysFont("Arial", int(24 * min(self.scale_x, self.scale_y)), bold=True)
        playing_board_text = header_font.render("Playing Board", True, WHITE)
        play_x = x_position + int(10 * self.scale_x)
        play_y = y_position + int(title_height * 0.2)  # Slight offset from top
        
        # Add glow effect for title
        glow_surf = pygame.Surface((playing_board_text.get_width()+int(8*self.scale_x), 
                                   playing_board_text.get_height()+int(8*self.scale_y)), pygame.SRCALPHA)
        pygame.draw.ellipse(glow_surf, (WHITE[0], WHITE[1], WHITE[2], 80), 
                           (0, 0, playing_board_text.get_width()+int(8*self.scale_x), 
                            playing_board_text.get_height()+int(8*self.scale_y)))
        self.screen.blit(glow_surf, (play_x - int(4*self.scale_x), play_y - int(4*self.scale_y)))
        self.screen.blit(playing_board_text, (play_x, play_y))
        
        # Draw board background - ensure proper width
        board_rect = pygame.Rect(
            x_position, 
            play_y + playing_board_text.get_height() + int(5 * self.scale_y),
            section_width,  # Use passed width
            board_height
        )
        self.draw_gradient_rect(board_rect, (40, 70, 80), (50, 80, 90), 10) 
        
        # Define BINGO colors
        b_color = (255, 30, 30)   # Bright red
        i_color = (30, 255, 80)   # Bright green
        n_color = (80, 80, 220)   # Blue/Purple
        g_color = (255, 30, 30)   # Bright red (matching B)
        o_color = (255, 220, 0)   # Gold yellow
        
        # Calculate grid dimensions based on available space
        grid_height = board_rect.height * 0.85
        grid_start_x = board_rect.x + int(board_rect.width * 0.05)  # 5% from left edge
        grid_start_y = board_rect.y + int(board_rect.height * 0.12)  # 12% from top
        grid_width = board_rect.width * 0.9
        
        # Calculate cell size based on available space
        cell_size = min(grid_width / 5, grid_height / 5)
        
        # Draw BINGO letters - positioned to align with columns
        colors = [b_color, i_color, n_color, g_color, o_color]
        letters = "BINGO"
        
        # Use a bolder font for BINGO letters
        bingo_header_font = pygame.font.SysFont("Arial", int(26 * min(self.scale_x, self.scale_y)), bold=True)
        
        # Draw each letter centered above its column
        for i, letter in enumerate(letters):
            # Calculate the center position of each column
            letter_x = grid_start_x + (i * cell_size) + (cell_size / 2)
            letter_y = board_rect.y + int(board_rect.height * 0.05)  # 5% from top of board
            
            # Shadow with the same 3D effect as the header
            shadow_surf = bingo_header_font.render(letter, True, (0, 0, 0))
            shadow_rect = shadow_surf.get_rect(center=(letter_x + int(3*self.scale_x), letter_y + int(3*self.scale_y)))
            self.screen.blit(shadow_surf, shadow_rect)
            
            # Main letter
            letter_surf = bingo_header_font.render(letter, True, colors[i])
            letter_rect = letter_surf.get_rect(center=(letter_x, letter_y))
            self.screen.blit(letter_surf, letter_rect)
        
        grid_line_thickness = max(1, int(1 * min(self.scale_x, self.scale_y)))
        grid_line_color = WHITE
        
        # Draw grid lines over the background
        # Horizontal lines
        for row in range(6):
            y = grid_start_y + row * cell_size
            pygame.draw.line(self.screen, grid_line_color, 
                            (grid_start_x, y), 
                            (grid_start_x + 5 * cell_size, y), 
                           grid_line_thickness)
        
        # Vertical lines
        for col in range(6):
            x = grid_start_x + col * cell_size
            pygame.draw.line(self.screen, grid_line_color, 
                            (x, grid_start_y), 
                            (x, grid_start_y + 5 * cell_size), 
                           grid_line_thickness)
        
        # Draw star in center (free space)
        center_col, center_row = 2, 2
        star_cell_rect = pygame.Rect(
            grid_start_x + center_col * cell_size + grid_line_thickness, 
            grid_start_y + center_row * cell_size + grid_line_thickness,
            cell_size - grid_line_thickness * 2,
            cell_size - grid_line_thickness * 2
        )
        
        # Draw dark green square background for the star
        green_bg_color = (0, 100, 50) # Dark green
        pygame.draw.rect(self.screen, green_bg_color, star_cell_rect)

        # Draw the gold star
        star_size = int(cell_size * 0.6)
        star_x = star_cell_rect.centerx - star_size // 2
        star_y = star_cell_rect.centery - star_size // 2
        
        star_img = pygame.Surface((star_size, star_size), pygame.SRCALPHA)
        star_center_x = star_size // 2
        star_center_y = star_size // 2
        star_radius = star_size // 2 * 0.9
        
        # Draw star points (gold)
        star_points = [
            (star_center_x, star_center_y - star_radius),  # Top point
            (star_center_x + star_radius * 0.3, star_center_y - star_radius * 0.3), # Top right point
            (star_center_x + star_radius, star_center_y), # Right point
            (star_center_x + star_radius * 0.4, star_center_y + star_radius * 0.3), # Bottom right point
            (star_center_x + star_radius * 0.6, star_center_y + star_radius), # Bottom point
            (star_center_x, star_center_y + star_radius * 0.5), # Bottom middle point
            (star_center_x - star_radius * 0.6, star_center_y + star_radius), # Bottom left point
            (star_center_x - star_radius * 0.4, star_center_y + star_radius * 0.3), # Top left point
            (star_center_x - star_radius, star_center_y), # Left point
            (star_center_x - star_radius * 0.3, star_center_y - star_radius * 0.3) # Top left point
        ]
        pygame.draw.polygon(star_img, GOLD, star_points)

        # Add subtle highlight to star
        highlight_color = (255, 255, 180) # Lighter gold
        highlight_points = [
             (star_center_x, star_center_y - star_radius), # Top
             (star_center_x + star_radius * 0.1, star_center_y - star_radius * 0.3),
             (star_center_x - star_radius * 0.1, star_center_y - star_radius * 0.3)
        ]
        pygame.draw.polygon(star_img, highlight_color, highlight_points)

        self.screen.blit(star_img, (star_x, star_y))
        
        # Draw board numbers (excluding the center free space)
        number_font_size = min(int(20 * min(self.scale_x, self.scale_y)), int(cell_size * 0.4))
        number_font = pygame.font.SysFont("Arial", number_font_size)
        for col in range(5):
            for row in range(5):
                # Skip the free space drawing logic here
                if col == center_col and row == center_row:
                    continue
                    
                number = self.bingo_board[col][row]
                if number > 0:
                    # Calculate cell center position
                    cell_center_x = grid_start_x + cell_size // 2 + col * cell_size
                    cell_center_y = grid_start_y + cell_size // 2 + row * cell_size
                    
                    # Check if number is marked/called
                    if number in self.called_numbers:
                        # Highlight called numbers with color matching the column header
                        pygame.draw.circle(self.screen, colors[col], 
                                          (cell_center_x, cell_center_y), 
                                          int(cell_size * 0.3))
                    
                    # Render number with increased shadow offset
                    shadow_surf = number_font.render(str(number), True, (0, 0, 0))
                    shadow_rect = shadow_surf.get_rect(center=(cell_center_x + int(3*self.scale_x), 
                                                             cell_center_y + int(3*self.scale_y)))
                    self.screen.blit(shadow_surf, shadow_rect)
                    
                    # Number
                    num_surf = number_font.render(str(number), True, WHITE)
                    num_rect = num_surf.get_rect(center=(cell_center_x, cell_center_y))
                    self.screen.blit(num_surf, num_rect)

    def create_board_from_number(self, number):
        """Get a bingo board for a specific cartella number from the loaded JSON file"""
        # Convert number to string for JSON dictionary key
        number_key = str(number)
        
        # Check if the board exists in the loaded JSON data
        if number_key in self.bingo_boards:
            self.bingo_board = self.bingo_boards[number_key]
        else:
            print(f"Board for cartella {number} not found in loaded data, generating deterministically...")
            # If not found, generate a deterministic board
            import random
            
            # Use the cartella number as seed for consistent results
            random.seed(number)
            
            self.bingo_board = []
            
            # For each column (B, I, N, G, O)
            for col in range(5):
                column_values = []
                # Range for this column: col*15+1 to col*15+15
                min_val = col * 15 + 1
                max_val = min_val + 14
                
                # Generate 5 unique random numbers for this column
                values = list(range(min_val, max_val + 1))
                random.shuffle(values)
                column_values = values[:5]
                
                # Set the center square (N column, 3rd row) to 0 (free space)
                if col == 2:
                    column_values[2] = 0
                    
                self.bingo_board.append(column_values)
            
            # Reset the random seed to avoid affecting other random operations
            random.seed()
        
        return self.bingo_board
        
def show_board_selection(screen):
    """
    Show the board selection window
    
    Args:
        screen: The pygame screen surface
        
    Returns:
        List of selected cartella numbers
    """
    # Create and run the board selection window
    board_selection = BoardSelectionWindow(screen)
    selected_cartellas = board_selection.run()
    
    return selected_cartellas 