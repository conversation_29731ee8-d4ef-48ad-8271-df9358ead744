"""
Usage tracker for the payment system.

This module tracks credit usage during games and logs it for commission calculation.
"""

import time
from . import get_voucher_manager

class UsageTracker:
    """Tracks credit usage during games."""

    def __init__(self):
        """Initialize the usage tracker."""
        self.voucher_manager = get_voucher_manager()
        self.current_game_id = None
        self.game_start_time = 0
        self.credits_used = 0
        self.active = False
        self.in_demo_mode = False  # Flag to indicate if we're in demo mode

    def start_game(self, game_id=None):
        """
        Start tracking a new game.

        Args:
            game_id: Optional game identifier

        Returns:
            bool: True if started successfully, False if insufficient credits
        """
        print(f"UsageTracker.start_game called with game_id={game_id}")
        print(f"Current credits: {self.voucher_manager.credits}")
        print(f"Current active state: {self.active}")

        # CRITICAL FIX: Always allow game tracking to start, even with 0 credits
        # This ensures that the end_game method will be called and can handle the minimum credit deduction
        print(f"Starting game tracking with {self.voucher_manager.credits} credits")
        if self.voucher_manager.credits <= 0:
            print("Warning: Starting game with 0 credits - minimum credit will be deducted at end")

        # Generate game ID if not provided
        if game_id is None:
            game_id = f"game_{int(time.time())}"

        self.current_game_id = game_id
        self.game_start_time = time.time()
        self.credits_used = 0
        self.active = True

        print(f"Game tracking started: game_id={self.current_game_id}, active={self.active}")
        return True

    def end_game(self, share_percentage=30, total_bets=0, commission_percentage=20):
        """
        End the current game and log usage.

        Args:
            share_percentage: Commission share percentage from voucher
            total_bets: Total bets amount for the game
            commission_percentage: Referee's commission percentage

        Returns:
            dict: Game usage statistics
        """
        print(f"UsageTracker.end_game called with: share_percentage={share_percentage}, total_bets={total_bets}, commission_percentage={commission_percentage}")

        # Check if we're in demo mode
        if self.in_demo_mode:
            print("Game in demo mode - no credits used")

            # Reset active state
            self.active = False
            self.game_start_time = 0
            self.current_game_id = None
            self.in_demo_mode = False  # Reset demo mode flag

            # Prepare demo result
            result = {
                "success": True,
                "demo_mode": True,
                "game_id": None,
                "duration": 0,
                "total_bets": total_bets,
                "commission_percentage": commission_percentage,
                "referee_commission": 0,
                "share_percentage": share_percentage,
                "credits_used": 0,
                "new_balance": self.voucher_manager.credits
            }

            print(f"UsageTracker.end_game returning demo result: {result}")
            return result

        if not self.active:
            print("STATS UPDATE FIX: No active game tracking, but checking for credit usage anyway")
            print(f"Current state: active={self.active}, game_id={self.current_game_id}, start_time={self.game_start_time}")

            # STATS UPDATE FIX: If we have recent credit usage, activate tracking retroactively
            try:
                import json
                import os
                import time
                usage_log_path = os.path.join('data', 'usage_log.json')
                if os.path.exists(usage_log_path):
                    with open(usage_log_path, 'r') as f:
                        usage_data = json.load(f)
                        recent_usage = usage_data.get('usage', [])
                        # Check for usage in the last 10 minutes
                        current_time = time.time()
                        recent_entries = [
                            entry for entry in recent_usage
                            if current_time - entry.get('timestamp', 0) < 600  # 10 minutes
                        ]
                        if recent_entries:
                            print(f"STATS UPDATE FIX: Found {len(recent_entries)} recent credit usage entries")
                            # Activate tracking retroactively
                            self.active = True
                            self.current_game_id = f"retroactive_game_{int(time.time())}"
                            self.game_start_time = recent_entries[-1].get('timestamp', time.time()) - 300  # 5 minutes ago
                            print(f"STATS UPDATE FIX: Retroactively activated tracking - game_id: {self.current_game_id}")
                        else:
                            return {"success": False, "message": "No active game tracking and no recent credit usage"}
                else:
                    return {"success": False, "message": "No active game tracking"}
            except Exception as e:
                print(f"STATS UPDATE FIX: Error checking for retroactive activation: {e}")
                return {"success": False, "message": "No active game tracking"}

        # Calculate referee commission amount
        referee_commission = total_bets * (commission_percentage / 100)
        print(f"Calculated referee_commission: {referee_commission}")

        # Calculate credits to deduct based on referee commission and share percentage
        # Formula: referee_commission * (share_percentage / 100)
        self.credits_used = round(referee_commission * (share_percentage / 100))
        print(f"Initial credits_used calculation: {self.credits_used}")

        # CRITICAL FIX: Always use at least 1 credit if the game is active
        # This ensures credits are deducted even if total_bets is 0
        if self.active:
            # Always use at least 1 credit when the game is active
            self.credits_used = max(1, self.credits_used)
            print(f"CRITICAL FIX: Ensuring at least 1 credit is used. credits_used: {self.credits_used}")
        else:
            # If the game is not active, don't charge any credits
            self.credits_used = 0
            print("Game not active, setting credits_used to 0")

        # Get current balance before update
        current_balance = self.voucher_manager.credits
        print(f"Current balance before update: {current_balance}")

        # Update credit balance
        new_balance = self.voucher_manager.update_credits(-self.credits_used)
        print(f"New balance after update: {new_balance}")

        # Log usage
        log_result = self.voucher_manager.log_usage(
            self.credits_used,
            share_percentage,
            self.current_game_id
        )
        print(f"Log usage result: {log_result}")

        # CRITICAL INTEGRATION: Record game in stats system when credits are deducted
        # This ensures perfect synchronization between credit deductions and game records
        if self.credits_used > 0:  # Only record if credits were actually deducted
            try:
                print("CREDIT-STATS SYNC: Recording game in stats system for credit deduction")
                self._record_game_for_credit_deduction(
                    total_bets=total_bets,
                    commission_percentage=commission_percentage,
                    share_percentage=share_percentage,
                    referee_commission=referee_commission,
                    credits_used=self.credits_used,
                    game_id=self.current_game_id
                )
                print("CREDIT-STATS SYNC: Game successfully recorded in stats system")
            except Exception as e:
                print(f"CREDIT-STATS SYNC ERROR: Failed to record game in stats: {e}")
                import traceback
                traceback.print_exc()

        # Reset active state
        self.active = False
        self.game_start_time = 0
        self.current_game_id = None
        print("Game tracking reset to inactive state")

        # Prepare result
        result = {
            "success": True,
            "demo_mode": False,
            "game_id": self.current_game_id,
            "duration": time.time() - self.game_start_time if self.game_start_time else 0,
            "total_bets": total_bets,
            "commission_percentage": commission_percentage,
            "referee_commission": referee_commission,
            "share_percentage": share_percentage,
            "credits_used": self.credits_used,
            "new_balance": new_balance
        }

        print(f"UsageTracker.end_game returning result: {result}")
        return result

    def _record_game_for_credit_deduction(self, total_bets, commission_percentage, share_percentage,
                                        referee_commission, credits_used, game_id):
        """
        Record a game in the stats system based on credit deduction.
        This ensures perfect synchronization between credit deductions and game records.

        Args:
            total_bets: Total bets amount for the game
            commission_percentage: Referee's commission percentage
            share_percentage: Commission share percentage from voucher
            referee_commission: Calculated referee commission amount
            credits_used: Number of credits deducted
            game_id: Game identifier
        """
        import time

        # Calculate game duration
        game_duration = time.time() - self.game_start_time if self.game_start_time else 0

        # Estimate player count based on total bets (assuming typical bet amounts)
        # This is an approximation since we don't have exact player data in the usage tracker
        estimated_bet_per_player = 25  # Typical bet amount
        estimated_player_count = max(1, int(total_bets / estimated_bet_per_player)) if total_bets > 0 else 1

        # Create game data for stats recording
        game_data = {
            "winner_name": "Credit Deduction Game",  # Indicates this was recorded from credit system
            "winner_cartella": 0,
            "claim_type": "credit_based_recording",  # Special type for credit-based records
            "game_duration": game_duration,
            "player_count": estimated_player_count,
            "prize_amount": total_bets,  # Total bets = prize pool
            "commission_percentage": commission_percentage,
            "called_numbers": [],  # We don't have this data in usage tracker
            "is_demo_mode": False,  # Credits were deducted, so not demo
            "date_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "stake": estimated_bet_per_player,  # Estimated stake per player
            "bet_amount": estimated_bet_per_player,
            "completion_type": "credit_deduction",
            # Additional credit-specific data
            "credits_used": credits_used,
            "share_percentage": share_percentage,
            "referee_commission": referee_commission,
            "game_id": game_id,
            "total_bets": total_bets
        }

        print(f"CREDIT-STATS SYNC: Creating game record for credit deduction: {game_data}")

        # Try to record using the stats integration system (includes cache invalidation)
        try:
            import importlib
            stats_integration_spec = importlib.util.find_spec('stats_integration')
            if stats_integration_spec is not None:
                from stats_integration import record_game_completed
                result = record_game_completed(game_data)
                print(f"CREDIT-STATS SYNC: Recorded via stats integration (with cache clearing): {result}")
                return result
            else:
                print("CREDIT-STATS SYNC: Stats integration not available, trying event hooks")
        except Exception as e:
            print(f"CREDIT-STATS SYNC: Error with stats integration: {e}")

        # Fallback to event hooks system
        try:
            import importlib
            stats_hooks_spec = importlib.util.find_spec('stats_event_hooks')
            if stats_hooks_spec is not None:
                from stats_event_hooks import get_stats_event_hooks
                stats_hooks = get_stats_event_hooks()
                result = stats_hooks.on_game_completed(game_data)
                print(f"CREDIT-STATS SYNC: Recorded via event hooks: {result}")
                if result:
                    # Manually trigger cache clearing since event hooks don't do it
                    self._clear_stats_caches()
                return result
            else:
                print("CREDIT-STATS SYNC: Event hooks not available, trying direct database")
        except Exception as e:
            print(f"CREDIT-STATS SYNC: Error with event hooks: {e}")

        # Fallback to direct database recording
        try:
            import thread_safe_db
            result = thread_safe_db.record_game_completed(game_data)
            print(f"CREDIT-STATS SYNC: Recorded via direct database: {result}")
            if result:
                # Manually trigger cache clearing since direct DB doesn't do it
                self._clear_stats_caches()
            return result
        except Exception as e:
            print(f"CREDIT-STATS SYNC: Error with direct database: {e}")
            raise

    def _clear_stats_caches(self):
        """
        Clear all stats caches to ensure fresh data is displayed after recording a game.
        This is critical for ensuring the stats page shows updated data immediately.
        """
        try:
            print("CACHE INVALIDATION: Clearing all stats caches after credit-based game recording")

            # Clear stats preloader cache
            try:
                from stats_preloader import get_stats_preloader
                preloader = get_stats_preloader()
                preloader.clear()
                print("CACHE INVALIDATION: Cleared stats preloader cache")
            except Exception as e:
                print(f"CACHE INVALIDATION: Could not clear preloader cache: {e}")

            # Clear instant loading cache
            try:
                from instant_loading.stats_loader import get_stats_loader
                loader = get_stats_loader()
                loader.clear_cache()
                print("CACHE INVALIDATION: Cleared instant loading cache")
            except Exception as e:
                print(f"CACHE INVALIDATION: Could not clear instant loading cache: {e}")

            # Force clear centralized stats provider cache
            try:
                from stats_page import CentralizedStatsProvider
                provider = CentralizedStatsProvider()
                provider.force_clear_cache()
                print("CACHE INVALIDATION: Force cleared centralized stats provider cache")
            except Exception as e:
                print(f"CACHE INVALIDATION: Could not clear centralized stats cache: {e}")

            # Force refresh data to update UI
            try:
                from stats_integration import force_refresh_data
                force_refresh_data()
                print("CACHE INVALIDATION: Forced data refresh completed")
            except Exception as e:
                print(f"CACHE INVALIDATION: Could not force refresh: {e}")

        except Exception as e:
            print(f"CACHE INVALIDATION: Error clearing caches: {e}")
            import traceback
            traceback.print_exc()

    def cancel_tracking(self):
        """
        Cancel the current tracking without using credits.

        Returns:
            bool: True if cancelled, False if no active tracking
        """
        if not self.active:
            return False

        self.active = False
        self.current_game_id = None
        self.game_start_time = 0
        self.credits_used = 0

        return True

    def get_estimated_usage(self, total_bets=0, commission_percentage=20):
        """
        Get estimated usage for the current game.

        Args:
            total_bets: Total bets amount for the game
            commission_percentage: Referee's commission percentage

        Returns:
            dict: Estimated usage information
        """
        # Check if we're in demo mode
        if self.in_demo_mode:
            # In demo mode, no credits are used
            return {
                "active": True,
                "demo_mode": True,
                "game_id": self.current_game_id,
                "duration": time.time() - self.game_start_time if self.game_start_time else 0,
                "total_bets": total_bets,
                "commission_percentage": commission_percentage,
                "referee_commission": 0,
                "share_percentage": 0,
                "credits_used": 0,
                "remaining_credits": self.voucher_manager.credits
            }

        if not self.active:
            return {
                "active": False,
                "demo_mode": False,
                "credits_used": 0,
                "duration": 0,
                "total_bets": 0,
                "commission_percentage": 0,
                "referee_commission": 0
            }

        # Get the most recent voucher's share percentage
        voucher_history = self.voucher_manager.get_voucher_history(1)
        share_percentage = voucher_history[0]['share'] if voucher_history else 30  # Default to 30% if no history

        # Calculate referee commission amount
        referee_commission = total_bets * (commission_percentage / 100)

        # Calculate credits to deduct based on referee commission and share percentage
        estimated_credits = round(referee_commission * (share_percentage / 100))

        # Ensure minimum 1 credit is used if there were any bets
        if total_bets > 0:
            estimated_credits = max(1, estimated_credits)
        else:
            # If no bets were made, don't charge any credits
            estimated_credits = 0

        return {
            "active": True,
            "demo_mode": False,
            "game_id": self.current_game_id,
            "duration": time.time() - self.game_start_time,
            "total_bets": total_bets,
            "commission_percentage": commission_percentage,
            "referee_commission": referee_commission,
            "share_percentage": share_percentage,
            "credits_used": estimated_credits,
            "remaining_credits": self.voucher_manager.credits - estimated_credits
        }

# Create a singleton instance
usage_tracker = None

def get_usage_tracker():
    """
    Get the singleton UsageTracker instance.

    Returns:
        UsageTracker: The usage tracker instance
    """
    global usage_tracker
    if usage_tracker is None:
        usage_tracker = UsageTracker()
    return usage_tracker
