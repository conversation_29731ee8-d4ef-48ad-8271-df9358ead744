import threading
import time
from datetime import datetime

class LazyStatsPageWrapper:
    """
    Lazy loading wrapper that prevents UI blocking during stats page initialization
    """
    
    def __init__(self, original_stats_page_class):
        self.original_class = original_stats_page_class
        self.instance = None
        self.loading = False
        self.load_complete = False
        self.load_error = None
        
        # Minimal placeholder data
        self.placeholder_data = {
            'daily_earnings': 0.0,
            'weekly_stats': [],
            'game_history': [],
            'wallet_balance': 0.0
        }
    
    def create_instance_async(self, *args, **kwargs):
        """Create the actual stats page instance asynchronously"""
        if self.loading or self.load_complete:
            return
        
        self.loading = True
        
        def load_async():
            try:
                print("Lazy loading: Creating stats page instance...")
                start_time = time.time()
                
                # Create the actual instance
                self.instance = self.original_class(*args, **kwargs)
                
                load_time = time.time() - start_time
                print(f"Lazy loading: Stats page loaded in {load_time:.2f}s")
                
                self.load_complete = True
                self.loading = False
                
            except Exception as e:
                print(f"Lazy loading: Error creating stats page: {e}")
                self.load_error = e
                self.loading = False
        
        # Start loading in background
        load_thread = threading.Thread(target=load_async, daemon=True)
        load_thread.start()
    
    def is_ready(self):
        """Check if the stats page is ready to use"""
        return self.load_complete and self.instance is not None
    
    def get_loading_progress(self):
        """Get loading progress information"""
        if self.load_complete:
            return {"status": "complete", "progress": 100}
        elif self.loading:
            return {"status": "loading", "progress": 50}
        elif self.load_error:
            return {"status": "error", "error": str(self.load_error)}
        else:
            return {"status": "not_started", "progress": 0}
    
    def __getattr__(self, name):
        """Delegate attribute access to the actual instance when ready"""
        if self.is_ready():
            return getattr(self.instance, name)
        else:
            # Return placeholder methods for common operations
            if name in ['draw', 'update', 'handle_event']:
                return self._placeholder_method
            elif name in ['daily_earnings', 'weekly_stats', 'game_history', 'wallet_balance']:
                return self.placeholder_data.get(name, 0)
            else:
                raise AttributeError(f"Stats page not ready, attribute '{name}' not available")
    
    def _placeholder_method(self, *args, **kwargs):
        """Placeholder method that does nothing while loading"""
        pass

def create_lazy_stats_page(*args, **kwargs):
    """Factory function to create a lazy-loaded stats page"""
    from stats_page import StatsPage
    
    wrapper = LazyStatsPageWrapper(StatsPage)
    wrapper.create_instance_async(*args, **kwargs)
    
    return wrapper
