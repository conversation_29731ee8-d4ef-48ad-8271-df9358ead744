# Technology Stack & Build System

## Core Technologies

### Primary Framework
- **Python 3.9+**: Main programming language
- **Pygame 2.5.0+**: Game engine and UI framework
- **Pygame-CE 2.4.0+**: Community edition for enhanced features

### Database Systems
- **SQLite**: Primary local database for statistics and game data
- **RethinkDB 2.4.10+**: Real-time synchronization and distributed data
- **Connection Pooling**: Custom database connection management

### UI & Graphics
- **Flet 0.24.0+**: Modern UI framework for admin interfaces
- **Custom Pygame UI**: Game-specific interface components
- **Hardware Acceleration**: GPU integration for performance

### Audio System
- **Pygame Mixer**: Audio playback and effects
- **Pydub 0.25.1+**: Audio processing and format conversion
- **Multi-language Audio**: Amharic and English announcements

### Security & Cryptography
- **Cryptography 41.0+**: Voucher validation and secure operations
- **Custom Security Module**: Database security and audit logging

## Build System

### Primary Build Tools
- **Nuitka 2.0+**: Primary compiler for executable generation
- **PyInstaller 6.0+**: Alternative executable builder
- **Auto-py-to-exe 2.40+**: GUI-based build tool

### Build Commands
```bash
# Quick build (recommended)
python build_executable.py

# Nuitka build (optimized)
nuitka_build_simple.bat

# Clean build
python build_executable.py --clean

# Build with dependencies
python build_executable.py --install-deps
```

### Development Tools
- **Black**: Code formatting
- **isort**: Import sorting  
- **MyPy**: Type checking
- **Pytest**: Testing framework

## Performance Optimizations

### Caching Systems
- Text rendering cache (500 items)
- Surface cache for complex elements
- Static element pre-rendering
- Memory pools for frequent objects

### Hardware Adaptation
- Automatic performance mode switching
- Frame skipping for older hardware
- Low-performance mode optimizations
- GPU acceleration when available

## Configuration Management

### Settings Files
- `data/game_settings.json`: Game configuration
- `data/settings.json`: Application settings
- `pyproject.toml`: Project metadata and dependencies
- `requirements.txt`: Python dependencies

### Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Development setup
pip install -e .[dev]

# Build dependencies
pip install -e .[build]
```