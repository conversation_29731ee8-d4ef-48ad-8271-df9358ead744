#!/usr/bin/env python3
"""
Test Stats Page Import

Quick test to verify the stats page can be imported without errors
"""

def test_stats_page_import():
    """Test importing the stats page"""
    try:
        print("Testing stats page import...")
        
        # Test importing the stats page
        from stats_page import StatsPage
        print("✓ StatsPage class imported successfully")
        
        # Test importing the show_stats_page function
        from stats_page import show_stats_page
        print("✓ show_stats_page function imported successfully")
        
        # Test simple stats provider
        try:
            from simple_stats_provider import get_simple_stats_provider
            provider = get_simple_stats_provider()
            print("✓ Simple stats provider available")
            
            # Test basic functionality
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            earnings = provider.get_daily_earnings(today)
            print(f"✓ Daily earnings test: {earnings}")
            
            weekly_stats = provider.get_weekly_stats()
            print(f"✓ Weekly stats test: {len(weekly_stats)} days")
            
        except ImportError as e:
            print(f"⚠ Simple stats provider not available: {e}")
        
        print("\n" + "=" * 50)
        print("✅ STATS PAGE IMPORT TEST PASSED!")
        print("=" * 50)
        print("The stats page should now load without errors.")
        return True
        
    except SyntaxError as e:
        print(f"✗ Syntax Error: {e}")
        print(f"  File: {e.filename}")
        print(f"  Line: {e.lineno}")
        print(f"  Text: {e.text}")
        return False
        
    except IndentationError as e:
        print(f"✗ Indentation Error: {e}")
        print(f"  File: {e.filename}")
        print(f"  Line: {e.lineno}")
        return False
        
    except ImportError as e:
        print(f"✗ Import Error: {e}")
        return False
        
    except Exception as e:
        print(f"✗ Unexpected Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_stats_page_import()