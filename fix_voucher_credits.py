#!/usr/bin/env python3
"""
Fix the voucher manager credits to show 4159.0 ETB
"""

import os
import sqlite3
from datetime import datetime

# Path to voucher database
VOUCHERS_DB_PATH = os.path.join('data', 'vouchers.db')

def fix_voucher_credits():
    """Fix voucher manager credits to 4159.0."""
    
    TARGET_CREDITS = 4159.0
    
    print("Fixing voucher manager credits...")
    print(f"Target credits: {TARGET_CREDITS}")
    print(f"Voucher DB path: {VOUCHERS_DB_PATH}")
    
    try:
        # Ensure data directory exists
        os.makedirs(os.path.dirname(VOUCHERS_DB_PATH), exist_ok=True)
        
        # Connect to voucher database
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()
        
        # Create credits table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS credits (
            id INTEGER PRIMARY KEY,
            balance REAL DEFAULT 0,
            last_updated TEXT
        )
        ''')
        
        # Check if credits record exists
        cursor.execute('SELECT id, balance FROM credits WHERE id = 1')
        result = cursor.fetchone()
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if result:
            # Update existing record
            current_balance = result[1]
            print(f"Current credits: {current_balance}")
            
            cursor.execute('''
            UPDATE credits 
            SET balance = ?, last_updated = ?
            WHERE id = 1
            ''', (TARGET_CREDITS, current_time))
            print(f"Updated credits from {current_balance} to {TARGET_CREDITS}")
        else:
            # Insert new record
            cursor.execute('''
            INSERT INTO credits (id, balance, last_updated)
            VALUES (1, ?, ?)
            ''', (TARGET_CREDITS, current_time))
            print(f"Inserted new credits record: {TARGET_CREDITS}")
        
        # Commit changes
        conn.commit()
        
        # Verify the change
        cursor.execute('SELECT balance FROM credits WHERE id = 1')
        result = cursor.fetchone()
        final_balance = result[0] if result else 0
        
        print(f"Final credits balance: {final_balance}")
        
        conn.close()
        
        # Success check
        if abs(final_balance - TARGET_CREDITS) < 0.001:
            print(f"\n*** VOUCHER CREDITS FIXED SUCCESSFULLY! ***")
            print(f"Wallet balance should now display: {TARGET_CREDITS} ETB")
            return True
        else:
            print(f"\n*** CREDITS UPDATE FAILED ***")
            return False
        
    except Exception as e:
        print(f"Error fixing voucher credits: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_all_balances():
    """Verify both stats DB and voucher DB balances."""
    
    print(f"\n=== VERIFYING ALL BALANCE SOURCES ===")
    
    # Check stats database wallet balance
    try:
        stats_db_path = os.path.join('data', 'stats.db')
        conn = sqlite3.connect(stats_db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT balance_after FROM wallet_transactions ORDER BY id DESC LIMIT 1')
        result = cursor.fetchone()
        stats_wallet = result[0] if result else 0
        conn.close()
        print(f"Stats DB wallet balance: {stats_wallet} ETB")
    except Exception as e:
        print(f"Error checking stats DB: {e}")
        stats_wallet = 0
    
    # Check voucher database credits
    try:
        conn = sqlite3.connect(VOUCHERS_DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT balance FROM credits WHERE id = 1')
        result = cursor.fetchone()
        voucher_credits = result[0] if result else 0
        conn.close()
        print(f"Voucher DB credits: {voucher_credits} ETB")
    except Exception as e:
        print(f"Error checking voucher DB: {e}")
        voucher_credits = 0
    
    # Summary
    target = 4159.0
    stats_ok = abs(stats_wallet - target) < 0.001
    voucher_ok = abs(voucher_credits - target) < 0.001
    
    print(f"\nBalance Status:")
    print(f"- Stats DB wallet: {'OK' if stats_ok else 'NEEDS FIX'} ({stats_wallet})")
    print(f"- Voucher DB credits: {'OK' if voucher_ok else 'NEEDS FIX'} ({voucher_credits})")
    
    if stats_ok and voucher_ok:
        print(f"\n*** ALL BALANCES CORRECT! ***")
        print(f"Stats page should now display 4159.0 ETB wallet balance")
        return True
    else:
        print(f"\n*** SOME BALANCES NEED ATTENTION ***")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("FIXING VOUCHER MANAGER CREDITS")
    print("=" * 60)
    
    success = fix_voucher_credits()
    
    if success:
        verify_all_balances()
    
    print("\n" + "=" * 60)