#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to update specific stats values in the database.
This script updates:
- Total earnings = 39782.6667
- Wednesday (06/25) earnings = 4086.667
- Wallet balance = 4159
"""

import os
import sqlite3
from datetime import datetime, timedelta
import sys

# Add current directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from stats_db import get_stats_db_manager, STATS_DB_PATH
    STATS_DB_AVAILABLE = True
    print("Stats database module imported successfully")
except ImportError as e:
    STATS_DB_AVAILABLE = False
    print(f"Stats database not available: {e}")
    # Fallback to direct database access
    STATS_DB_PATH = os.path.join('data', 'stats.db')

def update_stats_values():
    """Update the stats values as requested."""
    
    # Target values
    TARGET_TOTAL_EARNINGS = 39782.6667
    TARGET_WEDNESDAY_EARNINGS = 4086.667
    TARGET_WALLET_BALANCE = 4159.0
    WEDNESDAY_DATE = "2024-06-25"  # Wednesday 06/25
    
    print("Starting stats values update...")
    print(f"Target total earnings: {TARGET_TOTAL_EARNINGS}")
    print(f"Target Wednesday (06/25) earnings: {TARGET_WEDNESDAY_EARNINGS}")
    print(f"Target wallet balance: {TARGET_WALLET_BALANCE}")
    
    try:
        # Ensure data directory exists
        os.makedirs(os.path.dirname(STATS_DB_PATH), exist_ok=True)
        
        # Connect to database
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            date TEXT PRIMARY KEY,
            games_played INTEGER DEFAULT 0,
            earnings REAL DEFAULT 0,
            winners INTEGER DEFAULT 0,
            total_players INTEGER DEFAULT 0
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date_time TEXT,
            username TEXT,
            house TEXT,
            stake REAL,
            players INTEGER,
            total_calls INTEGER,
            commission_percent REAL,
            fee REAL,
            total_prize REAL,
            details TEXT,
            status TEXT
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wallet_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date_time TEXT,
            amount REAL,
            transaction_type TEXT,
            description TEXT,
            balance_after REAL
        )
        ''')
        
        print("Database tables ensured...")
        
        # Step 1: Update Wednesday (06/25) earnings in daily_stats
        print(f"\nStep 1: Updating Wednesday ({WEDNESDAY_DATE}) earnings...")
        
        # Check if record exists
        cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (WEDNESDAY_DATE,))
        existing_record = cursor.fetchone()
        
        if existing_record:
            # Update existing record
            cursor.execute('''
            UPDATE daily_stats 
            SET earnings = ?, games_played = COALESCE(games_played, 1)
            WHERE date = ?
            ''', (TARGET_WEDNESDAY_EARNINGS, WEDNESDAY_DATE))
            print(f"Updated existing Wednesday record with earnings: {TARGET_WEDNESDAY_EARNINGS}")
        else:
            # Insert new record
            cursor.execute('''
            INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
            VALUES (?, 1, ?, 1, 5)
            ''', (WEDNESDAY_DATE, TARGET_WEDNESDAY_EARNINGS))
            print(f"Inserted new Wednesday record with earnings: {TARGET_WEDNESDAY_EARNINGS}")
        
        # Step 2: Calculate and distribute remaining earnings across other days
        print(f"\nStep 2: Distributing remaining earnings to reach total of {TARGET_TOTAL_EARNINGS}...")
        
        # Get current total earnings excluding Wednesday
        cursor.execute('SELECT SUM(earnings) FROM daily_stats WHERE date != ?', (WEDNESDAY_DATE,))
        result = cursor.fetchone()
        current_other_earnings = result[0] if result and result[0] is not None else 0
        
        # Calculate remaining earnings needed
        remaining_earnings = TARGET_TOTAL_EARNINGS - TARGET_WEDNESDAY_EARNINGS - current_other_earnings
        
        print(f"Current other earnings (excluding Wednesday): {current_other_earnings}")
        print(f"Remaining earnings needed: {remaining_earnings}")
        
        if remaining_earnings > 0:
            # Distribute remaining earnings across recent days
            # Create some sample days with earnings
            sample_dates = [
                "2024-06-24",  # Tuesday
                "2024-06-23",  # Monday
                "2024-06-22",  # Sunday
                "2024-06-21",  # Saturday
                "2024-06-20",  # Friday
                "2024-06-19",  # Thursday
                "2024-06-18",  # Wednesday (previous week)
                "2024-06-17",  # Tuesday
                "2024-06-16",  # Monday
                "2024-06-15",  # Sunday
            ]
            
            # Distribute earnings across these dates
            earnings_per_day = remaining_earnings / len(sample_dates)
            
            for date in sample_dates:
                # Check if record exists
                cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (date,))
                existing = cursor.fetchone()
                
                if existing:
                    # Update existing record by adding to current earnings
                    cursor.execute('''
                    UPDATE daily_stats 
                    SET earnings = earnings + ?
                    WHERE date = ?
                    ''', (earnings_per_day, date))
                else:
                    # Insert new record
                    cursor.execute('''
                    INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                    VALUES (?, 2, ?, 1, 8)
                    ''', (date, earnings_per_day))
                
                print(f"Added {earnings_per_day:.2f} earnings to {date}")
        
        # Step 3: Update wallet balance
        print(f"\nStep 3: Setting wallet balance to {TARGET_WALLET_BALANCE}...")
        
        # Get current wallet balance
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        current_balance = result[0] if result and result[0] is not None else 0
        
        print(f"Current wallet balance: {current_balance}")
        
        # Calculate adjustment needed
        balance_adjustment = TARGET_WALLET_BALANCE - current_balance
        
        if balance_adjustment != 0:
            # Add wallet transaction to adjust balance
            cursor.execute('''
            INSERT INTO wallet_transactions
            (date_time, amount, transaction_type, description, balance_after)
            VALUES (?, ?, ?, ?, ?)
            ''', (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                balance_adjustment,
                'adjustment' if balance_adjustment > 0 else 'deduction',
                f'Balance adjustment to set wallet to {TARGET_WALLET_BALANCE} ETB',
                TARGET_WALLET_BALANCE
            ))
            print(f"Added wallet transaction: {balance_adjustment:+.2f} ETB")
            print(f"New wallet balance: {TARGET_WALLET_BALANCE}")
        else:
            print("Wallet balance already correct")
        
        # Step 4: Verify total earnings
        print(f"\nStep 4: Verifying total earnings...")
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        actual_total = result[0] if result and result[0] is not None else 0
        
        print(f"Actual total earnings: {actual_total}")
        print(f"Target total earnings: {TARGET_TOTAL_EARNINGS}")
        print(f"Difference: {actual_total - TARGET_TOTAL_EARNINGS}")
        
        # Commit all changes
        conn.commit()
        print(f"\nAll changes committed successfully!")
        
        # Final verification
        print(f"\nFinal verification:")
        
        # Check Wednesday earnings
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (WEDNESDAY_DATE,))
        result = cursor.fetchone()
        wednesday_earnings = result[0] if result else 0
        print(f"Wednesday ({WEDNESDAY_DATE}) earnings: {wednesday_earnings}")
        
        # Check total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        total_earnings = result[0] if result and result[0] is not None else 0
        print(f"Total earnings: {total_earnings}")
        
        # Check wallet balance
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0
        print(f"Wallet balance: {wallet_balance}")
        
        conn.close()
        
        print(f"\n✅ Stats values updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating stats values: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("STATS VALUES UPDATE SCRIPT")
    print("=" * 60)
    
    success = update_stats_values()
    
    if success:
        print("\n🎉 Update completed successfully!")
        print("\nThe stats page should now show:")
        print("- Total earnings: 39782.7 ETB")
        print("- Wednesday (06/25) earnings: 4086.7 ETB")
        print("- Wallet balance: 4159.0 ETB")
    else:
        print("\n❌ Update failed. Please check the error messages above.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()