2025-06-02 15:22:54.677 - ensure_database_exists called from thread 3684
2025-06-02 15:22:54.679 - Creating new thread-specific database connection to data\stats.db for thread 3684
2025-06-02 15:22:54.681 - New database connection created successfully for thread 3684
2025-06-02 15:22:54.685 - Stats database initialized successfully
2025-06-02 15:22:54.686 - ensure_database_exists called from thread 3684
2025-06-02 15:22:54.687 - Using existing connection for thread 3684
2025-06-02 15:22:54.688 - Stats database initialized successfully
2025-06-02 15:22:54.689 - ensure_database_exists called from thread 3684
2025-06-02 15:22:54.690 - Using existing connection for thread 3684
2025-06-02 15:22:54.691 - Stats database initialized successfully
2025-06-02 15:40:09.353 - ensure_database_exists called from thread 5620
2025-06-02 15:40:09.355 - Creating new thread-specific database connection to data\stats.db for thread 5620
2025-06-02 15:40:09.356 - New database connection created successfully for thread 5620
2025-06-02 15:40:09.359 - <PERSON><PERSON> database initialized successfully
2025-06-02 15:40:09.361 - ensure_database_exists called from thread 5620
2025-06-02 15:40:09.361 - Using existing connection for thread 5620
2025-06-02 15:40:09.362 - Stats database initialized successfully
2025-06-02 15:40:09.364 - ensure_database_exists called from thread 5620
2025-06-02 15:40:09.366 - Using existing connection for thread 5620
2025-06-02 15:40:09.367 - Stats database initialized successfully
2025-06-02 15:43:54.538 - ensure_database_exists called from thread 7740
2025-06-02 15:43:54.539 - Creating new thread-specific database connection to data\stats.db for thread 7740
2025-06-02 15:43:54.547 - New database connection created successfully for thread 7740
2025-06-02 15:43:54.550 - Stats database initialized successfully
2025-06-02 15:43:54.554 - ensure_database_exists called from thread 7740
2025-06-02 15:43:54.555 - Using existing connection for thread 7740
2025-06-02 15:43:54.556 - Stats database initialized successfully
2025-06-02 15:43:54.557 - ensure_database_exists called from thread 7740
2025-06-02 15:43:54.558 - Using existing connection for thread 7740
2025-06-02 15:43:54.559 - Stats database initialized successfully
2025-06-02 18:09:36.673 - ensure_database_exists called from thread 1676
2025-06-02 18:09:36.674 - Creating new thread-specific database connection to data\stats.db for thread 1676
2025-06-02 18:09:36.683 - New database connection created successfully for thread 1676
2025-06-02 18:09:36.685 - Stats database initialized successfully
2025-06-02 18:09:36.687 - ensure_database_exists called from thread 1676
2025-06-02 18:09:36.688 - Using existing connection for thread 1676
2025-06-02 18:09:36.689 - Stats database initialized successfully
2025-06-02 18:09:36.690 - ensure_database_exists called from thread 1676
2025-06-02 18:09:36.690 - Using existing connection for thread 1676
2025-06-02 18:09:36.691 - Stats database initialized successfully
2025-06-02 18:11:09.300 - Using existing connection for thread 1676
2025-06-02 18:11:09.344 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:11:09.345 - New database connection created successfully for thread 11212
2025-06-02 18:11:09.349 - Database connection closed for thread 11212
2025-06-02 18:11:09.351 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:11:09.352 - New database connection created successfully for thread 11212
2025-06-02 18:11:09.484 - get_summary_stats called from thread 11212
2025-06-02 18:11:09.484 - Using existing connection for thread 11212
2025-06-02 18:11:09.485 - Total earnings from database: 3480.0
2025-06-02 18:11:09.486 - Daily earnings from database: 0
2025-06-02 18:11:09.487 - Daily games from database: 0
2025-06-02 18:11:09.487 - Wallet balance from database: 0
2025-06-02 18:11:09.488 - Total games played from database: 6
2025-06-02 18:11:09.488 - Total winners from database: 0
2025-06-02 18:11:09.489 - Returning summary stats: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:14:07.590 - ensure_database_exists called from thread 1676
2025-06-02 18:14:07.591 - Using existing connection for thread 1676
2025-06-02 18:14:07.592 - Stats database initialized successfully
2025-06-02 18:14:35.094 - Using existing connection for thread 1676
2025-06-02 18:14:35.127 - Database connection closed for thread 11212
2025-06-02 18:14:35.132 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:14:35.137 - New database connection created successfully for thread 11212
2025-06-02 18:14:35.145 - Database connection closed for thread 11212
2025-06-02 18:14:35.150 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:14:35.160 - New database connection created successfully for thread 11212
2025-06-02 18:21:03.140 - Recreating expired connection for thread 1676
2025-06-02 18:21:03.168 - Database connection closed for thread 11212
2025-06-02 18:21:03.170 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:21:03.171 - New database connection created successfully for thread 11212
2025-06-02 18:21:03.175 - Database connection closed for thread 11212
2025-06-02 18:21:03.177 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:21:03.181 - New database connection created successfully for thread 11212
2025-06-02 18:30:18.565 - Recreating expired connection for thread 1676
2025-06-02 18:30:18.597 - Database connection closed for thread 11212
2025-06-02 18:30:18.603 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:30:18.607 - New database connection created successfully for thread 11212
2025-06-02 18:30:18.617 - Database connection closed for thread 11212
2025-06-02 18:30:18.621 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:30:18.628 - New database connection created successfully for thread 11212
2025-06-02 18:39:19.312 - Recreating expired connection for thread 1676

2025-06-02 18:39:25.386 - Database connection closed for thread 11212
2025-06-02 18:39:25.390 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:39:25.394 - New database connection created successfully for thread 11212
2025-06-02 18:39:25.404 - Database connection closed for thread 11212
2025-06-02 18:39:25.410 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:39:25.416 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.590 - Using existing connection for thread 11212
2025-06-02 18:41:14.683 - Database connection closed for thread 11212
2025-06-02 18:41:14.710 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.710 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.712 - Database connection closed for thread 11212
2025-06-02 18:41:14.712 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.713 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.717 - Database connection closed for thread 11212
2025-06-02 18:41:14.718 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.719 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.725 - Database connection closed for thread 11212
2025-06-02 18:41:14.726 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.727 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.729 - Database connection closed for thread 11212
2025-06-02 18:41:14.734 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.737 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.748 - Database connection closed for thread 11212
2025-06-02 18:41:14.749 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.752 - New database connection created successfully for thread 11212
2025-06-02 18:41:14.755 - Database connection closed for thread 11212
2025-06-02 18:41:14.756 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:14.757 - New database connection created successfully for thread 11212
2025-06-02 18:41:54.674 - Using existing connection for thread 1676
2025-06-02 18:41:54.741 - Using existing connection for thread 11212
2025-06-02 18:41:54.902 - Database connection closed for thread 11212
2025-06-02 18:41:54.908 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:54.911 - New database connection created successfully for thread 11212
2025-06-02 18:41:54.916 - Database connection closed for thread 11212
2025-06-02 18:41:54.921 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:41:54.926 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.671 - Using existing connection for thread 11212
2025-06-02 18:44:29.774 - Database connection closed for thread 11212
2025-06-02 18:44:29.785 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.786 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.786 - Database connection closed for thread 11212
2025-06-02 18:44:29.787 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.787 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.791 - Database connection closed for thread 11212
2025-06-02 18:44:29.794 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.796 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.802 - Database connection closed for thread 11212
2025-06-02 18:44:29.802 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.804 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.808 - Database connection closed for thread 11212
2025-06-02 18:44:29.809 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.810 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.823 - Database connection closed for thread 11212
2025-06-02 18:44:29.825 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.827 - New database connection created successfully for thread 11212
2025-06-02 18:44:29.829 - Database connection closed for thread 11212
2025-06-02 18:44:29.830 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:44:29.831 - New database connection created successfully for thread 11212
2025-06-02 18:45:46.185 - Using existing connection for thread 1676
2025-06-02 18:45:46.223 - Using existing connection for thread 11212
2025-06-02 18:45:46.411 - Database connection closed for thread 11212
2025-06-02 18:45:46.416 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:45:46.422 - New database connection created successfully for thread 11212
2025-06-02 18:45:46.432 - Database connection closed for thread 11212
2025-06-02 18:45:46.436 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:45:46.441 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.611 - Using existing connection for thread 11212
2025-06-02 18:48:40.734 - Database connection closed for thread 11212
2025-06-02 18:48:40.745 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.746 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.746 - Database connection closed for thread 11212
2025-06-02 18:48:40.748 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.749 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.751 - Database connection closed for thread 11212
2025-06-02 18:48:40.751 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.752 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.758 - Database connection closed for thread 11212
2025-06-02 18:48:40.760 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.761 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.765 - Database connection closed for thread 11212
2025-06-02 18:48:40.766 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.767 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.777 - Database connection closed for thread 11212
2025-06-02 18:48:40.781 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.783 - New database connection created successfully for thread 11212
2025-06-02 18:48:40.785 - Database connection closed for thread 11212
2025-06-02 18:48:40.786 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:48:40.787 - New database connection created successfully for thread 11212
2025-06-02 18:52:52.524 - Recreating expired connection for thread 1676
2025-06-02 18:52:52.584 - Using existing connection for thread 11212
2025-06-02 18:52:52.973 - Database connection closed for thread 11212
2025-06-02 18:52:52.978 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:52:52.987 - New database connection created successfully for thread 11212
2025-06-02 18:52:52.994 - Database connection closed for thread 11212
2025-06-02 18:52:53.000 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:52:53.007 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.329 - Using existing connection for thread 11212
2025-06-02 18:55:02.430 - Database connection closed for thread 11212
2025-06-02 18:55:02.444 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.445 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.445 - Database connection closed for thread 11212
2025-06-02 18:55:02.446 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.447 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.449 - Database connection closed for thread 11212
2025-06-02 18:55:02.449 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.451 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.458 - Database connection closed for thread 11212
2025-06-02 18:55:02.460 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.461 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.466 - Database connection closed for thread 11212
2025-06-02 18:55:02.467 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.467 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.476 - Database connection closed for thread 11212
2025-06-02 18:55:02.477 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.479 - New database connection created successfully for thread 11212
2025-06-02 18:55:02.482 - Database connection closed for thread 11212
2025-06-02 18:55:02.482 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:55:02.483 - New database connection created successfully for thread 11212
2025-06-02 18:58:04.989 - Recreating expired connection for thread 1676
2025-06-02 18:58:05.047 - Using existing connection for thread 11212
2025-06-02 18:58:05.233 - Database connection closed for thread 11212
2025-06-02 18:58:05.237 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:58:05.240 - New database connection created successfully for thread 11212
2025-06-02 18:58:05.248 - Database connection closed for thread 11212
2025-06-02 18:58:05.254 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 18:58:05.258 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.199 - Recreating expired connection for thread 11212
2025-06-02 19:05:20.249 - Database connection closed for thread 11212
2025-06-02 19:05:20.263 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.264 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.264 - Database connection closed for thread 11212
2025-06-02 19:05:20.265 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.265 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.268 - Database connection closed for thread 11212
2025-06-02 19:05:20.269 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.270 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.276 - Database connection closed for thread 11212
2025-06-02 19:05:20.277 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.278 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.281 - Database connection closed for thread 11212
2025-06-02 19:05:20.284 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.285 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.294 - Database connection closed for thread 11212
2025-06-02 19:05:20.298 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.300 - New database connection created successfully for thread 11212
2025-06-02 19:05:20.303 - Database connection closed for thread 11212
2025-06-02 19:05:20.303 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:05:20.305 - New database connection created successfully for thread 11212
2025-06-02 19:08:28.079 - Recreating expired connection for thread 1676
2025-06-02 19:08:28.160 - Using existing connection for thread 11212
2025-06-02 19:08:28.217 - Database connection closed for thread 11212
2025-06-02 19:08:28.221 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:08:28.222 - New database connection created successfully for thread 11212
2025-06-02 19:08:28.238 - Database connection closed for thread 11212
2025-06-02 19:08:28.243 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:08:28.247 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.722 - Using existing connection for thread 11212
2025-06-02 19:13:21.758 - Database connection closed for thread 11212
2025-06-02 19:13:21.768 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.769 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.771 - Database connection closed for thread 11212
2025-06-02 19:13:21.771 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.772 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.776 - Database connection closed for thread 11212
2025-06-02 19:13:21.776 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.777 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.783 - Database connection closed for thread 11212
2025-06-02 19:13:21.784 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.786 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.788 - Database connection closed for thread 11212
2025-06-02 19:13:21.790 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.792 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.802 - Database connection closed for thread 11212
2025-06-02 19:13:21.807 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.809 - New database connection created successfully for thread 11212
2025-06-02 19:13:21.812 - Database connection closed for thread 11212
2025-06-02 19:13:21.815 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:13:21.816 - New database connection created successfully for thread 11212
2025-06-02 19:15:59.884 - Recreating expired connection for thread 1676
2025-06-02 19:15:59.958 - Using existing connection for thread 11212
2025-06-02 19:16:00.048 - Database connection closed for thread 11212
2025-06-02 19:16:00.054 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:16:00.056 - New database connection created successfully for thread 11212
2025-06-02 19:16:00.064 - Database connection closed for thread 11212
2025-06-02 19:16:00.070 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:16:00.077 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.464 - Recreating expired connection for thread 11212
2025-06-02 19:21:10.496 - Database connection closed for thread 11212
2025-06-02 19:21:10.510 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.510 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.511 - Database connection closed for thread 11212
2025-06-02 19:21:10.511 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.513 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.515 - Database connection closed for thread 11212
2025-06-02 19:21:10.516 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.517 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.523 - Database connection closed for thread 11212
2025-06-02 19:21:10.524 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.525 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.529 - Database connection closed for thread 11212
2025-06-02 19:21:10.530 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.532 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.542 - Database connection closed for thread 11212
2025-06-02 19:21:10.543 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.546 - New database connection created successfully for thread 11212
2025-06-02 19:21:10.550 - Database connection closed for thread 11212
2025-06-02 19:21:10.551 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:21:10.552 - New database connection created successfully for thread 11212
2025-06-02 19:26:39.615 - Recreating expired connection for thread 1676
2025-06-02 19:26:39.651 - Recreating expired connection for thread 11212
2025-06-02 19:26:39.739 - Database connection closed for thread 11212
2025-06-02 19:26:39.743 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:26:39.748 - New database connection created successfully for thread 11212
2025-06-02 19:26:39.758 - Database connection closed for thread 11212
2025-06-02 19:26:39.762 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:26:39.767 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.023 - Using existing connection for thread 11212
2025-06-02 19:28:36.078 - Database connection closed for thread 11212
2025-06-02 19:28:36.089 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.090 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.090 - Database connection closed for thread 11212
2025-06-02 19:28:36.092 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.093 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.095 - Database connection closed for thread 11212
2025-06-02 19:28:36.096 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.097 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.102 - Database connection closed for thread 11212
2025-06-02 19:28:36.104 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.105 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.108 - Database connection closed for thread 11212
2025-06-02 19:28:36.112 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.115 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.124 - Database connection closed for thread 11212
2025-06-02 19:28:36.129 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.129 - New database connection created successfully for thread 11212
2025-06-02 19:28:36.136 - Database connection closed for thread 11212
2025-06-02 19:28:36.137 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:28:36.138 - New database connection created successfully for thread 11212
2025-06-02 19:32:32.758 - Recreating expired connection for thread 1676
2025-06-02 19:32:32.771 - Using existing connection for thread 11212
2025-06-02 19:32:32.842 - Database connection closed for thread 11212
2025-06-02 19:32:32.845 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:32:32.850 - New database connection created successfully for thread 11212
2025-06-02 19:32:32.861 - Database connection closed for thread 11212
2025-06-02 19:32:32.869 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:32:32.876 - New database connection created successfully for thread 11212
2025-06-02 19:34:05.956 - Using existing connection for thread 11212
2025-06-02 19:34:05.993 - Database connection closed for thread 11212
2025-06-02 19:34:06.006 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.007 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.007 - Database connection closed for thread 11212
2025-06-02 19:34:06.008 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.010 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.011 - Database connection closed for thread 11212
2025-06-02 19:34:06.012 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.013 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.018 - Database connection closed for thread 11212
2025-06-02 19:34:06.020 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.021 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.025 - Database connection closed for thread 11212
2025-06-02 19:34:06.026 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.026 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.037 - Database connection closed for thread 11212
2025-06-02 19:34:06.038 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.040 - New database connection created successfully for thread 11212
2025-06-02 19:34:06.043 - Database connection closed for thread 11212
2025-06-02 19:34:06.043 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:34:06.045 - New database connection created successfully for thread 11212
2025-06-02 19:37:59.099 - Recreating expired connection for thread 1676
2025-06-02 19:37:59.131 - Using existing connection for thread 11212
2025-06-02 19:37:59.220 - Database connection closed for thread 11212
2025-06-02 19:37:59.223 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:37:59.228 - New database connection created successfully for thread 11212
2025-06-02 19:37:59.237 - Database connection closed for thread 11212
2025-06-02 19:37:59.242 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:37:59.252 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.414 - Using existing connection for thread 11212
2025-06-02 19:41:59.468 - Database connection closed for thread 11212
2025-06-02 19:41:59.481 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.481 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.482 - Database connection closed for thread 11212
2025-06-02 19:41:59.483 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.483 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.485 - Database connection closed for thread 11212
2025-06-02 19:41:59.486 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.487 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.494 - Database connection closed for thread 11212
2025-06-02 19:41:59.495 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.497 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.501 - Database connection closed for thread 11212
2025-06-02 19:41:59.501 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.503 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.516 - Database connection closed for thread 11212
2025-06-02 19:41:59.517 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.518 - New database connection created successfully for thread 11212
2025-06-02 19:41:59.520 - Database connection closed for thread 11212
2025-06-02 19:41:59.521 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:41:59.522 - New database connection created successfully for thread 11212
2025-06-02 19:46:59.332 - Recreating expired connection for thread 1676
2025-06-02 19:46:59.401 - Using existing connection for thread 11212
2025-06-02 19:46:59.498 - Database connection closed for thread 11212
2025-06-02 19:46:59.502 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:46:59.507 - New database connection created successfully for thread 11212
2025-06-02 19:46:59.513 - Database connection closed for thread 11212
2025-06-02 19:46:59.517 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:46:59.524 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.103 - Using existing connection for thread 11212
2025-06-02 19:49:39.142 - Database connection closed for thread 11212
2025-06-02 19:49:39.154 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.155 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.156 - Database connection closed for thread 11212
2025-06-02 19:49:39.157 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.158 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.161 - Database connection closed for thread 11212
2025-06-02 19:49:39.162 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.163 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.169 - Database connection closed for thread 11212
2025-06-02 19:49:39.180 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.182 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.186 - Database connection closed for thread 11212
2025-06-02 19:49:39.186 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.187 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.202 - Database connection closed for thread 11212
2025-06-02 19:49:39.203 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.204 - New database connection created successfully for thread 11212
2025-06-02 19:49:39.206 - Database connection closed for thread 11212
2025-06-02 19:49:39.207 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:49:39.209 - New database connection created successfully for thread 11212
2025-06-02 19:52:59.077 - Recreating expired connection for thread 1676
2025-06-02 19:52:59.136 - Using existing connection for thread 11212
2025-06-02 19:52:59.227 - Database connection closed for thread 11212
2025-06-02 19:52:59.230 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:52:59.235 - New database connection created successfully for thread 11212
2025-06-02 19:52:59.243 - Database connection closed for thread 11212
2025-06-02 19:52:59.248 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:52:59.258 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.603 - Recreating expired connection for thread 11212
2025-06-02 19:58:38.652 - Database connection closed for thread 11212
2025-06-02 19:58:38.664 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.665 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.665 - Database connection closed for thread 11212
2025-06-02 19:58:38.666 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.666 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.669 - Database connection closed for thread 11212
2025-06-02 19:58:38.670 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.671 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.677 - Database connection closed for thread 11212
2025-06-02 19:58:38.679 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.680 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.684 - Database connection closed for thread 11212
2025-06-02 19:58:38.685 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.685 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.696 - Database connection closed for thread 11212
2025-06-02 19:58:38.699 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.701 - New database connection created successfully for thread 11212
2025-06-02 19:58:38.703 - Database connection closed for thread 11212
2025-06-02 19:58:38.704 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:58:38.705 - New database connection created successfully for thread 11212
2025-06-02 19:59:54.906 - Recreating expired connection for thread 1676
2025-06-02 19:59:54.919 - Using existing connection for thread 11212
2025-06-02 19:59:55.027 - Database connection closed for thread 11212
2025-06-02 19:59:55.028 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:59:55.032 - New database connection created successfully for thread 11212
2025-06-02 19:59:55.041 - Database connection closed for thread 11212
2025-06-02 19:59:55.046 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 19:59:55.049 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.240 - Using existing connection for thread 11212
2025-06-02 20:04:31.279 - Database connection closed for thread 11212
2025-06-02 20:04:31.291 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.292 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.294 - Database connection closed for thread 11212
2025-06-02 20:04:31.294 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.295 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.297 - Database connection closed for thread 11212
2025-06-02 20:04:31.299 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.300 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.306 - Database connection closed for thread 11212
2025-06-02 20:04:31.307 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.308 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.312 - Database connection closed for thread 11212
2025-06-02 20:04:31.313 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.314 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.326 - Database connection closed for thread 11212
2025-06-02 20:04:31.329 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.329 - New database connection created successfully for thread 11212
2025-06-02 20:04:31.333 - Database connection closed for thread 11212
2025-06-02 20:04:31.334 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:04:31.335 - New database connection created successfully for thread 11212
2025-06-02 20:07:39.236 - Recreating expired connection for thread 1676
2025-06-02 20:07:39.313 - Using existing connection for thread 11212
2025-06-02 20:07:39.417 - Database connection closed for thread 11212
2025-06-02 20:07:39.421 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:07:39.425 - New database connection created successfully for thread 11212
2025-06-02 20:07:39.436 - Database connection closed for thread 11212
2025-06-02 20:07:39.441 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:07:39.446 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.166 - Using existing connection for thread 11212
2025-06-02 20:10:51.213 - Database connection closed for thread 11212
2025-06-02 20:10:51.230 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.231 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.232 - Database connection closed for thread 11212
2025-06-02 20:10:51.233 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.233 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.236 - Database connection closed for thread 11212
2025-06-02 20:10:51.236 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.237 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.243 - Database connection closed for thread 11212
2025-06-02 20:10:51.244 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.246 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.250 - Database connection closed for thread 11212
2025-06-02 20:10:51.250 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.252 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.263 - Database connection closed for thread 11212
2025-06-02 20:10:51.264 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.266 - New database connection created successfully for thread 11212
2025-06-02 20:10:51.268 - Database connection closed for thread 11212
2025-06-02 20:10:51.269 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:10:51.271 - New database connection created successfully for thread 11212
2025-06-02 20:17:17.702 - Recreating expired connection for thread 1676
2025-06-02 20:17:17.751 - Recreating expired connection for thread 11212
2025-06-02 20:17:17.847 - Database connection closed for thread 11212
2025-06-02 20:17:17.855 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:17:17.859 - New database connection created successfully for thread 11212
2025-06-02 20:17:17.868 - Database connection closed for thread 11212
2025-06-02 20:17:17.873 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:17:17.879 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.106 - Using existing connection for thread 11212
2025-06-02 20:20:37.144 - Database connection closed for thread 11212
2025-06-02 20:20:37.155 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.156 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.156 - Database connection closed for thread 11212
2025-06-02 20:20:37.157 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.158 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.160 - Database connection closed for thread 11212
2025-06-02 20:20:37.161 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.162 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.169 - Database connection closed for thread 11212
2025-06-02 20:20:37.170 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.171 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.175 - Database connection closed for thread 11212
2025-06-02 20:20:37.176 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.176 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.188 - Database connection closed for thread 11212
2025-06-02 20:20:37.191 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.192 - New database connection created successfully for thread 11212
2025-06-02 20:20:37.194 - Database connection closed for thread 11212
2025-06-02 20:20:37.194 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:20:37.196 - New database connection created successfully for thread 11212
2025-06-02 20:23:21.789 - Recreating expired connection for thread 1676
2025-06-02 20:23:21.832 - Using existing connection for thread 11212
2025-06-02 20:23:21.934 - Database connection closed for thread 11212
2025-06-02 20:23:21.938 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:23:21.943 - New database connection created successfully for thread 11212
2025-06-02 20:23:21.953 - Database connection closed for thread 11212
2025-06-02 20:23:21.957 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:23:21.965 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.627 - Using existing connection for thread 11212
2025-06-02 20:26:20.661 - Database connection closed for thread 11212
2025-06-02 20:26:20.674 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.675 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.676 - Database connection closed for thread 11212
2025-06-02 20:26:20.676 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.677 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.679 - Database connection closed for thread 11212
2025-06-02 20:26:20.681 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.681 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.691 - Database connection closed for thread 11212
2025-06-02 20:26:20.693 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.695 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.698 - Database connection closed for thread 11212
2025-06-02 20:26:20.699 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.700 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.713 - Database connection closed for thread 11212
2025-06-02 20:26:20.714 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.715 - New database connection created successfully for thread 11212
2025-06-02 20:26:20.717 - Database connection closed for thread 11212
2025-06-02 20:26:20.718 - Creating new thread-specific database connection to data\stats.db for thread 11212
2025-06-02 20:26:20.719 - New database connection created successfully for thread 11212
2025-06-02 20:27:03.200 - ensure_database_exists called from thread 9284
2025-06-02 20:27:03.201 - Creating new thread-specific database connection to data\stats.db for thread 9284
2025-06-02 20:27:03.202 - New database connection created successfully for thread 9284
2025-06-02 20:27:03.205 - Stats database initialized successfully
2025-06-02 20:27:03.208 - ensure_database_exists called from thread 9284
2025-06-02 20:27:03.209 - Using existing connection for thread 9284
2025-06-02 20:27:03.210 - Stats database initialized successfully
2025-06-02 20:27:03.211 - ensure_database_exists called from thread 9284
2025-06-02 20:27:03.211 - Using existing connection for thread 9284
2025-06-02 20:27:03.212 - Stats database initialized successfully
2025-06-02 20:31:43.300 - Using existing connection for thread 9284
2025-06-02 20:31:43.336 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:31:43.343 - New database connection created successfully for thread 1428
2025-06-02 20:31:43.476 - Database connection closed for thread 1428
2025-06-02 20:31:43.477 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:31:43.478 - New database connection created successfully for thread 1428
2025-06-02 20:31:43.492 - Database connection closed for thread 1428
2025-06-02 20:31:43.499 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:31:43.503 - New database connection created successfully for thread 1428
2025-06-02 20:31:43.551 - get_summary_stats called from thread 1428
2025-06-02 20:31:43.552 - Using existing connection for thread 1428
2025-06-02 20:31:43.554 - Total earnings from database: 4120.0
2025-06-02 20:31:43.564 - Daily earnings from database: 640.0
2025-06-02 20:31:43.570 - Daily games from database: 16
2025-06-02 20:31:43.573 - Wallet balance from database: 0
2025-06-02 20:31:43.573 - Total games played from database: 22
2025-06-02 20:31:43.577 - Total winners from database: 16
2025-06-02 20:31:43.579 - Returning summary stats: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:33:54.104 - ensure_database_exists called from thread 9284
2025-06-02 20:33:54.105 - Using existing connection for thread 9284
2025-06-02 20:33:54.105 - Stats database initialized successfully
2025-06-02 20:33:54.146 - Using existing connection for thread 1428
2025-06-02 20:33:54.189 - Database connection closed for thread 1428
2025-06-02 20:33:54.202 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.204 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.205 - Database connection closed for thread 1428
2025-06-02 20:33:54.206 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.206 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.209 - Database connection closed for thread 1428
2025-06-02 20:33:54.209 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.211 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.216 - Database connection closed for thread 1428
2025-06-02 20:33:54.217 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.218 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.223 - Database connection closed for thread 1428
2025-06-02 20:33:54.225 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.226 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.237 - Database connection closed for thread 1428
2025-06-02 20:33:54.238 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.242 - New database connection created successfully for thread 1428
2025-06-02 20:33:54.245 - Database connection closed for thread 1428
2025-06-02 20:33:54.248 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:33:54.250 - New database connection created successfully for thread 1428
2025-06-02 20:37:57.478 - Using existing connection for thread 9284
2025-06-02 20:37:57.521 - Using existing connection for thread 1428
2025-06-02 20:37:57.607 - Database connection closed for thread 1428
2025-06-02 20:37:57.608 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:37:57.608 - New database connection created successfully for thread 1428
2025-06-02 20:37:57.617 - Database connection closed for thread 1428
2025-06-02 20:37:57.618 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:37:57.621 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.120 - Using existing connection for thread 1428
2025-06-02 20:39:56.167 - Database connection closed for thread 1428
2025-06-02 20:39:56.178 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.179 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.180 - Database connection closed for thread 1428
2025-06-02 20:39:56.180 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.181 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.183 - Database connection closed for thread 1428
2025-06-02 20:39:56.184 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.185 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.190 - Database connection closed for thread 1428
2025-06-02 20:39:56.192 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.194 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.198 - Database connection closed for thread 1428
2025-06-02 20:39:56.201 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.202 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.213 - Database connection closed for thread 1428
2025-06-02 20:39:56.214 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.217 - New database connection created successfully for thread 1428
2025-06-02 20:39:56.221 - Database connection closed for thread 1428
2025-06-02 20:39:56.222 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:39:56.223 - New database connection created successfully for thread 1428
2025-06-02 20:40:33.983 - Using existing connection for thread 9284
2025-06-02 20:40:34.003 - Database connection closed for thread 1428
2025-06-02 20:40:34.004 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:40:34.005 - New database connection created successfully for thread 1428
2025-06-02 20:40:34.009 - Database connection closed for thread 1428
2025-06-02 20:40:34.011 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:40:34.013 - New database connection created successfully for thread 1428
2025-06-02 20:44:13.932 - Using existing connection for thread 9284
2025-06-02 20:44:13.973 - Using existing connection for thread 1428
2025-06-02 20:44:14.099 - Database connection closed for thread 1428
2025-06-02 20:44:14.099 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:44:14.100 - New database connection created successfully for thread 1428
2025-06-02 20:44:14.110 - Database connection closed for thread 1428
2025-06-02 20:44:14.111 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:44:14.113 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.638 - Creating new thread-specific database connection to data\stats.db for thread 11048
2025-06-02 20:46:47.638 - New database connection created successfully for thread 11048
2025-06-02 20:46:47.641 - Database connection closed for thread 11048
2025-06-02 20:46:47.641 - Creating new thread-specific database connection to data\stats.db for thread 11048
2025-06-02 20:46:47.643 - New database connection created successfully for thread 11048
2025-06-02 20:46:47.733 - Using existing connection for thread 1428
2025-06-02 20:46:47.822 - Database connection closed for thread 1428
2025-06-02 20:46:47.836 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.836 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.837 - Database connection closed for thread 1428
2025-06-02 20:46:47.837 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.838 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.840 - Database connection closed for thread 1428
2025-06-02 20:46:47.840 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.842 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.847 - Database connection closed for thread 1428
2025-06-02 20:46:47.848 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.850 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.852 - Database connection closed for thread 1428
2025-06-02 20:46:47.856 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.863 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.881 - Database connection closed for thread 1428
2025-06-02 20:46:47.885 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.887 - New database connection created successfully for thread 1428
2025-06-02 20:46:47.891 - Database connection closed for thread 1428
2025-06-02 20:46:47.893 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:46:47.895 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.272 - Using existing connection for thread 1428
2025-06-02 20:50:19.317 - Database connection closed for thread 1428
2025-06-02 20:50:19.328 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.329 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.330 - Database connection closed for thread 1428
2025-06-02 20:50:19.331 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.332 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.365 - Database connection closed for thread 1428
2025-06-02 20:50:19.366 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.368 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.376 - Database connection closed for thread 1428
2025-06-02 20:50:19.377 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.378 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.381 - Database connection closed for thread 1428
2025-06-02 20:50:19.382 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.384 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.398 - Database connection closed for thread 1428
2025-06-02 20:50:19.401 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.402 - New database connection created successfully for thread 1428
2025-06-02 20:50:19.404 - Database connection closed for thread 1428
2025-06-02 20:50:19.406 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:50:19.407 - New database connection created successfully for thread 1428
2025-06-02 20:51:42.732 - Recreating expired connection for thread 9284
2025-06-02 20:51:42.796 - Using existing connection for thread 1428
2025-06-02 20:51:42.874 - Database connection closed for thread 1428
2025-06-02 20:51:42.875 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:51:42.876 - New database connection created successfully for thread 1428
2025-06-02 20:51:42.890 - Database connection closed for thread 1428
2025-06-02 20:51:42.892 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:51:42.894 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.173 - Using existing connection for thread 1428
2025-06-02 20:53:22.232 - Database connection closed for thread 1428
2025-06-02 20:53:22.245 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.246 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.247 - Database connection closed for thread 1428
2025-06-02 20:53:22.248 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.249 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.253 - Database connection closed for thread 1428
2025-06-02 20:53:22.253 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.254 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.259 - Database connection closed for thread 1428
2025-06-02 20:53:22.263 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.264 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.268 - Database connection closed for thread 1428
2025-06-02 20:53:22.268 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.269 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.280 - Database connection closed for thread 1428
2025-06-02 20:53:22.282 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.287 - New database connection created successfully for thread 1428
2025-06-02 20:53:22.289 - Database connection closed for thread 1428
2025-06-02 20:53:22.290 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:53:22.292 - New database connection created successfully for thread 1428
2025-06-02 20:55:56.003 - Using existing connection for thread 9284
2025-06-02 20:55:56.094 - Using existing connection for thread 1428
2025-06-02 20:55:56.176 - Database connection closed for thread 1428
2025-06-02 20:55:56.176 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:55:56.177 - New database connection created successfully for thread 1428
2025-06-02 20:55:56.186 - Database connection closed for thread 1428
2025-06-02 20:55:56.187 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 20:55:56.190 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.792 - Recreating expired connection for thread 1428
2025-06-02 21:01:25.829 - Database connection closed for thread 1428
2025-06-02 21:01:25.843 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.844 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.845 - Database connection closed for thread 1428
2025-06-02 21:01:25.845 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.846 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.848 - Database connection closed for thread 1428
2025-06-02 21:01:25.849 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.850 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.856 - Database connection closed for thread 1428
2025-06-02 21:01:25.857 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.858 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.861 - Database connection closed for thread 1428
2025-06-02 21:01:25.863 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.864 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.875 - Database connection closed for thread 1428
2025-06-02 21:01:25.878 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.880 - New database connection created successfully for thread 1428
2025-06-02 21:01:25.882 - Database connection closed for thread 1428
2025-06-02 21:01:25.883 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:01:25.884 - New database connection created successfully for thread 1428
2025-06-02 21:03:00.743 - Recreating expired connection for thread 9284
2025-06-02 21:03:00.747 - Using existing connection for thread 1428
2025-06-02 21:03:00.833 - Database connection closed for thread 1428
2025-06-02 21:03:00.834 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:03:00.835 - New database connection created successfully for thread 1428
2025-06-02 21:03:00.844 - Database connection closed for thread 1428
2025-06-02 21:03:00.845 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:03:00.847 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.216 - Using existing connection for thread 1428
2025-06-02 21:06:56.294 - Database connection closed for thread 1428
2025-06-02 21:06:56.305 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.305 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.306 - Database connection closed for thread 1428
2025-06-02 21:06:56.307 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.307 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.310 - Database connection closed for thread 1428
2025-06-02 21:06:56.311 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.312 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.317 - Database connection closed for thread 1428
2025-06-02 21:06:56.318 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.319 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.324 - Database connection closed for thread 1428
2025-06-02 21:06:56.324 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.325 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.336 - Database connection closed for thread 1428
2025-06-02 21:06:56.338 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.341 - New database connection created successfully for thread 1428
2025-06-02 21:06:56.344 - Database connection closed for thread 1428
2025-06-02 21:06:56.345 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:06:56.346 - New database connection created successfully for thread 1428
2025-06-02 21:07:10.170 - Using existing connection for thread 9284
2025-06-02 21:07:10.211 - Using existing connection for thread 1428
2025-06-02 21:07:10.308 - Database connection closed for thread 1428
2025-06-02 21:07:10.311 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:07:10.313 - New database connection created successfully for thread 1428
2025-06-02 21:07:10.326 - Database connection closed for thread 1428
2025-06-02 21:07:10.328 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:07:10.332 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.804 - Using existing connection for thread 1428
2025-06-02 21:09:48.846 - Database connection closed for thread 1428
2025-06-02 21:09:48.857 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.857 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.859 - Database connection closed for thread 1428
2025-06-02 21:09:48.859 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.859 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.862 - Database connection closed for thread 1428
2025-06-02 21:09:48.862 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.863 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.870 - Database connection closed for thread 1428
2025-06-02 21:09:48.871 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.872 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.874 - Database connection closed for thread 1428
2025-06-02 21:09:48.876 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.878 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.889 - Database connection closed for thread 1428
2025-06-02 21:09:48.891 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.893 - New database connection created successfully for thread 1428
2025-06-02 21:09:48.896 - Database connection closed for thread 1428
2025-06-02 21:09:48.896 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:09:48.897 - New database connection created successfully for thread 1428
2025-06-02 21:14:04.339 - Recreating expired connection for thread 9284
2025-06-02 21:14:04.370 - Using existing connection for thread 1428
2025-06-02 21:14:04.455 - Database connection closed for thread 1428
2025-06-02 21:14:04.457 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:14:04.457 - New database connection created successfully for thread 1428
2025-06-02 21:14:04.467 - Database connection closed for thread 1428
2025-06-02 21:14:04.468 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:14:04.470 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.485 - Using existing connection for thread 1428
2025-06-02 21:17:44.526 - Database connection closed for thread 1428
2025-06-02 21:17:44.538 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.538 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.539 - Database connection closed for thread 1428
2025-06-02 21:17:44.540 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.540 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.543 - Database connection closed for thread 1428
2025-06-02 21:17:44.544 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.546 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.552 - Database connection closed for thread 1428
2025-06-02 21:17:44.552 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.554 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.556 - Database connection closed for thread 1428
2025-06-02 21:17:44.560 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.561 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.572 - Database connection closed for thread 1428
2025-06-02 21:17:44.575 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.576 - New database connection created successfully for thread 1428
2025-06-02 21:17:44.582 - Database connection closed for thread 1428
2025-06-02 21:17:44.585 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:17:44.586 - New database connection created successfully for thread 1428
2025-06-02 21:19:31.099 - Recreating expired connection for thread 9284
2025-06-02 21:19:31.184 - Using existing connection for thread 1428
2025-06-02 21:19:31.261 - Database connection closed for thread 1428
2025-06-02 21:19:31.262 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:19:31.263 - New database connection created successfully for thread 1428
2025-06-02 21:19:31.275 - Database connection closed for thread 1428
2025-06-02 21:19:31.278 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:19:31.280 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.848 - Using existing connection for thread 1428
2025-06-02 21:21:52.892 - Database connection closed for thread 1428
2025-06-02 21:21:52.904 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.905 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.906 - Database connection closed for thread 1428
2025-06-02 21:21:52.906 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.906 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.910 - Database connection closed for thread 1428
2025-06-02 21:21:52.910 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.911 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.917 - Database connection closed for thread 1428
2025-06-02 21:21:52.917 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.918 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.922 - Database connection closed for thread 1428
2025-06-02 21:21:52.924 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.925 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.935 - Database connection closed for thread 1428
2025-06-02 21:21:52.936 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.939 - New database connection created successfully for thread 1428
2025-06-02 21:21:52.942 - Database connection closed for thread 1428
2025-06-02 21:21:52.943 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:21:52.945 - New database connection created successfully for thread 1428
2025-06-02 21:24:36.250 - Recreating expired connection for thread 9284
2025-06-02 21:24:36.306 - Using existing connection for thread 1428
2025-06-02 21:24:36.388 - Database connection closed for thread 1428
2025-06-02 21:24:36.389 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:24:36.390 - New database connection created successfully for thread 1428
2025-06-02 21:24:36.400 - Database connection closed for thread 1428
2025-06-02 21:24:36.401 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:24:36.404 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.378 - Using existing connection for thread 1428
2025-06-02 21:27:49.430 - Database connection closed for thread 1428
2025-06-02 21:27:49.446 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.447 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.447 - Database connection closed for thread 1428
2025-06-02 21:27:49.448 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.448 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.451 - Database connection closed for thread 1428
2025-06-02 21:27:49.451 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.453 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.458 - Database connection closed for thread 1428
2025-06-02 21:27:49.460 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.461 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.465 - Database connection closed for thread 1428
2025-06-02 21:27:49.503 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.505 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.512 - Database connection closed for thread 1428
2025-06-02 21:27:49.515 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.518 - New database connection created successfully for thread 1428
2025-06-02 21:27:49.520 - Database connection closed for thread 1428
2025-06-02 21:27:49.521 - Creating new thread-specific database connection to data\stats.db for thread 1428
2025-06-02 21:27:49.522 - New database connection created successfully for thread 1428
2025-06-02 23:01:10.274 - ensure_database_exists called from thread 5300
2025-06-02 23:01:10.281 - Creating new thread-specific database connection to data\stats.db for thread 5300
2025-06-02 23:01:10.293 - New database connection created successfully for thread 5300
2025-06-02 23:01:10.303 - Stats database initialized successfully
2025-06-02 23:01:10.304 - ensure_database_exists called from thread 5300
2025-06-02 23:01:10.304 - Using existing connection for thread 5300
2025-06-02 23:01:10.305 - Stats database initialized successfully
2025-06-02 23:01:10.306 - ensure_database_exists called from thread 5300
2025-06-02 23:01:10.306 - Using existing connection for thread 5300
2025-06-02 23:01:10.306 - Stats database initialized successfully
2025-06-02 23:02:11.003 - ensure_database_exists called from thread 8300
2025-06-02 23:02:11.004 - Creating new thread-specific database connection to data\stats.db for thread 8300
2025-06-02 23:02:11.005 - New database connection created successfully for thread 8300
2025-06-02 23:02:11.006 - Stats database initialized successfully
2025-06-02 23:02:11.009 - ensure_database_exists called from thread 8300
2025-06-02 23:02:11.010 - Using existing connection for thread 8300
2025-06-02 23:02:11.011 - Stats database initialized successfully
2025-06-02 23:02:11.011 - ensure_database_exists called from thread 8300
2025-06-02 23:02:11.012 - Using existing connection for thread 8300
2025-06-02 23:02:11.012 - Stats database initialized successfully
2025-06-04 17:12:23.458 - ensure_database_exists called from thread 7000
2025-06-04 17:12:23.484 - Creating new thread-specific database connection to data\stats.db for thread 7000
2025-06-04 17:12:23.532 - New database connection created successfully for thread 7000
2025-06-04 17:12:23.539 - Stats database initialized successfully
2025-06-04 17:12:23.540 - ensure_database_exists called from thread 7000
2025-06-04 17:12:23.541 - Using existing connection for thread 7000
2025-06-04 17:12:23.542 - Stats database initialized successfully
2025-06-04 17:12:23.543 - ensure_database_exists called from thread 7000
2025-06-04 17:12:23.545 - Using existing connection for thread 7000
2025-06-04 17:12:23.546 - Stats database initialized successfully
2025-06-04 17:15:46.863 - Using existing connection for thread 7000
2025-06-04 17:15:46.882 - Creating new thread-specific database connection to data\stats.db for thread 3744
2025-06-04 17:15:46.883 - New database connection created successfully for thread 3744
2025-06-04 17:15:46.887 - Database connection closed for thread 3744
2025-06-04 17:15:46.889 - Creating new thread-specific database connection to data\stats.db for thread 3744
2025-06-04 17:15:46.891 - New database connection created successfully for thread 3744
2025-06-04 17:15:46.962 - get_summary_stats called from thread 3744
2025-06-04 17:15:46.964 - Using existing connection for thread 3744
2025-06-04 17:15:46.966 - Total earnings from database: 4523.2
2025-06-04 17:15:46.967 - Daily earnings from database: 0
2025-06-04 17:15:46.968 - Daily games from database: 0
2025-06-04 17:15:46.969 - Wallet balance from database: 0
2025-06-04 17:15:46.970 - Total games played from database: 33
2025-06-04 17:15:46.971 - Total winners from database: 27
2025-06-04 17:15:46.973 - Returning summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:20:25.889 - ensure_database_exists called from thread 7000
2025-06-04 17:20:25.889 - Using existing connection for thread 7000
2025-06-04 17:20:25.891 - Stats database initialized successfully
2025-06-04 17:22:04.440 - ensure_database_exists called from thread 2956
2025-06-04 17:22:04.440 - Creating new thread-specific database connection to data\stats.db for thread 2956
2025-06-04 17:22:04.442 - New database connection created successfully for thread 2956
2025-06-04 17:22:04.444 - Stats database initialized successfully
2025-06-04 17:22:04.446 - ensure_database_exists called from thread 2956
2025-06-04 17:22:04.447 - Using existing connection for thread 2956
2025-06-04 17:22:04.448 - Stats database initialized successfully
2025-06-04 17:22:04.448 - ensure_database_exists called from thread 2956
2025-06-04 17:22:04.449 - Using existing connection for thread 2956
2025-06-04 17:22:04.449 - Stats database initialized successfully
2025-06-04 17:22:59.142 - Using existing connection for thread 2956
2025-06-04 17:22:59.156 - Creating new thread-specific database connection to data\stats.db for thread 6988
2025-06-04 17:22:59.160 - New database connection created successfully for thread 6988
2025-06-04 17:22:59.164 - Database connection closed for thread 6988
2025-06-04 17:22:59.166 - Creating new thread-specific database connection to data\stats.db for thread 6988
2025-06-04 17:22:59.167 - New database connection created successfully for thread 6988
2025-06-04 17:22:59.229 - get_summary_stats called from thread 6988
2025-06-04 17:22:59.231 - Using existing connection for thread 6988
2025-06-04 17:22:59.237 - Total earnings from database: 4523.2
2025-06-04 17:22:59.240 - Daily earnings from database: 0
2025-06-04 17:22:59.240 - Daily games from database: 0
2025-06-04 17:22:59.242 - Wallet balance from database: 0
2025-06-04 17:22:59.243 - Total games played from database: 33
2025-06-04 17:22:59.244 - Total winners from database: 27
2025-06-04 17:22:59.246 - Returning summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:23:05.530 - ensure_database_exists called from thread 2956
2025-06-04 17:23:05.530 - Using existing connection for thread 2956
2025-06-04 17:23:05.531 - Stats database initialized successfully
2025-06-04 17:23:44.180 - Using existing connection for thread 2956
2025-06-04 17:23:44.238 - Database connection closed for thread 6988
2025-06-04 17:23:44.239 - Creating new thread-specific database connection to data\stats.db for thread 6988
2025-06-04 17:23:44.239 - New database connection created successfully for thread 6988
2025-06-04 17:23:44.242 - Database connection closed for thread 6988
2025-06-04 17:23:44.243 - Creating new thread-specific database connection to data\stats.db for thread 6988
2025-06-04 17:23:44.244 - New database connection created successfully for thread 6988
2025-06-04 17:28:59.578 - ensure_database_exists called from thread 4824
2025-06-04 17:28:59.580 - Creating new thread-specific database connection to data\stats.db for thread 4824
2025-06-04 17:28:59.580 - New database connection created successfully for thread 4824
2025-06-04 17:28:59.583 - Stats database initialized successfully
2025-06-04 17:28:59.585 - ensure_database_exists called from thread 4824
2025-06-04 17:28:59.585 - Using existing connection for thread 4824
2025-06-04 17:28:59.586 - Stats database initialized successfully
2025-06-04 17:28:59.587 - ensure_database_exists called from thread 4824
2025-06-04 17:28:59.587 - Using existing connection for thread 4824
2025-06-04 17:28:59.588 - Stats database initialized successfully
2025-06-04 17:29:46.476 - Using existing connection for thread 4824
2025-06-04 17:29:46.490 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:29:46.495 - New database connection created successfully for thread 10740
2025-06-04 17:29:46.499 - Database connection closed for thread 10740
2025-06-04 17:29:46.501 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:29:46.503 - New database connection created successfully for thread 10740
2025-06-04 17:29:46.564 - get_summary_stats called from thread 10740
2025-06-04 17:29:46.568 - Using existing connection for thread 10740
2025-06-04 17:29:46.571 - Total earnings from database: 4523.2
2025-06-04 17:29:46.573 - Daily earnings from database: 0
2025-06-04 17:29:46.576 - Daily games from database: 0
2025-06-04 17:29:46.577 - Wallet balance from database: 0
2025-06-04 17:29:46.578 - Total games played from database: 33
2025-06-04 17:29:46.580 - Total winners from database: 27
2025-06-04 17:29:46.581 - Returning summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:33:39.064 - ensure_database_exists called from thread 4824
2025-06-04 17:33:39.064 - Using existing connection for thread 4824
2025-06-04 17:33:39.065 - Stats database initialized successfully
2025-06-04 17:35:47.757 - Using existing connection for thread 4824
2025-06-04 17:35:47.790 - Database connection closed for thread 10740
2025-06-04 17:35:47.794 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:35:47.800 - New database connection created successfully for thread 10740
2025-06-04 17:35:47.811 - Database connection closed for thread 10740
2025-06-04 17:35:47.817 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:35:47.819 - New database connection created successfully for thread 10740
2025-06-04 17:42:00.746 - Recreating expired connection for thread 4824
2025-06-04 17:42:00.807 - Recreating expired connection for thread 10740
2025-06-04 17:42:00.954 - Database connection closed for thread 10740
2025-06-04 17:42:00.955 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:00.956 - New database connection created successfully for thread 10740
2025-06-04 17:42:00.965 - Database connection closed for thread 10740
2025-06-04 17:42:00.966 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:00.971 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.410 - Using existing connection for thread 10740
2025-06-04 17:42:03.473 - Database connection closed for thread 10740
2025-06-04 17:42:03.496 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.498 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.498 - Database connection closed for thread 10740
2025-06-04 17:42:03.499 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.500 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.502 - Database connection closed for thread 10740
2025-06-04 17:42:03.503 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.504 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.510 - Database connection closed for thread 10740
2025-06-04 17:42:03.511 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.512 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.517 - Database connection closed for thread 10740
2025-06-04 17:42:03.518 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.519 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.529 - Database connection closed for thread 10740
2025-06-04 17:42:03.532 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.535 - New database connection created successfully for thread 10740
2025-06-04 17:42:03.538 - Database connection closed for thread 10740
2025-06-04 17:42:03.539 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:03.540 - New database connection created successfully for thread 10740
2025-06-04 17:42:20.288 - Using existing connection for thread 4824
2025-06-04 17:42:20.307 - Using existing connection for thread 10740
2025-06-04 17:42:20.345 - Database connection closed for thread 10740
2025-06-04 17:42:20.346 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:20.347 - New database connection created successfully for thread 10740
2025-06-04 17:42:20.357 - Database connection closed for thread 10740
2025-06-04 17:42:20.358 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:42:20.360 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.575 - Using existing connection for thread 10740
2025-06-04 17:47:01.638 - Database connection closed for thread 10740
2025-06-04 17:47:01.648 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.649 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.649 - Database connection closed for thread 10740
2025-06-04 17:47:01.650 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.652 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.652 - Database connection closed for thread 10740
2025-06-04 17:47:01.654 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.655 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.662 - Database connection closed for thread 10740
2025-06-04 17:47:01.664 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.665 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.669 - Database connection closed for thread 10740
2025-06-04 17:47:01.672 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.677 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.687 - Database connection closed for thread 10740
2025-06-04 17:47:01.688 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.690 - New database connection created successfully for thread 10740
2025-06-04 17:47:01.693 - Database connection closed for thread 10740
2025-06-04 17:47:01.694 - Creating new thread-specific database connection to data\stats.db for thread 10740
2025-06-04 17:47:01.695 - New database connection created successfully for thread 10740
2025-06-04 17:50:35.054 - ensure_database_exists called from thread 7364
2025-06-04 17:50:35.055 - Creating new thread-specific database connection to data\stats.db for thread 7364
2025-06-04 17:50:35.056 - New database connection created successfully for thread 7364
2025-06-04 17:50:35.059 - Stats database initialized successfully
2025-06-04 17:50:35.060 - ensure_database_exists called from thread 7364
2025-06-04 17:50:35.063 - Using existing connection for thread 7364
2025-06-04 17:50:35.064 - Stats database initialized successfully
2025-06-04 17:50:35.064 - ensure_database_exists called from thread 7364
2025-06-04 17:50:35.065 - Using existing connection for thread 7364
2025-06-04 17:50:35.066 - Stats database initialized successfully
2025-06-04 17:51:38.112 - Using existing connection for thread 7364
2025-06-04 17:51:38.179 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:51:38.186 - New database connection created successfully for thread 2972
2025-06-04 17:51:38.314 - Database connection closed for thread 2972
2025-06-04 17:51:38.319 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:51:38.320 - New database connection created successfully for thread 2972
2025-06-04 17:51:38.330 - Database connection closed for thread 2972
2025-06-04 17:51:38.332 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:51:38.335 - New database connection created successfully for thread 2972
2025-06-04 17:51:38.394 - get_summary_stats called from thread 2972
2025-06-04 17:51:38.395 - Using existing connection for thread 2972
2025-06-04 17:51:38.398 - Total earnings from database: 4568.0
2025-06-04 17:51:38.401 - Daily earnings from database: 44.8
2025-06-04 17:51:38.402 - Daily games from database: 2
2025-06-04 17:51:38.403 - Wallet balance from database: 0
2025-06-04 17:51:38.404 - Total games played from database: 35
2025-06-04 17:51:38.406 - Total winners from database: 29
2025-06-04 17:51:38.407 - Returning summary stats: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:55:15.093 - ensure_database_exists called from thread 7364
2025-06-04 17:55:15.094 - Using existing connection for thread 7364
2025-06-04 17:55:15.094 - Stats database initialized successfully
2025-06-04 17:55:15.127 - Using existing connection for thread 2972
2025-06-04 17:55:15.164 - Database connection closed for thread 2972
2025-06-04 17:55:15.176 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.177 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.178 - Database connection closed for thread 2972
2025-06-04 17:55:15.179 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.180 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.182 - Database connection closed for thread 2972
2025-06-04 17:55:15.183 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.185 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.190 - Database connection closed for thread 2972
2025-06-04 17:55:15.191 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.192 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.195 - Database connection closed for thread 2972
2025-06-04 17:55:15.198 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.199 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.211 - Database connection closed for thread 2972
2025-06-04 17:55:15.214 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.215 - New database connection created successfully for thread 2972
2025-06-04 17:55:15.216 - Database connection closed for thread 2972
2025-06-04 17:55:15.218 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:55:15.220 - New database connection created successfully for thread 2972
2025-06-04 17:59:33.050 - Using existing connection for thread 7364
2025-06-04 17:59:33.069 - Using existing connection for thread 2972
2025-06-04 17:59:33.162 - Database connection closed for thread 2972
2025-06-04 17:59:33.163 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:59:33.165 - New database connection created successfully for thread 2972
2025-06-04 17:59:33.178 - Database connection closed for thread 2972
2025-06-04 17:59:33.179 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 17:59:33.181 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.660 - Using existing connection for thread 2972
2025-06-04 18:03:21.720 - Database connection closed for thread 2972
2025-06-04 18:03:21.731 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.732 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.732 - Database connection closed for thread 2972
2025-06-04 18:03:21.732 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.733 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.735 - Database connection closed for thread 2972
2025-06-04 18:03:21.736 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.737 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.742 - Database connection closed for thread 2972
2025-06-04 18:03:21.744 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.745 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.750 - Database connection closed for thread 2972
2025-06-04 18:03:21.750 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.752 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.766 - Database connection closed for thread 2972
2025-06-04 18:03:21.769 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.772 - New database connection created successfully for thread 2972
2025-06-04 18:03:21.774 - Database connection closed for thread 2972
2025-06-04 18:03:21.774 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:03:21.775 - New database connection created successfully for thread 2972
2025-06-04 18:06:10.647 - Recreating expired connection for thread 7364
2025-06-04 18:06:10.692 - Using existing connection for thread 2972
2025-06-04 18:06:10.782 - Database connection closed for thread 2972
2025-06-04 18:06:10.785 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:06:10.789 - New database connection created successfully for thread 2972
2025-06-04 18:06:10.801 - Database connection closed for thread 2972
2025-06-04 18:06:10.803 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:06:10.806 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.633 - Using existing connection for thread 2972
2025-06-04 18:10:12.667 - Database connection closed for thread 2972
2025-06-04 18:10:12.677 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.678 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.678 - Database connection closed for thread 2972
2025-06-04 18:10:12.678 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.680 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.682 - Database connection closed for thread 2972
2025-06-04 18:10:12.682 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.684 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.690 - Database connection closed for thread 2972
2025-06-04 18:10:12.690 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.692 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.694 - Database connection closed for thread 2972
2025-06-04 18:10:12.732 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.757 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.769 - Database connection closed for thread 2972
2025-06-04 18:10:12.771 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.773 - New database connection created successfully for thread 2972
2025-06-04 18:10:12.776 - Database connection closed for thread 2972
2025-06-04 18:10:12.776 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:12.778 - New database connection created successfully for thread 2972
2025-06-04 18:10:57.129 - Using existing connection for thread 7364
2025-06-04 18:10:57.131 - Using existing connection for thread 2972
2025-06-04 18:10:57.221 - Database connection closed for thread 2972
2025-06-04 18:10:57.222 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:57.222 - New database connection created successfully for thread 2972
2025-06-04 18:10:57.232 - Database connection closed for thread 2972
2025-06-04 18:10:57.233 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:10:57.235 - New database connection created successfully for thread 2972
2025-06-04 18:11:00.998 - Using existing connection for thread 2972
2025-06-04 18:11:01.050 - Database connection closed for thread 2972
2025-06-04 18:11:01.060 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.061 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.061 - Database connection closed for thread 2972
2025-06-04 18:11:01.062 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.062 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.064 - Database connection closed for thread 2972
2025-06-04 18:11:01.065 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.066 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.073 - Database connection closed for thread 2972
2025-06-04 18:11:01.074 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.075 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.078 - Database connection closed for thread 2972
2025-06-04 18:11:01.079 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.079 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.090 - Database connection closed for thread 2972
2025-06-04 18:11:01.090 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.092 - New database connection created successfully for thread 2972
2025-06-04 18:11:01.094 - Database connection closed for thread 2972
2025-06-04 18:11:01.095 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:01.097 - New database connection created successfully for thread 2972
2025-06-04 18:11:17.688 - Using existing connection for thread 7364
2025-06-04 18:11:17.761 - Using existing connection for thread 2972
2025-06-04 18:11:17.836 - Database connection closed for thread 2972
2025-06-04 18:11:17.836 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:17.838 - New database connection created successfully for thread 2972
2025-06-04 18:11:17.849 - Database connection closed for thread 2972
2025-06-04 18:11:17.851 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:11:17.854 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.696 - Using existing connection for thread 2972
2025-06-04 18:13:37.732 - Database connection closed for thread 2972
2025-06-04 18:13:37.743 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.744 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.745 - Database connection closed for thread 2972
2025-06-04 18:13:37.745 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.746 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.748 - Database connection closed for thread 2972
2025-06-04 18:13:37.749 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.750 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.755 - Database connection closed for thread 2972
2025-06-04 18:13:37.757 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.758 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.762 - Database connection closed for thread 2972
2025-06-04 18:13:37.762 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.763 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.774 - Database connection closed for thread 2972
2025-06-04 18:13:37.776 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.779 - New database connection created successfully for thread 2972
2025-06-04 18:13:37.782 - Database connection closed for thread 2972
2025-06-04 18:13:37.783 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:13:37.784 - New database connection created successfully for thread 2972
2025-06-04 18:15:26.770 - Using existing connection for thread 7364
2025-06-04 18:15:26.802 - Using existing connection for thread 2972
2025-06-04 18:15:26.945 - Database connection closed for thread 2972
2025-06-04 18:15:26.945 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:15:26.946 - New database connection created successfully for thread 2972
2025-06-04 18:15:26.958 - Database connection closed for thread 2972
2025-06-04 18:15:26.962 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:15:26.964 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.451 - Using existing connection for thread 2972
2025-06-04 18:18:31.495 - Database connection closed for thread 2972
2025-06-04 18:18:31.506 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.506 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.507 - Database connection closed for thread 2972
2025-06-04 18:18:31.508 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.508 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.511 - Database connection closed for thread 2972
2025-06-04 18:18:31.513 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.514 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.523 - Database connection closed for thread 2972
2025-06-04 18:18:31.525 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.526 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.531 - Database connection closed for thread 2972
2025-06-04 18:18:31.531 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.533 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.544 - Database connection closed for thread 2972
2025-06-04 18:18:31.545 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.547 - New database connection created successfully for thread 2972
2025-06-04 18:18:31.549 - Database connection closed for thread 2972
2025-06-04 18:18:31.551 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:18:31.552 - New database connection created successfully for thread 2972
2025-06-04 18:19:41.366 - Using existing connection for thread 7364
2025-06-04 18:19:41.466 - Using existing connection for thread 2972
2025-06-04 18:19:41.544 - Database connection closed for thread 2972
2025-06-04 18:19:41.545 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:19:41.545 - New database connection created successfully for thread 2972
2025-06-04 18:19:41.559 - Database connection closed for thread 2972
2025-06-04 18:19:41.562 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:19:41.565 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.052 - Using existing connection for thread 2972
2025-06-04 18:22:31.117 - Database connection closed for thread 2972
2025-06-04 18:22:31.129 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.129 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.129 - Database connection closed for thread 2972
2025-06-04 18:22:31.131 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.132 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.135 - Database connection closed for thread 2972
2025-06-04 18:22:31.136 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.139 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.145 - Database connection closed for thread 2972
2025-06-04 18:22:31.146 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.147 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.152 - Database connection closed for thread 2972
2025-06-04 18:22:31.153 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.155 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.163 - Database connection closed for thread 2972
2025-06-04 18:22:31.167 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.169 - New database connection created successfully for thread 2972
2025-06-04 18:22:31.171 - Database connection closed for thread 2972
2025-06-04 18:22:31.172 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:22:31.173 - New database connection created successfully for thread 2972
2025-06-04 18:24:13.479 - Using existing connection for thread 7364
2025-06-04 18:24:13.490 - Using existing connection for thread 2972
2025-06-04 18:24:13.612 - Database connection closed for thread 2972
2025-06-04 18:24:13.614 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:24:13.619 - New database connection created successfully for thread 2972
2025-06-04 18:24:13.629 - Database connection closed for thread 2972
2025-06-04 18:24:13.633 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:24:13.636 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.493 - Recreating expired connection for thread 2972
2025-06-04 18:29:34.557 - Database connection closed for thread 2972
2025-06-04 18:29:34.568 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.569 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.570 - Database connection closed for thread 2972
2025-06-04 18:29:34.571 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.572 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.573 - Database connection closed for thread 2972
2025-06-04 18:29:34.574 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.575 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.580 - Database connection closed for thread 2972
2025-06-04 18:29:34.582 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.583 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.586 - Database connection closed for thread 2972
2025-06-04 18:29:34.589 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.591 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.601 - Database connection closed for thread 2972
2025-06-04 18:29:34.603 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.609 - New database connection created successfully for thread 2972
2025-06-04 18:29:34.611 - Database connection closed for thread 2972
2025-06-04 18:29:34.612 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:29:34.613 - New database connection created successfully for thread 2972
2025-06-04 18:30:43.100 - Recreating expired connection for thread 7364
2025-06-04 18:30:43.156 - Using existing connection for thread 2972
2025-06-04 18:30:43.232 - Database connection closed for thread 2972
2025-06-04 18:30:43.236 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:30:43.237 - New database connection created successfully for thread 2972
2025-06-04 18:30:43.251 - Database connection closed for thread 2972
2025-06-04 18:30:43.254 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:30:43.259 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.752 - Using existing connection for thread 2972
2025-06-04 18:35:07.788 - Database connection closed for thread 2972
2025-06-04 18:35:07.801 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.801 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.802 - Database connection closed for thread 2972
2025-06-04 18:35:07.802 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.803 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.806 - Database connection closed for thread 2972
2025-06-04 18:35:07.807 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.809 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.817 - Database connection closed for thread 2972
2025-06-04 18:35:07.818 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.819 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.823 - Database connection closed for thread 2972
2025-06-04 18:35:07.825 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.826 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.836 - Database connection closed for thread 2972
2025-06-04 18:35:07.838 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.841 - New database connection created successfully for thread 2972
2025-06-04 18:35:07.843 - Database connection closed for thread 2972
2025-06-04 18:35:07.843 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:35:07.844 - New database connection created successfully for thread 2972
2025-06-04 18:36:22.016 - Recreating expired connection for thread 7364
2025-06-04 18:36:22.058 - Using existing connection for thread 2972
2025-06-04 18:36:22.138 - Database connection closed for thread 2972
2025-06-04 18:36:22.139 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:36:22.140 - New database connection created successfully for thread 2972
2025-06-04 18:36:22.149 - Database connection closed for thread 2972
2025-06-04 18:36:22.154 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:36:22.160 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.291 - Using existing connection for thread 2972
2025-06-04 18:40:05.437 - Database connection closed for thread 2972
2025-06-04 18:40:05.448 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.448 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.450 - Database connection closed for thread 2972
2025-06-04 18:40:05.451 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.451 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.453 - Database connection closed for thread 2972
2025-06-04 18:40:05.454 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.455 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.461 - Database connection closed for thread 2972
2025-06-04 18:40:05.463 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.464 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.468 - Database connection closed for thread 2972
2025-06-04 18:40:05.468 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.471 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.480 - Database connection closed for thread 2972
2025-06-04 18:40:05.484 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.485 - New database connection created successfully for thread 2972
2025-06-04 18:40:05.487 - Database connection closed for thread 2972
2025-06-04 18:40:05.488 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:05.488 - New database connection created successfully for thread 2972
2025-06-04 18:40:55.074 - Using existing connection for thread 7364
2025-06-04 18:40:55.131 - Using existing connection for thread 2972
2025-06-04 18:40:55.232 - Database connection closed for thread 2972
2025-06-04 18:40:55.234 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:55.238 - New database connection created successfully for thread 2972
2025-06-04 18:40:55.250 - Database connection closed for thread 2972
2025-06-04 18:40:55.253 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:40:55.255 - New database connection created successfully for thread 2972
2025-06-04 18:43:11.743 - Creating new thread-specific database connection to data\stats.db for thread 9732
2025-06-04 18:43:11.743 - New database connection created successfully for thread 9732
2025-06-04 18:43:11.745 - Database connection closed for thread 9732
2025-06-04 18:43:11.746 - Creating new thread-specific database connection to data\stats.db for thread 9732
2025-06-04 18:43:11.747 - New database connection created successfully for thread 9732
2025-06-04 18:43:11.824 - Using existing connection for thread 2972
2025-06-04 18:43:11.917 - Database connection closed for thread 2972
2025-06-04 18:43:11.942 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:11.943 - New database connection created successfully for thread 2972
2025-06-04 18:43:11.944 - Database connection closed for thread 2972
2025-06-04 18:43:11.946 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:11.947 - New database connection created successfully for thread 2972
2025-06-04 18:43:11.952 - Database connection closed for thread 2972
2025-06-04 18:43:11.953 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:11.955 - New database connection created successfully for thread 2972
2025-06-04 18:43:11.965 - Database connection closed for thread 2972
2025-06-04 18:43:11.967 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:11.969 - New database connection created successfully for thread 2972
2025-06-04 18:43:11.975 - Database connection closed for thread 2972
2025-06-04 18:43:11.982 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:11.988 - New database connection created successfully for thread 2972
2025-06-04 18:43:12.018 - Database connection closed for thread 2972
2025-06-04 18:43:12.024 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:12.027 - New database connection created successfully for thread 2972
2025-06-04 18:43:12.032 - Database connection closed for thread 2972
2025-06-04 18:43:12.033 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:43:12.034 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.900 - Recreating expired connection for thread 2972
2025-06-04 18:48:14.923 - Database connection closed for thread 2972
2025-06-04 18:48:14.935 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.935 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.936 - Database connection closed for thread 2972
2025-06-04 18:48:14.936 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.937 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.939 - Database connection closed for thread 2972
2025-06-04 18:48:14.940 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.941 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.947 - Database connection closed for thread 2972
2025-06-04 18:48:14.948 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.949 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.952 - Database connection closed for thread 2972
2025-06-04 18:48:14.955 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.957 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.976 - Database connection closed for thread 2972
2025-06-04 18:48:14.978 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.981 - New database connection created successfully for thread 2972
2025-06-04 18:48:14.983 - Database connection closed for thread 2972
2025-06-04 18:48:14.984 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:48:14.985 - New database connection created successfully for thread 2972
2025-06-04 18:49:05.444 - Recreating expired connection for thread 7364
2025-06-04 18:49:05.456 - Using existing connection for thread 2972
2025-06-04 18:49:05.540 - Database connection closed for thread 2972
2025-06-04 18:49:05.541 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:49:05.542 - New database connection created successfully for thread 2972
2025-06-04 18:49:05.554 - Database connection closed for thread 2972
2025-06-04 18:49:05.556 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:49:05.558 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.014 - Using existing connection for thread 2972
2025-06-04 18:53:41.121 - Database connection closed for thread 2972
2025-06-04 18:53:41.132 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.133 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.133 - Database connection closed for thread 2972
2025-06-04 18:53:41.134 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.135 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.137 - Database connection closed for thread 2972
2025-06-04 18:53:41.137 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.139 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.145 - Database connection closed for thread 2972
2025-06-04 18:53:41.146 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.147 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.152 - Database connection closed for thread 2972
2025-06-04 18:53:41.153 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.153 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.164 - Database connection closed for thread 2972
2025-06-04 18:53:41.167 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.169 - New database connection created successfully for thread 2972
2025-06-04 18:53:41.172 - Database connection closed for thread 2972
2025-06-04 18:53:41.173 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:53:41.175 - New database connection created successfully for thread 2972
2025-06-04 18:54:50.415 - Recreating expired connection for thread 7364
2025-06-04 18:54:50.475 - Using existing connection for thread 2972
2025-06-04 18:54:50.556 - Database connection closed for thread 2972
2025-06-04 18:54:50.557 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:54:50.558 - New database connection created successfully for thread 2972
2025-06-04 18:54:50.569 - Database connection closed for thread 2972
2025-06-04 18:54:50.570 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:54:50.572 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.490 - Using existing connection for thread 2972
2025-06-04 18:57:38.545 - Database connection closed for thread 2972
2025-06-04 18:57:38.559 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.561 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.562 - Database connection closed for thread 2972
2025-06-04 18:57:38.562 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.563 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.565 - Database connection closed for thread 2972
2025-06-04 18:57:38.566 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.568 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.577 - Database connection closed for thread 2972
2025-06-04 18:57:38.578 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.579 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.583 - Database connection closed for thread 2972
2025-06-04 18:57:38.583 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.584 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.593 - Database connection closed for thread 2972
2025-06-04 18:57:38.595 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.596 - New database connection created successfully for thread 2972
2025-06-04 18:57:38.599 - Database connection closed for thread 2972
2025-06-04 18:57:38.599 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 18:57:38.601 - New database connection created successfully for thread 2972
2025-06-04 19:00:48.132 - Recreating expired connection for thread 7364
2025-06-04 19:00:48.206 - Using existing connection for thread 2972
2025-06-04 19:00:48.284 - Database connection closed for thread 2972
2025-06-04 19:00:48.284 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:00:48.285 - New database connection created successfully for thread 2972
2025-06-04 19:00:48.294 - Database connection closed for thread 2972
2025-06-04 19:00:48.299 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:00:48.304 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.189 - Using existing connection for thread 2972
2025-06-04 19:05:05.256 - Database connection closed for thread 2972
2025-06-04 19:05:05.271 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.272 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.272 - Database connection closed for thread 2972
2025-06-04 19:05:05.273 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.273 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.275 - Database connection closed for thread 2972
2025-06-04 19:05:05.276 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.276 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.281 - Database connection closed for thread 2972
2025-06-04 19:05:05.284 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.286 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.289 - Database connection closed for thread 2972
2025-06-04 19:05:05.291 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.292 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.303 - Database connection closed for thread 2972
2025-06-04 19:05:05.307 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.309 - New database connection created successfully for thread 2972
2025-06-04 19:05:05.311 - Database connection closed for thread 2972
2025-06-04 19:05:05.311 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:05.312 - New database connection created successfully for thread 2972
2025-06-04 19:05:31.596 - Using existing connection for thread 7364
2025-06-04 19:05:31.599 - Using existing connection for thread 2972
2025-06-04 19:05:31.690 - Database connection closed for thread 2972
2025-06-04 19:05:31.692 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:31.693 - New database connection created successfully for thread 2972
2025-06-04 19:05:31.702 - Database connection closed for thread 2972
2025-06-04 19:05:31.704 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:05:31.706 - New database connection created successfully for thread 2972
2025-06-04 19:08:56.982 - Using existing connection for thread 2972
2025-06-04 19:08:57.057 - Database connection closed for thread 2972
2025-06-04 19:08:57.069 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.070 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.071 - Database connection closed for thread 2972
2025-06-04 19:08:57.072 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.072 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.074 - Database connection closed for thread 2972
2025-06-04 19:08:57.075 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.076 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.081 - Database connection closed for thread 2972
2025-06-04 19:08:57.082 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.083 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.090 - Database connection closed for thread 2972
2025-06-04 19:08:57.091 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.092 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.106 - Database connection closed for thread 2972
2025-06-04 19:08:57.107 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.111 - New database connection created successfully for thread 2972
2025-06-04 19:08:57.114 - Database connection closed for thread 2972
2025-06-04 19:08:57.114 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:08:57.115 - New database connection created successfully for thread 2972
2025-06-04 19:09:44.606 - Using existing connection for thread 7364
2025-06-04 19:09:44.680 - Using existing connection for thread 2972
2025-06-04 19:09:44.789 - Database connection closed for thread 2972
2025-06-04 19:09:44.790 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:09:44.791 - New database connection created successfully for thread 2972
2025-06-04 19:09:44.801 - Database connection closed for thread 2972
2025-06-04 19:09:44.802 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:09:44.805 - New database connection created successfully for thread 2972
2025-06-04 19:15:08.905 - Recreating expired connection for thread 2972
2025-06-04 19:15:08.974 - Database connection closed for thread 2972
2025-06-04 19:15:08.986 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:08.987 - New database connection created successfully for thread 2972
2025-06-04 19:15:08.987 - Database connection closed for thread 2972
2025-06-04 19:15:08.988 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:08.989 - New database connection created successfully for thread 2972
2025-06-04 19:15:08.992 - Database connection closed for thread 2972
2025-06-04 19:15:08.993 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:08.994 - New database connection created successfully for thread 2972
2025-06-04 19:15:08.999 - Database connection closed for thread 2972
2025-06-04 19:15:09.001 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:09.002 - New database connection created successfully for thread 2972
2025-06-04 19:15:09.005 - Database connection closed for thread 2972
2025-06-04 19:15:09.007 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:09.008 - New database connection created successfully for thread 2972
2025-06-04 19:15:09.017 - Database connection closed for thread 2972
2025-06-04 19:15:09.023 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:09.024 - New database connection created successfully for thread 2972
2025-06-04 19:15:09.026 - Database connection closed for thread 2972
2025-06-04 19:15:09.027 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:09.027 - New database connection created successfully for thread 2972
2025-06-04 19:15:47.376 - Recreating expired connection for thread 7364
2025-06-04 19:15:47.445 - Using existing connection for thread 2972
2025-06-04 19:15:47.512 - Database connection closed for thread 2972
2025-06-04 19:15:47.513 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:47.514 - New database connection created successfully for thread 2972
2025-06-04 19:15:47.527 - Database connection closed for thread 2972
2025-06-04 19:15:47.527 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:15:47.529 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.613 - Creating new thread-specific database connection to data\stats.db for thread 9056
2025-06-04 19:20:10.614 - New database connection created successfully for thread 9056
2025-06-04 19:20:10.617 - Database connection closed for thread 9056
2025-06-04 19:20:10.617 - Creating new thread-specific database connection to data\stats.db for thread 9056
2025-06-04 19:20:10.618 - New database connection created successfully for thread 9056
2025-06-04 19:20:10.692 - Using existing connection for thread 2972
2025-06-04 19:20:10.771 - Database connection closed for thread 2972
2025-06-04 19:20:10.785 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.785 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.786 - Database connection closed for thread 2972
2025-06-04 19:20:10.787 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.787 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.789 - Database connection closed for thread 2972
2025-06-04 19:20:10.790 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.797 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.808 - Database connection closed for thread 2972
2025-06-04 19:20:10.810 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.812 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.818 - Database connection closed for thread 2972
2025-06-04 19:20:10.820 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.822 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.841 - Database connection closed for thread 2972
2025-06-04 19:20:10.844 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.847 - New database connection created successfully for thread 2972
2025-06-04 19:20:10.857 - Database connection closed for thread 2972
2025-06-04 19:20:10.861 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:20:10.864 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.394 - Recreating expired connection for thread 2972
2025-06-04 19:28:17.542 - Database connection closed for thread 2972
2025-06-04 19:28:17.552 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.552 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.553 - Database connection closed for thread 2972
2025-06-04 19:28:17.553 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.554 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.557 - Database connection closed for thread 2972
2025-06-04 19:28:17.558 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.559 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.567 - Database connection closed for thread 2972
2025-06-04 19:28:17.568 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.570 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.577 - Database connection closed for thread 2972
2025-06-04 19:28:17.577 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.578 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.592 - Database connection closed for thread 2972
2025-06-04 19:28:17.594 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.594 - New database connection created successfully for thread 2972
2025-06-04 19:28:17.598 - Database connection closed for thread 2972
2025-06-04 19:28:17.598 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:17.599 - New database connection created successfully for thread 2972
2025-06-04 19:28:37.275 - Recreating expired connection for thread 7364
2025-06-04 19:28:37.276 - Database connection closed for thread 2972
2025-06-04 19:28:37.277 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:37.277 - New database connection created successfully for thread 2972
2025-06-04 19:28:37.280 - Database connection closed for thread 2972
2025-06-04 19:28:37.290 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:28:37.298 - New database connection created successfully for thread 2972
2025-06-04 19:42:52.580 - Recreating expired connection for thread 7364
2025-06-04 19:42:52.661 - Recreating expired connection for thread 2972
2025-06-04 19:42:52.742 - Database connection closed for thread 2972
2025-06-04 19:42:52.743 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:42:52.743 - New database connection created successfully for thread 2972
2025-06-04 19:42:52.758 - Database connection closed for thread 2972
2025-06-04 19:42:52.759 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:42:52.763 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.397 - Using existing connection for thread 2972
2025-06-04 19:45:48.418 - Database connection closed for thread 2972
2025-06-04 19:45:48.431 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.433 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.433 - Database connection closed for thread 2972
2025-06-04 19:45:48.433 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.434 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.439 - Database connection closed for thread 2972
2025-06-04 19:45:48.441 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.443 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.449 - Database connection closed for thread 2972
2025-06-04 19:45:48.450 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.451 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.455 - Database connection closed for thread 2972
2025-06-04 19:45:48.456 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.457 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.467 - Database connection closed for thread 2972
2025-06-04 19:45:48.469 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.472 - New database connection created successfully for thread 2972
2025-06-04 19:45:48.474 - Database connection closed for thread 2972
2025-06-04 19:45:48.475 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:45:48.476 - New database connection created successfully for thread 2972
2025-06-04 19:48:28.432 - Recreating expired connection for thread 7364
2025-06-04 19:48:28.450 - Using existing connection for thread 2972
2025-06-04 19:48:28.498 - Database connection closed for thread 2972
2025-06-04 19:48:28.502 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:48:28.505 - New database connection created successfully for thread 2972
2025-06-04 19:48:28.511 - Database connection closed for thread 2972
2025-06-04 19:48:28.516 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:48:28.519 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.199 - Using existing connection for thread 2972
2025-06-04 19:51:04.267 - Database connection closed for thread 2972
2025-06-04 19:51:04.279 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.280 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.281 - Database connection closed for thread 2972
2025-06-04 19:51:04.282 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.283 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.286 - Database connection closed for thread 2972
2025-06-04 19:51:04.287 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.287 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.293 - Database connection closed for thread 2972
2025-06-04 19:51:04.294 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.295 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.300 - Database connection closed for thread 2972
2025-06-04 19:51:04.301 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.302 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.311 - Database connection closed for thread 2972
2025-06-04 19:51:04.313 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.315 - New database connection created successfully for thread 2972
2025-06-04 19:51:04.319 - Database connection closed for thread 2972
2025-06-04 19:51:04.319 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:51:04.321 - New database connection created successfully for thread 2972
2025-06-04 19:52:43.634 - Using existing connection for thread 7364
2025-06-04 19:52:43.681 - Using existing connection for thread 2972
2025-06-04 19:52:43.758 - Database connection closed for thread 2972
2025-06-04 19:52:43.759 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:52:43.759 - New database connection created successfully for thread 2972
2025-06-04 19:52:43.768 - Database connection closed for thread 2972
2025-06-04 19:52:43.769 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:52:43.771 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.141 - Using existing connection for thread 2972
2025-06-04 19:56:06.180 - Database connection closed for thread 2972
2025-06-04 19:56:06.190 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.191 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.192 - Database connection closed for thread 2972
2025-06-04 19:56:06.193 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.193 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.196 - Database connection closed for thread 2972
2025-06-04 19:56:06.196 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.197 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.203 - Database connection closed for thread 2972
2025-06-04 19:56:06.204 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.205 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.208 - Database connection closed for thread 2972
2025-06-04 19:56:06.210 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.211 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.220 - Database connection closed for thread 2972
2025-06-04 19:56:06.224 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.225 - New database connection created successfully for thread 2972
2025-06-04 19:56:06.228 - Database connection closed for thread 2972
2025-06-04 19:56:06.229 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:06.230 - New database connection created successfully for thread 2972
2025-06-04 19:56:28.601 - Using existing connection for thread 7364
2025-06-04 19:56:28.602 - Using existing connection for thread 2972
2025-06-04 19:56:28.653 - Database connection closed for thread 2972
2025-06-04 19:56:28.663 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:28.666 - New database connection created successfully for thread 2972
2025-06-04 19:56:28.670 - Database connection closed for thread 2972
2025-06-04 19:56:28.673 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 19:56:28.675 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.587 - Using existing connection for thread 2972
2025-06-04 20:00:34.665 - Database connection closed for thread 2972
2025-06-04 20:00:34.676 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.677 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.678 - Database connection closed for thread 2972
2025-06-04 20:00:34.679 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.679 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.681 - Database connection closed for thread 2972
2025-06-04 20:00:34.681 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.683 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.689 - Database connection closed for thread 2972
2025-06-04 20:00:34.690 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.691 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.694 - Database connection closed for thread 2972
2025-06-04 20:00:34.696 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.699 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.715 - Database connection closed for thread 2972
2025-06-04 20:00:34.717 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.718 - New database connection created successfully for thread 2972
2025-06-04 20:00:34.721 - Database connection closed for thread 2972
2025-06-04 20:00:34.721 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:00:34.722 - New database connection created successfully for thread 2972
2025-06-04 20:02:47.628 - Recreating expired connection for thread 7364
2025-06-04 20:02:47.705 - Using existing connection for thread 2972
2025-06-04 20:02:47.784 - Database connection closed for thread 2972
2025-06-04 20:02:47.785 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:02:47.786 - New database connection created successfully for thread 2972
2025-06-04 20:02:47.795 - Database connection closed for thread 2972
2025-06-04 20:02:47.796 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:02:47.798 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.559 - Using existing connection for thread 2972
2025-06-04 20:06:17.638 - Database connection closed for thread 2972
2025-06-04 20:06:17.648 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.649 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.649 - Database connection closed for thread 2972
2025-06-04 20:06:17.650 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.651 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.653 - Database connection closed for thread 2972
2025-06-04 20:06:17.653 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.655 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.661 - Database connection closed for thread 2972
2025-06-04 20:06:17.662 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.663 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.669 - Database connection closed for thread 2972
2025-06-04 20:06:17.670 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.673 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.682 - Database connection closed for thread 2972
2025-06-04 20:06:17.685 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.687 - New database connection created successfully for thread 2972
2025-06-04 20:06:17.689 - Database connection closed for thread 2972
2025-06-04 20:06:17.690 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:06:17.691 - New database connection created successfully for thread 2972
2025-06-04 20:08:22.096 - Recreating expired connection for thread 7364
2025-06-04 20:08:22.150 - Using existing connection for thread 2972
2025-06-04 20:08:22.207 - Database connection closed for thread 2972
2025-06-04 20:08:22.208 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:08:22.209 - New database connection created successfully for thread 2972
2025-06-04 20:08:22.221 - Database connection closed for thread 2972
2025-06-04 20:08:22.224 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:08:22.225 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.598 - Using existing connection for thread 2972
2025-06-04 20:11:21.710 - Database connection closed for thread 2972
2025-06-04 20:11:21.721 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.722 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.722 - Database connection closed for thread 2972
2025-06-04 20:11:21.723 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.724 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.726 - Database connection closed for thread 2972
2025-06-04 20:11:21.726 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.727 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.733 - Database connection closed for thread 2972
2025-06-04 20:11:21.735 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.737 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.740 - Database connection closed for thread 2972
2025-06-04 20:11:21.742 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.743 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.755 - Database connection closed for thread 2972
2025-06-04 20:11:21.758 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.762 - New database connection created successfully for thread 2972
2025-06-04 20:11:21.764 - Database connection closed for thread 2972
2025-06-04 20:11:21.764 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:11:21.766 - New database connection created successfully for thread 2972
2025-06-04 20:12:20.756 - Using existing connection for thread 7364
2025-06-04 20:12:20.815 - Using existing connection for thread 2972
2025-06-04 20:12:20.904 - Database connection closed for thread 2972
2025-06-04 20:12:20.905 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:12:20.906 - New database connection created successfully for thread 2972
2025-06-04 20:12:20.918 - Database connection closed for thread 2972
2025-06-04 20:12:20.919 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:12:20.921 - New database connection created successfully for thread 2972
2025-06-04 20:15:44.798 - Using existing connection for thread 2972
2025-06-04 20:15:44.978 - Database connection closed for thread 2972
2025-06-04 20:15:44.990 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:44.991 - New database connection created successfully for thread 2972
2025-06-04 20:15:44.992 - Database connection closed for thread 2972
2025-06-04 20:15:44.992 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:44.993 - New database connection created successfully for thread 2972
2025-06-04 20:15:44.996 - Database connection closed for thread 2972
2025-06-04 20:15:45.004 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:45.005 - New database connection created successfully for thread 2972
2025-06-04 20:15:45.021 - Database connection closed for thread 2972
2025-06-04 20:15:45.024 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:45.033 - New database connection created successfully for thread 2972
2025-06-04 20:15:45.045 - Database connection closed for thread 2972
2025-06-04 20:15:45.050 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:45.074 - New database connection created successfully for thread 2972
2025-06-04 20:15:45.131 - Database connection closed for thread 2972
2025-06-04 20:15:45.135 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:45.161 - New database connection created successfully for thread 2972
2025-06-04 20:15:45.177 - Database connection closed for thread 2972
2025-06-04 20:15:45.186 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:15:45.190 - New database connection created successfully for thread 2972
2025-06-04 20:16:39.244 - Using existing connection for thread 7364
2025-06-04 20:16:39.324 - Using existing connection for thread 2972
2025-06-04 20:16:39.419 - Database connection closed for thread 2972
2025-06-04 20:16:39.420 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:16:39.420 - New database connection created successfully for thread 2972
2025-06-04 20:16:39.431 - Database connection closed for thread 2972
2025-06-04 20:16:39.438 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:16:39.442 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.129 - Using existing connection for thread 2972
2025-06-04 20:20:52.376 - Database connection closed for thread 2972
2025-06-04 20:20:52.386 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.387 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.387 - Database connection closed for thread 2972
2025-06-04 20:20:52.388 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.388 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.391 - Database connection closed for thread 2972
2025-06-04 20:20:52.391 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.392 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.398 - Database connection closed for thread 2972
2025-06-04 20:20:52.400 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.401 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.406 - Database connection closed for thread 2972
2025-06-04 20:20:52.406 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.407 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.417 - Database connection closed for thread 2972
2025-06-04 20:20:52.419 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.420 - New database connection created successfully for thread 2972
2025-06-04 20:20:52.422 - Database connection closed for thread 2972
2025-06-04 20:20:52.423 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:20:52.424 - New database connection created successfully for thread 2972
2025-06-04 20:21:09.990 - Using existing connection for thread 7364
2025-06-04 20:21:10.088 - Using existing connection for thread 2972
2025-06-04 20:21:10.183 - Database connection closed for thread 2972
2025-06-04 20:21:10.183 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:21:10.184 - New database connection created successfully for thread 2972
2025-06-04 20:21:10.196 - Database connection closed for thread 2972
2025-06-04 20:21:10.199 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:21:10.201 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.714 - Using existing connection for thread 2972
2025-06-04 20:25:14.749 - Database connection closed for thread 2972
2025-06-04 20:25:14.760 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.761 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.762 - Database connection closed for thread 2972
2025-06-04 20:25:14.762 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.763 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.765 - Database connection closed for thread 2972
2025-06-04 20:25:14.765 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.766 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.772 - Database connection closed for thread 2972
2025-06-04 20:25:14.774 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.774 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.779 - Database connection closed for thread 2972
2025-06-04 20:25:14.780 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.781 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.789 - Database connection closed for thread 2972
2025-06-04 20:25:14.790 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.792 - New database connection created successfully for thread 2972
2025-06-04 20:25:14.795 - Database connection closed for thread 2972
2025-06-04 20:25:14.795 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:14.796 - New database connection created successfully for thread 2972
2025-06-04 20:25:34.023 - Using existing connection for thread 7364
2025-06-04 20:25:34.043 - Using existing connection for thread 2972
2025-06-04 20:25:34.134 - Database connection closed for thread 2972
2025-06-04 20:25:34.134 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:34.136 - New database connection created successfully for thread 2972
2025-06-04 20:25:34.147 - Database connection closed for thread 2972
2025-06-04 20:25:34.149 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:25:34.153 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.132 - Using existing connection for thread 2972
2025-06-04 20:28:17.164 - Database connection closed for thread 2972
2025-06-04 20:28:17.178 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.178 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.179 - Database connection closed for thread 2972
2025-06-04 20:28:17.180 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.181 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.183 - Database connection closed for thread 2972
2025-06-04 20:28:17.184 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.184 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.192 - Database connection closed for thread 2972
2025-06-04 20:28:17.195 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.197 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.199 - Database connection closed for thread 2972
2025-06-04 20:28:17.202 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.204 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.214 - Database connection closed for thread 2972
2025-06-04 20:28:17.217 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.218 - New database connection created successfully for thread 2972
2025-06-04 20:28:17.223 - Database connection closed for thread 2972
2025-06-04 20:28:17.224 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:17.225 - New database connection created successfully for thread 2972
2025-06-04 20:28:44.406 - Using existing connection for thread 7364
2025-06-04 20:28:44.471 - Using existing connection for thread 2972
2025-06-04 20:28:44.560 - Database connection closed for thread 2972
2025-06-04 20:28:44.561 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:44.562 - New database connection created successfully for thread 2972
2025-06-04 20:28:44.571 - Database connection closed for thread 2972
2025-06-04 20:28:44.572 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:28:44.574 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.893 - Using existing connection for thread 2972
2025-06-04 20:32:37.931 - Database connection closed for thread 2972
2025-06-04 20:32:37.942 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.943 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.943 - Database connection closed for thread 2972
2025-06-04 20:32:37.944 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.945 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.947 - Database connection closed for thread 2972
2025-06-04 20:32:37.948 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.949 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.954 - Database connection closed for thread 2972
2025-06-04 20:32:37.955 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.956 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.959 - Database connection closed for thread 2972
2025-06-04 20:32:37.961 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.963 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.974 - Database connection closed for thread 2972
2025-06-04 20:32:37.976 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.981 - New database connection created successfully for thread 2972
2025-06-04 20:32:37.984 - Database connection closed for thread 2972
2025-06-04 20:32:37.984 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:32:37.988 - New database connection created successfully for thread 2972
2025-06-04 20:33:16.939 - Using existing connection for thread 7364
2025-06-04 20:33:16.950 - Using existing connection for thread 2972
2025-06-04 20:33:17.044 - Database connection closed for thread 2972
2025-06-04 20:33:17.046 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:33:17.047 - New database connection created successfully for thread 2972
2025-06-04 20:33:17.058 - Database connection closed for thread 2972
2025-06-04 20:33:17.060 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:33:17.061 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.697 - Using existing connection for thread 2972
2025-06-04 20:36:30.729 - Database connection closed for thread 2972
2025-06-04 20:36:30.740 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.740 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.741 - Database connection closed for thread 2972
2025-06-04 20:36:30.741 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.742 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.744 - Database connection closed for thread 2972
2025-06-04 20:36:30.745 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.747 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.752 - Database connection closed for thread 2972
2025-06-04 20:36:30.752 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.754 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.758 - Database connection closed for thread 2972
2025-06-04 20:36:30.759 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.762 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.774 - Database connection closed for thread 2972
2025-06-04 20:36:30.776 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.777 - New database connection created successfully for thread 2972
2025-06-04 20:36:30.780 - Database connection closed for thread 2972
2025-06-04 20:36:30.781 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:30.782 - New database connection created successfully for thread 2972
2025-06-04 20:36:53.420 - Using existing connection for thread 7364
2025-06-04 20:36:53.457 - Using existing connection for thread 2972
2025-06-04 20:36:53.553 - Database connection closed for thread 2972
2025-06-04 20:36:53.555 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:53.556 - New database connection created successfully for thread 2972
2025-06-04 20:36:53.566 - Database connection closed for thread 2972
2025-06-04 20:36:53.567 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:36:53.570 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.380 - Using existing connection for thread 2972
2025-06-04 20:40:09.425 - Database connection closed for thread 2972
2025-06-04 20:40:09.436 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.437 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.437 - Database connection closed for thread 2972
2025-06-04 20:40:09.437 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.438 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.440 - Database connection closed for thread 2972
2025-06-04 20:40:09.441 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.442 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.448 - Database connection closed for thread 2972
2025-06-04 20:40:09.449 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.450 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.453 - Database connection closed for thread 2972
2025-06-04 20:40:09.455 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.457 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.472 - Database connection closed for thread 2972
2025-06-04 20:40:09.472 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.473 - New database connection created successfully for thread 2972
2025-06-04 20:40:09.476 - Database connection closed for thread 2972
2025-06-04 20:40:09.476 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:09.477 - New database connection created successfully for thread 2972
2025-06-04 20:40:52.994 - Using existing connection for thread 7364
2025-06-04 20:40:53.044 - Using existing connection for thread 2972
2025-06-04 20:40:53.129 - Database connection closed for thread 2972
2025-06-04 20:40:53.131 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:53.132 - New database connection created successfully for thread 2972
2025-06-04 20:40:53.145 - Database connection closed for thread 2972
2025-06-04 20:40:53.146 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:40:53.148 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.795 - Using existing connection for thread 2972
2025-06-04 20:44:08.846 - Database connection closed for thread 2972
2025-06-04 20:44:08.857 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.857 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.858 - Database connection closed for thread 2972
2025-06-04 20:44:08.859 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.860 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.862 - Database connection closed for thread 2972
2025-06-04 20:44:08.863 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.865 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.871 - Database connection closed for thread 2972
2025-06-04 20:44:08.873 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.873 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.878 - Database connection closed for thread 2972
2025-06-04 20:44:08.881 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.883 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.893 - Database connection closed for thread 2972
2025-06-04 20:44:08.897 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.898 - New database connection created successfully for thread 2972
2025-06-04 20:44:08.901 - Database connection closed for thread 2972
2025-06-04 20:44:08.901 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:44:08.903 - New database connection created successfully for thread 2972
2025-06-04 20:45:14.303 - Using existing connection for thread 7364
2025-06-04 20:45:14.350 - Using existing connection for thread 2972
2025-06-04 20:45:14.453 - Database connection closed for thread 2972
2025-06-04 20:45:14.454 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:45:14.455 - New database connection created successfully for thread 2972
2025-06-04 20:45:14.466 - Database connection closed for thread 2972
2025-06-04 20:45:14.467 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:45:14.469 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.042 - Using existing connection for thread 2972
2025-06-04 20:48:55.078 - Database connection closed for thread 2972
2025-06-04 20:48:55.088 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.088 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.089 - Database connection closed for thread 2972
2025-06-04 20:48:55.090 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.090 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.093 - Database connection closed for thread 2972
2025-06-04 20:48:55.093 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.095 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.101 - Database connection closed for thread 2972
2025-06-04 20:48:55.102 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.111 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.118 - Database connection closed for thread 2972
2025-06-04 20:48:55.118 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.119 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.130 - Database connection closed for thread 2972
2025-06-04 20:48:55.132 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.135 - New database connection created successfully for thread 2972
2025-06-04 20:48:55.137 - Database connection closed for thread 2972
2025-06-04 20:48:55.138 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:48:55.139 - New database connection created successfully for thread 2972
2025-06-04 20:50:00.765 - Using existing connection for thread 7364
2025-06-04 20:50:00.783 - Using existing connection for thread 2972
2025-06-04 20:50:00.875 - Database connection closed for thread 2972
2025-06-04 20:50:00.876 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:50:00.877 - New database connection created successfully for thread 2972
2025-06-04 20:50:00.892 - Database connection closed for thread 2972
2025-06-04 20:50:00.895 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:50:00.898 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.904 - Using existing connection for thread 2972
2025-06-04 20:52:59.947 - Database connection closed for thread 2972
2025-06-04 20:52:59.960 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.961 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.962 - Database connection closed for thread 2972
2025-06-04 20:52:59.962 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.963 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.965 - Database connection closed for thread 2972
2025-06-04 20:52:59.966 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.967 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.972 - Database connection closed for thread 2972
2025-06-04 20:52:59.973 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.975 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.979 - Database connection closed for thread 2972
2025-06-04 20:52:59.980 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.981 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.993 - Database connection closed for thread 2972
2025-06-04 20:52:59.994 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:52:59.996 - New database connection created successfully for thread 2972
2025-06-04 20:52:59.998 - Database connection closed for thread 2972
2025-06-04 20:52:59.999 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:53:00.000 - New database connection created successfully for thread 2972
2025-06-04 20:54:57.434 - Using existing connection for thread 7364
2025-06-04 20:54:57.485 - Using existing connection for thread 2972
2025-06-04 20:54:57.579 - Database connection closed for thread 2972
2025-06-04 20:54:57.580 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:54:57.581 - New database connection created successfully for thread 2972
2025-06-04 20:54:57.590 - Database connection closed for thread 2972
2025-06-04 20:54:57.591 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:54:57.594 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.211 - Using existing connection for thread 2972
2025-06-04 20:57:31.245 - Database connection closed for thread 2972
2025-06-04 20:57:31.254 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.257 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.257 - Database connection closed for thread 2972
2025-06-04 20:57:31.258 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.259 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.261 - Database connection closed for thread 2972
2025-06-04 20:57:31.261 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.263 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.268 - Database connection closed for thread 2972
2025-06-04 20:57:31.269 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.270 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.275 - Database connection closed for thread 2972
2025-06-04 20:57:31.276 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.285 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.298 - Database connection closed for thread 2972
2025-06-04 20:57:31.299 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.305 - New database connection created successfully for thread 2972
2025-06-04 20:57:31.309 - Database connection closed for thread 2972
2025-06-04 20:57:31.310 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:57:31.312 - New database connection created successfully for thread 2972
2025-06-04 20:58:14.652 - Using existing connection for thread 7364
2025-06-04 20:58:14.703 - Using existing connection for thread 2972
2025-06-04 20:58:14.794 - Database connection closed for thread 2972
2025-06-04 20:58:14.796 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:58:14.798 - New database connection created successfully for thread 2972
2025-06-04 20:58:14.808 - Database connection closed for thread 2972
2025-06-04 20:58:14.809 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 20:58:14.811 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.109 - Using existing connection for thread 2972
2025-06-04 21:02:53.146 - Database connection closed for thread 2972
2025-06-04 21:02:53.157 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.158 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.158 - Database connection closed for thread 2972
2025-06-04 21:02:53.159 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.160 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.162 - Database connection closed for thread 2972
2025-06-04 21:02:53.164 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.171 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.179 - Database connection closed for thread 2972
2025-06-04 21:02:53.181 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.182 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.187 - Database connection closed for thread 2972
2025-06-04 21:02:53.188 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.191 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.202 - Database connection closed for thread 2972
2025-06-04 21:02:53.205 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.208 - New database connection created successfully for thread 2972
2025-06-04 21:02:53.210 - Database connection closed for thread 2972
2025-06-04 21:02:53.211 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:02:53.212 - New database connection created successfully for thread 2972
2025-06-04 21:04:13.163 - Recreating expired connection for thread 7364
2025-06-04 21:04:13.212 - Using existing connection for thread 2972
2025-06-04 21:04:13.310 - Database connection closed for thread 2972
2025-06-04 21:04:13.312 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:04:13.313 - New database connection created successfully for thread 2972
2025-06-04 21:04:13.324 - Database connection closed for thread 2972
2025-06-04 21:04:13.325 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:04:13.327 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.012 - Using existing connection for thread 2972
2025-06-04 21:07:12.063 - Database connection closed for thread 2972
2025-06-04 21:07:12.073 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.074 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.074 - Database connection closed for thread 2972
2025-06-04 21:07:12.075 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.076 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.079 - Database connection closed for thread 2972
2025-06-04 21:07:12.079 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.080 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.086 - Database connection closed for thread 2972
2025-06-04 21:07:12.087 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.088 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.090 - Database connection closed for thread 2972
2025-06-04 21:07:12.092 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.095 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.104 - Database connection closed for thread 2972
2025-06-04 21:07:12.105 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.107 - New database connection created successfully for thread 2972
2025-06-04 21:07:12.112 - Database connection closed for thread 2972
2025-06-04 21:07:12.114 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:07:12.116 - New database connection created successfully for thread 2972
2025-06-04 21:08:23.480 - Using existing connection for thread 7364
2025-06-04 21:08:23.534 - Using existing connection for thread 2972
2025-06-04 21:08:23.628 - Database connection closed for thread 2972
2025-06-04 21:08:23.629 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:08:23.631 - New database connection created successfully for thread 2972
2025-06-04 21:08:23.641 - Database connection closed for thread 2972
2025-06-04 21:08:23.642 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:08:23.644 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.841 - Using existing connection for thread 2972
2025-06-04 21:11:19.906 - Database connection closed for thread 2972
2025-06-04 21:11:19.917 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.917 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.917 - Database connection closed for thread 2972
2025-06-04 21:11:19.918 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.919 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.921 - Database connection closed for thread 2972
2025-06-04 21:11:19.921 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.922 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.928 - Database connection closed for thread 2972
2025-06-04 21:11:19.929 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.930 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.935 - Database connection closed for thread 2972
2025-06-04 21:11:19.936 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.937 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.944 - Database connection closed for thread 2972
2025-06-04 21:11:19.946 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.947 - New database connection created successfully for thread 2972
2025-06-04 21:11:19.951 - Database connection closed for thread 2972
2025-06-04 21:11:19.952 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:19.953 - New database connection created successfully for thread 2972
2025-06-04 21:11:56.720 - Using existing connection for thread 7364
2025-06-04 21:11:56.722 - Using existing connection for thread 2972
2025-06-04 21:11:56.806 - Database connection closed for thread 2972
2025-06-04 21:11:56.807 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:56.808 - New database connection created successfully for thread 2972
2025-06-04 21:11:56.820 - Database connection closed for thread 2972
2025-06-04 21:11:56.822 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:11:56.826 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.328 - Using existing connection for thread 2972
2025-06-04 21:16:03.454 - Database connection closed for thread 2972
2025-06-04 21:16:03.464 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.464 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.466 - Database connection closed for thread 2972
2025-06-04 21:16:03.467 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.467 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.469 - Database connection closed for thread 2972
2025-06-04 21:16:03.470 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.471 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.477 - Database connection closed for thread 2972
2025-06-04 21:16:03.478 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.479 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.482 - Database connection closed for thread 2972
2025-06-04 21:16:03.483 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.486 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.496 - Database connection closed for thread 2972
2025-06-04 21:16:03.499 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.501 - New database connection created successfully for thread 2972
2025-06-04 21:16:03.504 - Database connection closed for thread 2972
2025-06-04 21:16:03.505 - Creating new thread-specific database connection to data\stats.db for thread 2972
2025-06-04 21:16:03.506 - New database connection created successfully for thread 2972
2025-06-04 22:38:34.480 - ensure_database_exists called from thread 8448
2025-06-04 22:38:34.492 - Creating new thread-specific database connection to data\stats.db for thread 8448
2025-06-04 22:38:34.577 - New database connection created successfully for thread 8448
2025-06-04 22:38:34.583 - Stats database initialized successfully
2025-06-04 22:38:34.584 - ensure_database_exists called from thread 8448
2025-06-04 22:38:34.591 - Using existing connection for thread 8448
2025-06-04 22:38:34.592 - Stats database initialized successfully
2025-06-04 22:38:34.594 - ensure_database_exists called from thread 8448
2025-06-04 22:38:34.595 - Using existing connection for thread 8448
2025-06-04 22:38:34.596 - Stats database initialized successfully
