"""
Setup Script for Independent Time System

This script sets up the independent time system for your WOW Games application.
It's a simpler alternative to the full migration script.
"""

import os
import sqlite3
from time_manager import get_time_manager, independent_now, independent_strftime
from db_time_integration import log_time_system_activation

def ensure_database_tables():
    """Ensure all required database tables exist."""
    print("Setting up database tables...")
    
    db_path = "data/stats.db"
    
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create game_history table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date_time TEXT,
            username TEXT,
            house TEXT,
            stake REAL,
            players INTEGER,
            total_calls INTEGER,
            commission_percent REAL,
            fee REAL,
            total_prize REAL,
            details TEXT,
            status TEXT
        )
        ''')
        
        # Create daily_stats table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            date TEXT PRIMARY KEY,
            games_played INTEGER DEFAULT 0,
            earnings REAL DEFAULT 0,
            winners INTEGER DEFAULT 0,
            total_players INTEGER DEFAULT 0
        )
        ''')
        
        # Create wallet_transactions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wallet_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date_time TEXT,
            amount REAL,
            transaction_type TEXT,
            description TEXT,
            balance_after REAL
        )
        ''')
        
        conn.commit()
        conn.close()
        
        print("Database tables created successfully!")
        return True
        
    except Exception as e:
        print(f"Error setting up database: {e}")
        return False

def test_independent_time_system():
    """Test the independent time system."""
    print("\nTesting Independent Time System...")
    
    try:
        # Test basic time functions
        current_time = independent_now()
        formatted_time = independent_strftime()
        
        print(f"Current independent time: {current_time}")
        print(f"Formatted time: {formatted_time}")
        
        # Test time manager stats
        tm = get_time_manager()
        stats = tm.get_stats()
        
        print(f"Reference time: {stats['reference_time']}")
        print(f"Startup count: {stats['startup_count']}")
        print(f"Days since first startup: {stats['elapsed_since_first_startup']['days']}")
        
        return True
        
    except Exception as e:
        print(f"Error testing time system: {e}")
        return False

def test_database_integration():
    """Test database operations with independent time."""
    print("\nTesting Database Integration...")
    
    try:
        from db_time_integration import DatabaseTimeIntegration
        
        db_time = DatabaseTimeIntegration("data/stats.db")
        
        # Test adding a game record
        record_id = db_time.add_game_record(
            username="TestPlayer",
            house="TestHouse",
            stake=100,
            players=4,
            total_calls=25,
            commission_percent=10,
            fee=10,
            total_prize=360,
            details='{"pattern": "Full House", "test": true}',
            status="completed"
        )
        
        if record_id:
            print(f"Test game record created with ID: {record_id}")
        
        # Test daily stats update
        success = db_time.update_daily_stats()
        if success:
            print("Daily stats updated successfully")
        
        return True
        
    except Exception as e:
        print(f"Error testing database integration: {e}")
        return False

def main():
    """Main setup function."""
    print("=" * 60)
    print("Setting up Independent Time System for WOW Games")
    print("=" * 60)
    
    # Step 1: Initialize time manager
    print("1. Initializing Independent Time Manager...")
    tm = get_time_manager()
    print(f"   Time manager initialized (startup #{tm.startup_count})")
    
    # Step 2: Setup database tables
    print("\n2. Setting up database tables...")
    if ensure_database_tables():
        print("   Database setup completed!")
    else:
        print("   Database setup failed!")
        return
    
    # Step 3: Test time system
    print("\n3. Testing time system...")
    if test_independent_time_system():
        print("   Time system test passed!")
    else:
        print("   Time system test failed!")
        return
    
    # Step 4: Test database integration
    print("\n4. Testing database integration...")
    if test_database_integration():
        print("   Database integration test passed!")
    else:
        print("   Database integration test failed!")
        return
    
    # Step 5: Log activation
    print("\n5. Logging system activation...")
    try:
        log_time_system_activation("data/stats.db")
        print("   System activation logged!")
    except Exception as e:
        print(f"   Warning: Could not log activation: {e}")
    
    print("\n" + "=" * 60)
    print("INDEPENDENT TIME SYSTEM SETUP COMPLETED!")
    print("=" * 60)
    print()
    print("Your application now has:")
    print("- Independent time system immune to PC clock changes")
    print("- Consistent database timestamps")
    print("- Monotonic clock-based time calculation")
    print("- Backward compatibility with existing data")
    print()
    print("Next steps:")
    print("1. Update your code to use independent_now() instead of datetime.now()")
    print("2. Use db_time_integration functions for database operations")
    print("3. Monitor the system with get_time_stats()")
    print()
    print("Example usage:")
    print("  from time_manager import independent_now, independent_strftime")
    print("  current_time = independent_now()")
    print("  formatted = independent_strftime('%Y-%m-%d %H:%M:%S')")

if __name__ == "__main__":
    main()