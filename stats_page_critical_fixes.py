"""
Critical fixes for stats page performance and blinking issues.
This module provides optimized replacements for problematic methods.
"""

import pygame
import threading
import time
import queue
from datetime import datetime, timedelta

class OptimizedStatsProvider:
    """
    PERFORMANCE FIX: Optimized stats provider that eliminates blocking operations
    and implements proper caching with state management.
    """
    
    def __init__(self):
        self._cache = {}
        self._cache_timeout = 300  # 5 minutes - longer cache for better performance
        self._last_cache_time = {}
        self._loading_states = {}  # Track loading states to prevent duplicates
        self._data_ready = {}  # Track which data is ready
        
    def get_daily_earnings(self, date_str):
        """PERFORMANCE: Get daily earnings with instant cache return."""
        cache_key = f"daily_earnings_{date_str}"
        
        # INSTANT RETURN: Always return cached value immediately if available
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Set default value immediately for UI responsiveness
        self._cache[cache_key] = 0.0
        self._last_cache_time[cache_key] = time.time()
        
        # Start background loading only if not already loading
        if cache_key not in self._loading_states:
            self._loading_states[cache_key] = True
            threading.Thread(
                target=self._load_daily_earnings_background,
                args=(date_str, cache_key),
                daemon=True
            ).start()
        
        return 0.0  # Return immediately with default
    
    def _load_daily_earnings_background(self, date_str, cache_key):
        """Background loading of daily earnings."""
        try:
            # Import here to avoid blocking main thread
            from stats_db import get_stats_db_manager
            stats_db = get_stats_db_manager()
            
            with stats_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT earnings FROM daily_stats WHERE date = ?',
                    (date_str,)
                )
                result = cursor.fetchone()
                earnings = result[0] if result else 0.0
                
                # Update cache
                self._cache[cache_key] = earnings
                self._last_cache_time[cache_key] = time.time()
                self._data_ready[cache_key] = True
                
        except Exception as e:
            print(f"Background loading error for {date_str}: {e}")
            # Keep default value
        finally:
            self._loading_states.pop(cache_key, None)
    
    def get_game_history(self, limit=50):
        """PERFORMANCE: Get game history with instant return and background loading."""
        cache_key = "game_history"
        
        # INSTANT RETURN: Always return cached value immediately
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Set empty list immediately for UI responsiveness
        self._cache[cache_key] = []
        self._last_cache_time[cache_key] = time.time()
        
        # Start background loading only if not already loading
        if cache_key not in self._loading_states:
            self._loading_states[cache_key] = True
            threading.Thread(
                target=self._load_game_history_background,
                args=(limit, cache_key),
                daemon=True
            ).start()
        
        return []  # Return immediately with empty list

    def _load_game_history_background(self, limit, cache_key):
        """Background loading of game history."""
        try:
            from stats_db import get_stats_db_manager
            stats_db = get_stats_db_manager()
            
            with stats_db.get_connection_context() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM game_history 
                    ORDER BY date_time DESC 
                    LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                history = []
                
                for row in rows:
                    history.append({
                        'id': row[0],
                        'date_time': row[1],
                        'username': row[2],
                        'house': row[3],
                        'stake': row[4],
                        'players': row[5],
                        'total_calls': row[6],
                        'commission_percent': row[7],
                        'fee': row[8],
                        'total_prize': row[9],
                        'details': row[10],
                        'status': row[11],
                        'tips': row[12] if len(row) > 12 else 0
                    })
                
                # Update cache
                self._cache[cache_key] = history
                self._last_cache_time[cache_key] = time.time()
                self._data_ready[cache_key] = True
                
        except Exception as e:
            print(f"Background loading error for game history: {e}")
            # Keep empty list
        finally:
            self._loading_states.pop(cache_key, None)

class AntiBlinkingManager:
    """
    BLINKING FIX: Manages UI state to prevent continuous refreshing and blinking.
    """
    
    def __init__(self):
        self._last_update_time = {}
        self._update_cooldown = 1.0  # 1 second minimum between updates
        self._stable_data = {}  # Store stable data to prevent flickering
        self._refresh_lock = threading.Lock()
        self._pending_refreshes = set()
        
    def should_update_section(self, section_name):
        """Check if a section should be updated based on cooldown."""
        current_time = time.time()
        last_update = self._last_update_time.get(section_name, 0)
        
        if current_time - last_update >= self._update_cooldown:
            self._last_update_time[section_name] = current_time
            return True
        return False
    
    def set_stable_data(self, section_name, data):
        """Set stable data for a section to prevent flickering."""
        with self._refresh_lock:
            self._stable_data[section_name] = data
    
    def get_stable_data(self, section_name, default=None):
        """Get stable data for a section."""
        return self._stable_data.get(section_name, default)
    
    def request_refresh(self, section_name):
        """Request a refresh for a section with debouncing."""
        with self._refresh_lock:
            if section_name not in self._pending_refreshes:
                self._pending_refreshes.add(section_name)
                # Schedule refresh after a short delay
                threading.Timer(0.5, self._execute_refresh, args=(section_name,)).start()
    
    def _execute_refresh(self, section_name):
        """Execute a refresh after debouncing delay."""
        with self._refresh_lock:
            self._pending_refreshes.discard(section_name)
            # Reset update time to allow immediate update
            self._last_update_time[section_name] = 0

def apply_critical_fixes_to_stats_page(stats_page_instance):
    """
    Apply critical performance and anti-blinking fixes to a StatsPage instance.
    
    Args:
        stats_page_instance: Instance of StatsPage to fix
    """
    
    # PERFORMANCE FIX 1: Replace stats provider with optimized version
    stats_page_instance.optimized_stats_provider = OptimizedStatsProvider()
    
    # BLINKING FIX 1: Add anti-blinking manager
    stats_page_instance.anti_blink_manager = AntiBlinkingManager()
    
    # PERFORMANCE FIX 2: Override initialization to be non-blocking
    original_init_background = getattr(stats_page_instance, '_load_data_background', None)
    
    def optimized_background_loading():
        """Optimized background loading that doesn't block UI."""
        try:
            # Minimal essential loading only
            time.sleep(0.1)  # Small delay to let UI initialize
            
            # Load only critical data
            today = datetime.now().strftime('%Y-%m-%d')
            stats_page_instance.daily_earnings = stats_page_instance.optimized_stats_provider.get_daily_earnings(today)
            
            # Set loading complete flag
            stats_page_instance.initial_loading_complete = True
            
        except Exception as e:
            print(f"Optimized background loading error: {e}")
            stats_page_instance.initial_loading_complete = True
    
    # Replace background loading
    stats_page_instance._load_data_background = optimized_background_loading
    
    # BLINKING FIX 2: Override update method to prevent continuous refreshing
    original_update = getattr(stats_page_instance, 'update', None)
    
    def anti_blink_update(*args):
        """Update method with anti-blinking logic."""
        try:
            # Only update message timer and animations - no data refreshing
            if hasattr(stats_page_instance, 'message_timer') and stats_page_instance.message_timer > 0:
                stats_page_instance.message_timer -= 1
            
            # Update button animations only
            if hasattr(stats_page_instance, 'update_button_animations'):
                stats_page_instance.update_button_animations()
            
            # Update wallet balance only every 10 seconds (reduced frequency)
            current_time = pygame.time.get_ticks()
            if not hasattr(stats_page_instance, '_last_balance_update'):
                stats_page_instance._last_balance_update = current_time
            elif current_time - stats_page_instance._last_balance_update > 10000:  # 10 seconds
                try:
                    if hasattr(stats_page_instance, 'get_current_wallet_balance'):
                        new_balance = stats_page_instance.get_current_wallet_balance()
                        # Only update if significantly different to prevent flickering
                        if abs(new_balance - getattr(stats_page_instance, 'wallet_balance', 0)) > 0.01:
                            stats_page_instance.wallet_balance = new_balance
                    stats_page_instance._last_balance_update = current_time
                except Exception as e:
                    print(f"Error updating wallet balance: {e}")
            
            # Handle search debouncing (keep existing logic)
            if hasattr(stats_page_instance, 'search_debounce_timer') and stats_page_instance.search_debounce_timer > 0:
                if current_time - stats_page_instance.search_debounce_timer >= getattr(stats_page_instance, 'search_debounce_delay', 300):
                    if hasattr(stats_page_instance, 'perform_search'):
                        stats_page_instance.perform_search()
                    stats_page_instance.search_debounce_timer = 0
            
        except Exception as e:
            print(f"Error in anti-blink update: {e}")
            # Fallback to original update if available
            if original_update:
                original_update(*args)
    
    # Replace update method
    stats_page_instance.update = anti_blink_update
    
    # PERFORMANCE FIX 3: Override game history loading
    def optimized_load_game_history():
        """Load game history with optimized performance."""
        if stats_page_instance.anti_blink_manager.should_update_section('game_history'):
            history = stats_page_instance.optimized_stats_provider.get_game_history(50)
            stats_page_instance.anti_blink_manager.set_stable_data('game_history', history)
            stats_page_instance.game_history = history
    
    stats_page_instance.load_game_history = optimized_load_game_history
    
    # BLINKING FIX 3: Override authentication check to be stable
    original_is_authenticated = getattr(stats_page_instance, 'is_authenticated', None)
    
    def stable_is_authenticated():
        """Stable authentication check that doesn't cause flickering."""
        # Cache authentication state to prevent continuous checking
        if not hasattr(stats_page_instance, '_cached_auth_state'):
            stats_page_instance._cached_auth_state = True  # Default to authenticated
            stats_page_instance._cached_auth_time = time.time()
        
        # Only recheck authentication every 5 seconds
        current_time = time.time()
        if current_time - stats_page_instance._cached_auth_time > 5.0:
            if original_is_authenticated:
                stats_page_instance._cached_auth_state = original_is_authenticated()
            stats_page_instance._cached_auth_time = current_time
        
        return stats_page_instance._cached_auth_state
    
    stats_page_instance.is_authenticated = stable_is_authenticated
    
    # PERFORMANCE FIX 4: Set flags for immediate UI responsiveness
    stats_page_instance.initial_loading_complete = False
    stats_page_instance.performance_mode = True
    
    print("CRITICAL FIXES: Applied performance and anti-blinking fixes to stats page")

def create_optimized_show_stats_page():
    """
    Create an optimized version of show_stats_page function.
    
    Returns:
        function: Optimized show_stats_page function
    """
    
    def optimized_show_stats_page(screen, on_close_callback=None, previous_page=None):
        """
        PERFORMANCE OPTIMIZED: Show stats page with instant loading and no blocking operations.
        """
        print("PERFORMANCE: Starting optimized stats page initialization...")
        start_time = time.time()
        
        try:
            # Import here to avoid blocking
            from stats_page import StatsPage
            
            # Create stats page instance
            stats_page = StatsPage(screen, on_close_callback, previous_page)
            
            # Apply critical fixes immediately
            apply_critical_fixes_to_stats_page(stats_page)
            
            # Skip all blocking integrations during initialization
            print("PERFORMANCE: Skipping blocking integrations for instant loading...")
            
            # Run the stats page
            stats_page.run()
            
            end_time = time.time()
            print(f"PERFORMANCE: Stats page loaded in {end_time - start_time:.2f} seconds")
            
        except Exception as e:
            print(f"Error in optimized stats page: {e}")
            import traceback
            traceback.print_exc()
    
    return optimized_show_stats_page

# Export the optimized function
optimized_show_stats_page = create_optimized_show_stats_page()