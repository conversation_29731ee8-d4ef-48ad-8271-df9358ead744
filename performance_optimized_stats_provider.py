import os
import sqlite3
import time
from datetime import datetime, timedelta

class PerformanceOptimizedStatsProvider:
    """Performance-optimized provider that works with original stats page"""
    
    def __init__(self):
        self.db_path = os.path.join('data', 'stats.db')
        self._cache = {}
        self._cache_timeout = 60  # 1 minute cache
        self._last_cache_time = {}
        print("PerformanceOptimizedStatsProvider: Initialized with caching")
    
    def _is_cache_valid(self, cache_key):
        """Check if cached data is still valid"""
        if cache_key not in self._cache:
            return False
        if cache_key not in self._last_cache_time:
            return False
        return (time.time() - self._last_cache_time[cache_key]) < self._cache_timeout
    
    def _cache_result(self, cache_key, result):
        """Cache a result"""
        self._cache[cache_key] = result
        self._last_cache_time[cache_key] = time.time()
    
    def get_daily_earnings(self, date_str):
        """Get daily earnings with performance optimization"""
        cache_key = f"daily_earnings_{date_str}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        try:
            if not os.path.exists(self.db_path):
                return 0.0
            
            conn = sqlite3.connect(self.db_path, timeout=0.5)  # 500ms timeout
            cursor = conn.cursor()
            cursor.execute("SELECT earnings FROM daily_stats WHERE date = ? LIMIT 1", (date_str,))
            result = cursor.fetchone()
            conn.close()
            
            earnings = float(result[0]) if result else 0.0
            self._cache_result(cache_key, earnings)
            return earnings
            
        except Exception:
            # Return cached value or default
            return self._cache.get(cache_key, 0.0)
    
    def get_weekly_stats(self, end_date=None):
        """Get weekly stats with performance optimization"""
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        cache_key = f"weekly_stats_{end_date.strftime('%Y-%m-%d')}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        try:
            if not os.path.exists(self.db_path):
                return self._generate_fallback_weekly_stats(end_date)
            
            start_date = end_date - timedelta(days=6)
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')
            
            conn = sqlite3.connect(self.db_path, timeout=0.5)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT date, games_played, earnings, winners, total_players
                FROM daily_stats 
                WHERE date BETWEEN ? AND ?
                ORDER BY date
            """, (start_str, end_str))
            
            rows = cursor.fetchall()
            conn.close()
            
            # Convert to expected format
            result = []
            row_dict = {row[0]: row for row in rows}
            
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                
                if date_str in row_dict:
                    row = row_dict[date_str]
                    stats = {
                        'date': date_str,
                        'games_played': int(row[1]),
                        'earnings': float(row[2]),
                        'winners': int(row[3]),
                        'total_players': int(row[4])
                    }
                else:
                    stats = {
                        'date': date_str,
                        'games_played': 0,
                        'earnings': 0.0,
                        'winners': 0,
                        'total_players': 0
                    }
                
                result.append(stats)
                current_date += timedelta(days=1)
            
            self._cache_result(cache_key, result)
            return result
            
        except Exception:
            return self._cache.get(cache_key, self._generate_fallback_weekly_stats(end_date))
    
    def _generate_fallback_weekly_stats(self, end_date):
        """Generate fallback weekly stats"""
        start_date = end_date - timedelta(days=6)
        stats = []
        current_date = start_date
        
        while current_date <= end_date:
            stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'games_played': 0,
                'earnings': 0.0,
                'winners': 0,
                'total_players': 0
            })
            current_date += timedelta(days=1)
        
        return stats
    
    def clear_cache(self):
        """Clear cache"""
        self._cache.clear()
        self._last_cache_time.clear()

def get_performance_optimized_stats_provider():
    return PerformanceOptimizedStatsProvider()
