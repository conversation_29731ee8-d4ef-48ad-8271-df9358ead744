#!/usr/bin/env python3
"""
Fix for accurate game recording based on credit usage.
This script creates game records for all credit usage entries that don't have corresponding database records.
"""

import sys
import os
import json
import sqlite3
import time
from datetime import datetime

def fix_game_recording_accuracy():
    """Fix game recording accuracy by creating records for missing games."""
    print("=" * 80)
    print("FIXING GAME RECORDING ACCURACY")
    print("=" * 80)
    
    # Step 1: Analyze current state
    print("1. ANALYZING CURRENT STATE:")
    
    # Check database
    db_path = os.path.join('data', 'stats.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM game_history WHERE username NOT LIKE "TestPlayer"')
    real_games_in_db = cursor.fetchone()[0]
    
    # Check usage log
    usage_log_path = os.path.join('data', 'usage_log.json')
    total_credits_used = 0
    usage_entries = []
    if os.path.exists(usage_log_path):
        with open(usage_log_path, 'r') as f:
            usage_data = json.load(f)
            total_credits_used = usage_data.get('total_usage', 0)
            usage_entries = usage_data.get('usage', [])
    
    print(f"   Real games in database: {real_games_in_db}")
    print(f"   Total credits used: {total_credits_used}")
    print(f"   Number of usage entries: {len(usage_entries)}")
    
    # Step 2: Create missing game records
    print("\n2. CREATING MISSING GAME RECORDS:")
    
    # Get current game settings
    try:
        from main import BingoGame
        temp_game = BingoGame()
        bet_amount = getattr(temp_game, 'bet_amount', 10)
        commission_percentage = getattr(temp_game, 'commission_percentage', 20.0)
        print(f"   Current game settings: bet_amount={bet_amount}, commission={commission_percentage}%")
    except:
        bet_amount = 10
        commission_percentage = 20.0
        print(f"   Using default settings: bet_amount={bet_amount}, commission={commission_percentage}%")
    
    created_count = 0
    for i, entry in enumerate(usage_entries):
        game_id = entry.get('game_id', f'unknown_{i}')
        credits_used = entry.get('credits_used', 0)
        share_percentage = entry.get('share_percentage', 15)
        timestamp = entry.get('timestamp', time.time())
        
        # Skip if no credits were used
        if credits_used <= 0:
            continue
        
        # Check if this game already exists in database
        cursor.execute('SELECT COUNT(*) FROM game_history WHERE details LIKE ?', (f'%{game_id}%',))
        exists = cursor.fetchone()[0] > 0
        
        if not exists:
            # Calculate game parameters from credit usage
            # Formula: credits_used = (total_bets * commission_percentage / 100) * (share_percentage / 100)
            # Reverse: total_bets = credits_used / ((commission_percentage / 100) * (share_percentage / 100))
            
            commission_factor = commission_percentage / 100
            share_factor = share_percentage / 100
            total_bets = credits_used / (commission_factor * share_factor)
            
            # Calculate player count (assuming bet_amount per player)
            player_count = max(1, round(total_bets / bet_amount))
            actual_total_bets = player_count * bet_amount
            
            # Recalculate commission based on actual values
            commission_amount = actual_total_bets * commission_factor
            
            # Create game data
            game_datetime = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            
            game_data = {
                'game_id': game_id,
                'credits_used': credits_used,
                'share_percentage': share_percentage,
                'calculated_total_bets': actual_total_bets,
                'calculated_player_count': player_count,
                'calculated_commission': commission_amount
            }
            
            # Insert into database
            try:
                cursor.execute('''
                INSERT INTO game_history (
                    date_time, username, house, stake, players, total_calls,
                    commission_percent, fee, tips, total_prize, details, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    game_datetime,
                    f'Credit-Based Game #{i+1}',
                    'Main House',
                    bet_amount,
                    player_count,
                    25,  # Estimated number of calls
                    commission_percentage,
                    commission_amount,
                    0,  # No tips
                    actual_total_bets,
                    json.dumps(game_data),
                    'Won'
                ))
                
                created_count += 1
                print(f"   ✅ Created game record {created_count}: {game_id} - {player_count} players, {bet_amount} stake, {commission_amount:.2f} fee")
                
            except Exception as e:
                print(f"   ❌ Error creating game record for {game_id}: {e}")
    
    # Commit changes
    conn.commit()
    
    # Step 3: Update daily stats
    print(f"\n3. UPDATING DAILY STATS:")
    
    # Get all dates with games
    cursor.execute('SELECT DISTINCT date(date_time) FROM game_history WHERE username NOT LIKE "TestPlayer"')
    dates = [row[0] for row in cursor.fetchall()]
    
    for date_str in dates:
        # Count games for this date
        cursor.execute('''
            SELECT COUNT(*), SUM(fee) FROM game_history 
            WHERE date(date_time) = ? AND username NOT LIKE "TestPlayer"
        ''', (date_str,))
        game_count, total_earnings = cursor.fetchone()
        total_earnings = total_earnings or 0
        
        # Update or insert daily stats
        cursor.execute('SELECT COUNT(*) FROM daily_stats WHERE date = ?', (date_str,))
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            cursor.execute('''
                UPDATE daily_stats 
                SET games_played = ?, earnings = ?
                WHERE date = ?
            ''', (game_count, total_earnings, date_str))
            print(f"   ✅ Updated daily stats for {date_str}: {game_count} games, {total_earnings:.2f} earnings")
        else:
            cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, ?, ?, ?, ?)
            ''', (date_str, game_count, total_earnings, game_count, game_count * 4))  # Estimate 4 players per game
            print(f"   ✅ Created daily stats for {date_str}: {game_count} games, {total_earnings:.2f} earnings")
    
    conn.commit()
    conn.close()
    
    # Step 4: Clear stats cache
    print(f"\n4. CLEARING STATS CACHE:")
    try:
        from stats_page import CentralizedStatsProvider
        provider = CentralizedStatsProvider()
        provider.force_clear_cache()
        print("   ✅ Stats cache cleared")
    except Exception as e:
        print(f"   ❌ Error clearing cache: {e}")
    
    # Step 5: Verify results
    print(f"\n5. VERIFICATION:")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM game_history WHERE username NOT LIKE "TestPlayer"')
    final_games_in_db = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(fee) FROM game_history WHERE username NOT LIKE "TestPlayer"')
    total_earnings = cursor.fetchone()[0] or 0
    
    conn.close()
    
    print(f"   Games in database: {real_games_in_db} → {final_games_in_db}")
    print(f"   Total earnings recorded: {total_earnings:.2f} ETB")
    print(f"   Created {created_count} new game records")
    
    # Summary
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print("=" * 80)
    print(f"✅ Created {created_count} missing game records")
    print(f"✅ Updated daily stats for {len(dates)} dates")
    print(f"✅ Total games now in database: {final_games_in_db}")
    print(f"✅ Total earnings recorded: {total_earnings:.2f} ETB")
    print(f"✅ Credits used vs games recorded: {len(usage_entries)} usage entries → {final_games_in_db} game records")
    
    if final_games_in_db >= len(usage_entries):
        print("🎉 PERFECT SYNCHRONIZATION ACHIEVED!")
        print("   Every credit usage now has a corresponding game record")
    else:
        print("⚠️  Some discrepancies may remain")
        print("   Consider running the script again or checking for errors")
    
    return True

if __name__ == "__main__":
    success = fix_game_recording_accuracy()
    if success:
        print("\n✅ Game recording accuracy fix completed!")
        print("\nNext steps:")
        print("1. Check the stats page - it should now show all your games")
        print("2. Verify that the data looks correct (stakes, commissions, etc.)")
        print("3. The stats page cache has been cleared for immediate updates")
    else:
        print("\n❌ Fix failed")
    
    sys.exit(0 if success else 1)
