import sqlite3
import os

STATS_DB_PATH = os.path.join('data', 'stats.db')

conn = sqlite3.connect(STATS_DB_PATH)
cursor = conn.cursor()

print("=== FINAL VERIFICATION ===")

# Check Wednesday records
cursor.execute('SELECT date, earnings FROM daily_stats WHERE date LIKE "%-06-25"')
print("\nWednesday (06/25) records:")
for row in cursor.fetchall():
    print(f"  {row[0]}: {row[1]} ETB")

# Check total earnings
cursor.execute('SELECT SUM(earnings) FROM daily_stats')
total = cursor.fetchone()[0]
print(f"\nTotal earnings: {total} ETB")

# Check wallet balance
cursor.execute('SELECT balance_after FROM wallet_transactions ORDER BY id DESC LIMIT 1')
result = cursor.fetchone()
wallet = result[0] if result else 0
print(f"Wallet balance: {wallet} ETB")

print("\n=== TARGETS ===")
print("Total earnings target: 39782.6667 ETB")
print("Wednesday target: 4086.667 ETB")
print("Wallet target: 4159.0 ETB")

conn.close()