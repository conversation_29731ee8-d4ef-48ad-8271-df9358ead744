"""
REAL-WORLD DIAGNOSIS: Test the actual stats page performance in real conditions.
This script will identify the exact root cause of the performance issues.
"""

import pygame
import time
import threading
import sys
import traceback
import psutil
import os

def measure_real_stats_performance():
    """Measure the actual stats page performance in real conditions."""
    print("🔍 REAL-WORLD STATS PAGE DIAGNOSIS")
    print("=" * 60)
    print()
    
    # Initialize pygame exactly like the real application
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Real Stats Performance Test")
    
    print("✓ Pygame initialized (real application mode)")
    
    # Measure system resources before
    process = psutil.Process()
    cpu_before = process.cpu_percent()
    memory_before = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"📊 System state before:")
    print(f"   CPU: {cpu_before:.1f}%")
    print(f"   Memory: {memory_before:.1f} MB")
    print()
    
    # Test 1: Import time
    print("🔍 Testing import time...")
    import_start = time.time()
    
    try:
        from stats_page import show_stats_page, StatsPage
        import_end = time.time()
        import_time = import_end - import_start
        print(f"✓ Import time: {import_time:.2f} seconds")
        
        if import_time > 2.0:
            print("⚠️  ISSUE FOUND: Import time is too slow!")
            print("   This suggests heavy initialization during import")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False
    
    # Test 2: StatsPage creation time
    print("\n🔍 Testing StatsPage creation...")
    creation_start = time.time()
    
    try:
        stats_page = StatsPage(screen, None, "test")
        creation_end = time.time()
        creation_time = creation_end - creation_start
        print(f"✓ Creation time: {creation_time:.2f} seconds")
        
        if creation_time > 5.0:
            print("⚠️  ISSUE FOUND: StatsPage creation is too slow!")
            print("   This suggests heavy initialization in __init__")
    except Exception as e:
        print(f"❌ StatsPage creation failed: {e}")
        traceback.print_exc()
        return False
    
    # Test 3: First draw time
    print("\n🔍 Testing first draw performance...")
    draw_start = time.time()
    
    try:
        stats_page.draw()
        draw_end = time.time()
        draw_time = draw_end - draw_start
        print(f"✓ First draw time: {draw_time:.2f} seconds")
        
        if draw_time > 1.0:
            print("⚠️  ISSUE FOUND: First draw is too slow!")
            print("   This suggests heavy operations in draw method")
    except Exception as e:
        print(f"❌ First draw failed: {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Check for loading indicators
    print("\n🔍 Checking for loading indicators...")
    loading_in_progress = getattr(stats_page, 'stats_loading_in_progress', None)
    initial_loading_complete = getattr(stats_page, 'initial_loading_complete', None)
    
    print(f"📊 Loading state:")
    print(f"   stats_loading_in_progress: {loading_in_progress}")
    print(f"   initial_loading_complete: {initial_loading_complete}")
    
    if loading_in_progress is True:
        print("⚠️  ISSUE FOUND: Loading indicator is still active!")
        print("   This will cause continuous blinking")
    
    # Test 5: Multiple draw calls to check for blinking
    print("\n🔍 Testing for continuous blinking...")
    blink_count = 0
    stable_count = 0
    
    for i in range(20):  # Test 20 frames
        try:
            stats_page.update()
            stats_page.draw()
            
            # Check if loading state changes
            current_loading = getattr(stats_page, 'stats_loading_in_progress', None)
            if current_loading is True:
                blink_count += 1
            else:
                stable_count += 1
                
            time.sleep(0.05)  # Simulate frame rate
        except Exception as e:
            print(f"❌ Frame {i} failed: {e}")
            break
    
    print(f"📊 Blinking test results:")
    print(f"   Frames with loading indicator: {blink_count}")
    print(f"   Stable frames: {stable_count}")
    
    if blink_count > 0:
        print("⚠️  ISSUE FOUND: Loading indicator is blinking!")
        print(f"   {blink_count}/{blink_count + stable_count} frames show loading")
    
    # Test 6: Real show_stats_page function
    print("\n🔍 Testing real show_stats_page function...")
    
    # Create a test callback
    test_complete = threading.Event()
    def close_callback():
        test_complete.set()
    
    # Measure the complete user experience
    full_start = time.time()
    
    def run_real_stats():
        try:
            show_stats_page(screen, on_close_callback=close_callback, previous_page="test")
        except Exception as e:
            print(f"❌ show_stats_page error: {e}")
            traceback.print_exc()
            test_complete.set()
    
    # Run in thread to measure loading time
    stats_thread = threading.Thread(target=run_real_stats, daemon=True)
    stats_thread.start()
    
    # Wait for initialization (but not full completion)
    time.sleep(3)
    full_end = time.time()
    full_time = full_end - full_start
    
    print(f"✓ Full show_stats_page time: {full_time:.2f} seconds")
    
    if full_time > 5.0:
        print("⚠️  ISSUE FOUND: show_stats_page is too slow!")
        print("   This is the main user experience issue")
    
    # Close the test
    test_complete.set()
    
    # Measure system resources after
    cpu_after = process.cpu_percent()
    memory_after = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"\n📊 System state after:")
    print(f"   CPU: {cpu_after:.1f}% (change: {cpu_after - cpu_before:+.1f}%)")
    print(f"   Memory: {memory_after:.1f} MB (change: {memory_after - memory_before:+.1f} MB)")
    
    pygame.quit()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    issues_found = []
    
    if import_time > 2.0:
        issues_found.append(f"Slow import ({import_time:.2f}s)")
    if creation_time > 5.0:
        issues_found.append(f"Slow creation ({creation_time:.2f}s)")
    if draw_time > 1.0:
        issues_found.append(f"Slow first draw ({draw_time:.2f}s)")
    if loading_in_progress is True:
        issues_found.append("Loading indicator active")
    if blink_count > 0:
        issues_found.append(f"Blinking detected ({blink_count} frames)")
    if full_time > 5.0:
        issues_found.append(f"Slow overall loading ({full_time:.2f}s)")
    
    if issues_found:
        print("❌ ISSUES FOUND:")
        for issue in issues_found:
            print(f"   • {issue}")
        print()
        print("ROOT CAUSE ANALYSIS:")
        
        if import_time > 2.0:
            print("• Heavy imports suggest database/module initialization issues")
        if creation_time > 5.0:
            print("• Slow creation suggests blocking operations in __init__")
        if draw_time > 1.0:
            print("• Slow draw suggests expensive rendering operations")
        if loading_in_progress is True or blink_count > 0:
            print("• Loading indicators not properly disabled")
        if full_time > 5.0:
            print("• Overall slow performance suggests multiple bottlenecks")
            
        return False
    else:
        print("✅ NO MAJOR ISSUES FOUND")
        print("The stats page appears to be working correctly.")
        return True

def main():
    """Main diagnostic function."""
    print("🧪 REAL-WORLD STATS PAGE DIAGNOSIS")
    print("=" * 80)
    print()
    print("This script will test the actual stats page performance")
    print("in real conditions to identify the exact root cause.")
    print()
    
    try:
        success = measure_real_stats_performance()
        
        print("\n" + "=" * 80)
        if success:
            print("✅ DIAGNOSIS COMPLETE - NO MAJOR ISSUES")
            print("The stats page appears to be working correctly.")
        else:
            print("❌ DIAGNOSIS COMPLETE - ISSUES FOUND")
            print("See the analysis above for specific problems.")
        print("=" * 80)
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Diagnosis interrupted by user")
        return False
    except Exception as e:
        print(f"\n\n💥 Unexpected error during diagnosis: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)