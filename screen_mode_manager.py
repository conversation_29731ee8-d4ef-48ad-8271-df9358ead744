"""
Global Screen Mode Manager for WOW Games

This module provides centralized screen mode management across all pages
to ensure consistent fullscreen/windowed behavior throughout the application.
"""

import pygame
from settings_manager import SettingsManager


class ScreenModeManager:
    """
    Centralized manager for screen mode consistency across all pages
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        """Singleton pattern to ensure only one instance exists"""
        if cls._instance is None:
            cls._instance = super(ScreenModeManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the screen mode manager"""
        if not self._initialized:
            self.settings_manager = SettingsManager()
            self._current_screen = None
            self._windowed_size = (1280, 720)  # Default windowed size
            self._last_windowed_size = None
            self._screen_mode_callbacks = []  # Callbacks to notify when screen mode changes
            ScreenModeManager._initialized = True

    def register_screen_mode_callback(self, callback):
        """
        Register a callback to be called when screen mode changes

        Args:
            callback: Function to call when screen mode changes
        """
        if callback not in self._screen_mode_callbacks:
            self._screen_mode_callbacks.append(callback)

    def unregister_screen_mode_callback(self, callback):
        """
        Unregister a screen mode callback

        Args:
            callback: Function to remove from callbacks
        """
        if callback in self._screen_mode_callbacks:
            self._screen_mode_callbacks.remove(callback)

    def _notify_screen_mode_change(self, is_fullscreen):
        """
        Notify all registered callbacks about screen mode change

        Args:
            is_fullscreen (bool): New screen mode
        """
        for callback in self._screen_mode_callbacks:
            try:
                callback(is_fullscreen)
            except Exception as e:
                print(f"Error in screen mode callback: {e}")

    def get_current_screen_mode(self):
        """
        Get the current screen mode from settings

        Returns:
            bool: True if fullscreen, False if windowed
        """
        return self.settings_manager.get_current_screen_mode()

    def is_fullscreen(self):
        """
        Check if currently in fullscreen mode

        Returns:
            bool: True if fullscreen, False if windowed
        """
        if self._current_screen:
            return bool(self._current_screen.get_flags() & pygame.FULLSCREEN)
        return self.get_current_screen_mode()

    def set_screen_reference(self, screen):
        """
        Set the current screen reference for mode detection

        Args:
            screen: Pygame screen surface
        """
        self._current_screen = screen

        # Store windowed size if not in fullscreen
        if not self.is_fullscreen():
            self._windowed_size = screen.get_size()

    def apply_screen_mode(self, screen, force_mode=None):
        """
        Apply the current screen mode setting to the screen with simplified approach
        to prevent flickering with external displays

        Args:
            screen: Pygame screen surface
            force_mode (bool, optional): Force specific mode (True=fullscreen, False=windowed)

        Returns:
            pygame.Surface: Updated screen surface
        """
        target_mode = force_mode if force_mode is not None else self.get_current_screen_mode()
        current_is_fullscreen = bool(screen.get_flags() & pygame.FULLSCREEN)

        # Only change mode if needed
        if current_is_fullscreen != target_mode:
            try:
                # Use direct pygame mode switching to prevent flickering
                if target_mode:
                    # Switch to fullscreen with simplified approach
                    print("Switching to fullscreen mode")
                    # Store current windowed size before going fullscreen
                    if not current_is_fullscreen:
                        self._last_windowed_size = screen.get_size()
                    
                    # Use a single set_mode call to enter fullscreen
                    screen_info = pygame.display.Info()
                    screen = pygame.display.set_mode(
                        (screen_info.current_w, screen_info.current_h), 
                        pygame.FULLSCREEN
                    )
                    
                    # Update scaling factors for responsive layout
                    self._update_scaling_factors(screen_info.current_w, screen_info.current_h)
                else:
                    # Switch to windowed mode with simplified approach
                    print("Switching to windowed mode")
                    # Use last windowed size or default
                    windowed_size = self._last_windowed_size or self._windowed_size
                    
                    # Use a single set_mode call to enter windowed mode
                    screen = pygame.display.set_mode(windowed_size, pygame.RESIZABLE)
                    
                    # Update scaling factors for responsive layout
                    self._update_scaling_factors(windowed_size[0], windowed_size[1])

                # Update screen reference
                self.set_screen_reference(screen)

                # Notify callbacks about the change
                self._notify_screen_mode_change(target_mode)
                
                # Save the setting
                self.settings_manager.set_screen_mode(target_mode)

            except Exception as e:
                print(f"Screen mode change failed: {e}")
                # Don't attempt recovery to prevent flickering

        return screen
        
    def _update_scaling_factors(self, width, height):
        """
        Update global scaling factors for responsive layout
        
        Args:
            width: New screen width
            height: New screen height
        """
        try:
            import sys
            main_module = sys.modules.get('__main__')
            if main_module:
                # Update scaling factors if they exist
                if hasattr(main_module, 'scale_x') and hasattr(main_module, 'scale_y'):
                    # Get base dimensions from main module
                    base_width = getattr(main_module, 'BASE_WIDTH', 1024)
                    base_height = getattr(main_module, 'BASE_HEIGHT', 768)
                    
                    # Only update scaling if there's a significant change (>1% difference)
                    # This prevents unnecessary updates that could cause visual flickering
                    current_scale_x = getattr(main_module, 'scale_x', 1.0)
                    current_scale_y = getattr(main_module, 'scale_y', 1.0)
                    new_scale_x = width / base_width
                    new_scale_y = height / base_height
                    
                    # Calculate percentage difference
                    x_diff_pct = abs(new_scale_x - current_scale_x) / current_scale_x * 100
                    y_diff_pct = abs(new_scale_y - current_scale_y) / current_scale_y * 100
                    
                    # Only update if difference is significant
                    if x_diff_pct > 1.0 or y_diff_pct > 1.0:
                        # Update scaling factors
                        main_module.scale_x = new_scale_x
                        main_module.scale_y = new_scale_y
                        
                        # Update game instance if it exists
                        if hasattr(main_module, 'game') and hasattr(main_module.game, 'update_scaling'):
                            main_module.game.update_scaling()
                            
                        print(f"Updated scaling factors: scale_x={main_module.scale_x}, scale_y={main_module.scale_y}")
                    else:
                        # Skip update for minor changes
                        print(f"Skipping scaling update - change too small (x_diff={x_diff_pct:.2f}%, y_diff={y_diff_pct:.2f}%)")
        except Exception as e:
            print(f"Note: Could not update scaling factors: {e}")

    def toggle_screen_mode(self, screen):
        """
        Toggle between fullscreen and windowed mode

        Args:
            screen: Pygame screen surface

        Returns:
            tuple: (updated_screen, new_mode)
        """
        current_mode = self.is_fullscreen()
        new_mode = not current_mode

        # Update settings
        self.settings_manager.set_screen_mode(new_mode)

        # Apply the new mode
        updated_screen = self.apply_screen_mode(screen, force_mode=new_mode)

        print(f"Screen mode toggled to: {'fullscreen' if new_mode else 'windowed'}")
        return updated_screen, new_mode

    def ensure_consistent_mode(self, screen):
        """
        Ensure the screen is in the correct mode based on settings
        Modified to prevent flickering with external displays

        Args:
            screen: Pygame screen surface

        Returns:
            pygame.Surface: Updated screen surface
        """
        # Check if the current screen mode matches the settings
        current_is_fullscreen = bool(screen.get_flags() & pygame.FULLSCREEN)
        setting_is_fullscreen = self.get_current_screen_mode()
        
        # Only change mode if there's a significant mismatch
        if current_is_fullscreen != setting_is_fullscreen:
            print(f"Screen mode mismatch detected: current={current_is_fullscreen}, setting={setting_is_fullscreen}")
            return self.apply_screen_mode(screen)
        else:
            # No change needed, just update the reference
            self.set_screen_reference(screen)
            return screen

    def get_windowed_size(self):
        """
        Get the preferred windowed size

        Returns:
            tuple: (width, height) for windowed mode
        """
        return self._windowed_size

    def set_windowed_size(self, size):
        """
        Set the preferred windowed size

        Args:
            size (tuple): (width, height) for windowed mode
        """
        self._windowed_size = size
        print(f"Windowed size set to: {size}")

    def sync_with_settings(self):
        """
        Synchronize screen mode with current settings
        """
        if self._current_screen:
            self._current_screen = self.apply_screen_mode(self._current_screen)

    def handle_f_key_toggle(self, screen):
        """
        Handle F key press for screen mode toggle

        Args:
            screen: Pygame screen surface

        Returns:
            pygame.Surface: Updated screen surface
        """
        updated_screen, new_mode = self.toggle_screen_mode(screen)
        return updated_screen

    def handle_escape_key(self, screen):
        """
        Handle Escape key press (exit fullscreen if in fullscreen)

        Args:
            screen: Pygame screen surface

        Returns:
            tuple: (updated_screen, mode_changed)
        """
        if self.is_fullscreen():
            # Exit fullscreen mode
            self.settings_manager.set_screen_mode(False)
            updated_screen = self.apply_screen_mode(screen, force_mode=False)
            return updated_screen, True
        return screen, False


# Global instance
_screen_mode_manager = None

def get_screen_mode_manager():
    """
    Get the global screen mode manager instance

    Returns:
        ScreenModeManager: Global screen mode manager
    """
    global _screen_mode_manager
    if _screen_mode_manager is None:
        _screen_mode_manager = ScreenModeManager()
    return _screen_mode_manager
