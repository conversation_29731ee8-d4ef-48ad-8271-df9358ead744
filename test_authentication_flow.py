#!/usr/bin/env python3
"""
Test script to verify that the authentication flow is working correctly.

This script will:
1. Test that the stats page shows login screen first for unauthenticated users
2. Verify that data loading only happens after successful authentication
3. Test that authentication checks are in place for all data loading methods
4. Verify that the authentication state is properly maintained
"""

import sys
import os
import time
import threading

def test_authentication_priority():
    """Test that authentication is checked before any data operations."""
    print("🔐 Testing Authentication Priority...")
    print("=" * 50)
    
    try:
        # Import the stats page module
        sys.path.append('.')
        from stats_page import StatsPage
        
        # Create a mock screen for testing
        import pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        
        # Create a stats page instance (should not be authenticated by default)
        stats_page = StatsPage(screen)
        
        print("✅ Successfully created StatsPage instance")
        
        # Test 1: Check initial authentication state
        if stats_page.is_authenticated():
            print("❌ User should not be authenticated by default")
            return False
        else:
            print("✅ User is not authenticated by default (correct)")
        
        # Test 2: Check that data loading methods respect authentication
        print("\n🔍 Testing data loading authentication checks...")
        
        # Test essential data loading
        stats_page._load_essential_data()
        print("✅ Essential data loading respects authentication")
        
        # Test secondary data loading
        stats_page._load_secondary_data()
        print("✅ Secondary data loading respects authentication")
        
        # Test optional data loading
        stats_page._load_optional_data()
        print("✅ Optional data loading respects authentication")
        
        # Test stats refresh
        refresh_result = stats_page.refresh_stats()
        if refresh_result == False:
            print("✅ Stats refresh correctly blocked for unauthenticated user")
        else:
            print("❌ Stats refresh should be blocked for unauthenticated user")
            return False
        
        # Test 3: Test successful authentication triggers data loading
        print("\n🔑 Testing authentication and data loading trigger...")
        
        # Mock the data loading method to track if it's called
        data_loading_called = [False]
        original_method = stats_page._load_data_background_optimized
        
        def mock_data_loading():
            data_loading_called[0] = True
            print("✅ Data loading triggered after authentication")
        
        stats_page._load_data_background_optimized = mock_data_loading
        
        # Authenticate with default credentials
        auth_result = stats_page.authenticate_user("wowbingo", "wowgames")
        
        if not auth_result:
            print("❌ Authentication failed with correct credentials")
            return False
        
        print("✅ Authentication successful with correct credentials")
        
        # Check if user is now authenticated
        if not stats_page.is_authenticated():
            print("❌ User should be authenticated after successful login")
            return False
        else:
            print("✅ User is authenticated after successful login")
        
        # Give a moment for background thread to start
        time.sleep(0.2)
        
        # Check if data loading was triggered
        if not data_loading_called[0]:
            print("❌ Data loading was not triggered after authentication")
            return False
        else:
            print("✅ Data loading was triggered after authentication")
        
        # Restore original method
        stats_page._load_data_background_optimized = original_method
        
        # Test 4: Test that data loading now works for authenticated user
        print("\n📊 Testing data loading for authenticated user...")
        
        # Now that user is authenticated, data loading should work
        stats_page._load_essential_data()
        stats_page._load_secondary_data()
        stats_page._load_optional_data()
        
        refresh_result = stats_page.refresh_stats()
        if refresh_result != False:
            print("✅ Stats refresh works for authenticated user")
        else:
            print("❌ Stats refresh should work for authenticated user")
            return False
        
        # Test 5: Test logout clears authentication
        print("\n🚪 Testing logout functionality...")
        
        stats_page.logout()
        
        if stats_page.is_authenticated():
            print("❌ User should not be authenticated after logout")
            return False
        else:
            print("✅ User is not authenticated after logout")
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error during authentication testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_source_code_changes():
    """Test that the source code has the correct authentication checks."""
    print("\n📝 Testing Source Code Changes...")
    print("=" * 50)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check that data loading is not started in constructor
        if 'loading_thread = threading.Thread(target=self._load_data_background_optimized, daemon=True)' in content:
            # Check if it's in the constructor or in the authentication method
            constructor_section = content[content.find('def __init__('):content.find('def _start_authenticated_data_loading(')]
            if 'loading_thread = threading.Thread(target=self._load_data_background_optimized, daemon=True)' in constructor_section:
                print("❌ Data loading thread is still started in constructor")
                return False
            else:
                print("✅ Data loading thread moved out of constructor")
        
        # Test 2: Check for authentication checks in data loading methods
        auth_checks = [
            ('_load_data_background_optimized', 'if not self.is_authenticated():'),
            ('_load_essential_data', 'if not self.is_authenticated():'),
            ('_load_secondary_data', 'if not self.is_authenticated():'),
            ('_load_optional_data', 'if not self.is_authenticated():'),
            ('refresh_stats', 'if not self.is_authenticated():'),
        ]
        
        for method_name, auth_check in auth_checks:
            method_start = content.find(f'def {method_name}(')
            if method_start == -1:
                print(f"❌ Method {method_name} not found")
                return False
            
            # Find the next method to limit search scope
            next_method = content.find('\n    def ', method_start + 1)
            if next_method == -1:
                method_content = content[method_start:]
            else:
                method_content = content[method_start:next_method]
            
            if auth_check in method_content:
                print(f"✅ Authentication check found in {method_name}")
            else:
                print(f"❌ Authentication check missing in {method_name}")
                return False
        
        # Test 3: Check for _start_authenticated_data_loading method
        if '_start_authenticated_data_loading' in content:
            print("✅ _start_authenticated_data_loading method found")
        else:
            print("❌ _start_authenticated_data_loading method not found")
            return False
        
        # Test 4: Check that authentication triggers data loading
        if 'self._start_authenticated_data_loading()' in content:
            print("✅ Data loading is triggered after authentication")
        else:
            print("❌ Data loading is not triggered after authentication")
            return False
        
        # Test 5: Check for AUTHENTICATION FIX comments
        auth_fix_comments = content.count('AUTHENTICATION FIX:')
        if auth_fix_comments >= 5:
            print(f"✅ Found {auth_fix_comments} AUTHENTICATION FIX comments")
        else:
            print(f"❌ Expected at least 5 AUTHENTICATION FIX comments, found {auth_fix_comments}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing source code changes: {e}")
        return False

def test_draw_method_authentication():
    """Test that the draw method properly shows login screen for unauthenticated users."""
    print("\n🎨 Testing Draw Method Authentication...")
    print("=" * 50)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the draw method
        draw_start = content.find('def draw(self):')
        if draw_start == -1:
            print("❌ Draw method not found")
            return False
        
        # Find the next method to limit search scope
        next_method = content.find('\n    def ', draw_start + 1)
        if next_method == -1:
            draw_content = content[draw_start:]
        else:
            draw_content = content[draw_start:next_method]
        
        # Check for authentication check in draw method
        if 'if not self.is_authenticated():' in draw_content:
            print("✅ Authentication check found in draw method")
        else:
            print("❌ Authentication check missing in draw method")
            return False
        
        # Check that login screen is drawn for unauthenticated users
        if 'self.draw_login_screen(screen_width, screen_height)' in draw_content:
            print("✅ Login screen is drawn for unauthenticated users")
        else:
            print("❌ Login screen is not drawn for unauthenticated users")
            return False
        
        # Check that method returns after drawing login screen
        auth_block = draw_content[draw_content.find('if not self.is_authenticated():'):]
        if 'return' in auth_block[:auth_block.find('\n        # ')]:
            print("✅ Draw method returns after showing login screen")
        else:
            print("❌ Draw method should return after showing login screen")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing draw method authentication: {e}")
        return False

if __name__ == "__main__":
    print("Authentication Flow Test Suite")
    print("==============================\n")
    
    # Run the tests
    auth_priority_passed = test_authentication_priority()
    source_code_passed = test_source_code_changes()
    draw_method_passed = test_draw_method_authentication()
    
    print("\n" + "=" * 50)
    if auth_priority_passed and source_code_passed and draw_method_passed:
        print("🎉 ALL AUTHENTICATION TESTS PASSED!")
        print("\nAuthentication flow is working correctly:")
        print("✅ Login page appears first for unauthenticated users")
        print("✅ Data loading only happens after successful authentication")
        print("✅ All data loading methods have authentication checks")
        print("✅ Authentication state is properly maintained")
        print("✅ Draw method prioritizes login screen")
        sys.exit(0)
    else:
        print("❌ Some authentication tests failed. Please review the implementation.")
        sys.exit(1)
