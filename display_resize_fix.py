"""
Display Resize Fix for WOW Games

This module patches the external display manager and screen mode manager
to prevent automatic resizing of the window while still allowing
manual resizing and fullscreen toggling.
"""

import sys
import pygame
from external_display_manager import get_external_display_manager
from screen_mode_manager import get_screen_mode_manager

def apply_display_resize_fix():
    """
    Apply fixes to prevent automatic window resizing while still allowing
    manual resizing and fullscreen toggling
    """
    print("Applying display resize fix to prevent automatic window resizing...")
    
    # Get the external display manager
    display_manager = get_external_display_manager()
    
    # Store the original detect_display_change_and_recover method
    original_detect_display_change = display_manager.detect_display_change_and_recover
    
    def patched_detect_display_change_and_recover():
        """
        Patched version that prevents automatic resizing but allows manual resizing
        """
        # Check if this is a user-initiated resize
        import traceback
        stack = traceback.extract_stack()
        user_initiated = False
        
        # Look for user-initiated resize functions in the stack
        for frame in stack:
            if any(func in frame.name for func in [
                'handle_f_key_toggle', 
                'handle_escape_key', 
                'toggle_screen_mode',
                'handle_display_mode_switch',
                'set_mode'
            ]):
                user_initiated = True
                break
        
        if user_initiated:
            # Allow user-initiated resizing
            print("Allowing user-initiated display change")
            return True
        else:
            # Prevent automatic resizing
            print("Preventing automatic display change")
            return False
    
    # Apply the patch
    display_manager.detect_display_change_and_recover = patched_detect_display_change_and_recover
    
    # Get the screen mode manager
    screen_mode_manager = get_screen_mode_manager()
    
    # Store the original handle_f_key_toggle method
    original_handle_f_key_toggle = screen_mode_manager.handle_f_key_toggle
    
    def patched_handle_f_key_toggle(screen):
        """
        Patched version that ensures F key toggle works correctly
        """
        print("F key toggle: Allowing fullscreen toggle")
        return original_handle_f_key_toggle(screen)
    
    # Apply the patch
    screen_mode_manager.handle_f_key_toggle = patched_handle_f_key_toggle
    
    # Store the original handle_escape_key method
    original_handle_escape_key = screen_mode_manager.handle_escape_key
    
    def patched_handle_escape_key(screen):
        """
        Patched version that ensures Escape key works correctly
        """
        print("Escape key: Allowing exit from fullscreen")
        return original_handle_escape_key(screen)
    
    # Apply the patch
    screen_mode_manager.handle_escape_key = patched_handle_escape_key
    
    print("Display resize fix applied successfully")
    return True

# Apply the fix when imported
if __name__ != "__main__":
    apply_display_resize_fix()