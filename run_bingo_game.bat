@echo off
:: ================================================================
:: WOW Bingo Game - Runner Script
:: ================================================================
:: This script runs the WOW Bingo Game from the directory build
:: ================================================================

setlocal
cd /d "%~dp0"

echo ================================================================
echo     WOW Bingo Game - Launcher
echo ================================================================
echo.

:: Check if the directory build exists
if exist "build_directory\main.py.exe" (
    echo Starting WOW Bingo Game from directory build...
    start "" "build_directory\main.py.exe"
) else if exist "build_fixed\WOWBingoGame.exe" (
    echo Starting WOW Bingo Game from onefile build...
    start "" "build_fixed\WOWBingoGame.exe"
) else (
    echo Error: WOW Bingo Game executable not found!
    echo.
    echo Please build the game first using one of these commands:
    echo - nuitka_build_directory.bat (for directory build)
    echo - nuitka_build_fixed.bat (for onefile build)
    echo - build_with_uv.bat (for onefile build with Uv)
    echo.
    pause
    exit /b 1
)

exit /b 0