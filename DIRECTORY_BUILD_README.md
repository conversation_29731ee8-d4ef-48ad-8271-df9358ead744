# WOW Bingo Game - Directory Build Instructions

## Directory Build vs. Onefile Build

There are two ways to build the WOW Bingo Game:

1. **Onefile Build** (original method):
   - Creates a single executable file
   - Slower startup time (needs to extract files on each run)
   - Easier distribution (just one file)

2. **Directory Build** (new method):
   - Creates a directory with the executable and all dependencies
   - Faster startup time
   - Requires distributing the entire directory

## Building the Directory Version

To build the directory version:

```
nuitka_build_directory.bat
```

This will create a `build_directory` folder containing:
- `main.py.exe` - The main executable
- Various DLL files and dependencies
- `assets` and `data` folders

## Running the Directory Build

There are several ways to run the directory build:

### Method 1: Using the Desktop Shortcut

The build script automatically creates a desktop shortcut. Simply double-click the "WOW Bingo Game" shortcut on your desktop.

### Method 2: Running the Executable Directly

1. Navigate to the `build_directory` folder
2. Double-click `main.py.exe`

### Method 3: Command Line

```
cd "d:/GAME PROJECTS/LAST-GAME_CONCEPT-/build_directory"
main.py.exe
```

## Distributing the Directory Build

To distribute the directory build:

1. Zip the entire `build_directory` folder
2. Send the zip file to the recipient
3. Instruct them to extract the zip and run `main.py.exe`

Alternatively, you can create an installer using a tool like Inno Setup:

```
inno_setup_script.iss
```

## Troubleshooting

If you encounter issues:

1. Make sure all files in the `build_directory` folder are present
2. Check that the `assets` and `data` folders are in the correct location
3. Try running the executable from the command line to see any error messages
4. Ensure all required DLLs are present in the directory