#!/usr/bin/env python3
"""
Enhanced fix for the remaining stats page game history blinking issue.

This fix targets the specific blinking behavior by:
1. Improving the stable data update logic
2. Adding more conservative update conditions
3. Preventing unnecessary re-renders
4. Ensuring loading state stability
"""

import time

def apply_enhanced_blinking_fix():
    """Apply enhanced fixes to eliminate the remaining blinking."""
    
    print("🔧 Enhanced Stats Page Blinking Fix")
    print("=" * 50)
    
    try:
        # Read the current file
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_filename = f'stats_page_history_fix_backup_{int(time.time())}.py'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📁 Created backup: {backup_filename}")
        
        fixes_applied = []
        
        # Fix 1: Make the stable data update logic more conservative
        old_stable_logic = '''        # FLICKERING FIX: Only update stable data if we have new data and enough time has passed
        if hasattr(self, 'game_history') and self.game_history:
            if not stable_history or self.should_update_section('game_history'):
                self.set_stable_ui_data('game_history', self.game_history.copy())
                stable_history = self.game_history'''
        
        new_stable_logic = '''        # FLICKERING FIX: Only update stable data if we have new data and enough time has passed
        if hasattr(self, 'game_history') and self.game_history:
            # ANTI-BLINK: More conservative update - only if data actually changed
            if not stable_history:
                # First time - set stable data
                self.set_stable_ui_data('game_history', self.game_history.copy())
                stable_history = self.game_history
                print("ANTI-BLINK: Initial game history data set")
            elif (len(self.game_history) != len(stable_history) or 
                  self.game_history != stable_history) and self.should_update_section('game_history'):
                # Only update if data actually changed and cooldown passed
                self.set_stable_ui_data('game_history', self.game_history.copy())
                stable_history = self.game_history
                print(f"ANTI-BLINK: Game history updated - new count: {len(self.game_history)}")'''
        
        if old_stable_logic in content:
            content = content.replace(old_stable_logic, new_stable_logic)
            fixes_applied.append("Enhanced stable data update logic")
        
        # Fix 2: Add a flag to prevent rapid re-renders
        render_control_init = '''        # FLICKERING FIX: Anti-blinking state management
        self._last_section_update = {}
        self._section_update_cooldown = 5.0  # 5 seconds minimum between section updates (anti-blink)
        self._stable_ui_state = {}
        self._refresh_debounce = {}'''
        
        enhanced_render_control = '''        # FLICKERING FIX: Anti-blinking state management
        self._last_section_update = {}
        self._section_update_cooldown = 5.0  # 5 seconds minimum between section updates (anti-blink)
        self._stable_ui_state = {}
        self._refresh_debounce = {}
        self._last_render_time = {}  # Track last render time for each section
        self._render_cooldown = 1.0  # Minimum 1 second between renders'''
        
        if render_control_init in content:
            content = content.replace(render_control_init, enhanced_render_control)
            fixes_applied.append("Added render cooldown control")
        
        # Fix 3: Add a method to check if section should be rendered
        render_check_method = '''
    def should_render_section(self, section_name):
        """ANTI-BLINK: Check if a section should be rendered to prevent rapid updates."""
        if not hasattr(self, '_last_render_time'):
            self._last_render_time = {}
        if not hasattr(self, '_render_cooldown'):
            self._render_cooldown = 1.0
        
        current_time = time.time()
        last_render = self._last_render_time.get(section_name, 0)
        
        if current_time - last_render >= self._render_cooldown:
            self._last_render_time[section_name] = current_time
            return True
        return False
    '''
        
        # Add the method if it doesn't exist
        if 'def should_render_section(self, section_name):' not in content:
            # Find a good place to insert the method
            insertion_point = content.find('    def should_update_section(self, section_name):')
            if insertion_point != -1:
                content = content[:insertion_point] + render_check_method + '\n' + content[insertion_point:]
                fixes_applied.append("Added render cooldown check method")
        
        # Fix 4: Improve the fallback data logic to be more stable
        old_fallback = '''        # FLICKERING FIX: Always use stable data for rendering
        if not stable_history and hasattr(self, 'game_history'):
            stable_history = self.game_history'''
        
        new_fallback = '''        # FLICKERING FIX: Always use stable data for rendering
        if not stable_history and hasattr(self, 'game_history') and self.game_history:
            # ANTI-BLINK: Use fallback only once and cache it
            stable_history = self.game_history.copy()
            self.set_stable_ui_data('game_history', stable_history)
            print("ANTI-BLINK: Used fallback game history data and cached it")'''
        
        if old_fallback in content:
            content = content.replace(old_fallback, new_fallback)
            fixes_applied.append("Improved fallback data stability")
        
        # Fix 5: Add a check at the beginning of draw_game_history to prevent unnecessary renders
        draw_method_start = '''    def draw_game_history(self, start_y):
        """Draw game history table

        Returns:
            int: Height of the drawn table
        """
        # Calculate table dimensions and position
        table_width = int(950 * self.scale_x)
        table_height = int(250 * self.scale_y)
        table_x = (self.screen.get_width() - table_width) // 2
        table_y = start_y + int(10 * self.scale_y)'''
        
        enhanced_draw_start = '''    def draw_game_history(self, start_y):
        """Draw game history table

        Returns:
            int: Height of the drawn table
        """
        # ANTI-BLINK: Check if we should render this section
        if not self.should_render_section('game_history_render'):
            # Return cached height if available
            if hasattr(self, '_cached_game_history_height'):
                return self._cached_game_history_height
        
        # Calculate table dimensions and position
        table_width = int(950 * self.scale_x)
        table_height = int(250 * self.scale_y)
        table_x = (self.screen.get_width() - table_width) // 2
        table_y = start_y + int(10 * self.scale_y)'''
        
        if draw_method_start in content:
            content = content.replace(draw_method_start, enhanced_draw_start)
            fixes_applied.append("Added render throttling to draw_game_history")
        
        # Fix 6: Cache the height at the end of draw_game_history
        height_return_pattern = '''        # Return the total height of the game history section
        total_height = table_height + title_font.get_height() + int(10 * self.scale_y)
        if self.total_history_pages > 1:
            total_height += int(40 * self.scale_y)  # Add space for pagination controls

        return total_height'''
        
        cached_height_return = '''        # Return the total height of the game history section
        total_height = table_height + title_font.get_height() + int(10 * self.scale_y)
        if self.total_history_pages > 1:
            total_height += int(40 * self.scale_y)  # Add space for pagination controls

        # ANTI-BLINK: Cache the height for throttled renders
        self._cached_game_history_height = total_height
        return total_height'''
        
        if height_return_pattern in content:
            content = content.replace(height_return_pattern, cached_height_return)
            fixes_applied.append("Added height caching for throttled renders")
        
        # Write the enhanced content
        with open('stats_page.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Successfully applied enhanced fixes:")
        for fix in fixes_applied:
            print(f"   • {fix}")
        
        if fixes_applied:
            print("\n🎯 Enhanced blinking fix applied!")
            print("   The game history section should now be completely stable.")
            return True
        else:
            print("\n⚠️  No fixes were applied - patterns may have changed.")
            return False
        
    except Exception as e:
        print(f"❌ Error applying enhanced fixes: {e}")
        return False

def test_enhanced_fix():
    """Test that the enhanced fix has been applied correctly."""
    
    print("\n🧪 Testing Enhanced Fix")
    print("-" * 25)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key enhancements
        checks = [
            ('should_render_section', 'Render throttling method'),
            ('_render_cooldown = 1.0', 'Render cooldown setting'),
            ('len(self.game_history) != len(stable_history)', 'Data change detection'),
            ('_cached_game_history_height', 'Height caching'),
            ('ANTI-BLINK: Initial game history data set', 'Conservative update logging'),
        ]
        
        all_good = True
        for pattern, description in checks:
            if pattern in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - Not found")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error testing enhanced fix: {e}")
        return False

def main():
    """Apply and test the enhanced blinking fix."""
    
    print("🔧 Enhanced Stats Page Blinking Fix")
    print("=" * 60)
    
    # Apply the enhanced fix
    fix_success = apply_enhanced_blinking_fix()
    
    if fix_success:
        # Test the fix
        test_success = test_enhanced_fix()
        
        if test_success:
            print("\n🎉 ENHANCED BLINKING FIX COMPLETED!")
            print("   • Conservative stable data updates")
            print("   • Render throttling (1 second minimum)")
            print("   • Data change detection")
            print("   • Height caching for performance")
            print("   • Enhanced fallback stability")
            print("\n🚀 The game history should now be completely stable!")
        else:
            print("\n⚠️  Enhanced fix applied but testing had issues")
    else:
        print("\n❌ Enhanced fix failed to apply")
    
    print("\n📋 Testing Instructions:")
    print("   1. Open the stats page in your application")
    print("   2. Watch the game history section for 15-20 seconds")
    print("   3. It should remain completely stable without any blinking")
    print("   4. Check console for 'ANTI-BLINK' messages")
    print("   5. The section should only update when data actually changes")
    
    return fix_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)