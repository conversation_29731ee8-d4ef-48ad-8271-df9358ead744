"""
Stats Event Hooks for WOW Games application.

This module provides hooks that connect game events to the stats system.
It ensures that all game events are properly recorded and reflected in real-time.
"""

import os
import json
import logging
import threading
import time
import gc  # For finding StatsPage instances
from datetime import datetime

# Configure logging
logging.basicConfig(
    filename=os.path.join('data', 'stats_event_hooks.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Try to import our stats integration components
try:
    from game_stats_integration import GameStatsIntegration
    STATS_INTEGRATION_AVAILABLE = True
    logging.info("Game stats integration module available")
except ImportError:
    STATS_INTEGRATION_AVAILABLE = False
    logging.warning("Game stats integration module not available")

# Event hooks for stats system
class StatsEventHooks:
    """
    Event hooks for the stats system.

    This class provides methods that hook into various game events
    and ensure that the stats system is updated accordingly.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Implement singleton pattern."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(StatsEventHooks, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the stats event hooks."""
        # Skip initialization if already initialized (singleton pattern)
        if self._initialized:
            return

        with self._lock:
            # Initialize event queue
            self.event_queue = []
            self.queue_lock = threading.RLock()

            # Initialize worker thread
            self.worker_thread = None
            self.stop_event = threading.Event()

            # Start worker thread
            self.start_worker_thread()

            self._initialized = True
            logging.info("Stats event hooks initialized")

    def start_worker_thread(self):
        """Start the worker thread that processes events in the background."""
        if self.worker_thread is not None and self.worker_thread.is_alive():
            return  # Thread already running

        self.stop_event.clear()
        self.worker_thread = threading.Thread(target=self._process_events)
        self.worker_thread.daemon = True  # Thread will exit when main program exits
        self.worker_thread.start()
        logging.info("Started stats event worker thread")

    def stop_worker_thread(self):
        """Stop the worker thread."""
        if self.worker_thread is not None and self.worker_thread.is_alive():
            self.stop_event.set()
            self.worker_thread.join(timeout=2.0)  # Wait up to 2 seconds for thread to finish
            logging.info("Stopped stats event worker thread")

    def _process_events(self):
        """Background thread that processes events in the queue."""
        while not self.stop_event.is_set():
            # Process all events in the queue
            with self.queue_lock:
                events = self.event_queue.copy()
                self.event_queue.clear()

            # Process each event
            for event_type, event_data in events:
                try:
                    if event_type == 'game_started':
                        self._process_game_started(event_data)
                    elif event_type == 'game_completed':
                        self._process_game_completed(event_data)
                    elif event_type == 'wallet_transaction':
                        self._process_wallet_transaction(event_data)
                    else:
                        logging.warning(f"Unknown event type: {event_type}")
                except Exception as e:
                    logging.error(f"Error processing event {event_type}: {e}")
                    import traceback
                    logging.error(traceback.format_exc())

            # Sleep for a short time to avoid burning CPU
            time.sleep(0.1)

    def _process_game_started(self, event_data):
        """Process a game started event."""
        if not STATS_INTEGRATION_AVAILABLE:
            logging.warning("Stats integration not available, skipping game_started event")
            return

        try:
            player_count = event_data.get('player_count', 0)
            bet_amount = event_data.get('bet_amount', 50)
            is_demo_mode = event_data.get('is_demo_mode', False)

            # Record the game start
            result = GameStatsIntegration.record_game_started(
                player_count=player_count,
                bet_amount=bet_amount,
                is_demo_mode=is_demo_mode
            )

            # Force refresh data
            GameStatsIntegration.force_refresh_data()

            logging.info(f"Processed game_started event: {result}")
        except Exception as e:
            logging.error(f"Error processing game_started event: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def _process_game_completed(self, event_data):
        """Process a game completed event with proper duplicate detection."""
        
        # Skip if in demo mode
        if event_data.get("is_demo_mode", False):
            print("Game was in demo mode - statistics not updated")
            return False
        
        # Skip Game Reset entries
        winner_name = event_data.get("winner_name", "")
        if winner_name == "Game Reset":
            print("Skipping Game Reset entry - not counting as real game")
            return False
        
        # Create a unique signature for this game to detect duplicates
        try:
            username = str(event_data.get("winner_name", "Unknown"))
            stake = str(event_data.get("stake", 0))
            players = str(event_data.get("player_count", 0))
            total_calls = str(len(event_data.get("called_numbers", [])))
            timestamp = str(event_data.get("timestamp", time.time()))
            
            # Create a unique signature
            signature_str = f"{username}:{stake}:{players}:{total_calls}:{timestamp}"
            game_signature = hashlib.md5(signature_str.encode()).hexdigest()
            
            # Check if this game has already been processed
            import sqlite3
            conn = sqlite3.connect('data/stats.db')
            cursor = conn.cursor()
            
            # Ensure processed_games table exists
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS processed_games (
                game_signature TEXT PRIMARY KEY,
                date_time TEXT,
                username TEXT,
                stake REAL,
                players INTEGER,
                total_calls INTEGER,
                processed_time TEXT,
                recording_path TEXT
            )
            """)
            
            cursor.execute("""
            SELECT COUNT(*) FROM processed_games 
            WHERE game_signature = ?
            """, (game_signature,))
            
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"⚠️ Duplicate game detected with signature {game_signature} - skipping")
                conn.close()
                return False
            
            # Record this game to prevent future duplicates
            cursor.execute("""
            INSERT INTO processed_games 
            (game_signature, date_time, username, stake, players, total_calls, processed_time, recording_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                game_signature,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                username,
                event_data.get("stake", 0),
                event_data.get("player_count", 0),
                len(event_data.get("called_numbers", [])),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "stats_event_hooks._process_game_completed"
            ))
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error in duplicate detection: {e}")
            # Continue with normal processing if duplicate detection fails
        
        print("=" * 80)
        print("PROCESSING GAME COMPLETED EVENT IN STATS_EVENT_HOOKS")
        print(f"Event data: {event_data}")
        print("=" * 80)
        
        # Use only thread_safe_db for recording to prevent duplicates
        try:
            import thread_safe_db
            print("Recording game completion directly with thread_safe_db")
            db_result = thread_safe_db.record_game_completed(event_data)
            
            if db_result:
                print("✅ Game recorded successfully")
                # Trigger stats refresh
                self._trigger_stats_refresh()
                return True
            else:
                print("❌ Failed to record game")
                return False
                
        except Exception as e:
            print(f"❌ Error recording game: {e}")
            return False
    def _process_wallet_transaction(self, event_data):
        """Process a wallet transaction event."""
        if not STATS_INTEGRATION_AVAILABLE:
            logging.warning("Stats integration not available, skipping wallet_transaction event")
            return

        try:
            # We don't have a direct method for this in GameStatsIntegration,
            # so we'll need to use thread_safe_db directly or enhance GameStatsIntegration
            # For now, just log that we would process this
            logging.info(f"Would process wallet_transaction: {event_data}")

            # Force refresh data
            GameStatsIntegration.force_refresh_data()
        except Exception as e:
            logging.error(f"Error processing wallet_transaction event: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def queue_event(self, event_type, event_data):
        """
        Queue an event for processing.

        Args:
            event_type: The type of event ('game_started', 'game_completed', etc.)
            event_data: Dictionary containing event data

        Returns:
            bool: True if the event was queued, False otherwise
        """
        try:
            with self.queue_lock:
                self.event_queue.append((event_type, event_data))

            # Ensure worker thread is running
            self.start_worker_thread()

            return True
        except Exception as e:
            logging.error(f"Error queueing event {event_type}: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    # Public API methods

    def on_game_started(self, player_count, bet_amount=50, is_demo_mode=False):
        """
        Called when a game is started.

        Args:
            player_count: Number of players in the game
            bet_amount: Bet amount per player
            is_demo_mode: Boolean indicating if the game is in demo mode

        Returns:
            bool: True if the event was queued, False otherwise
        """
        event_data = {
            'player_count': player_count,
            'bet_amount': bet_amount,
            'is_demo_mode': is_demo_mode,
            'timestamp': datetime.now().isoformat()
        }
        return self.queue_event('game_started', event_data)

    def on_game_completed(self, game_data):
        """
        Called when a game is completed.

        Args:
            game_data: Dictionary containing game data
                - winner_name: Name of the winner
                - winner_cartella: Cartella number of the winner
                - claim_type: Type of claim (e.g., 'Full House', 'First Line')
                - game_duration: Duration of the game in seconds
                - player_count: Number of players in the game
                - prize_amount: Prize amount for this game
                - commission_percentage: Commission percentage for this game
                - called_numbers: List of numbers called during the game
                - is_demo_mode: Boolean indicating if the game was in demo mode

        Returns:
            bool: True if the event was queued, False otherwise
        """
        # Add timestamp if not present
        if 'timestamp' not in game_data:
            game_data['timestamp'] = datetime.now().isoformat()

        # CRITICAL FIX: Ensure we have non-zero values for key fields
        if 'stake' not in game_data or game_data['stake'] == 0:
            game_data['stake'] = 50
            print(f"CRITICAL: Added default stake value of {game_data['stake']}")

        if 'player_count' not in game_data or game_data['player_count'] == 0:
            game_data['player_count'] = 4
            print(f"CRITICAL: Added default player count of {game_data['player_count']}")

        if 'prize_amount' not in game_data or game_data['prize_amount'] == 0:
            # Calculate a default prize amount based on stake and player count
            game_data['prize_amount'] = game_data['stake'] * game_data['player_count'] * 0.8  # 80% of total stakes
            print(f"CRITICAL: Calculated default prize amount of {game_data['prize_amount']}")

        # CRITICAL FIX: Force a refresh of the stats page after queueing the event
        result = self.queue_event('game_completed', game_data)

        # Force a refresh of the stats page
        try:
            import pygame
            import time
            if pygame.get_init():
                print("CRITICAL: Posting refresh_stats event directly from on_game_completed")
                refresh_event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'refresh_stats',
                    'force': True,
                    'force_reload': True,
                    'high_priority': True,
                    'source': 'on_game_completed',
                    'timestamp': time.time()
                })
                pygame.event.post(refresh_event)
                print("CRITICAL: Posted refresh_stats event directly from on_game_completed")
        except Exception as e:
            print(f"CRITICAL: Error posting refresh event from on_game_completed: {e}")
            import traceback
            traceback.print_exc()

        return result

    def on_wallet_transaction(self, amount, transaction_type, description):
        """
        Called when a wallet transaction occurs.

        Args:
            amount: Transaction amount
            transaction_type: Type of transaction (deposit, withdrawal, fee, etc.)
            description: Transaction description

        Returns:
            bool: True if the event was queued, False otherwise
        """
        event_data = {
            'amount': amount,
            'transaction_type': transaction_type,
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        return self.queue_event('wallet_transaction', event_data)

# Global function to get the stats event hooks instance
def get_stats_event_hooks():
    """
    Get the stats event hooks singleton instance.

    Returns:
        StatsEventHooks: The stats event hooks instance
    """
    return StatsEventHooks()

# Create singleton instance on module import
import hashlib
_stats_event_hooks = StatsEventHooks()

def register_stats_event_handler():
    """
    Register the stats event handler with the game system.

    This function is called by the stats integration system to ensure
    that game events are properly hooked into the stats system.

    Returns:
        StatsEventHooks: The registered stats event hooks instance
    """
    global _stats_event_hooks

    # Ensure the singleton is initialized
    if _stats_event_hooks is None:
        _stats_event_hooks = StatsEventHooks()

    # Log the registration
    logging.info("Stats event handler registered successfully")
    print("Stats event handler registered successfully")

    return _stats_event_hooks

def unregister_stats_event_handler():
    """
    Unregister the stats event handler and clean up resources.

    This function is called when shutting down the stats integration system
    to ensure proper cleanup of resources.

    Returns:
        bool: True if successfully unregistered, False otherwise
    """
    global _stats_event_hooks

    try:
        if _stats_event_hooks is not None:
            # Stop the worker thread
            _stats_event_hooks.stop_worker_thread()

            # Clear the event queue
            with _stats_event_hooks.queue_lock:
                _stats_event_hooks.event_queue.clear()

            # Reset the singleton
            _stats_event_hooks = None

            logging.info("Stats event handler unregistered successfully")
            print("Stats event handler unregistered successfully")
            return True
        else:
            logging.info("Stats event handler was not registered")
            print("Stats event handler was not registered")
            return True

    except Exception as e:
        logging.error(f"Error unregistering stats event handler: {e}")
        print(f"Error unregistering stats event handler: {e}")
        return False