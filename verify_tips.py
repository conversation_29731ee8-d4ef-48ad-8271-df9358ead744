#!/usr/bin/env python3
"""
Simple script to verify tips calculation in the game history.
"""

import os
import sqlite3
import sys

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

def main():
    """Main function."""
    print("TIPS VERIFICATION SCRIPT")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists(STATS_DB_PATH):
        print(f"Database not found at: {STATS_DB_PATH}")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get game history records with tips
        cursor.execute("""
        SELECT id, stake, players, total_prize, tips
        FROM game_history
        ORDER BY id DESC
        LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        if not results:
            print("No game history records found")
            conn.close()
            return False
        
        print(f"Found {len(results)} game history records")
        
        # Print the results
        print("\nGAME HISTORY RECORDS:")
        for result in results:
            game_id, stake, players, total_prize, tips = result
            base_prize = stake * players
            expected_tips = base_prize - ((base_prize // 10) * 10)
            
            print(f"\nGame ID: {game_id}")
            print(f"  Stake: {stake} ETB")
            print(f"  Players: {players}")
            print(f"  Base Prize Pool: {base_prize} ETB")
            print(f"  Total Prize: {total_prize} ETB")
            print(f"  Tips in DB: {tips} ETB")
            print(f"  Expected Tips: {expected_tips} ETB")
            print(f"  Tips Calculation {'CORRECT' if tips == expected_tips else 'INCORRECT'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    main()