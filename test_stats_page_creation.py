#!/usr/bin/env python3
"""
Test that the stats page can be created without threading errors.
"""

def test_threading_availability():
    """Test that threading is available in the stats_page module."""
    
    print("🧪 Testing Threading Availability in Stats Page")
    print("=" * 50)
    
    try:
        # Import the module
        print("📦 Importing stats_page module...")
        import stats_page
        
        # Check if threading is available at module level
        if hasattr(stats_page, 'threading'):
            print("✅ Threading module is available")
        else:
            print("❌ Threading module not available")
            return False
        
        # Test that we can create a Lock
        try:
            lock = stats_page.threading.Lock()
            print("✅ Can create threading.Lock()")
        except Exception as e:
            print(f"❌ Cannot create threading.Lock(): {e}")
            return False
        
        # Test that we can create a Thread
        try:
            def dummy_func():
                pass
            thread = stats_page.threading.Thread(target=dummy_func, daemon=True)
            print("✅ Can create threading.Thread()")
        except Exception as e:
            print(f"❌ Cannot create threading.Thread(): {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing threading: {e}")
        return False

def test_stats_page_class():
    """Test that the StatsPage class can be accessed."""
    
    print("\n🏗️  Testing StatsPage Class Access")
    print("-" * 35)
    
    try:
        import stats_page
        
        # Check if StatsPage class exists
        if hasattr(stats_page, 'StatsPage'):
            print("✅ StatsPage class is available")
        else:
            print("❌ StatsPage class not found")
            return False
        
        # Check if the class has the problematic __init__ method
        if hasattr(stats_page.StatsPage, '__init__'):
            print("✅ StatsPage.__init__ method is available")
        else:
            print("❌ StatsPage.__init__ method not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing StatsPage class: {e}")
        return False

def test_import_count():
    """Test that there's only one threading import."""
    
    print("\n🔍 Testing Threading Import Count")
    print("-" * 35)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        import_count = content.count('import threading')
        print(f"📊 Found {import_count} 'import threading' statements")
        
        if import_count == 1:
            print("✅ Perfect! Only one threading import found")
            return True
        elif import_count == 0:
            print("❌ No threading imports found - this will cause errors")
            return False
        else:
            print(f"⚠️  Found {import_count} threading imports - may cause shadowing issues")
            return False
        
    except Exception as e:
        print(f"❌ Error checking import count: {e}")
        return False

def main():
    """Run all tests."""
    
    print("🔧 Stats Page Threading Fix Verification")
    print("=" * 60)
    
    # Test 1: Threading availability
    threading_ok = test_threading_availability()
    
    # Test 2: StatsPage class access
    class_ok = test_stats_page_class()
    
    # Test 3: Import count
    import_ok = test_import_count()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 25)
    
    if threading_ok:
        print("✅ Threading availability: PASSED")
    else:
        print("❌ Threading availability: FAILED")
    
    if class_ok:
        print("✅ StatsPage class access: PASSED")
    else:
        print("❌ StatsPage class access: FAILED")
    
    if import_ok:
        print("✅ Threading import count: CORRECT")
    else:
        print("❌ Threading import count: INCORRECT")
    
    overall_success = threading_ok and class_ok and import_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("   The threading error should be completely resolved.")
        print("   The stats page should now open without issues.")
        print("\n🚀 Ready to test in your application!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   There may still be threading-related issues.")
        print("   Check the error messages above for details.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)