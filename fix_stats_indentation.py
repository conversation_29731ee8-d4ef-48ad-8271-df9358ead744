#!/usr/bin/env python3
"""
Fix Stats Page Indentation Errors

This script fixes all indentation errors in stats_page.py
"""

import re

def fix_indentation_errors():
    """Fix all indentation errors in stats_page.py"""
    print("Fixing indentation errors in stats_page.py...")
    
    try:
        # Read the file
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        import time
        backup_name = f'stats_page.py.indent_fix_backup_{int(time.time())}'
        with open(backup_name, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ Created backup: {backup_name}")
        
        # Fix the indentation pattern
        # Pattern: if SIMPLE_STATS_AVAILABLE:\n            self.stats_provider
        # Should be: if SIMPLE_STATS_AVAILABLE:\n                self.stats_provider
        
        # Find and fix all instances
        pattern = r'(if SIMPLE_STATS_AVAILABLE:)\n            (self\.stats_provider = get_simple_stats_provider\(\))\n            (print\("Using simple stats provider"\))\n        (else:)\n            (self\.stats_provider = CentralizedStatsProvider\(\))\n            (print\("Using original stats provider"\))'
        
        replacement = r'\1\n                \2\n                \3\n            \4\n                \5\n                \6'
        
        # Apply the fix
        fixed_content = re.sub(pattern, replacement, content)
        
        # Also fix any remaining single instances
        fixed_content = re.sub(
            r'(if SIMPLE_STATS_AVAILABLE:)\n            (self\.stats_provider = get_simple_stats_provider\(\))',
            r'\1\n                \2',
            fixed_content
        )
        
        # Fix the else clauses too
        fixed_content = re.sub(
            r'        (else:)\n            (self\.stats_provider = CentralizedStatsProvider\(\))',
            r'            \1\n                \2',
            fixed_content
        )
        
        # Fix print statements
        fixed_content = re.sub(
            r'            (print\("Using simple stats provider"\))',
            r'                \1',
            fixed_content
        )
        
        fixed_content = re.sub(
            r'            (print\("Using original stats provider"\))',
            r'                \1',
            fixed_content
        )
        
        # Write the fixed content
        with open('stats_page.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✓ Fixed indentation errors in stats_page.py")
        return True
        
    except Exception as e:
        print(f"✗ Error fixing indentation: {e}")
        return False

def test_syntax():
    """Test if the file has valid Python syntax"""
    print("Testing Python syntax...")
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to compile the code
        compile(content, 'stats_page.py', 'exec')
        print("✓ Python syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"✗ Syntax Error: {e}")
        print(f"  Line: {e.lineno}")
        print(f"  Text: {e.text}")
        return False
        
    except Exception as e:
        print(f"✗ Error testing syntax: {e}")
        return False

def main():
    """Main function"""
    print("STATS PAGE INDENTATION FIX")
    print("=" * 40)
    
    if fix_indentation_errors():
        if test_syntax():
            print("\n" + "=" * 40)
            print("✅ INDENTATION FIX SUCCESSFUL!")
            print("=" * 40)
            print("The stats page should now load without errors.")
        else:
            print("\n" + "=" * 40)
            print("⚠ INDENTATION FIXED BUT SYNTAX ERRORS REMAIN")
            print("=" * 40)
            print("Manual review may be needed.")
    else:
        print("\n" + "=" * 40)
        print("✗ INDENTATION FIX FAILED")
        print("=" * 40)

if __name__ == "__main__":
    main()