#!/usr/bin/env python3
"""
Test script for the stats page to verify it works correctly.
"""

import pygame
import sys
import time
from stats_page import StatsPage

def test_stats_page():
    """Test the stats page functionality"""
    print("Testing stats page functionality...")
    
    # Initialize pygame
    pygame.init()
    
    # Create a test screen
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("Stats Page Test")
    
    try:
        # Create stats page instance
        print("Creating stats page instance...")
        stats_page = StatsPage(screen)
        print("✓ Stats page created successfully")
        
        # Test drawing the stats page
        print("Testing stats page drawing...")
        stats_page.draw()
        pygame.display.flip()
        print("✓ Stats page drawn successfully")
        
        # Test loading statistics
        print("Testing statistics loading...")
        stats_page.load_statistics()
        print("✓ Statistics loaded successfully")
        
        # Test getting wallet balance
        print("Testing wallet balance retrieval...")
        balance = stats_page.get_current_wallet_balance()
        print(f"✓ Wallet balance retrieved: {balance}")
        
        # Test authentication status
        print("Testing authentication...")
        auth_status = stats_page.is_authenticated()
        print(f"✓ Authentication status: {auth_status}")
        
        # Test stats provider
        print("Testing stats provider...")
        if hasattr(stats_page, 'stats_provider'):
            today = time.strftime('%Y-%m-%d')
            daily_earnings = stats_page.stats_provider.get_daily_earnings(today)
            print(f"✓ Daily earnings retrieved: {daily_earnings}")
            
            weekly_stats = stats_page.stats_provider.get_weekly_stats()
            print(f"✓ Weekly stats retrieved: {len(weekly_stats)} days")
        
        print("\n" + "="*50)
        print("✓ ALL TESTS PASSED - Stats page is working correctly!")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\n" + "="*50)
        print(f"✗ TEST FAILED: {e}")
        print("="*50)
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

if __name__ == "__main__":
    success = test_stats_page()
    sys.exit(0 if success else 1)