2025-07-16 13:48:32.575 - ensure_database_exists called from thread 1548
2025-07-16 13:48:32.576 - Creating new thread-specific database connection to data\stats.db for thread 1548
2025-07-16 13:48:32.577 - New database connection created successfully for thread 1548
2025-07-16 13:48:33.396 - Stats database initialized successfully
2025-07-16 13:48:33.397 - ensure_database_exists called from thread 1548
2025-07-16 13:48:33.399 - Using existing connection for thread 1548
2025-07-16 13:48:33.400 - Stats database initialized successfully
2025-07-16 13:48:33.401 - ensure_database_exists called from thread 1548
2025-07-16 13:48:33.401 - Using existing connection for thread 1548
2025-07-16 13:48:33.402 - <PERSON>ats database initialized successfully
2025-07-16 13:50:06.814 - Using existing connection for thread 1548
2025-07-16 13:50:06.854 - Creating new thread-specific database connection to data\stats.db for thread 7948
2025-07-16 13:50:06.855 - New database connection created successfully for thread 7948
2025-07-16 13:50:06.867 - Database connection closed for thread 7948
2025-07-16 13:50:06.876 - Creating new thread-specific database connection to data\stats.db for thread 7948
2025-07-16 13:50:06.879 - New database connection created successfully for thread 7948
2025-07-16 13:50:06.980 - get_summary_stats called from thread 7948
2025-07-16 13:50:06.980 - Using existing connection for thread 7948
2025-07-16 13:50:06.981 - Total earnings from database: 0
2025-07-16 13:50:06.982 - Daily earnings from database: 0
2025-07-16 13:50:06.982 - Daily games from database: 0
2025-07-16 13:50:06.983 - Wallet balance from database: 0
2025-07-16 13:50:06.983 - Total games played from database: 0
2025-07-16 13:50:06.984 - Total winners from database: 0
2025-07-16 13:50:06.984 - Returning summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:57.211 - Using existing connection for thread 1548
2025-07-16 13:50:57.307 - Using existing connection for thread 7948
2025-07-16 13:50:57.568 - Database connection closed for thread 7948
2025-07-16 13:50:57.569 - Creating new thread-specific database connection to data\stats.db for thread 7948
2025-07-16 13:50:57.570 - New database connection created successfully for thread 7948
2025-07-16 13:50:57.573 - Database connection closed for thread 7948
2025-07-16 13:50:57.574 - Creating new thread-specific database connection to data\stats.db for thread 7948
2025-07-16 13:50:57.575 - New database connection created successfully for thread 7948
