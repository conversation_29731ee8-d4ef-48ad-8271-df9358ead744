#!/usr/bin/env python3
"""
Debug script to check game recording conditions.
This script helps identify why games aren't being recorded during actual gameplay.
"""

import sys
import os
import time
from datetime import datetime

def debug_game_conditions():
    """Debug the conditions that prevent game recording."""
    print("=" * 60)
    print("DEBUGGING GAME RECORDING CONDITIONS")
    print("=" * 60)
    
    # Check if there's a current game instance
    try:
        # Try to find any game-related modules that might have state
        import importlib.util
        
        # Check for main game module
        game_spec = importlib.util.find_spec('main')
        if game_spec:
            print("✓ Main game module found")
            import main
            if hasattr(main, 'game'):
                game = main.game
                print(f"✓ Game instance found: {type(game)}")
                
                # Check game state
                if hasattr(game, 'game_state'):
                    print(f"✓ Game state handler found: {type(game.game_state)}")
                    
                    # Check key attributes for recording conditions
                    attrs_to_check = [
                        'show_winner_display',
                        'validation_result', 
                        'player_claim_cartella',
                        'called_numbers',
                        'players',
                        'game_started',
                        'is_demo_mode'
                    ]
                    
                    print("\nGame State Attributes:")
                    for attr in attrs_to_check:
                        if hasattr(game, attr):
                            value = getattr(game, attr)
                            print(f"  game.{attr}: {value} (type: {type(value)})")
                        elif hasattr(game.game_state, attr):
                            value = getattr(game.game_state, attr)
                            print(f"  game_state.{attr}: {value} (type: {type(value)})")
                        else:
                            print(f"  {attr}: NOT FOUND")
                    
                    # Check bingo logic
                    if hasattr(game, 'bingo_logic'):
                        bingo_logic = game.bingo_logic
                        print(f"\nBingo Logic: {type(bingo_logic)}")
                        if hasattr(bingo_logic, 'game_state'):
                            print(f"  Bingo game state: {bingo_logic.game_state}")
                        if hasattr(bingo_logic, 'winners'):
                            print(f"  Winners: {bingo_logic.winners}")
                        if hasattr(bingo_logic, 'player_cards'):
                            print(f"  Player cards count: {len(bingo_logic.player_cards)}")
                    
                    # Check if demo mode
                    is_demo = getattr(game, 'is_demo_mode', 'NOT SET')
                    print(f"\nDemo mode check: {is_demo}")
                    
                else:
                    print("❌ No game_state handler found")
            else:
                print("❌ No game instance found in main module")
        else:
            print("❌ Main game module not found")
            
    except Exception as e:
        print(f"❌ Error checking game state: {e}")
    
    # Check the recording conditions logic
    print("\n" + "=" * 60)
    print("ANALYZING RECORDING CONDITIONS")
    print("=" * 60)
    
    print("The game recording requires ONE of these conditions:")
    print("1. Valid winner claim:")
    print("   - show_winner_display = True")
    print("   - validation_result = True") 
    print("   - player_claim_cartella = valid number")
    print()
    print("2. Completed game without claim:")
    print("   - called_numbers exists and > 0")
    print("   - players exists and > 0")
    print("   - NOT in demo mode")
    print()
    print("3. Early ended game:")
    print("   - called_numbers >= 5")
    print("   - game_started = True")
    print("   - NOT in demo mode")
    
    # Check usage tracker state
    print("\n" + "=" * 60)
    print("CHECKING USAGE TRACKER STATE")
    print("=" * 60)
    
    try:
        from payment.usage_tracker import UsageTracker
        tracker = UsageTracker()
        
        print(f"Usage tracker active: {tracker.active}")
        print(f"Current game ID: {tracker.current_game_id}")
        print(f"Credits used: {tracker.credits_used}")
        
        # Check if tracker is properly initialized for games
        if not tracker.active:
            print("⚠️  Usage tracker is NOT active - this might prevent game recording")
            print("   Games might not be properly tracked if usage tracker isn't active")
        
        # Check recent usage
        import json
        usage_log_path = os.path.join('data', 'usage_log.json')
        if os.path.exists(usage_log_path):
            with open(usage_log_path, 'r') as f:
                usage_data = json.load(f)
                recent_usage = usage_data.get('usage', [])[-5:]  # Last 5 entries
                print(f"\nRecent credit usage (last 5):")
                for i, usage in enumerate(recent_usage, 1):
                    print(f"  {i}. {usage}")
        
    except Exception as e:
        print(f"❌ Error checking usage tracker: {e}")
    
    # Check if there are any log files that show game attempts
    print("\n" + "=" * 60)
    print("CHECKING FOR GAME ACTIVITY LOGS")
    print("=" * 60)
    
    log_files = [
        'data/thread_safe_db.log',
        'data/game_stats_integration.log',
        'data/successful_game_records.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n{log_file}:")
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        # Show last few lines
                        recent_lines = lines[-3:]
                        for line in recent_lines:
                            print(f"  {line.strip()}")
                    else:
                        print("  (empty)")
            except Exception as e:
                print(f"  Error reading: {e}")
        else:
            print(f"\n{log_file}: NOT FOUND")
    
    # Recommendations
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS")
    print("=" * 60)
    
    print("To fix the game recording issue:")
    print("1. Check if games are actually completing (not just credits being used)")
    print("2. Verify that game.called_numbers and game.players are properly set")
    print("3. Ensure is_demo_mode is False during real games")
    print("4. Check if the usage tracker is being activated when games start")
    print("5. Look for any exceptions during game completion that prevent recording")
    
    return True

if __name__ == "__main__":
    success = debug_game_conditions()
    if success:
        print("\n✅ Debug completed successfully")
    else:
        print("\n❌ Debug failed")
    
    sys.exit(0 if success else 1)
