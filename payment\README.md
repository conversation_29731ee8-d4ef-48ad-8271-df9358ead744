# Payment System Integration

This document describes the payment system integration with the stats page.

## Overview

The payment system provides a voucher-based credit system for the game. Users can redeem vouchers to add credits to their account, which are then used to pay for game usage.

## Components

1. **VoucherManager** - Manages voucher validation, redemption, and credit tracking
2. **CryptoUtils** - Provides cryptographic functions for voucher validation
3. **RechargeUI** - UI component for redeeming vouchers
4. **SimpleRechargeUI** - Simplified UI component for redeeming vouchers
5. **SimpleIntegration** - Integration with the stats page

## Integration with Stats Page

The payment system is integrated with the stats page through the `integrate_with_stats_page` function in `simple_integration.py`. This function:

1. Creates a recharge UI component
2. Enhances the stats page's draw, update, and handle_event methods to include the recharge UI
3. Adds a credit display to the stats page
4. Adds a recharge button to the stats page

## Testing

Several test scripts are provided to verify the payment system functionality:

1. **test_payment_integration.py** - Tests the payment system integration with a mock stats page
2. **test_voucher_generation.py** - Generates a test voucher for testing
3. **test_stats_page_integration.py** - Tests the integration with the actual stats page

## Usage

To use the payment system:

1. Click the "RECHARGE" button on the stats page
2. Enter a voucher code in the input field
3. Click "REDEEM" to validate the voucher
4. If valid, credits will be added to your account

## Voucher Format

Vouchers can be in one of two formats:

1. **Compact Vouchers** - 10-15 characters, used for smaller credit amounts
2. **Legacy Vouchers** - 20+ characters, used for larger credit amounts

## Machine UUID

The payment system uses a machine UUID to validate vouchers. This UUID is stored in the `data/machine_uuid.txt` file and is used to ensure that vouchers can only be redeemed on the machine they were generated for.

## Credit Usage

Credits are used based on the commission earned by the referee. The formula is:

```
Credits Used = Referee Commission × Share Percentage
```

For example, if the total bets are 1000 ETB, the commission is 20%, and the share percentage is 30%, then 60 credits would be used (1000 × 0.2 × 0.3).

## Troubleshooting

If you encounter issues with the payment system:

1. Check that the `data/machine_uuid.txt` file exists and contains a valid UUID
2. Verify that the voucher code is in the correct format
3. Make sure the voucher has not already been redeemed
4. Check that the voucher has not expired