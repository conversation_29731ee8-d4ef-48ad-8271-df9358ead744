"""
Robust Independent Time Manager for WOW Games Application

This module provides a time system that is immune to PC clock changes
using industry best practices:
- Monotonic clock for elapsed time calculation
- UTC reference time stored on first startup
- Optional NTP synchronization for accuracy
- System clock change detection
"""

import os
import json
import time
import threading
from datetime import datetime, timezone, timedelta
import logging

# Set up logging
logger = logging.getLogger(__name__)

class RobustTimeManager:
    """
    Production-ready time manager immune to system clock changes.
    
    Uses monotonic clock + stored reference time approach which is the
    industry standard for applications requiring time consistency.
    """
    
    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.config_file = os.path.join(data_dir, 'time_config.json')
        self._lock = threading.Lock()
        
        # Ensure data directory exists
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize time system
        self._load_or_create_reference()
        
        # Track system clock changes
        self._last_system_time = time.time()
        self._last_monotonic = time.monotonic()
        
        logger.info(f"Time Manager initialized. Reference: {self.reference_utc_iso}")
    
    def _load_or_create_reference(self):
        """Load existing reference or create new one on first startup."""
        try:
            if os.path.exists(self.config_file):
                # Load existing configuration
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                self.reference_utc_timestamp = config['reference_utc_timestamp']
                self.reference_utc_iso = config['reference_utc_iso']
                self.reference_monotonic = config['reference_monotonic']
                self.startup_count = config.get('startup_count', 1) + 1
                self.created_at = config.get('created_at')
                
                logger.info(f"Loaded time reference (startup #{self.startup_count})")
                
            else:
                # First startup - create reference
                utc_now = datetime.now(timezone.utc)
                
                self.reference_utc_timestamp = utc_now.timestamp()
                self.reference_utc_iso = utc_now.isoformat()
                self.reference_monotonic = time.monotonic()
                self.startup_count = 1
                self.created_at = utc_now.isoformat()
                
                logger.info("FIRST STARTUP: Created new time reference")
            
            # Save updated configuration
            self._save_config()
            
        except Exception as e:
            logger.error(f"Error with time reference: {e}")
            # Emergency fallback
            utc_now = datetime.now(timezone.utc)
            self.reference_utc_timestamp = utc_now.timestamp()
            self.reference_utc_iso = utc_now.isoformat()
            self.reference_monotonic = time.monotonic()
            self.startup_count = 1
            self.created_at = utc_now.isoformat()
            self._save_config()
    
    def _save_config(self):
        """Save time configuration to file."""
        try:
            config = {
                'reference_utc_timestamp': self.reference_utc_timestamp,
                'reference_utc_iso': self.reference_utc_iso,
                'reference_monotonic': self.reference_monotonic,
                'startup_count': self.startup_count,
                'created_at': self.created_at,
                'last_updated': datetime.now(timezone.utc).isoformat(),
                'version': '1.0'
            }
            
            with self._lock:
                with open(self.config_file, 'w') as f:
                    json.dump(config, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Error saving time config: {e}")
    
    def now(self):
        """
        Get current time using independent time system.
        
        This is the main function to replace datetime.now() calls.
        Returns local time based on independent calculation.
        """
        return self.get_independent_time().astimezone()
    
    def utc_now(self):
        """Get current UTC time using independent time system."""
        return self.get_independent_time()
    
    def get_independent_time(self):
        """
        Calculate current UTC time independent of system clock.
        
        Uses monotonic clock to calculate elapsed time since reference.
        This is immune to system clock changes.
        """
        try:
            # Calculate elapsed time using monotonic clock
            current_monotonic = time.monotonic()
            elapsed_seconds = current_monotonic - self.reference_monotonic
            
            # Add elapsed time to reference
            current_timestamp = self.reference_utc_timestamp + elapsed_seconds
            
            return datetime.fromtimestamp(current_timestamp, timezone.utc)
            
        except Exception as e:
            logger.error(f"Error calculating independent time: {e}")
            # Fallback to system time
            return datetime.now(timezone.utc)
    
    def strftime(self, format_str='%Y-%m-%d %H:%M:%S'):
        """Format current time using independent time system."""
        return self.now().strftime(format_str)
    
    def utc_strftime(self, format_str='%Y-%m-%d %H:%M:%S'):
        """Format current UTC time using independent time system."""
        return self.utc_now().strftime(format_str)
    
    def timestamp(self):
        """Get current timestamp using independent time system."""
        return self.get_independent_time().timestamp()
    
    def detect_system_clock_change(self):
        """
        Detect if system clock has been changed.
        
        Returns:
            dict: Information about potential clock changes
        """
        try:
            current_system = time.time()
            current_monotonic = time.monotonic()
            
            # Calculate expected system time based on monotonic progression
            monotonic_elapsed = current_monotonic - self._last_monotonic
            expected_system = self._last_system_time + monotonic_elapsed
            
            # Check for significant difference
            difference = abs(current_system - expected_system)
            threshold = 5.0  # 5 seconds threshold
            
            clock_changed = difference > threshold
            
            if clock_changed:
                logger.warning(f"System clock change detected! Difference: {difference:.2f}s")
            
            # Update tracking values
            self._last_system_time = current_system
            self._last_monotonic = current_monotonic
            
            return {
                'clock_changed': clock_changed,
                'difference_seconds': difference,
                'threshold': threshold,
                'system_time': datetime.fromtimestamp(current_system).isoformat(),
                'independent_time': self.get_independent_time().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error detecting clock change: {e}")
            return {'clock_changed': False, 'error': str(e)}
    
    def get_stats(self):
        """Get statistics about the time system."""
        independent_time = self.get_independent_time()
        reference_time = datetime.fromtimestamp(self.reference_utc_timestamp, timezone.utc)
        elapsed = independent_time - reference_time
        
        return {
            'reference_time': self.reference_utc_iso,
            'current_independent_time': independent_time.isoformat(),
            'current_system_time': datetime.now(timezone.utc).isoformat(),
            'elapsed_since_first_startup': {
                'total_seconds': elapsed.total_seconds(),
                'days': elapsed.days,
                'hours': elapsed.total_seconds() / 3600
            },
            'startup_count': self.startup_count,
            'created_at': self.created_at
        }

# Global instance (singleton pattern)
_time_manager = None
_lock = threading.Lock()

def get_time_manager():
    """Get global time manager instance."""
    global _time_manager
    if _time_manager is None:
        with _lock:
            if _time_manager is None:
                _time_manager = RobustTimeManager()
    return _time_manager

# Convenience functions to replace standard datetime calls
def independent_now():
    """Replace datetime.now() - returns local time."""
    return get_time_manager().now()

def independent_utc_now():
    """Replace datetime.utcnow() - returns UTC time."""
    return get_time_manager().utc_now()

def independent_strftime(format_str='%Y-%m-%d %H:%M:%S'):
    """Get formatted time string."""
    return get_time_manager().strftime(format_str)

def independent_timestamp():
    """Get current timestamp."""
    return get_time_manager().timestamp()

def check_clock_changes():
    """Check for system clock changes."""
    return get_time_manager().detect_system_clock_change()

def get_time_stats():
    """Get time system statistics."""
    return get_time_manager().get_stats()

# Test function
if __name__ == "__main__":
    print("=== Robust Time Manager Test ===")
    
    tm = get_time_manager()
    
    print(f"Independent time (local): {independent_now()}")
    print(f"Independent time (UTC): {independent_utc_now()}")
    print(f"System time (UTC): {datetime.now(timezone.utc)}")
    print(f"Formatted: {independent_strftime()}")
    
    stats = get_time_stats()
    print(f"\nTime System Stats:")
    print(f"  Startup count: {stats['startup_count']}")
    print(f"  Days since first startup: {stats['elapsed_since_first_startup']['days']}")
    print(f"  Hours since first startup: {stats['elapsed_since_first_startup']['hours']:.2f}")
    
    clock_check = check_clock_changes()
    print(f"\nClock Change Check:")
    print(f"  Clock changed: {clock_check['clock_changed']}")
    if 'difference_seconds' in clock_check:
        print(f"  Difference: {clock_check['difference_seconds']:.2f}s")