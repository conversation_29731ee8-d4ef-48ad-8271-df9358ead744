import os
import sqlite3

STATS_DB_PATH = os.path.join('data', 'stats.db')

print("=== FINAL VERIFICATION - JUNE 25, 2025 ===")

conn = sqlite3.connect(STATS_DB_PATH)
cursor = conn.cursor()

# Check total earnings
cursor.execute('SELECT SUM(earnings) FROM daily_stats')
total = cursor.fetchone()[0] or 0
print(f"Total earnings: {total} ETB (target: 39782.6667)")

# Check yesterday (6/25/2025)
cursor.execute('SELECT earnings FROM daily_stats WHERE date = "2025-06-25"')
result = cursor.fetchone()
yesterday = result[0] if result else 0
print(f"Yesterday (6/25/2025): {yesterday} ETB (target: 4086.667)")

# Check wallet balance
cursor.execute('SELECT balance_after FROM wallet_transactions ORDER BY id DESC LIMIT 1')
result = cursor.fetchone()
wallet = result[0] if result else 0
print(f"Wallet balance: {wallet} ETB (target: 4159.0)")

# Show recent daily stats to confirm
print(f"\nRecent daily stats:")
cursor.execute('SELECT date, earnings FROM daily_stats WHERE earnings > 0 ORDER BY date DESC LIMIT 5')
for row in cursor.fetchall():
    date, earnings = row
    marker = " <- YESTERDAY" if date == "2025-06-25" else ""
    print(f"  {date}: {earnings} ETB{marker}")

# Status check
total_ok = abs(total - 39782.6667) < 0.001
yesterday_ok = abs(yesterday - 4086.667) < 0.001
wallet_ok = abs(wallet - 4159.0) < 0.001

print(f"\nFinal Status:")
print(f"- Total earnings: {'CORRECT' if total_ok else 'INCORRECT'}")
print(f"- Yesterday (6/25/2025): {'CORRECT' if yesterday_ok else 'INCORRECT'}")
print(f"- Wallet balance: {'CORRECT' if wallet_ok else 'INCORRECT'}")

if total_ok and yesterday_ok and wallet_ok:
    print(f"\n*** ALL VALUES ARE PERFECT! ***")
    print(f"Stats page will show:")
    print(f"  - Total earnings: 39782.7 ETB")
    print(f"  - Yesterday (6/25/2025): 4086.7 ETB")
    print(f"  - Wallet balance: 4159.0 ETB")
else:
    print(f"\n*** SOME VALUES NEED ATTENTION ***")

conn.close()