2025-07-21 11:08:06,165 - INFO - Started background data loading
2025-07-21 11:08:06,165 - INFO - OptimizedStatsLoader initialized
2025-07-21 11:08:06,165 - INFO - Using optimized stats loader for integration
2025-07-21 11:08:06,166 - ERROR - Error loading weekly stats: no such table: daily_stats
2025-07-21 11:08:06,166 - ERROR - Error loading summary data: no such table: daily_stats
2025-07-21 11:08:06,167 - ERROR - Error loading game history: no such table: game_history
2025-07-21 11:08:06,168 - INFO - Saved 0 items to cache
2025-07-21 11:08:06,168 - INFO - Background data loading completed
2025-07-21 11:08:08,422 - INFO - Database schema initialized successfully
2025-07-21 11:08:08,436 - INFO - Database schema initialized successfully
2025-07-21 11:08:08,440 - INFO - Stats database initialized successfully
2025-07-21 11:08:08,448 - INFO - Database schema initialized successfully
2025-07-21 11:08:08,455 - INFO - Stats database initialized successfully
2025-07-21 11:08:08,462 - INFO - Game stats integration module available
2025-07-21 11:08:08,464 - INFO - Started stats event worker thread
2025-07-21 11:08:08,464 - INFO - Stats event hooks initialized
2025-07-21 11:08:08,691 - INFO - New encryption key generated
2025-07-21 11:08:08,691 - INFO - Database security initialized successfully
2025-07-21 11:08:08,699 - INFO - DB Operation: {"timestamp": "2025-07-21 11:08:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:08:08,699 - INFO - Created secure database connection
2025-07-21 11:08:10,154 - INFO - Database schema initialized successfully
2025-07-21 11:08:14,253 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:08:14,254 - INFO - Sync thread started
2025-07-21 11:08:14,254 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 11:08:14,259 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 11:08:15,058 - INFO - Hooked into game's start_game method
2025-07-21 11:08:15,058 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 11:08:20,874 - INFO - Hooked into game's start_game method
2025-07-21 11:08:20,875 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 11:08:48,847 - INFO - Loaded 0 items from persistent cache
2025-07-21 11:08:48,848 - INFO - Stats cache initialized
2025-07-21 11:08:48,849 - INFO - DB Operation: {"timestamp": "2025-07-21 11:08:48", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:08:48,849 - INFO - DB Operation: {"timestamp": "2025-07-21 11:08:48", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:08:48,851 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-21 11:08:48,851 - INFO - Stats preloader initialized
2025-07-21 11:08:48,851 - INFO - Starting stats data preloading
2025-07-21 11:08:48,851 - INFO - Started stats data preloading
2025-07-21 11:08:48,851 - INFO - Loading data using optimized functions
2025-07-21 11:08:48,852 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:08:48,852 - INFO - DB Operation: {"timestamp": "2025-07-21 11:08:48", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:08:48,857 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:08:48,858 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:08:48,858 - INFO - DB Operation: {"timestamp": "2025-07-21 11:08:48", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:08:48,859 - INFO - Preloaded summary data
2025-07-21 11:08:48,860 - INFO - Preloaded game history (0 records)
2025-07-21 11:08:48,860 - INFO - Preloaded wallet data
2025-07-21 11:08:48,861 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 11:08:48,862 - INFO - Saved 8 items to persistent cache
2025-07-21 11:08:49,514 - INFO - Stats performance monitor initialized
2025-07-21 11:08:49,544 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-21 11:08:50,056 - INFO - generate_background: 0.0071s
2025-07-21 11:08:50,129 - INFO - draw_background: 0.0813s
2025-07-21 11:08:50,952 - INFO - Background thread started
2025-07-21 11:08:50,952 - INFO - Hybrid database integration initialized
2025-07-21 11:08:51,087 - INFO - draw_stats_page: 1.0392s
2025-07-21 11:08:51,105 - INFO - draw_background: 0.0016s
2025-07-21 11:08:51,221 - INFO - draw_stats_page: 0.1163s
2025-07-21 11:08:51,249 - INFO - draw_background: 0.0010s
2025-07-21 11:08:51,330 - INFO - draw_stats_page: 0.0825s
2025-07-21 11:08:51,359 - INFO - draw_background: 0.0009s
2025-07-21 11:08:51,435 - INFO - draw_stats_page: 0.0778s
2025-07-21 11:08:51,462 - INFO - draw_background: 0.0009s
2025-07-21 11:08:51,464 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:51,542 - INFO - draw_stats_page: 0.0814s
2025-07-21 11:08:51,549 - INFO - Admin button added to stats page
2025-07-21 11:08:51,570 - INFO - draw_background: 0.0009s
2025-07-21 11:08:51,649 - INFO - draw_stats_page: 0.0807s
2025-07-21 11:08:51,677 - INFO - draw_background: 0.0010s
2025-07-21 11:08:51,747 - INFO - draw_stats_page: 0.0714s
2025-07-21 11:08:51,842 - INFO - draw_background: 0.0025s
2025-07-21 11:08:51,984 - INFO - draw_stats_page: 0.1425s
2025-07-21 11:08:52,024 - INFO - draw_background: 0.0011s
2025-07-21 11:08:52,157 - INFO - draw_stats_page: 0.1349s
2025-07-21 11:08:52,195 - INFO - draw_background: 0.0025s
2025-07-21 11:08:52,198 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:52,285 - INFO - draw_stats_page: 0.0927s
2025-07-21 11:08:52,306 - INFO - draw_background: 0.0010s
2025-07-21 11:08:52,354 - INFO - draw_stats_page: 0.0487s
2025-07-21 11:08:52,444 - INFO - draw_background: 0.0025s
2025-07-21 11:08:52,567 - INFO - draw_stats_page: 0.1241s
2025-07-21 11:08:52,604 - INFO - draw_background: 0.0025s
2025-07-21 11:08:52,736 - INFO - draw_stats_page: 0.1335s
2025-07-21 11:08:52,777 - INFO - draw_background: 0.0034s
2025-07-21 11:08:52,916 - INFO - draw_stats_page: 0.1424s
2025-07-21 11:08:52,955 - INFO - draw_background: 0.0027s
2025-07-21 11:08:52,957 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:53,089 - INFO - draw_stats_page: 0.1377s
2025-07-21 11:08:53,129 - INFO - draw_background: 0.0030s
2025-07-21 11:08:53,241 - INFO - draw_stats_page: 0.1141s
2025-07-21 11:08:53,267 - INFO - draw_background: 0.0020s
2025-07-21 11:08:53,332 - INFO - draw_stats_page: 0.0675s
2025-07-21 11:08:53,419 - INFO - draw_background: 0.0000s
2025-07-21 11:08:53,530 - INFO - draw_stats_page: 0.1110s
2025-07-21 11:08:53,569 - INFO - draw_background: 0.0030s
2025-07-21 11:08:53,703 - INFO - draw_stats_page: 0.1365s
2025-07-21 11:08:53,741 - INFO - draw_background: 0.0025s
2025-07-21 11:08:53,744 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:53,872 - INFO - draw_stats_page: 0.1332s
2025-07-21 11:08:53,911 - INFO - draw_background: 0.0026s
2025-07-21 11:08:53,983 - INFO - draw_stats_page: 0.0751s
2025-07-21 11:08:54,022 - INFO - draw_background: 0.0012s
2025-07-21 11:08:54,059 - INFO - draw_stats_page: 0.0382s
2025-07-21 11:08:54,163 - INFO - draw_background: 0.0009s
2025-07-21 11:08:54,199 - INFO - draw_stats_page: 0.0364s
2025-07-21 11:08:54,307 - INFO - draw_background: 0.0019s
2025-07-21 11:08:54,396 - INFO - draw_stats_page: 0.0904s
2025-07-21 11:08:54,469 - INFO - draw_background: 0.0009s
2025-07-21 11:08:54,470 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:54,555 - INFO - draw_stats_page: 0.0878s
2025-07-21 11:08:54,627 - INFO - draw_background: 0.0020s
2025-07-21 11:08:54,711 - INFO - draw_stats_page: 0.0860s
2025-07-21 11:08:54,769 - INFO - draw_background: 0.0000s
2025-07-21 11:08:54,817 - INFO - draw_stats_page: 0.0478s
2025-07-21 11:08:54,924 - INFO - draw_background: 0.0009s
2025-07-21 11:08:55,042 - INFO - draw_stats_page: 0.1190s
2025-07-21 11:08:55,057 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:08:55,057 - WARNING - Failed to connect to RethinkDB
2025-07-21 11:08:55,141 - INFO - draw_background: 0.0025s
2025-07-21 11:08:55,167 - INFO - draw_stats_page: 0.0286s
2025-07-21 11:08:55,259 - INFO - draw_background: 0.0019s
2025-07-21 11:08:55,261 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:55,269 - INFO - draw_stats_page: 0.0121s
2025-07-21 11:08:55,399 - INFO - draw_background: 0.0030s
2025-07-21 11:08:55,413 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:08:55,555 - INFO - draw_background: 0.0015s
2025-07-21 11:08:55,570 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:08:55,667 - INFO - draw_background: 0.0031s
2025-07-21 11:08:55,681 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:08:55,817 - INFO - draw_background: 0.0020s
2025-07-21 11:08:55,830 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:08:55,930 - INFO - draw_background: 0.0029s
2025-07-21 11:08:55,934 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:55,950 - INFO - draw_stats_page: 0.0221s
2025-07-21 11:08:56,052 - INFO - draw_background: 0.0037s
2025-07-21 11:08:56,068 - INFO - draw_stats_page: 0.0192s
2025-07-21 11:08:56,146 - INFO - draw_background: 0.0020s
2025-07-21 11:08:56,151 - INFO - draw_stats_page: 0.0069s
2025-07-21 11:08:56,287 - INFO - draw_background: 0.0020s
2025-07-21 11:08:56,302 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:08:56,404 - INFO - draw_background: 0.0015s
2025-07-21 11:08:56,420 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:08:56,514 - INFO - draw_background: 0.0035s
2025-07-21 11:08:56,525 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:56,544 - INFO - draw_stats_page: 0.0332s
2025-07-21 11:08:56,681 - INFO - draw_background: 0.0030s
2025-07-21 11:08:56,694 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:08:56,780 - INFO - draw_background: 0.0010s
2025-07-21 11:08:56,788 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:08:56,914 - INFO - draw_background: 0.0016s
2025-07-21 11:08:56,929 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:08:57,009 - INFO - draw_background: 0.0010s
2025-07-21 11:08:57,019 - INFO - draw_stats_page: 0.0112s
2025-07-21 11:08:57,146 - INFO - draw_background: 0.0026s
2025-07-21 11:08:57,149 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:57,166 - INFO - draw_stats_page: 0.0210s
2025-07-21 11:08:57,264 - INFO - draw_background: 0.0025s
2025-07-21 11:08:57,278 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:08:57,381 - INFO - draw_background: 0.0031s
2025-07-21 11:08:57,396 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:08:57,488 - INFO - draw_background: 0.0020s
2025-07-21 11:08:57,504 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:08:57,599 - INFO - draw_background: 0.0030s
2025-07-21 11:08:57,617 - INFO - draw_stats_page: 0.0213s
2025-07-21 11:08:57,731 - INFO - draw_background: 0.0030s
2025-07-21 11:08:57,734 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:57,750 - INFO - draw_stats_page: 0.0220s
2025-07-21 11:08:57,834 - INFO - draw_background: 0.0017s
2025-07-21 11:08:57,841 - INFO - draw_stats_page: 0.0086s
2025-07-21 11:08:57,967 - INFO - draw_background: 0.0020s
2025-07-21 11:08:57,982 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:08:58,061 - INFO - draw_background: 0.0020s
2025-07-21 11:08:58,068 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:08:58,193 - INFO - draw_background: 0.0009s
2025-07-21 11:08:58,197 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:08:58,326 - INFO - draw_background: 0.0009s
2025-07-21 11:08:58,328 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:58,335 - INFO - draw_stats_page: 0.0095s
2025-07-21 11:08:58,460 - INFO - draw_background: 0.0031s
2025-07-21 11:08:58,472 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:08:58,592 - INFO - draw_background: 0.0025s
2025-07-21 11:08:58,606 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:08:58,727 - INFO - draw_background: 0.0026s
2025-07-21 11:08:58,744 - INFO - draw_stats_page: 0.0198s
2025-07-21 11:08:58,858 - INFO - draw_background: 0.0009s
2025-07-21 11:08:58,867 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:08:58,992 - INFO - draw_background: 0.0005s
2025-07-21 11:08:58,994 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:59,001 - INFO - draw_stats_page: 0.0107s
2025-07-21 11:08:59,127 - INFO - draw_background: 0.0021s
2025-07-21 11:08:59,143 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:08:59,264 - INFO - draw_background: 0.0028s
2025-07-21 11:08:59,279 - INFO - draw_stats_page: 0.0179s
2025-07-21 11:08:59,397 - INFO - draw_background: 0.0020s
2025-07-21 11:08:59,412 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:08:59,530 - INFO - draw_background: 0.0019s
2025-07-21 11:08:59,538 - INFO - draw_stats_page: 0.0100s
2025-07-21 11:08:59,662 - INFO - draw_background: 0.0011s
2025-07-21 11:08:59,664 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:08:59,672 - INFO - draw_stats_page: 0.0103s
2025-07-21 11:08:59,796 - INFO - draw_background: 0.0010s
2025-07-21 11:08:59,806 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:08:59,865 - INFO - draw_background: 0.0026s
2025-07-21 11:08:59,879 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:00,205 - INFO - draw_background: 0.0020s
2025-07-21 11:09:00,265 - INFO - draw_stats_page: 0.0615s
2025-07-21 11:09:00,330 - INFO - draw_background: 0.0020s
2025-07-21 11:09:00,338 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:00,459 - INFO - draw_background: 0.0020s
2025-07-21 11:09:00,461 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:00,466 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:00,592 - INFO - draw_background: 0.0010s
2025-07-21 11:09:00,601 - INFO - draw_stats_page: 0.0096s
2025-07-21 11:09:00,726 - INFO - draw_background: 0.0020s
2025-07-21 11:09:00,731 - INFO - draw_stats_page: 0.0065s
2025-07-21 11:09:00,859 - INFO - draw_background: 0.0000s
2025-07-21 11:09:00,863 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:09:00,993 - INFO - draw_background: 0.0010s
2025-07-21 11:09:00,996 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:01,125 - INFO - draw_background: 0.0010s
2025-07-21 11:09:01,126 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:01,129 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:01,259 - INFO - draw_background: 0.0010s
2025-07-21 11:09:01,268 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:01,392 - INFO - draw_background: 0.0011s
2025-07-21 11:09:01,400 - INFO - draw_stats_page: 0.0086s
2025-07-21 11:09:01,526 - INFO - draw_background: 0.0010s
2025-07-21 11:09:01,535 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:01,659 - INFO - draw_background: 0.0009s
2025-07-21 11:09:01,668 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:01,794 - INFO - draw_background: 0.0016s
2025-07-21 11:09:01,794 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:01,804 - INFO - draw_stats_page: 0.0118s
2025-07-21 11:09:01,926 - INFO - draw_background: 0.0010s
2025-07-21 11:09:01,934 - INFO - draw_stats_page: 0.0092s
2025-07-21 11:09:02,060 - INFO - draw_background: 0.0010s
2025-07-21 11:09:02,069 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:02,194 - INFO - draw_background: 0.0027s
2025-07-21 11:09:02,202 - INFO - draw_stats_page: 0.0112s
2025-07-21 11:09:02,326 - INFO - draw_background: 0.0010s
2025-07-21 11:09:02,335 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:02,462 - INFO - draw_background: 0.0035s
2025-07-21 11:09:02,464 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:02,475 - INFO - draw_stats_page: 0.0162s
2025-07-21 11:09:02,594 - INFO - draw_background: 0.0015s
2025-07-21 11:09:02,601 - INFO - draw_stats_page: 0.0081s
2025-07-21 11:09:02,735 - INFO - draw_background: 0.0010s
2025-07-21 11:09:02,740 - INFO - draw_stats_page: 0.0062s
2025-07-21 11:09:02,867 - INFO - draw_background: 0.0021s
2025-07-21 11:09:02,882 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:03,001 - INFO - draw_background: 0.0026s
2025-07-21 11:09:03,029 - INFO - draw_stats_page: 0.0303s
2025-07-21 11:09:03,065 - INFO - draw_background: 0.0013s
2025-07-21 11:09:03,067 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:03,075 - INFO - draw_stats_page: 0.0115s
2025-07-21 11:09:03,199 - INFO - draw_background: 0.0010s
2025-07-21 11:09:03,208 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:03,332 - INFO - draw_background: 0.0011s
2025-07-21 11:09:03,340 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:03,468 - INFO - draw_background: 0.0020s
2025-07-21 11:09:03,482 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:09:03,601 - INFO - draw_background: 0.0026s
2025-07-21 11:09:03,622 - INFO - draw_stats_page: 0.0230s
2025-07-21 11:09:03,735 - INFO - draw_background: 0.0026s
2025-07-21 11:09:03,737 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:03,751 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:03,869 - INFO - draw_background: 0.0020s
2025-07-21 11:09:03,882 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:04,002 - INFO - draw_background: 0.0026s
2025-07-21 11:09:04,016 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:04,134 - INFO - draw_background: 0.0025s
2025-07-21 11:09:04,148 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:04,267 - INFO - draw_background: 0.0020s
2025-07-21 11:09:04,281 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:04,399 - INFO - draw_background: 0.0019s
2025-07-21 11:09:04,402 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:04,416 - INFO - draw_stats_page: 0.0190s
2025-07-21 11:09:04,533 - INFO - draw_background: 0.0020s
2025-07-21 11:09:04,548 - INFO - draw_stats_page: 0.0170s
2025-07-21 11:09:04,666 - INFO - draw_background: 0.0010s
2025-07-21 11:09:04,674 - INFO - draw_stats_page: 0.0092s
2025-07-21 11:09:04,800 - INFO - draw_background: 0.0020s
2025-07-21 11:09:04,815 - INFO - draw_stats_page: 0.0160s
2025-07-21 11:09:04,935 - INFO - draw_background: 0.0026s
2025-07-21 11:09:04,949 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:05,067 - INFO - draw_background: 0.0029s
2025-07-21 11:09:05,069 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:05,085 - INFO - draw_stats_page: 0.0213s
2025-07-21 11:09:05,201 - INFO - draw_background: 0.0026s
2025-07-21 11:09:05,225 - INFO - draw_stats_page: 0.0263s
2025-07-21 11:09:05,336 - INFO - draw_background: 0.0026s
2025-07-21 11:09:05,351 - INFO - draw_stats_page: 0.0190s
2025-07-21 11:09:05,467 - INFO - draw_background: 0.0028s
2025-07-21 11:09:05,481 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:05,599 - INFO - draw_background: 0.0020s
2025-07-21 11:09:05,615 - INFO - draw_stats_page: 0.0174s
2025-07-21 11:09:05,734 - INFO - draw_background: 0.0028s
2025-07-21 11:09:05,737 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:05,754 - INFO - draw_stats_page: 0.0218s
2025-07-21 11:09:05,867 - INFO - draw_background: 0.0020s
2025-07-21 11:09:05,881 - INFO - draw_stats_page: 0.0166s
2025-07-21 11:09:06,001 - INFO - draw_background: 0.0011s
2025-07-21 11:09:06,015 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:06,136 - INFO - draw_background: 0.0018s
2025-07-21 11:09:06,151 - INFO - draw_stats_page: 0.0172s
2025-07-21 11:09:06,272 - INFO - draw_background: 0.0026s
2025-07-21 11:09:06,287 - INFO - draw_stats_page: 0.0192s
2025-07-21 11:09:06,403 - INFO - draw_background: 0.0010s
2025-07-21 11:09:06,404 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:06,413 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:06,536 - INFO - draw_background: 0.0010s
2025-07-21 11:09:06,544 - INFO - draw_stats_page: 0.0093s
2025-07-21 11:09:06,671 - INFO - draw_background: 0.0009s
2025-07-21 11:09:06,680 - INFO - draw_stats_page: 0.0087s
2025-07-21 11:09:06,806 - INFO - draw_background: 0.0025s
2025-07-21 11:09:06,822 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:06,940 - INFO - draw_background: 0.0020s
2025-07-21 11:09:06,955 - INFO - draw_stats_page: 0.0185s
2025-07-21 11:09:07,073 - INFO - draw_background: 0.0036s
2025-07-21 11:09:07,076 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:07,089 - INFO - draw_stats_page: 0.0195s
2025-07-21 11:09:07,202 - INFO - draw_background: 0.0017s
2025-07-21 11:09:07,218 - INFO - draw_stats_page: 0.0176s
2025-07-21 11:09:07,338 - INFO - draw_background: 0.0031s
2025-07-21 11:09:07,356 - INFO - draw_stats_page: 0.0210s
2025-07-21 11:09:07,470 - INFO - draw_background: 0.0010s
2025-07-21 11:09:07,478 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:07,604 - INFO - draw_background: 0.0015s
2025-07-21 11:09:07,612 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:07,739 - INFO - draw_background: 0.0020s
2025-07-21 11:09:07,742 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:07,757 - INFO - draw_stats_page: 0.0196s
2025-07-21 11:09:07,872 - INFO - draw_background: 0.0025s
2025-07-21 11:09:07,886 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:08,005 - INFO - draw_background: 0.0024s
2025-07-21 11:09:08,019 - INFO - draw_stats_page: 0.0166s
2025-07-21 11:09:08,140 - INFO - draw_background: 0.0029s
2025-07-21 11:09:08,154 - INFO - draw_stats_page: 0.0182s
2025-07-21 11:09:08,275 - INFO - draw_background: 0.0026s
2025-07-21 11:09:08,288 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:08,407 - INFO - draw_background: 0.0020s
2025-07-21 11:09:08,410 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:08,425 - INFO - draw_stats_page: 0.0205s
2025-07-21 11:09:08,541 - INFO - draw_background: 0.0035s
2025-07-21 11:09:08,568 - INFO - draw_stats_page: 0.0304s
2025-07-21 11:09:08,675 - INFO - draw_background: 0.0015s
2025-07-21 11:09:08,687 - INFO - draw_stats_page: 0.0137s
2025-07-21 11:09:08,811 - INFO - draw_background: 0.0020s
2025-07-21 11:09:08,827 - INFO - draw_stats_page: 0.0203s
2025-07-21 11:09:08,945 - INFO - draw_background: 0.0030s
2025-07-21 11:09:08,961 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:09,108 - INFO - draw_background: 0.0009s
2025-07-21 11:09:09,110 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:09,118 - INFO - draw_stats_page: 0.0109s
2025-07-21 11:09:09,243 - INFO - draw_background: 0.0020s
2025-07-21 11:09:09,258 - INFO - draw_stats_page: 0.0170s
2025-07-21 11:09:09,375 - INFO - draw_background: 0.0009s
2025-07-21 11:09:09,382 - INFO - draw_stats_page: 0.0084s
2025-07-21 11:09:09,509 - INFO - draw_background: 0.0030s
2025-07-21 11:09:09,531 - INFO - draw_stats_page: 0.0243s
2025-07-21 11:09:09,640 - INFO - draw_background: 0.0020s
2025-07-21 11:09:09,654 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:09:09,774 - INFO - draw_background: 0.0025s
2025-07-21 11:09:09,777 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:09,791 - INFO - draw_stats_page: 0.0191s
2025-07-21 11:09:09,907 - INFO - draw_background: 0.0021s
2025-07-21 11:09:09,944 - INFO - draw_stats_page: 0.0384s
2025-07-21 11:09:10,023 - INFO - draw_background: 0.0016s
2025-07-21 11:09:10,031 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:10,152 - INFO - draw_background: 0.0017s
2025-07-21 11:09:10,167 - INFO - draw_stats_page: 0.0154s
2025-07-21 11:09:10,287 - INFO - draw_background: 0.0030s
2025-07-21 11:09:10,312 - INFO - draw_stats_page: 0.0280s
2025-07-21 11:09:10,425 - INFO - draw_background: 0.0026s
2025-07-21 11:09:10,428 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:10,442 - INFO - draw_stats_page: 0.0203s
2025-07-21 11:09:10,557 - INFO - draw_background: 0.0028s
2025-07-21 11:09:10,572 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:10,690 - INFO - draw_background: 0.0010s
2025-07-21 11:09:10,702 - INFO - draw_stats_page: 0.0133s
2025-07-21 11:09:10,825 - INFO - draw_background: 0.0027s
2025-07-21 11:09:10,839 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:10,958 - INFO - draw_background: 0.0019s
2025-07-21 11:09:10,974 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:09:11,092 - INFO - draw_background: 0.0027s
2025-07-21 11:09:11,096 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:11,111 - INFO - draw_stats_page: 0.0216s
2025-07-21 11:09:11,227 - INFO - draw_background: 0.0029s
2025-07-21 11:09:11,241 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:11,358 - INFO - draw_background: 0.0020s
2025-07-21 11:09:11,371 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:11,492 - INFO - draw_background: 0.0025s
2025-07-21 11:09:11,506 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:11,626 - INFO - draw_background: 0.0026s
2025-07-21 11:09:11,641 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:11,758 - INFO - draw_background: 0.0020s
2025-07-21 11:09:11,766 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:11,780 - INFO - draw_stats_page: 0.0233s
2025-07-21 11:09:12,034 - INFO - draw_background: 0.0015s
2025-07-21 11:09:12,041 - INFO - draw_stats_page: 0.0081s
2025-07-21 11:09:12,195 - INFO - draw_background: 0.0010s
2025-07-21 11:09:12,199 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:12,328 - INFO - draw_background: 0.0010s
2025-07-21 11:09:12,333 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:12,465 - INFO - draw_background: 0.0036s
2025-07-21 11:09:12,490 - INFO - draw_stats_page: 0.0279s
2025-07-21 11:09:12,602 - INFO - draw_background: 0.0025s
2025-07-21 11:09:12,606 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:12,617 - INFO - draw_stats_page: 0.0172s
2025-07-21 11:09:12,736 - INFO - draw_background: 0.0010s
2025-07-21 11:09:12,744 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:12,869 - INFO - draw_background: 0.0009s
2025-07-21 11:09:12,877 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:13,002 - INFO - draw_background: 0.0009s
2025-07-21 11:09:13,010 - INFO - draw_stats_page: 0.0095s
2025-07-21 11:09:13,136 - INFO - draw_background: 0.0011s
2025-07-21 11:09:13,145 - INFO - draw_stats_page: 0.0099s
2025-07-21 11:09:13,271 - INFO - draw_background: 0.0026s
2025-07-21 11:09:13,273 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:13,289 - INFO - draw_stats_page: 0.0203s
2025-07-21 11:09:13,405 - INFO - draw_background: 0.0026s
2025-07-21 11:09:13,425 - INFO - draw_stats_page: 0.0230s
2025-07-21 11:09:13,537 - INFO - draw_background: 0.0020s
2025-07-21 11:09:13,553 - INFO - draw_stats_page: 0.0180s
2025-07-21 11:09:13,675 - INFO - draw_background: 0.0071s
2025-07-21 11:09:13,694 - INFO - draw_stats_page: 0.0261s
2025-07-21 11:09:13,803 - INFO - draw_background: 0.0010s
2025-07-21 11:09:13,812 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:13,936 - INFO - draw_background: 0.0000s
2025-07-21 11:09:13,937 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:13,941 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:09:14,069 - INFO - draw_background: 0.0010s
2025-07-21 11:09:14,073 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:14,202 - INFO - draw_background: 0.0010s
2025-07-21 11:09:14,205 - INFO - draw_stats_page: 0.0046s
2025-07-21 11:09:14,334 - INFO - draw_background: 0.0000s
2025-07-21 11:09:14,338 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:14,469 - INFO - draw_background: 0.0009s
2025-07-21 11:09:14,478 - INFO - draw_stats_page: 0.0099s
2025-07-21 11:09:14,609 - INFO - draw_background: 0.0009s
2025-07-21 11:09:14,611 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:14,618 - INFO - draw_stats_page: 0.0090s
2025-07-21 11:09:14,745 - INFO - draw_background: 0.0010s
2025-07-21 11:09:14,748 - INFO - draw_stats_page: 0.0040s
2025-07-21 11:09:14,880 - INFO - draw_background: 0.0020s
2025-07-21 11:09:14,894 - INFO - draw_stats_page: 0.0162s
2025-07-21 11:09:15,018 - INFO - draw_background: 0.0020s
2025-07-21 11:09:15,028 - INFO - draw_stats_page: 0.0122s
2025-07-21 11:09:15,147 - INFO - draw_background: 0.0020s
2025-07-21 11:09:15,161 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:15,281 - INFO - draw_background: 0.0025s
2025-07-21 11:09:15,287 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:15,304 - INFO - draw_stats_page: 0.0255s
2025-07-21 11:09:15,414 - INFO - draw_background: 0.0026s
2025-07-21 11:09:15,429 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:15,547 - INFO - draw_background: 0.0020s
2025-07-21 11:09:15,561 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:15,681 - INFO - draw_background: 0.0025s
2025-07-21 11:09:15,696 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:09:15,812 - INFO - draw_background: 0.0000s
2025-07-21 11:09:15,816 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:15,946 - INFO - draw_background: 0.0000s
2025-07-21 11:09:15,946 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:15,950 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:16,080 - INFO - draw_background: 0.0010s
2025-07-21 11:09:16,089 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:16,213 - INFO - draw_background: 0.0010s
2025-07-21 11:09:16,222 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:16,346 - INFO - draw_background: 0.0010s
2025-07-21 11:09:16,350 - INFO - draw_stats_page: 0.0049s
2025-07-21 11:09:16,479 - INFO - draw_background: 0.0009s
2025-07-21 11:09:16,483 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:16,615 - INFO - draw_background: 0.0015s
2025-07-21 11:09:16,617 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:16,629 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:16,748 - INFO - draw_background: 0.0019s
2025-07-21 11:09:16,765 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:09:16,882 - INFO - draw_background: 0.0015s
2025-07-21 11:09:16,894 - INFO - draw_stats_page: 0.0142s
2025-07-21 11:09:17,015 - INFO - draw_background: 0.0026s
2025-07-21 11:09:17,029 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:17,148 - INFO - draw_background: 0.0020s
2025-07-21 11:09:17,167 - INFO - draw_stats_page: 0.0200s
2025-07-21 11:09:17,281 - INFO - draw_background: 0.0025s
2025-07-21 11:09:17,286 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:17,301 - INFO - draw_stats_page: 0.0219s
2025-07-21 11:09:17,415 - INFO - draw_background: 0.0025s
2025-07-21 11:09:17,429 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:17,549 - INFO - draw_background: 0.0020s
2025-07-21 11:09:17,562 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:09:17,684 - INFO - draw_background: 0.0027s
2025-07-21 11:09:17,699 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:09:17,817 - INFO - draw_background: 0.0020s
2025-07-21 11:09:17,831 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:17,951 - INFO - draw_background: 0.0030s
2025-07-21 11:09:17,952 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:17,968 - INFO - draw_stats_page: 0.0193s
2025-07-21 11:09:18,082 - INFO - draw_background: 0.0010s
2025-07-21 11:09:18,090 - INFO - draw_stats_page: 0.0095s
2025-07-21 11:09:18,216 - INFO - draw_background: 0.0026s
2025-07-21 11:09:18,230 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:18,285 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:09:18,347 - INFO - draw_background: 0.0010s
2025-07-21 11:09:18,357 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:18,481 - INFO - draw_background: 0.0015s
2025-07-21 11:09:18,489 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:18,647 - INFO - draw_background: 0.0091s
2025-07-21 11:09:18,649 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:18,655 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:18,747 - INFO - draw_background: 0.0000s
2025-07-21 11:09:18,750 - INFO - draw_stats_page: 0.0040s
2025-07-21 11:09:18,882 - INFO - draw_background: 0.0010s
2025-07-21 11:09:18,889 - INFO - draw_stats_page: 0.0075s
2025-07-21 11:09:19,014 - INFO - draw_background: 0.0015s
2025-07-21 11:09:19,019 - INFO - draw_stats_page: 0.0066s
2025-07-21 11:09:19,146 - INFO - draw_background: 0.0010s
2025-07-21 11:09:19,151 - INFO - draw_stats_page: 0.0065s
2025-07-21 11:09:19,279 - INFO - draw_background: 0.0010s
2025-07-21 11:09:19,279 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:19,285 - INFO - draw_stats_page: 0.0071s
2025-07-21 11:09:19,412 - INFO - draw_background: 0.0010s
2025-07-21 11:09:19,418 - INFO - draw_stats_page: 0.0065s
2025-07-21 11:09:19,545 - INFO - draw_background: 0.0010s
2025-07-21 11:09:19,551 - INFO - draw_stats_page: 0.0075s
2025-07-21 11:09:19,678 - INFO - draw_background: 0.0010s
2025-07-21 11:09:19,681 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:19,811 - INFO - draw_background: 0.0005s
2025-07-21 11:09:19,816 - INFO - draw_stats_page: 0.0061s
2025-07-21 11:09:19,944 - INFO - draw_background: 0.0005s
2025-07-21 11:09:19,945 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:19,952 - INFO - draw_stats_page: 0.0081s
2025-07-21 11:09:20,079 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,083 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:09:20,212 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,216 - INFO - draw_stats_page: 0.0051s
2025-07-21 11:09:20,345 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,349 - INFO - draw_stats_page: 0.0040s
2025-07-21 11:09:20,478 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,483 - INFO - draw_stats_page: 0.0061s
2025-07-21 11:09:20,614 - INFO - draw_background: 0.0005s
2025-07-21 11:09:20,615 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:20,618 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:20,747 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,751 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:20,879 - INFO - draw_background: 0.0010s
2025-07-21 11:09:20,884 - INFO - draw_stats_page: 0.0060s
2025-07-21 11:09:21,013 - INFO - draw_background: 0.0010s
2025-07-21 11:09:21,016 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:09:21,146 - INFO - draw_background: 0.0010s
2025-07-21 11:09:21,150 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:21,279 - INFO - draw_background: 0.0010s
2025-07-21 11:09:21,280 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:21,284 - INFO - draw_stats_page: 0.0060s
2025-07-21 11:09:21,413 - INFO - draw_background: 0.0000s
2025-07-21 11:09:21,418 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:09:21,547 - INFO - draw_background: 0.0010s
2025-07-21 11:09:21,551 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:09:21,681 - INFO - draw_background: 0.0015s
2025-07-21 11:09:21,691 - INFO - draw_stats_page: 0.0116s
2025-07-21 11:09:21,815 - INFO - draw_background: 0.0019s
2025-07-21 11:09:21,826 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:21,948 - INFO - draw_background: 0.0030s
2025-07-21 11:09:21,951 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:21,965 - INFO - draw_stats_page: 0.0184s
2025-07-21 11:09:22,079 - INFO - draw_background: 0.0000s
2025-07-21 11:09:22,084 - INFO - draw_stats_page: 0.0051s
2025-07-21 11:09:22,214 - INFO - draw_background: 0.0026s
2025-07-21 11:09:22,229 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:09:22,347 - INFO - draw_background: 0.0020s
2025-07-21 11:09:22,362 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:22,481 - INFO - draw_background: 0.0019s
2025-07-21 11:09:22,495 - INFO - draw_stats_page: 0.0162s
2025-07-21 11:09:22,614 - INFO - draw_background: 0.0025s
2025-07-21 11:09:22,617 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:22,631 - INFO - draw_stats_page: 0.0176s
2025-07-21 11:09:22,749 - INFO - draw_background: 0.0020s
2025-07-21 11:09:22,763 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:22,881 - INFO - draw_background: 0.0025s
2025-07-21 11:09:22,895 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:23,016 - INFO - draw_background: 0.0019s
2025-07-21 11:09:23,030 - INFO - draw_stats_page: 0.0151s
2025-07-21 11:09:23,148 - INFO - draw_background: 0.0020s
2025-07-21 11:09:23,168 - INFO - draw_stats_page: 0.0228s
2025-07-21 11:09:23,279 - INFO - draw_background: 0.0009s
2025-07-21 11:09:23,281 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:23,289 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:23,415 - INFO - draw_background: 0.0026s
2025-07-21 11:09:23,428 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:23,547 - INFO - draw_background: 0.0021s
2025-07-21 11:09:23,562 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:23,681 - INFO - draw_background: 0.0025s
2025-07-21 11:09:23,695 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:23,813 - INFO - draw_background: 0.0026s
2025-07-21 11:09:23,827 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:23,947 - INFO - draw_background: 0.0019s
2025-07-21 11:09:23,949 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:23,962 - INFO - draw_stats_page: 0.0184s
2025-07-21 11:09:24,080 - INFO - draw_background: 0.0019s
2025-07-21 11:09:24,094 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:24,215 - INFO - draw_background: 0.0026s
2025-07-21 11:09:24,228 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:24,347 - INFO - draw_background: 0.0019s
2025-07-21 11:09:24,361 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:24,479 - INFO - draw_background: 0.0010s
2025-07-21 11:09:24,491 - INFO - draw_stats_page: 0.0127s
2025-07-21 11:09:24,617 - INFO - draw_background: 0.0019s
2025-07-21 11:09:24,619 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:24,632 - INFO - draw_stats_page: 0.0186s
2025-07-21 11:09:24,749 - INFO - draw_background: 0.0020s
2025-07-21 11:09:24,762 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:24,882 - INFO - draw_background: 0.0027s
2025-07-21 11:09:24,895 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:25,014 - INFO - draw_background: 0.0027s
2025-07-21 11:09:25,027 - INFO - draw_stats_page: 0.0150s
2025-07-21 11:09:25,147 - INFO - draw_background: 0.0021s
2025-07-21 11:09:25,162 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:25,281 - INFO - draw_background: 0.0019s
2025-07-21 11:09:25,282 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:25,296 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:25,411 - INFO - draw_background: 0.0009s
2025-07-21 11:09:25,422 - INFO - draw_stats_page: 0.0122s
2025-07-21 11:09:25,545 - INFO - draw_background: 0.0016s
2025-07-21 11:09:25,555 - INFO - draw_stats_page: 0.0113s
2025-07-21 11:09:25,681 - INFO - draw_background: 0.0020s
2025-07-21 11:09:25,690 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:25,812 - INFO - draw_background: 0.0014s
2025-07-21 11:09:25,827 - INFO - draw_stats_page: 0.0162s
2025-07-21 11:09:25,948 - INFO - draw_background: 0.0020s
2025-07-21 11:09:25,951 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:25,964 - INFO - draw_stats_page: 0.0185s
2025-07-21 11:09:26,079 - INFO - draw_background: 0.0021s
2025-07-21 11:09:26,094 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:26,214 - INFO - draw_background: 0.0029s
2025-07-21 11:09:26,229 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:09:26,346 - INFO - draw_background: 0.0018s
2025-07-21 11:09:26,361 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:26,480 - INFO - draw_background: 0.0019s
2025-07-21 11:09:26,495 - INFO - draw_stats_page: 0.0172s
2025-07-21 11:09:26,615 - INFO - draw_background: 0.0026s
2025-07-21 11:09:26,618 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:26,631 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:26,750 - INFO - draw_background: 0.0019s
2025-07-21 11:09:26,764 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:26,884 - INFO - draw_background: 0.0025s
2025-07-21 11:09:26,896 - INFO - draw_stats_page: 0.0149s
2025-07-21 11:09:27,017 - INFO - draw_background: 0.0021s
2025-07-21 11:09:27,031 - INFO - draw_stats_page: 0.0156s
2025-07-21 11:09:27,151 - INFO - draw_background: 0.0027s
2025-07-21 11:09:27,165 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:27,284 - INFO - draw_background: 0.0025s
2025-07-21 11:09:27,287 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:27,301 - INFO - draw_stats_page: 0.0187s
2025-07-21 11:09:27,417 - INFO - draw_background: 0.0021s
2025-07-21 11:09:27,431 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:27,549 - INFO - draw_background: 0.0019s
2025-07-21 11:09:27,564 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:27,681 - INFO - draw_background: 0.0010s
2025-07-21 11:09:27,689 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:27,816 - INFO - draw_background: 0.0021s
2025-07-21 11:09:27,831 - INFO - draw_stats_page: 0.0152s
2025-07-21 11:09:27,949 - INFO - draw_background: 0.0021s
2025-07-21 11:09:27,953 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:27,967 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:09:28,086 - INFO - draw_background: 0.0018s
2025-07-21 11:09:28,099 - INFO - draw_stats_page: 0.0150s
2025-07-21 11:09:28,219 - INFO - draw_background: 0.0030s
2025-07-21 11:09:28,232 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:28,352 - INFO - draw_background: 0.0026s
2025-07-21 11:09:28,365 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:28,485 - INFO - draw_background: 0.0009s
2025-07-21 11:09:28,492 - INFO - draw_stats_page: 0.0085s
2025-07-21 11:09:28,618 - INFO - draw_background: 0.0030s
2025-07-21 11:09:28,621 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:28,629 - INFO - draw_stats_page: 0.0142s
2025-07-21 11:09:28,751 - INFO - draw_background: 0.0019s
2025-07-21 11:09:28,765 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:28,884 - INFO - draw_background: 0.0025s
2025-07-21 11:09:28,898 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:29,017 - INFO - draw_background: 0.0020s
2025-07-21 11:09:29,030 - INFO - draw_stats_page: 0.0152s
2025-07-21 11:09:29,151 - INFO - draw_background: 0.0026s
2025-07-21 11:09:29,164 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:29,285 - INFO - draw_background: 0.0026s
2025-07-21 11:09:29,288 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:29,302 - INFO - draw_stats_page: 0.0182s
2025-07-21 11:09:29,416 - INFO - draw_background: 0.0021s
2025-07-21 11:09:29,431 - INFO - draw_stats_page: 0.0162s
2025-07-21 11:09:29,552 - INFO - draw_background: 0.0026s
2025-07-21 11:09:29,565 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:29,684 - INFO - draw_background: 0.0026s
2025-07-21 11:09:29,697 - INFO - draw_stats_page: 0.0149s
2025-07-21 11:09:29,816 - INFO - draw_background: 0.0010s
2025-07-21 11:09:29,826 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:29,948 - INFO - draw_background: 0.0020s
2025-07-21 11:09:29,951 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:29,964 - INFO - draw_stats_page: 0.0174s
2025-07-21 11:09:30,083 - INFO - draw_background: 0.0026s
2025-07-21 11:09:30,096 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:30,217 - INFO - draw_background: 0.0030s
2025-07-21 11:09:30,229 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:09:30,346 - INFO - draw_background: 0.0009s
2025-07-21 11:09:30,356 - INFO - draw_stats_page: 0.0100s
2025-07-21 11:09:30,479 - INFO - draw_background: 0.0016s
2025-07-21 11:09:30,489 - INFO - draw_stats_page: 0.0117s
2025-07-21 11:09:30,615 - INFO - draw_background: 0.0026s
2025-07-21 11:09:30,618 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:30,630 - INFO - draw_stats_page: 0.0178s
2025-07-21 11:09:30,747 - INFO - draw_background: 0.0021s
2025-07-21 11:09:30,761 - INFO - draw_stats_page: 0.0160s
2025-07-21 11:09:30,883 - INFO - draw_background: 0.0036s
2025-07-21 11:09:30,899 - INFO - draw_stats_page: 0.0213s
2025-07-21 11:09:31,014 - INFO - draw_background: 0.0026s
2025-07-21 11:09:31,028 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:31,147 - INFO - draw_background: 0.0020s
2025-07-21 11:09:31,159 - INFO - draw_stats_page: 0.0143s
2025-07-21 11:09:31,281 - INFO - draw_background: 0.0026s
2025-07-21 11:09:31,284 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:31,297 - INFO - draw_stats_page: 0.0180s
2025-07-21 11:09:31,414 - INFO - draw_background: 0.0015s
2025-07-21 11:09:31,421 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:31,547 - INFO - draw_background: 0.0020s
2025-07-21 11:09:31,562 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:31,681 - INFO - draw_background: 0.0024s
2025-07-21 11:09:31,695 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:09:31,815 - INFO - draw_background: 0.0009s
2025-07-21 11:09:31,822 - INFO - draw_stats_page: 0.0085s
2025-07-21 11:09:31,947 - INFO - draw_background: 0.0020s
2025-07-21 11:09:31,949 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:31,969 - INFO - draw_stats_page: 0.0224s
2025-07-21 11:09:32,079 - INFO - draw_background: 0.0015s
2025-07-21 11:09:32,086 - INFO - draw_stats_page: 0.0086s
2025-07-21 11:09:32,214 - INFO - draw_background: 0.0025s
2025-07-21 11:09:32,228 - INFO - draw_stats_page: 0.0166s
2025-07-21 11:09:32,347 - INFO - draw_background: 0.0020s
2025-07-21 11:09:32,361 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:32,479 - INFO - draw_background: 0.0024s
2025-07-21 11:09:32,494 - INFO - draw_stats_page: 0.0151s
2025-07-21 11:09:32,612 - INFO - draw_background: 0.0016s
2025-07-21 11:09:32,616 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:32,630 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:32,746 - INFO - draw_background: 0.0018s
2025-07-21 11:09:32,759 - INFO - draw_stats_page: 0.0150s
2025-07-21 11:09:32,882 - INFO - draw_background: 0.0017s
2025-07-21 11:09:32,895 - INFO - draw_stats_page: 0.0156s
2025-07-21 11:09:33,014 - INFO - draw_background: 0.0027s
2025-07-21 11:09:33,031 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:33,146 - INFO - draw_background: 0.0018s
2025-07-21 11:09:33,159 - INFO - draw_stats_page: 0.0151s
2025-07-21 11:09:33,278 - INFO - draw_background: 0.0010s
2025-07-21 11:09:33,278 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:33,284 - INFO - draw_stats_page: 0.0066s
2025-07-21 11:09:33,412 - INFO - draw_background: 0.0010s
2025-07-21 11:09:33,417 - INFO - draw_stats_page: 0.0056s
2025-07-21 11:09:33,546 - INFO - draw_background: 0.0010s
2025-07-21 11:09:33,556 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:33,681 - INFO - draw_background: 0.0024s
2025-07-21 11:09:33,694 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:33,814 - INFO - draw_background: 0.0026s
2025-07-21 11:09:33,828 - INFO - draw_stats_page: 0.0156s
2025-07-21 11:09:33,947 - INFO - draw_background: 0.0021s
2025-07-21 11:09:33,950 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:33,963 - INFO - draw_stats_page: 0.0180s
2025-07-21 11:09:34,081 - INFO - draw_background: 0.0026s
2025-07-21 11:09:34,096 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:09:34,214 - INFO - draw_background: 0.0025s
2025-07-21 11:09:34,229 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:34,347 - INFO - draw_background: 0.0020s
2025-07-21 11:09:34,362 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:09:34,479 - INFO - draw_background: 0.0010s
2025-07-21 11:09:34,487 - INFO - draw_stats_page: 0.0097s
2025-07-21 11:09:34,614 - INFO - draw_background: 0.0025s
2025-07-21 11:09:34,617 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:34,631 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:09:34,747 - INFO - draw_background: 0.0020s
2025-07-21 11:09:34,762 - INFO - draw_stats_page: 0.0170s
2025-07-21 11:09:34,880 - INFO - draw_background: 0.0019s
2025-07-21 11:09:34,895 - INFO - draw_stats_page: 0.0176s
2025-07-21 11:09:35,015 - INFO - draw_background: 0.0032s
2025-07-21 11:09:35,029 - INFO - draw_stats_page: 0.0172s
2025-07-21 11:09:35,147 - INFO - draw_background: 0.0020s
2025-07-21 11:09:35,162 - INFO - draw_stats_page: 0.0165s
2025-07-21 11:09:35,280 - INFO - draw_background: 0.0010s
2025-07-21 11:09:35,281 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:35,290 - INFO - draw_stats_page: 0.0107s
2025-07-21 11:09:35,414 - INFO - draw_background: 0.0016s
2025-07-21 11:09:35,422 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:35,546 - INFO - draw_background: 0.0000s
2025-07-21 11:09:35,550 - INFO - draw_stats_page: 0.0040s
2025-07-21 11:09:35,680 - INFO - draw_background: 0.0009s
2025-07-21 11:09:35,684 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:35,814 - INFO - draw_background: 0.0010s
2025-07-21 11:09:35,822 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:35,948 - INFO - draw_background: 0.0020s
2025-07-21 11:09:35,951 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:35,964 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:36,081 - INFO - draw_background: 0.0025s
2025-07-21 11:09:36,095 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:09:36,214 - INFO - draw_background: 0.0026s
2025-07-21 11:09:36,227 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:36,346 - INFO - draw_background: 0.0021s
2025-07-21 11:09:36,362 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:36,485 - INFO - draw_background: 0.0043s
2025-07-21 11:09:36,519 - INFO - draw_stats_page: 0.0387s
2025-07-21 11:09:36,621 - INFO - draw_background: 0.0015s
2025-07-21 11:09:36,622 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:36,631 - INFO - draw_stats_page: 0.0116s
2025-07-21 11:09:36,755 - INFO - draw_background: 0.0015s
2025-07-21 11:09:36,764 - INFO - draw_stats_page: 0.0106s
2025-07-21 11:09:36,888 - INFO - draw_background: 0.0010s
2025-07-21 11:09:36,896 - INFO - draw_stats_page: 0.0090s
2025-07-21 11:09:37,022 - INFO - draw_background: 0.0026s
2025-07-21 11:09:37,035 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:37,155 - INFO - draw_background: 0.0019s
2025-07-21 11:09:37,170 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:37,288 - INFO - draw_background: 0.0026s
2025-07-21 11:09:37,291 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:37,306 - INFO - draw_stats_page: 0.0190s
2025-07-21 11:09:37,421 - INFO - draw_background: 0.0026s
2025-07-21 11:09:37,436 - INFO - draw_stats_page: 0.0181s
2025-07-21 11:09:37,555 - INFO - draw_background: 0.0025s
2025-07-21 11:09:37,571 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:37,689 - INFO - draw_background: 0.0030s
2025-07-21 11:09:37,703 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:37,821 - INFO - draw_background: 0.0025s
2025-07-21 11:09:37,834 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:37,954 - INFO - draw_background: 0.0024s
2025-07-21 11:09:37,957 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:37,977 - INFO - draw_stats_page: 0.0248s
2025-07-21 11:09:38,091 - INFO - draw_background: 0.0026s
2025-07-21 11:09:38,106 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:38,223 - INFO - draw_background: 0.0010s
2025-07-21 11:09:38,228 - INFO - draw_stats_page: 0.0060s
2025-07-21 11:09:38,358 - INFO - draw_background: 0.0019s
2025-07-21 11:09:38,369 - INFO - draw_stats_page: 0.0131s
2025-07-21 11:09:38,491 - INFO - draw_background: 0.0019s
2025-07-21 11:09:38,504 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:38,625 - INFO - draw_background: 0.0015s
2025-07-21 11:09:38,627 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:38,638 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:38,760 - INFO - draw_background: 0.0029s
2025-07-21 11:09:38,774 - INFO - draw_stats_page: 0.0170s
2025-07-21 11:09:38,892 - INFO - draw_background: 0.0026s
2025-07-21 11:09:38,907 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:39,025 - INFO - draw_background: 0.0026s
2025-07-21 11:09:39,039 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:39,158 - INFO - draw_background: 0.0021s
2025-07-21 11:09:39,172 - INFO - draw_stats_page: 0.0174s
2025-07-21 11:09:39,292 - INFO - draw_background: 0.0027s
2025-07-21 11:09:39,294 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:39,307 - INFO - draw_stats_page: 0.0186s
2025-07-21 11:09:39,424 - INFO - draw_background: 0.0028s
2025-07-21 11:09:39,438 - INFO - draw_stats_page: 0.0171s
2025-07-21 11:09:39,558 - INFO - draw_background: 0.0031s
2025-07-21 11:09:39,572 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:39,691 - INFO - draw_background: 0.0025s
2025-07-21 11:09:39,704 - INFO - draw_stats_page: 0.0169s
2025-07-21 11:09:39,824 - INFO - draw_background: 0.0025s
2025-07-21 11:09:39,842 - INFO - draw_stats_page: 0.0202s
2025-07-21 11:09:39,957 - INFO - draw_background: 0.0020s
2025-07-21 11:09:39,960 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:39,974 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:40,024 - INFO - draw_background: 0.0010s
2025-07-21 11:09:40,031 - INFO - draw_stats_page: 0.0082s
2025-07-21 11:09:40,156 - INFO - draw_background: 0.0010s
2025-07-21 11:09:40,160 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:40,291 - INFO - draw_background: 0.0017s
2025-07-21 11:09:40,298 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:40,425 - INFO - draw_background: 0.0005s
2025-07-21 11:09:40,433 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:40,590 - INFO - draw_background: 0.0020s
2025-07-21 11:09:40,592 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:40,608 - INFO - draw_stats_page: 0.0213s
2025-07-21 11:09:40,703 - INFO - draw_background: 0.0021s
2025-07-21 11:09:40,712 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:40,851 - INFO - draw_background: 0.0025s
2025-07-21 11:09:40,867 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:40,971 - INFO - draw_background: 0.0030s
2025-07-21 11:09:40,985 - INFO - draw_stats_page: 0.0185s
2025-07-21 11:09:41,089 - INFO - draw_background: 0.0020s
2025-07-21 11:09:41,105 - INFO - draw_stats_page: 0.0174s
2025-07-21 11:09:41,206 - INFO - draw_background: 0.0019s
2025-07-21 11:09:41,209 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:41,226 - INFO - draw_stats_page: 0.0224s
2025-07-21 11:09:41,297 - INFO - draw_background: 0.0010s
2025-07-21 11:09:41,307 - INFO - draw_stats_page: 0.0122s
2025-07-21 11:09:41,429 - INFO - draw_background: 0.0010s
2025-07-21 11:09:41,435 - INFO - draw_stats_page: 0.0070s
2025-07-21 11:09:41,566 - INFO - draw_background: 0.0036s
2025-07-21 11:09:41,581 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:41,668 - INFO - draw_background: 0.0020s
2025-07-21 11:09:41,677 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:41,800 - INFO - draw_background: 0.0030s
2025-07-21 11:09:41,802 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:41,825 - INFO - draw_stats_page: 0.0286s
2025-07-21 11:09:41,963 - INFO - draw_background: 0.0016s
2025-07-21 11:09:41,972 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:42,099 - INFO - draw_background: 0.0021s
2025-07-21 11:09:42,114 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:09:42,215 - INFO - draw_background: 0.0044s
2025-07-21 11:09:42,233 - INFO - draw_stats_page: 0.0213s
2025-07-21 11:09:42,335 - INFO - draw_background: 0.0046s
2025-07-21 11:09:42,349 - INFO - draw_stats_page: 0.0186s
2025-07-21 11:09:42,455 - INFO - draw_background: 0.0036s
2025-07-21 11:09:42,459 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:42,472 - INFO - draw_stats_page: 0.0214s
2025-07-21 11:09:42,574 - INFO - draw_background: 0.0029s
2025-07-21 11:09:42,588 - INFO - draw_stats_page: 0.0176s
2025-07-21 11:09:42,682 - INFO - draw_background: 0.0026s
2025-07-21 11:09:42,699 - INFO - draw_stats_page: 0.0193s
2025-07-21 11:09:42,810 - INFO - draw_background: 0.0029s
2025-07-21 11:09:42,827 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:09:42,937 - INFO - draw_background: 0.0030s
2025-07-21 11:09:42,956 - INFO - draw_stats_page: 0.0214s
2025-07-21 11:09:43,089 - INFO - draw_background: 0.0019s
2025-07-21 11:09:43,092 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:43,106 - INFO - draw_stats_page: 0.0191s
2025-07-21 11:09:43,207 - INFO - draw_background: 0.0029s
2025-07-21 11:09:43,224 - INFO - draw_stats_page: 0.0198s
2025-07-21 11:09:43,337 - INFO - draw_background: 0.0011s
2025-07-21 11:09:43,341 - INFO - draw_stats_page: 0.0046s
2025-07-21 11:09:43,471 - INFO - draw_background: 0.0010s
2025-07-21 11:09:43,475 - INFO - draw_stats_page: 0.0050s
2025-07-21 11:09:43,607 - INFO - draw_background: 0.0020s
2025-07-21 11:09:43,621 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:43,741 - INFO - draw_background: 0.0026s
2025-07-21 11:09:43,745 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:43,759 - INFO - draw_stats_page: 0.0195s
2025-07-21 11:09:43,874 - INFO - draw_background: 0.0010s
2025-07-21 11:09:43,882 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:44,009 - INFO - draw_background: 0.0020s
2025-07-21 11:09:44,026 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:09:44,138 - INFO - draw_background: 0.0011s
2025-07-21 11:09:44,142 - INFO - draw_stats_page: 0.0046s
2025-07-21 11:09:44,272 - INFO - draw_background: 0.0009s
2025-07-21 11:09:44,282 - INFO - draw_stats_page: 0.0101s
2025-07-21 11:09:44,405 - INFO - draw_background: 0.0010s
2025-07-21 11:09:44,407 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:44,416 - INFO - draw_stats_page: 0.0121s
2025-07-21 11:09:44,472 - INFO - draw_background: 0.0013s
2025-07-21 11:09:44,478 - INFO - draw_stats_page: 0.0069s
2025-07-21 11:09:44,607 - INFO - draw_background: 0.0031s
2025-07-21 11:09:44,621 - INFO - draw_stats_page: 0.0170s
2025-07-21 11:09:44,741 - INFO - draw_background: 0.0010s
2025-07-21 11:09:44,754 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:44,877 - INFO - draw_background: 0.0019s
2025-07-21 11:09:44,891 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:45,008 - INFO - draw_background: 0.0020s
2025-07-21 11:09:45,011 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:45,025 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:09:45,141 - INFO - draw_background: 0.0020s
2025-07-21 11:09:45,149 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:45,207 - INFO - draw_background: 0.0030s
2025-07-21 11:09:45,225 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:09:45,339 - INFO - draw_background: 0.0011s
2025-07-21 11:09:45,347 - INFO - draw_stats_page: 0.0091s
2025-07-21 11:09:45,475 - INFO - draw_background: 0.0026s
2025-07-21 11:09:45,488 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:45,607 - INFO - draw_background: 0.0021s
2025-07-21 11:09:45,610 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:45,622 - INFO - draw_stats_page: 0.0177s
2025-07-21 11:09:45,741 - INFO - draw_background: 0.0026s
2025-07-21 11:09:45,755 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:45,875 - INFO - draw_background: 0.0025s
2025-07-21 11:09:45,887 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:09:46,007 - INFO - draw_background: 0.0020s
2025-07-21 11:09:46,022 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:46,074 - INFO - draw_background: 0.0029s
2025-07-21 11:09:46,087 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:46,207 - INFO - draw_background: 0.0010s
2025-07-21 11:09:46,209 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:46,217 - INFO - draw_stats_page: 0.0111s
2025-07-21 11:09:46,340 - INFO - draw_background: 0.0010s
2025-07-21 11:09:46,347 - INFO - draw_stats_page: 0.0087s
2025-07-21 11:09:46,475 - INFO - draw_background: 0.0026s
2025-07-21 11:09:46,489 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:46,606 - INFO - draw_background: 0.0018s
2025-07-21 11:09:46,619 - INFO - draw_stats_page: 0.0150s
2025-07-21 11:09:46,675 - INFO - draw_background: 0.0018s
2025-07-21 11:09:46,688 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:46,807 - INFO - draw_background: 0.0020s
2025-07-21 11:09:46,810 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:46,824 - INFO - draw_stats_page: 0.0188s
2025-07-21 11:09:46,877 - INFO - draw_background: 0.0010s
2025-07-21 11:09:46,885 - INFO - draw_stats_page: 0.0108s
2025-07-21 11:09:47,008 - INFO - draw_background: 0.0031s
2025-07-21 11:09:47,022 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:47,143 - INFO - draw_background: 0.0026s
2025-07-21 11:09:47,156 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:47,276 - INFO - draw_background: 0.0009s
2025-07-21 11:09:47,282 - INFO - draw_stats_page: 0.0085s
2025-07-21 11:09:47,411 - INFO - draw_background: 0.0026s
2025-07-21 11:09:47,414 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:47,427 - INFO - draw_stats_page: 0.0192s
2025-07-21 11:09:47,542 - INFO - draw_background: 0.0025s
2025-07-21 11:09:47,556 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:47,609 - INFO - draw_background: 0.0020s
2025-07-21 11:09:47,622 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:47,747 - INFO - draw_background: 0.0023s
2025-07-21 11:09:47,759 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:09:47,879 - INFO - draw_background: 0.0021s
2025-07-21 11:09:47,892 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:48,012 - INFO - draw_background: 0.0025s
2025-07-21 11:09:48,015 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:48,028 - INFO - draw_stats_page: 0.0182s
2025-07-21 11:09:48,078 - INFO - draw_background: 0.0030s
2025-07-21 11:09:48,091 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:48,212 - INFO - draw_background: 0.0026s
2025-07-21 11:09:48,227 - INFO - draw_stats_page: 0.0173s
2025-07-21 11:09:48,279 - INFO - load_statistics: 0.0010s
2025-07-21 11:09:48,280 - INFO - draw_background: 0.0010s
2025-07-21 11:09:48,285 - INFO - draw_stats_page: 0.0067s
2025-07-21 11:09:48,411 - INFO - draw_background: 0.0017s
2025-07-21 11:09:48,419 - INFO - draw_stats_page: 0.0102s
2025-07-21 11:09:48,422 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:48,545 - INFO - draw_background: 0.0018s
2025-07-21 11:09:48,560 - INFO - draw_stats_page: 0.0160s
2025-07-21 11:09:48,681 - INFO - draw_background: 0.0019s
2025-07-21 11:09:48,694 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:48,815 - INFO - draw_background: 0.0026s
2025-07-21 11:09:48,829 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:48,948 - INFO - draw_background: 0.0030s
2025-07-21 11:09:48,962 - INFO - draw_stats_page: 0.0168s
2025-07-21 11:09:49,081 - INFO - draw_background: 0.0029s
2025-07-21 11:09:49,095 - INFO - draw_stats_page: 0.0174s
2025-07-21 11:09:49,097 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:49,215 - INFO - draw_background: 0.0027s
2025-07-21 11:09:49,229 - INFO - draw_stats_page: 0.0167s
2025-07-21 11:09:49,347 - INFO - draw_background: 0.0030s
2025-07-21 11:09:49,359 - INFO - draw_stats_page: 0.0141s
2025-07-21 11:09:49,480 - INFO - draw_background: 0.0019s
2025-07-21 11:09:49,494 - INFO - draw_stats_page: 0.0164s
2025-07-21 11:09:49,615 - INFO - draw_background: 0.0026s
2025-07-21 11:09:49,628 - INFO - draw_stats_page: 0.0158s
2025-07-21 11:09:49,748 - INFO - draw_background: 0.0030s
2025-07-21 11:09:49,765 - INFO - draw_stats_page: 0.0203s
2025-07-21 11:09:49,769 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:49,881 - INFO - draw_background: 0.0025s
2025-07-21 11:09:49,895 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:09:50,014 - INFO - draw_background: 0.0025s
2025-07-21 11:09:50,029 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:09:50,148 - INFO - draw_background: 0.0020s
2025-07-21 11:09:50,162 - INFO - draw_stats_page: 0.0156s
2025-07-21 11:09:50,281 - INFO - draw_background: 0.0019s
2025-07-21 11:09:50,294 - INFO - draw_stats_page: 0.0163s
2025-07-21 11:09:50,414 - INFO - draw_background: 0.0026s
2025-07-21 11:09:50,432 - INFO - draw_stats_page: 0.0204s
2025-07-21 11:09:50,435 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:50,546 - INFO - draw_background: 0.0019s
2025-07-21 11:09:50,559 - INFO - draw_stats_page: 0.0151s
2025-07-21 11:09:50,681 - INFO - draw_background: 0.0026s
2025-07-21 11:09:50,695 - INFO - draw_stats_page: 0.0161s
2025-07-21 11:09:50,816 - INFO - draw_background: 0.0019s
2025-07-21 11:09:50,954 - INFO - draw_stats_page: 0.1401s
2025-07-21 11:09:50,990 - INFO - draw_background: 0.0021s
2025-07-21 11:09:51,107 - INFO - draw_stats_page: 0.1191s
2025-07-21 11:09:51,145 - INFO - draw_background: 0.0026s
2025-07-21 11:09:51,272 - INFO - draw_stats_page: 0.1297s
2025-07-21 11:09:51,274 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:51,314 - INFO - draw_background: 0.0029s
2025-07-21 11:09:51,442 - INFO - draw_stats_page: 0.1312s
2025-07-21 11:09:51,482 - INFO - draw_background: 0.0027s
2025-07-21 11:09:51,589 - INFO - draw_stats_page: 0.1109s
2025-07-21 11:09:51,616 - INFO - draw_background: 0.0010s
2025-07-21 11:09:51,687 - INFO - draw_stats_page: 0.0726s
2025-07-21 11:09:51,781 - INFO - draw_background: 0.0025s
2025-07-21 11:09:51,901 - INFO - draw_stats_page: 0.1220s
2025-07-21 11:09:51,938 - INFO - draw_background: 0.0029s
2025-07-21 11:09:52,066 - INFO - draw_stats_page: 0.1307s
2025-07-21 11:09:52,069 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:52,108 - INFO - draw_background: 0.0035s
2025-07-21 11:09:52,226 - INFO - draw_stats_page: 0.1213s
2025-07-21 11:09:52,255 - INFO - draw_background: 0.0016s
2025-07-21 11:09:52,337 - INFO - draw_stats_page: 0.0835s
2025-07-21 11:09:52,366 - INFO - draw_background: 0.0020s
2025-07-21 11:09:52,446 - INFO - draw_stats_page: 0.0807s
2025-07-21 11:09:52,477 - INFO - draw_background: 0.0019s
2025-07-21 11:09:52,605 - INFO - draw_stats_page: 0.1318s
2025-07-21 11:09:52,650 - INFO - draw_background: 0.0030s
2025-07-21 11:09:52,722 - INFO - draw_stats_page: 0.0757s
2025-07-21 11:09:52,724 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:52,815 - INFO - draw_background: 0.0025s
2025-07-21 11:09:52,936 - INFO - draw_stats_page: 0.1232s
2025-07-21 11:09:52,977 - INFO - draw_background: 0.0031s
2025-07-21 11:09:53,072 - INFO - draw_stats_page: 0.0983s
2025-07-21 11:09:53,109 - INFO - draw_background: 0.0010s
2025-07-21 11:09:53,145 - INFO - draw_stats_page: 0.0368s
2025-07-21 11:09:53,277 - INFO - draw_background: 0.0029s
2025-07-21 11:09:53,407 - INFO - draw_stats_page: 0.1332s
2025-07-21 11:09:53,479 - INFO - draw_background: 0.0009s
2025-07-21 11:09:53,551 - INFO - draw_stats_page: 0.0731s
2025-07-21 11:09:53,552 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:53,603 - INFO - draw_background: 0.0010s
2025-07-21 11:09:53,644 - INFO - draw_stats_page: 0.0412s
2025-07-21 11:09:53,749 - INFO - draw_background: 0.0010s
2025-07-21 11:09:53,809 - INFO - draw_stats_page: 0.0613s
2025-07-21 11:09:53,864 - INFO - draw_background: 0.0010s
2025-07-21 11:09:53,902 - INFO - draw_stats_page: 0.0387s
2025-07-21 11:09:54,009 - INFO - draw_background: 0.0009s
2025-07-21 11:09:54,046 - INFO - draw_stats_page: 0.0384s
2025-07-21 11:09:54,154 - INFO - draw_background: 0.0000s
2025-07-21 11:09:54,192 - INFO - draw_stats_page: 0.0385s
2025-07-21 11:09:54,192 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:54,241 - INFO - draw_background: 0.0016s
2025-07-21 11:09:54,282 - INFO - draw_stats_page: 0.0422s
2025-07-21 11:09:54,391 - INFO - draw_background: 0.0025s
2025-07-21 11:09:54,513 - INFO - draw_stats_page: 0.1246s
2025-07-21 11:09:54,601 - INFO - draw_background: 0.0015s
2025-07-21 11:09:54,671 - INFO - draw_stats_page: 0.0716s
2025-07-21 11:09:54,739 - INFO - draw_background: 0.0020s
2025-07-21 11:09:54,817 - INFO - draw_stats_page: 0.0806s
2025-07-21 11:09:54,858 - INFO - draw_background: 0.0010s
2025-07-21 11:09:54,900 - INFO - draw_stats_page: 0.0421s
2025-07-21 11:09:54,901 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:55,008 - INFO - draw_background: 0.0000s
2025-07-21 11:09:55,093 - INFO - draw_stats_page: 0.0855s
2025-07-21 11:09:55,162 - INFO - draw_background: 0.0014s
2025-07-21 11:09:55,235 - INFO - draw_stats_page: 0.0748s
2025-07-21 11:09:55,287 - INFO - draw_background: 0.0019s
2025-07-21 11:09:55,329 - INFO - draw_stats_page: 0.0438s
2025-07-21 11:09:55,439 - INFO - draw_background: 0.0009s
2025-07-21 11:09:55,504 - INFO - draw_stats_page: 0.0669s
2025-07-21 11:09:55,564 - INFO - draw_background: 0.0009s
2025-07-21 11:09:55,632 - INFO - draw_stats_page: 0.0685s
2025-07-21 11:09:55,633 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:55,695 - INFO - draw_background: 0.0009s
2025-07-21 11:09:55,767 - INFO - draw_stats_page: 0.0723s
2025-07-21 11:09:55,822 - INFO - draw_background: 0.0010s
2025-07-21 11:09:55,871 - INFO - draw_stats_page: 0.0494s
2025-07-21 11:09:55,978 - INFO - draw_background: 0.0010s
2025-07-21 11:09:56,044 - INFO - draw_stats_page: 0.0666s
2025-07-21 11:09:56,096 - INFO - draw_background: 0.0010s
2025-07-21 11:09:56,137 - INFO - draw_stats_page: 0.0416s
2025-07-21 11:09:56,245 - INFO - draw_background: 0.0019s
2025-07-21 11:09:56,374 - INFO - draw_stats_page: 0.1302s
2025-07-21 11:09:56,377 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:56,736 - INFO - draw_background: 0.0023s
2025-07-21 11:09:56,811 - INFO - draw_stats_page: 0.0774s
2025-07-21 11:09:56,904 - INFO - draw_background: 0.0010s
2025-07-21 11:09:56,939 - INFO - draw_stats_page: 0.0363s
2025-07-21 11:09:57,054 - INFO - draw_background: 0.0009s
2025-07-21 11:09:57,162 - INFO - draw_stats_page: 0.1094s
2025-07-21 11:09:57,283 - INFO - draw_background: 0.0015s
2025-07-21 11:09:57,349 - INFO - draw_stats_page: 0.0676s
2025-07-21 11:09:57,425 - INFO - draw_background: 0.0009s
2025-07-21 11:09:57,463 - INFO - draw_stats_page: 0.0396s
2025-07-21 11:09:57,465 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:57,524 - INFO - draw_background: 0.0009s
2025-07-21 11:09:57,558 - INFO - draw_stats_page: 0.0350s
2025-07-21 11:09:57,684 - INFO - draw_background: 0.0017s
2025-07-21 11:09:57,790 - INFO - draw_stats_page: 0.1076s
2025-07-21 11:09:57,931 - INFO - draw_background: 0.0015s
2025-07-21 11:09:58,000 - INFO - draw_stats_page: 0.0707s
2025-07-21 11:09:58,061 - INFO - draw_background: 0.0005s
2025-07-21 11:09:58,102 - INFO - draw_stats_page: 0.0413s
2025-07-21 11:09:58,164 - INFO - draw_background: 0.0010s
2025-07-21 11:09:58,251 - INFO - draw_stats_page: 0.0876s
2025-07-21 11:09:58,254 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:58,330 - INFO - draw_background: 0.0020s
2025-07-21 11:09:58,414 - INFO - draw_stats_page: 0.0858s
2025-07-21 11:09:58,497 - INFO - draw_background: 0.0010s
2025-07-21 11:09:58,550 - INFO - draw_stats_page: 0.0533s
2025-07-21 11:09:58,624 - INFO - draw_background: 0.0015s
2025-07-21 11:09:58,704 - INFO - draw_stats_page: 0.0811s
2025-07-21 11:09:58,811 - INFO - draw_background: 0.0005s
2025-07-21 11:09:58,861 - INFO - draw_stats_page: 0.0509s
2025-07-21 11:09:58,936 - INFO - draw_background: 0.0010s
2025-07-21 11:09:58,989 - INFO - draw_stats_page: 0.0538s
2025-07-21 11:09:58,991 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:59,071 - INFO - draw_background: 0.0009s
2025-07-21 11:09:59,154 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:09:59,155 - WARNING - Failed to connect to RethinkDB
2025-07-21 11:09:59,191 - INFO - draw_stats_page: 0.1208s
2025-07-21 11:09:59,316 - INFO - draw_background: 0.0020s
2025-07-21 11:09:59,398 - INFO - draw_stats_page: 0.0839s
2025-07-21 11:09:59,481 - INFO - draw_background: 0.0010s
2025-07-21 11:09:59,514 - INFO - draw_stats_page: 0.0342s
2025-07-21 11:09:59,632 - INFO - draw_background: 0.0026s
2025-07-21 11:09:59,713 - INFO - draw_stats_page: 0.0822s
2025-07-21 11:09:59,751 - INFO - draw_background: 0.0015s
2025-07-21 11:09:59,785 - INFO - draw_stats_page: 0.0358s
2025-07-21 11:09:59,786 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:09:59,884 - INFO - draw_background: 0.0009s
2025-07-21 11:09:59,949 - INFO - draw_stats_page: 0.0659s
2025-07-21 11:10:00,048 - INFO - draw_background: 0.0010s
2025-07-21 11:10:00,099 - INFO - draw_stats_page: 0.0516s
2025-07-21 11:10:00,188 - INFO - draw_background: 0.0010s
2025-07-21 11:10:00,227 - INFO - draw_stats_page: 0.0398s
2025-07-21 11:10:00,322 - INFO - draw_background: 0.0011s
2025-07-21 11:10:00,361 - INFO - draw_stats_page: 0.0397s
2025-07-21 11:10:00,456 - INFO - draw_background: 0.0019s
2025-07-21 11:10:00,574 - INFO - draw_stats_page: 0.1207s
2025-07-21 11:10:00,577 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:00,625 - INFO - draw_background: 0.0026s
2025-07-21 11:10:00,740 - INFO - draw_stats_page: 0.1167s
2025-07-21 11:10:00,785 - INFO - draw_background: 0.0026s
2025-07-21 11:10:00,905 - INFO - draw_stats_page: 0.1222s
2025-07-21 11:10:00,947 - INFO - draw_background: 0.0009s
2025-07-21 11:10:01,014 - INFO - draw_stats_page: 0.0668s
2025-07-21 11:10:01,113 - INFO - draw_background: 0.0019s
2025-07-21 11:10:01,181 - INFO - draw_stats_page: 0.0705s
2025-07-21 11:10:01,213 - INFO - draw_background: 0.0016s
2025-07-21 11:10:01,285 - INFO - draw_stats_page: 0.0735s
2025-07-21 11:10:01,287 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:01,319 - INFO - draw_background: 0.0011s
2025-07-21 11:10:01,384 - INFO - draw_stats_page: 0.0657s
2025-07-21 11:10:01,482 - INFO - draw_background: 0.0018s
2025-07-21 11:10:01,582 - INFO - draw_stats_page: 0.1012s
2025-07-21 11:10:01,614 - INFO - draw_background: 0.0010s
2025-07-21 11:10:01,679 - INFO - draw_stats_page: 0.0666s
2025-07-21 11:10:01,779 - INFO - draw_background: 0.0020s
2025-07-21 11:10:01,881 - INFO - draw_stats_page: 0.1051s
2025-07-21 11:10:01,916 - INFO - draw_background: 0.0017s
2025-07-21 11:10:01,995 - INFO - draw_stats_page: 0.0804s
2025-07-21 11:10:01,997 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:02,032 - INFO - draw_background: 0.0009s
2025-07-21 11:10:02,139 - INFO - draw_stats_page: 0.1083s
2025-07-21 11:10:02,191 - INFO - draw_background: 0.0027s
2025-07-21 11:10:02,279 - INFO - draw_stats_page: 0.0912s
2025-07-21 11:10:02,311 - INFO - draw_background: 0.0016s
2025-07-21 11:10:02,426 - INFO - draw_stats_page: 0.1159s
2025-07-21 11:10:02,471 - INFO - draw_background: 0.0010s
2025-07-21 11:10:02,535 - INFO - draw_stats_page: 0.0655s
2025-07-21 11:10:02,635 - INFO - draw_background: 0.0026s
2025-07-21 11:10:02,752 - INFO - draw_stats_page: 0.1198s
2025-07-21 11:10:02,754 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:02,801 - INFO - draw_background: 0.0026s
2025-07-21 11:10:02,919 - INFO - draw_stats_page: 0.1201s
2025-07-21 11:10:02,959 - INFO - draw_background: 0.0009s
2025-07-21 11:10:03,030 - INFO - draw_stats_page: 0.0702s
2025-07-21 11:10:03,061 - INFO - draw_background: 0.0015s
2025-07-21 11:10:03,126 - INFO - draw_stats_page: 0.0671s
2025-07-21 11:10:03,226 - INFO - draw_background: 0.0019s
2025-07-21 11:10:03,321 - INFO - draw_stats_page: 0.0969s
2025-07-21 11:10:03,354 - INFO - draw_background: 0.0015s
2025-07-21 11:10:03,463 - INFO - draw_stats_page: 0.1098s
2025-07-21 11:10:03,465 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:03,513 - INFO - draw_background: 0.0027s
2025-07-21 11:10:03,629 - INFO - draw_stats_page: 0.1178s
2025-07-21 11:10:03,675 - INFO - draw_background: 0.0005s
2025-07-21 11:10:03,776 - INFO - draw_stats_page: 0.1025s
2025-07-21 11:10:03,821 - INFO - draw_background: 0.0010s
2025-07-21 11:10:03,886 - INFO - draw_stats_page: 0.0664s
2025-07-21 11:10:03,985 - INFO - draw_background: 0.0025s
2025-07-21 11:10:04,098 - INFO - draw_stats_page: 0.1156s
2025-07-21 11:10:04,147 - INFO - draw_background: 0.0028s
2025-07-21 11:10:04,266 - INFO - draw_stats_page: 0.1221s
2025-07-21 11:10:04,269 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:04,311 - INFO - draw_background: 0.0015s
2025-07-21 11:10:04,398 - INFO - draw_stats_page: 0.0886s
2025-07-21 11:10:04,430 - INFO - draw_background: 0.0019s
2025-07-21 11:10:04,536 - INFO - draw_stats_page: 0.1082s
2025-07-21 11:10:04,582 - INFO - draw_background: 0.0017s
2025-07-21 11:10:04,702 - INFO - draw_stats_page: 0.1217s
2025-07-21 11:10:04,737 - INFO - draw_background: 0.0010s
2025-07-21 11:10:04,810 - INFO - draw_stats_page: 0.0731s
2025-07-21 11:10:04,842 - INFO - draw_background: 0.0016s
2025-07-21 11:10:04,908 - INFO - draw_stats_page: 0.0663s
2025-07-21 11:10:04,909 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:05,009 - INFO - draw_background: 0.0021s
2025-07-21 11:10:05,125 - INFO - draw_stats_page: 0.1184s
2025-07-21 11:10:05,174 - INFO - draw_background: 0.0010s
2025-07-21 11:10:05,251 - INFO - draw_stats_page: 0.0792s
2025-07-21 11:10:05,286 - INFO - draw_background: 0.0018s
2025-07-21 11:10:05,401 - INFO - draw_stats_page: 0.1152s
2025-07-21 11:10:05,448 - INFO - draw_background: 0.0010s
2025-07-21 11:10:05,518 - INFO - draw_stats_page: 0.0709s
2025-07-21 11:10:05,549 - INFO - draw_background: 0.0009s
2025-07-21 11:10:05,665 - INFO - draw_stats_page: 0.1165s
2025-07-21 11:10:05,668 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:05,714 - INFO - draw_background: 0.0015s
2025-07-21 11:10:05,777 - INFO - draw_stats_page: 0.0650s
2025-07-21 11:10:05,879 - INFO - draw_background: 0.0020s
2025-07-21 11:10:05,995 - INFO - draw_stats_page: 0.1192s
2025-07-21 11:10:06,042 - INFO - draw_background: 0.0025s
2025-07-21 11:10:06,142 - INFO - draw_stats_page: 0.1023s
2025-07-21 11:10:06,173 - INFO - draw_background: 0.0015s
2025-07-21 11:10:06,285 - INFO - draw_stats_page: 0.1130s
2025-07-21 11:10:06,331 - INFO - draw_background: 0.0026s
2025-07-21 11:10:06,447 - INFO - draw_stats_page: 0.1187s
2025-07-21 11:10:06,450 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:06,495 - INFO - draw_background: 0.0010s
2025-07-21 11:10:06,550 - INFO - draw_stats_page: 0.0555s
2025-07-21 11:10:06,642 - INFO - draw_background: 0.0016s
2025-07-21 11:10:06,825 - INFO - draw_stats_page: 0.1844s
2025-07-21 11:10:06,867 - INFO - draw_background: 0.0020s
2025-07-21 11:10:06,968 - INFO - draw_stats_page: 0.1039s
2025-07-21 11:10:07,012 - INFO - draw_background: 0.0015s
2025-07-21 11:10:07,127 - INFO - draw_stats_page: 0.1171s
2025-07-21 11:10:07,172 - INFO - draw_background: 0.0016s
2025-07-21 11:10:07,295 - INFO - draw_stats_page: 0.1247s
2025-07-21 11:10:07,296 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:07,329 - INFO - draw_background: 0.0019s
2025-07-21 11:10:07,445 - INFO - draw_stats_page: 0.1184s
2025-07-21 11:10:07,496 - INFO - draw_background: 0.0016s
2025-07-21 11:10:07,631 - INFO - draw_stats_page: 0.1376s
2025-07-21 11:10:07,674 - INFO - draw_background: 0.0009s
2025-07-21 11:10:07,787 - INFO - draw_stats_page: 0.1128s
2025-07-21 11:10:07,834 - INFO - draw_background: 0.0026s
2025-07-21 11:10:07,950 - INFO - draw_stats_page: 0.1176s
2025-07-21 11:10:07,994 - INFO - draw_background: 0.0009s
2025-07-21 11:10:08,061 - INFO - draw_stats_page: 0.0673s
2025-07-21 11:10:08,062 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:08,162 - INFO - draw_background: 0.0025s
2025-07-21 11:10:08,287 - INFO - draw_stats_page: 0.1259s
2025-07-21 11:10:08,318 - INFO - draw_background: 0.0019s
2025-07-21 11:10:08,419 - INFO - draw_stats_page: 0.1027s
2025-07-21 11:10:08,451 - INFO - draw_background: 0.0017s
2025-07-21 11:10:08,555 - INFO - draw_stats_page: 0.1064s
2025-07-21 11:10:08,599 - INFO - draw_background: 0.0010s
2025-07-21 11:10:08,666 - INFO - draw_stats_page: 0.0676s
2025-07-21 11:10:08,704 - INFO - draw_background: 0.0010s
2025-07-21 11:10:08,743 - INFO - draw_stats_page: 0.0398s
2025-07-21 11:10:08,743 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:08,838 - INFO - draw_background: 0.0020s
2025-07-21 11:10:08,950 - INFO - draw_stats_page: 0.1139s
2025-07-21 11:10:08,992 - INFO - draw_background: 0.0016s
2025-07-21 11:10:09,059 - INFO - draw_stats_page: 0.0692s
2025-07-21 11:10:09,092 - INFO - draw_background: 0.0009s
2025-07-21 11:10:09,209 - INFO - draw_stats_page: 0.1170s
2025-07-21 11:10:09,254 - INFO - draw_background: 0.0015s
2025-07-21 11:10:09,324 - INFO - draw_stats_page: 0.0707s
2025-07-21 11:10:09,354 - INFO - draw_background: 0.0009s
2025-07-21 11:10:09,460 - INFO - draw_stats_page: 0.1059s
2025-07-21 11:10:09,462 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:09,510 - INFO - draw_background: 0.0030s
2025-07-21 11:10:09,592 - INFO - draw_stats_page: 0.0850s
2025-07-21 11:10:09,625 - INFO - draw_background: 0.0018s
2025-07-21 11:10:09,689 - INFO - draw_stats_page: 0.0668s
2025-07-21 11:10:09,787 - INFO - draw_background: 0.0019s
2025-07-21 11:10:09,899 - INFO - draw_stats_page: 0.1130s
2025-07-21 11:10:09,944 - INFO - draw_background: 0.0011s
2025-07-21 11:10:10,009 - INFO - draw_stats_page: 0.0655s
2025-07-21 11:10:10,108 - INFO - draw_background: 0.0020s
2025-07-21 11:10:10,225 - INFO - draw_stats_page: 0.1190s
2025-07-21 11:10:10,228 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:10,276 - INFO - draw_background: 0.0026s
2025-07-21 11:10:10,349 - INFO - draw_stats_page: 0.0757s
2025-07-21 11:10:10,382 - INFO - draw_background: 0.0016s
2025-07-21 11:10:10,496 - INFO - draw_stats_page: 0.1147s
2025-07-21 11:10:10,541 - INFO - draw_background: 0.0016s
2025-07-21 11:10:10,609 - INFO - draw_stats_page: 0.0695s
2025-07-21 11:10:10,641 - INFO - draw_background: 0.0017s
2025-07-21 11:10:10,706 - INFO - draw_stats_page: 0.0658s
2025-07-21 11:10:10,804 - INFO - draw_background: 0.0015s
2025-07-21 11:10:10,862 - INFO - draw_stats_page: 0.0581s
2025-07-21 11:10:10,863 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:10,957 - INFO - draw_background: 0.0020s
2025-07-21 11:10:11,059 - INFO - draw_stats_page: 0.1039s
2025-07-21 11:10:11,101 - INFO - draw_background: 0.0019s
2025-07-21 11:10:11,201 - INFO - draw_stats_page: 0.1019s
2025-07-21 11:10:11,239 - INFO - draw_background: 0.0019s
2025-07-21 11:10:11,320 - INFO - draw_stats_page: 0.0821s
2025-07-21 11:10:11,351 - INFO - draw_background: 0.0000s
2025-07-21 11:10:11,417 - INFO - draw_stats_page: 0.0675s
2025-07-21 11:10:11,517 - INFO - draw_background: 0.0020s
2025-07-21 11:10:11,638 - INFO - draw_stats_page: 0.1234s
2025-07-21 11:10:11,641 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:11,691 - INFO - draw_background: 0.0025s
2025-07-21 11:10:11,821 - INFO - draw_stats_page: 0.1317s
2025-07-21 11:10:11,867 - INFO - draw_background: 0.0010s
2025-07-21 11:10:11,942 - INFO - draw_stats_page: 0.0761s
2025-07-21 11:10:11,974 - INFO - draw_background: 0.0015s
2025-07-21 11:10:12,045 - INFO - draw_stats_page: 0.0729s
2025-07-21 11:10:12,077 - INFO - draw_background: 0.0011s
2025-07-21 11:10:12,147 - INFO - draw_stats_page: 0.0705s
2025-07-21 11:10:12,178 - INFO - draw_background: 0.0009s
2025-07-21 11:10:12,309 - INFO - draw_stats_page: 0.1324s
2025-07-21 11:10:12,312 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:12,363 - INFO - draw_background: 0.0036s
2025-07-21 11:10:12,484 - INFO - draw_stats_page: 0.1253s
2025-07-21 11:10:12,537 - INFO - draw_background: 0.0036s
2025-07-21 11:10:12,659 - INFO - draw_stats_page: 0.1256s
2025-07-21 11:10:12,711 - INFO - draw_background: 0.0026s
2025-07-21 11:10:12,830 - INFO - draw_stats_page: 0.1210s
2025-07-21 11:10:12,862 - INFO - draw_background: 0.0016s
2025-07-21 11:10:12,935 - INFO - draw_stats_page: 0.0746s
2025-07-21 11:10:12,971 - INFO - draw_background: 0.0025s
2025-07-21 11:10:13,042 - INFO - draw_stats_page: 0.0733s
2025-07-21 11:10:13,043 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:13,075 - INFO - draw_background: 0.0013s
2025-07-21 11:10:13,143 - INFO - draw_stats_page: 0.0700s
2025-07-21 11:10:13,182 - INFO - draw_background: 0.0010s
2025-07-21 11:10:13,249 - INFO - draw_stats_page: 0.0685s
2025-07-21 11:10:13,288 - INFO - draw_background: 0.0020s
2025-07-21 11:10:13,356 - INFO - draw_stats_page: 0.0700s
2025-07-21 11:10:13,395 - INFO - draw_background: 0.0010s
2025-07-21 11:10:13,469 - INFO - draw_stats_page: 0.0747s
2025-07-21 11:10:13,508 - INFO - draw_background: 0.0009s
2025-07-21 11:10:13,575 - INFO - draw_stats_page: 0.0687s
2025-07-21 11:10:13,577 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:10:13,615 - INFO - draw_background: 0.0010s
2025-07-21 11:10:13,741 - INFO - draw_stats_page: 0.1270s
2025-07-21 11:10:13,779 - INFO - draw_background: 0.0020s
2025-07-21 11:10:13,874 - INFO - draw_stats_page: 0.0959s
2025-07-21 11:10:22,378 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:10:31,593 - INFO - Game started in demo mode - statistics not updated
2025-07-21 11:10:31,602 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 11:10:31,606 - INFO - Started background data loading
2025-07-21 11:10:31,606 - INFO - Forced data refresh
2025-07-21 11:10:31,606 - INFO - Forced refresh of optimized loader data
2025-07-21 11:10:31,611 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:10:31,615 - INFO - Loaded summary data
2025-07-21 11:10:31,619 - INFO - Loaded game history page 0 (0 records)
2025-07-21 11:10:31,621 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 11:10:31,624 - INFO - Saved 7 items to cache
2025-07-21 11:10:31,624 - INFO - Background data loading completed
2025-07-21 11:10:31,625 - INFO - Forced refresh of thread_safe_db data
2025-07-21 11:10:31,626 - INFO - Saved 0 items to persistent cache
2025-07-21 11:10:31,626 - INFO - Cache cleared
2025-07-21 11:10:31,626 - INFO - Cleared all preloader cache data
2025-07-21 11:10:31,626 - INFO - Starting stats data preloading
2025-07-21 11:10:31,626 - INFO - Loading data using optimized functions
2025-07-21 11:10:31,627 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:10:31,627 - INFO - Started stats data preloading
2025-07-21 11:10:31,627 - INFO - Cleared stats preloader cache
2025-07-21 11:10:31,627 - INFO - DB Operation: {"timestamp": "2025-07-21 11:10:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:10:31,628 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:10:31,628 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:10:31,629 - INFO - DB Operation: {"timestamp": "2025-07-21 11:10:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:10:31,629 - INFO - Preloaded summary data
2025-07-21 11:10:31,631 - INFO - Preloaded game history (0 records)
2025-07-21 11:10:31,632 - INFO - Preloaded wallet data
2025-07-21 11:10:31,632 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 11:10:31,632 - INFO - Saved 8 items to persistent cache
2025-07-21 11:10:31,640 - INFO - Got summary stats
2025-07-21 11:10:31,675 - INFO - Posted refresh_stats event
2025-07-21 11:10:31,676 - INFO - Processed game_started event: False
2025-07-21 11:10:31,677 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-07-21 11:10:31,678 - INFO - Loaded weekly stats from GameStatsIntegration
2025-07-21 11:10:43,414 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 11:10:43,415 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 11:11:00,739 - INFO - Game start event recorded in database (players: 3)
2025-07-21 11:11:00,742 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 11:11:00,744 - INFO - Started background data loading
2025-07-21 11:11:00,745 - INFO - Forced data refresh
2025-07-21 11:11:00,746 - INFO - Game start recorded with optimized integration: True
2025-07-21 11:11:00,751 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:11:00,755 - INFO - Loaded summary data
2025-07-21 11:11:00,757 - INFO - Loaded game history page 0 (0 records)
2025-07-21 11:11:00,758 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 11:11:00,761 - INFO - Saved 7 items to cache
2025-07-21 11:11:00,761 - INFO - Background data loading completed
2025-07-21 11:11:00,768 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 11:11:00,771 - INFO - Started background data loading
2025-07-21 11:11:00,772 - INFO - Forced data refresh
2025-07-21 11:11:00,772 - INFO - Forced refresh of optimized loader data
2025-07-21 11:11:00,775 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:11:00,779 - INFO - Loaded summary data
2025-07-21 11:11:00,780 - INFO - Loaded game history page 0 (0 records)
2025-07-21 11:11:00,781 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 11:11:00,784 - INFO - Saved 7 items to cache
2025-07-21 11:11:00,784 - INFO - Background data loading completed
2025-07-21 11:11:00,789 - INFO - Forced refresh of thread_safe_db data
2025-07-21 11:11:00,796 - INFO - Saved 0 items to persistent cache
2025-07-21 11:11:00,796 - INFO - Cache cleared
2025-07-21 11:11:00,796 - INFO - Cleared all preloader cache data
2025-07-21 11:11:00,799 - INFO - Starting stats data preloading
2025-07-21 11:11:00,799 - INFO - Started stats data preloading
2025-07-21 11:11:00,799 - INFO - Loading data using optimized functions
2025-07-21 11:11:00,801 - INFO - Cleared stats preloader cache
2025-07-21 11:11:00,803 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:11:00,805 - INFO - DB Operation: {"timestamp": "2025-07-21 11:11:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:11:00,809 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:11:00,809 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:11:00,810 - INFO - DB Operation: {"timestamp": "2025-07-21 11:11:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:11:00,810 - INFO - Posted refresh_stats event
2025-07-21 11:11:00,821 - INFO - Processed game_started event: True
2025-07-21 11:11:00,824 - INFO - Preloaded summary data
2025-07-21 11:11:00,824 - INFO - Preloaded game history (0 records)
2025-07-21 11:11:00,825 - INFO - Preloaded wallet data
2025-07-21 11:11:00,825 - INFO - Stats data preloaded successfully in 0.03 seconds
2025-07-21 11:11:00,826 - INFO - Saved 8 items to persistent cache
2025-07-21 11:11:03,218 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:11:03,218 - WARNING - Failed to connect to RethinkDB
2025-07-21 11:11:07,321 - INFO - generate_background: 0.0116s
2025-07-21 11:11:07,367 - INFO - draw_background: 0.0594s
2025-07-21 11:11:07,444 - INFO - draw_stats_page: 0.1366s
2025-07-21 11:11:07,457 - INFO - draw_background: 0.0009s
2025-07-21 11:11:07,489 - INFO - draw_stats_page: 0.0328s
2025-07-21 11:11:07,592 - INFO - draw_background: 0.0000s
2025-07-21 11:11:07,593 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:07,622 - INFO - draw_stats_page: 0.0302s
2025-07-21 11:11:07,727 - INFO - draw_background: 0.0020s
2025-07-21 11:11:07,805 - INFO - draw_stats_page: 0.0800s
2025-07-21 11:11:07,886 - INFO - draw_background: 0.0010s
2025-07-21 11:11:07,968 - INFO - draw_stats_page: 0.0836s
2025-07-21 11:11:08,049 - INFO - draw_background: 0.0010s
2025-07-21 11:11:08,133 - INFO - draw_stats_page: 0.0862s
2025-07-21 11:11:08,214 - INFO - draw_background: 0.0009s
2025-07-21 11:11:08,326 - INFO - draw_stats_page: 0.1119s
2025-07-21 11:11:08,342 - INFO - draw_background: 0.0017s
2025-07-21 11:11:08,344 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:08,439 - INFO - draw_stats_page: 0.0998s
2025-07-21 11:11:08,468 - INFO - draw_background: 0.0010s
2025-07-21 11:11:08,508 - INFO - draw_stats_page: 0.0410s
2025-07-21 11:11:08,603 - INFO - draw_background: 0.0016s
2025-07-21 11:11:08,714 - INFO - draw_stats_page: 0.1134s
2025-07-21 11:11:08,762 - INFO - draw_background: 0.0016s
2025-07-21 11:11:08,808 - INFO - Admin button added to stats page
2025-07-21 11:11:08,882 - INFO - draw_stats_page: 0.1213s
2025-07-21 11:11:08,934 - INFO - draw_background: 0.0026s
2025-07-21 11:11:09,056 - INFO - draw_stats_page: 0.1247s
2025-07-21 11:11:09,108 - INFO - draw_background: 0.0021s
2025-07-21 11:11:09,112 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:09,232 - INFO - draw_stats_page: 0.1262s
2025-07-21 11:11:09,285 - INFO - draw_background: 0.0026s
2025-07-21 11:11:09,404 - INFO - draw_stats_page: 0.1221s
2025-07-21 11:11:09,452 - INFO - draw_background: 0.0026s
2025-07-21 11:11:09,584 - INFO - draw_stats_page: 0.1338s
2025-07-21 11:11:09,632 - INFO - draw_background: 0.0026s
2025-07-21 11:11:09,744 - INFO - draw_stats_page: 0.1143s
2025-07-21 11:11:09,793 - INFO - draw_background: 0.0036s
2025-07-21 11:11:09,905 - INFO - draw_stats_page: 0.1157s
2025-07-21 11:11:09,954 - INFO - draw_background: 0.0024s
2025-07-21 11:11:09,961 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:10,069 - INFO - draw_stats_page: 0.1174s
2025-07-21 11:11:10,122 - INFO - draw_background: 0.0016s
2025-07-21 11:11:10,183 - INFO - draw_stats_page: 0.0629s
2025-07-21 11:11:10,284 - INFO - draw_background: 0.0022s
2025-07-21 11:11:10,398 - INFO - draw_stats_page: 0.1165s
2025-07-21 11:11:10,448 - INFO - draw_background: 0.0020s
2025-07-21 11:11:10,680 - INFO - draw_stats_page: 0.2334s
2025-07-21 11:11:10,712 - INFO - draw_background: 0.0013s
2025-07-21 11:11:10,825 - INFO - draw_stats_page: 0.1148s
2025-07-21 11:11:10,872 - INFO - draw_background: 0.0026s
2025-07-21 11:11:10,877 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:10,991 - INFO - draw_stats_page: 0.1203s
2025-07-21 11:11:11,041 - INFO - draw_background: 0.0019s
2025-07-21 11:11:11,155 - INFO - draw_stats_page: 0.1177s
2025-07-21 11:11:11,201 - INFO - draw_background: 0.0017s
2025-07-21 11:11:11,321 - INFO - draw_stats_page: 0.1218s
2025-07-21 11:11:11,366 - INFO - draw_background: 0.0020s
2025-07-21 11:11:11,484 - INFO - draw_stats_page: 0.1183s
2025-07-21 11:11:11,522 - INFO - draw_background: 0.0016s
2025-07-21 11:11:11,638 - INFO - draw_stats_page: 0.1178s
2025-07-21 11:11:11,682 - INFO - draw_background: 0.0016s
2025-07-21 11:11:11,687 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:11,787 - INFO - draw_stats_page: 0.1065s
2025-07-21 11:11:11,808 - INFO - draw_background: 0.0010s
2025-07-21 11:11:11,852 - INFO - draw_stats_page: 0.0452s
2025-07-21 11:11:11,916 - INFO - draw_background: 0.0010s
2025-07-21 11:11:11,995 - INFO - draw_stats_page: 0.0802s
2025-07-21 11:11:12,021 - INFO - draw_background: 0.0025s
2025-07-21 11:11:12,075 - INFO - draw_stats_page: 0.0565s
2025-07-21 11:11:12,145 - INFO - draw_background: 0.0010s
2025-07-21 11:11:12,177 - INFO - draw_stats_page: 0.0328s
2025-07-21 11:11:12,264 - INFO - generate_background: 0.0065s
2025-07-21 11:11:12,265 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:12,344 - INFO - draw_background: 0.0893s
2025-07-21 11:11:12,373 - INFO - draw_stats_page: 0.1189s
2025-07-21 11:11:12,384 - INFO - draw_background: 0.0005s
2025-07-21 11:11:12,394 - INFO - draw_stats_page: 0.0106s
2025-07-21 11:11:12,488 - INFO - draw_background: 0.0000s
2025-07-21 11:11:12,491 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:11:12,588 - INFO - draw_background: 0.0009s
2025-07-21 11:11:12,591 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:11:12,702 - INFO - draw_background: 0.0000s
2025-07-21 11:11:12,706 - INFO - draw_stats_page: 0.0045s
2025-07-21 11:11:12,707 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:12,805 - INFO - draw_background: 0.0015s
2025-07-21 11:11:12,818 - INFO - draw_stats_page: 0.0157s
2025-07-21 11:11:12,907 - INFO - draw_background: 0.0018s
2025-07-21 11:11:12,918 - INFO - draw_stats_page: 0.0140s
2025-07-21 11:11:13,020 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,035 - INFO - draw_stats_page: 0.0183s
2025-07-21 11:11:13,122 - INFO - draw_background: 0.0025s
2025-07-21 11:11:13,135 - INFO - draw_stats_page: 0.0153s
2025-07-21 11:11:13,237 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,249 - INFO - draw_stats_page: 0.0142s
2025-07-21 11:11:13,252 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:13,345 - INFO - draw_background: 0.0026s
2025-07-21 11:11:13,358 - INFO - draw_stats_page: 0.0159s
2025-07-21 11:11:13,446 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,459 - INFO - draw_stats_page: 0.0152s
2025-07-21 11:11:13,561 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,574 - INFO - draw_stats_page: 0.0155s
2025-07-21 11:11:13,664 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,679 - INFO - draw_stats_page: 0.0180s
2025-07-21 11:11:13,768 - INFO - draw_background: 0.0020s
2025-07-21 11:11:13,780 - INFO - draw_stats_page: 0.0150s
2025-07-21 11:11:13,783 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:13,876 - INFO - draw_background: 0.0026s
2025-07-21 11:11:13,892 - INFO - draw_stats_page: 0.0194s
2025-07-21 11:11:13,937 - INFO - draw_background: 0.0030s
2025-07-21 11:11:13,954 - INFO - draw_stats_page: 0.0193s
2025-07-21 11:11:14,044 - INFO - draw_background: 0.0009s
2025-07-21 11:11:14,052 - INFO - draw_stats_page: 0.0086s
2025-07-21 11:11:14,156 - INFO - draw_background: 0.0010s
2025-07-21 11:11:14,162 - INFO - draw_stats_page: 0.0065s
2025-07-21 11:11:14,263 - INFO - draw_background: 0.0010s
2025-07-21 11:11:14,268 - INFO - draw_stats_page: 0.0055s
2025-07-21 11:11:14,269 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 11:11:14,349 - INFO - draw_background: 0.0010s
2025-07-21 11:11:14,352 - INFO - draw_stats_page: 0.0046s
2025-07-21 11:11:18,931 - INFO - Saved 0 items to persistent cache
2025-07-21 11:11:18,932 - INFO - Cache cleared
2025-07-21 11:11:18,932 - INFO - Cleared all preloader cache data
2025-07-21 11:11:18,935 - INFO - Starting stats data preloading
2025-07-21 11:11:18,935 - INFO - Loading data using optimized functions
2025-07-21 11:11:18,936 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:11:18,936 - INFO - Started stats data preloading
2025-07-21 11:11:18,937 - INFO - DB Operation: {"timestamp": "2025-07-21 11:11:18", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:11:18,941 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:11:18,941 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:11:18,942 - INFO - DB Operation: {"timestamp": "2025-07-21 11:11:18", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:11:18,947 - INFO - Preloaded summary data
2025-07-21 11:11:18,951 - INFO - Preloaded game history (0 records)
2025-07-21 11:11:18,953 - INFO - Preloaded wallet data
2025-07-21 11:11:18,954 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-21 11:11:18,959 - INFO - Saved 8 items to persistent cache
2025-07-21 11:11:18,995 - INFO - BaseLoader initialized with cache directory: data/cache
2025-07-21 11:11:18,996 - INFO - PlaceholderGenerator initialized
2025-07-21 11:11:18,999 - INFO - Started cache cleanup thread
2025-07-21 11:11:18,999 - INFO - DiskCache initialized with max age: 3600s
2025-07-21 11:11:19,002 - INFO - Background worker started
2025-07-21 11:11:19,002 - INFO - BackgroundLoader initialized
2025-07-21 11:11:19,004 - INFO - UIUpdater initialized
2025-07-21 11:11:19,005 - INFO - Generated weekly stats placeholder
2025-07-21 11:11:19,005 - INFO - Generated game history placeholder with 10 records
2025-07-21 11:11:19,006 - INFO - Generated summary stats placeholder
2025-07-21 11:11:19,011 - INFO - Saved all placeholders to cache
2025-07-21 11:11:19,012 - INFO - Loaded placeholder data
2025-07-21 11:11:19,013 - INFO - Queued task weekly_stats
2025-07-21 11:11:19,014 - INFO - Queued task game_history_page_0
2025-07-21 11:11:19,015 - INFO - Queued task summary_stats
2025-07-21 11:11:19,015 - INFO - Started background loading
2025-07-21 11:11:19,015 - INFO - StatsLoader initialized
2025-07-21 11:11:19,059 - INFO - Saved cache metadata with 0 entries
2025-07-21 11:11:19,060 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 11:11:19,060 - INFO - Cleared all function caches
2025-07-21 11:11:19,061 - INFO - Generated weekly stats placeholder
2025-07-21 11:11:19,062 - INFO - Generated game history placeholder with 10 records
2025-07-21 11:11:19,062 - INFO - Generated summary stats placeholder
2025-07-21 11:11:19,069 - INFO - Saved all placeholders to cache
2025-07-21 11:11:19,069 - INFO - Cleared all caches
2025-07-21 11:11:19,076 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 11:11:19,078 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 11:11:19,451 - INFO - Got weekly stats: 7 days
2025-07-21 11:11:19,453 - INFO - Weekly stats loaded
2025-07-21 11:11:19,453 - INFO - Task weekly_stats completed in 0.4374s
2025-07-21 11:11:19,454 - INFO - Got game history page 0: 0 records
2025-07-21 11:11:19,456 - INFO - Game history page 0 loaded
2025-07-21 11:11:19,456 - INFO - Task game_history_page_0 completed in 0.0000s
2025-07-21 11:11:19,457 - INFO - Got summary stats
2025-07-21 11:11:19,458 - INFO - Summary stats loaded
2025-07-21 11:11:19,458 - INFO - Task summary_stats completed in 0.0010s
2025-07-21 11:11:26,442 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:12:22,036 - INFO - Loaded 8 items from cache
2025-07-21 11:12:22,037 - INFO - Started background data loading
2025-07-21 11:12:22,037 - INFO - OptimizedStatsLoader initialized
2025-07-21 11:12:22,038 - INFO - Using optimized stats loader for integration
2025-07-21 11:12:22,042 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:12:22,042 - INFO - Loaded summary data
2025-07-21 11:12:22,042 - INFO - Loaded game history page 0 (0 records)
2025-07-21 11:12:22,043 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 11:12:22,044 - INFO - Saved 9 items to cache
2025-07-21 11:12:22,044 - INFO - Background data loading completed
2025-07-21 11:12:22,044 - INFO - Database schema initialized successfully
2025-07-21 11:12:22,046 - INFO - Database schema initialized successfully
2025-07-21 11:12:22,047 - INFO - Stats database initialized successfully
2025-07-21 11:12:22,048 - INFO - Database schema initialized successfully
2025-07-21 11:12:22,049 - INFO - Stats database initialized successfully
2025-07-21 11:12:22,051 - INFO - Game stats integration module available
2025-07-21 11:12:22,051 - INFO - Started stats event worker thread
2025-07-21 11:12:22,051 - INFO - Stats event hooks initialized
2025-07-21 11:12:22,137 - INFO - Database security initialized successfully
2025-07-21 11:12:22,138 - INFO - DB Operation: {"timestamp": "2025-07-21 11:12:22", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:12:22,138 - INFO - Created secure database connection
2025-07-21 11:12:22,140 - INFO - Database schema initialized successfully
2025-07-21 11:12:26,229 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:12:26,231 - INFO - Sync thread started
2025-07-21 11:12:26,232 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 11:12:26,238 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 11:12:26,854 - INFO - Hooked into game's start_game method
2025-07-21 11:12:26,855 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 11:12:31,859 - INFO - Hooked into game's start_game method
2025-07-21 11:12:31,859 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 11:13:30,327 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 11:13:47,261 - INFO - Game start event recorded in database (players: 3)
2025-07-21 11:13:47,263 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 11:13:47,268 - INFO - Started background data loading
2025-07-21 11:13:47,268 - INFO - Forced data refresh
2025-07-21 11:13:47,268 - INFO - Game start recorded with optimized integration: True
2025-07-21 11:13:47,285 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:13:47,286 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 11:13:47,287 - INFO - Forced data refresh
2025-07-21 11:13:47,287 - INFO - Forced refresh of optimized loader data
2025-07-21 11:13:47,288 - INFO - Loaded summary data
2025-07-21 11:13:47,289 - INFO - Loaded game history page 0 (0 records)
2025-07-21 11:13:47,289 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 11:13:47,290 - INFO - Saved 6 items to cache
2025-07-21 11:13:47,291 - INFO - Background data loading completed
2025-07-21 11:13:47,296 - INFO - Forced refresh of thread_safe_db data
2025-07-21 11:13:47,313 - INFO - Loaded 6 items from persistent cache
2025-07-21 11:13:47,315 - INFO - Stats cache initialized
2025-07-21 11:13:47,320 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,321 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,322 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-21 11:13:47,322 - INFO - Stats preloader initialized
2025-07-21 11:13:47,324 - INFO - Starting stats data preloading
2025-07-21 11:13:47,324 - INFO - Started stats data preloading
2025-07-21 11:13:47,325 - INFO - Loading data using optimized functions
2025-07-21 11:13:47,326 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:47,326 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,327 - INFO - Saved 0 items to persistent cache
2025-07-21 11:13:47,334 - INFO - Cache cleared
2025-07-21 11:13:47,334 - INFO - Cleared all preloader cache data
2025-07-21 11:13:47,337 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:13:47,337 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:47,337 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,338 - INFO - Preloaded summary data
2025-07-21 11:13:47,338 - INFO - Preloaded game history (0 records)
2025-07-21 11:13:47,338 - INFO - Preloaded wallet data
2025-07-21 11:13:47,338 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 11:13:47,339 - INFO - Saved 8 items to persistent cache
2025-07-21 11:13:47,339 - INFO - Stopped stats data preloading
2025-07-21 11:13:47,339 - INFO - Starting stats data preloading
2025-07-21 11:13:47,339 - INFO - Loading data using optimized functions
2025-07-21 11:13:47,341 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:47,341 - INFO - Started stats data preloading
2025-07-21 11:13:47,341 - INFO - Cleared stats preloader cache
2025-07-21 11:13:47,341 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,343 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:13:47,343 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:47,344 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:47,345 - INFO - Preloaded summary data
2025-07-21 11:13:47,346 - INFO - Preloaded game history (0 records)
2025-07-21 11:13:47,346 - INFO - Preloaded wallet data
2025-07-21 11:13:47,347 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 11:13:47,348 - INFO - Saved 8 items to persistent cache
2025-07-21 11:13:47,360 - INFO - Got summary stats
2025-07-21 11:13:47,395 - INFO - Posted refresh_stats event
2025-07-21 11:13:47,396 - INFO - Processed game_started event: True
2025-07-21 11:13:47,398 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-07-21 11:13:47,399 - INFO - Loaded weekly stats for 7 days
2025-07-21 11:13:47,399 - INFO - Loaded weekly stats from GameStatsIntegration
2025-07-21 11:13:53,041 - INFO - Saved 0 items to persistent cache
2025-07-21 11:13:53,041 - INFO - Cache cleared
2025-07-21 11:13:53,041 - INFO - Cleared all preloader cache data
2025-07-21 11:13:53,041 - INFO - Starting stats data preloading
2025-07-21 11:13:53,041 - INFO - Loading data using optimized functions
2025-07-21 11:13:53,041 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:53,041 - INFO - Started stats data preloading
2025-07-21 11:13:53,043 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:53", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:53,044 - INFO - Preloaded weekly stats for 7 days
2025-07-21 11:13:53,044 - WARNING - Connection validation failed, creating new connection
2025-07-21 11:13:53,045 - INFO - DB Operation: {"timestamp": "2025-07-21 11:13:53", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 11:13:53,046 - INFO - Preloaded summary data
2025-07-21 11:13:53,047 - INFO - Preloaded game history (0 records)
2025-07-21 11:13:53,047 - INFO - Preloaded wallet data
2025-07-21 11:13:53,047 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 11:13:53,049 - INFO - Saved 8 items to persistent cache
2025-07-21 11:13:53,062 - INFO - BaseLoader initialized with cache directory: data/cache
2025-07-21 11:13:53,063 - INFO - PlaceholderGenerator initialized
2025-07-21 11:13:53,064 - INFO - Loaded cache metadata with 0 entries
2025-07-21 11:13:53,064 - INFO - Started cache cleanup thread
2025-07-21 11:13:53,065 - INFO - DiskCache initialized with max age: 3600s
2025-07-21 11:13:53,065 - INFO - Background worker started
2025-07-21 11:13:53,065 - INFO - BackgroundLoader initialized
2025-07-21 11:13:53,065 - INFO - UIUpdater initialized
2025-07-21 11:13:53,078 - INFO - Loaded placeholders from cache
2025-07-21 11:13:53,078 - INFO - Loaded placeholder data
2025-07-21 11:13:53,078 - INFO - Queued task weekly_stats
2025-07-21 11:13:53,078 - INFO - Queued task game_history_page_0
2025-07-21 11:13:53,079 - INFO - Queued task summary_stats
2025-07-21 11:13:53,079 - INFO - Started background loading
2025-07-21 11:13:53,079 - INFO - StatsLoader initialized
2025-07-21 11:13:53,079 - INFO - Saved cache metadata with 0 entries
2025-07-21 11:13:53,080 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 11:13:53,080 - INFO - Cleared all function caches
2025-07-21 11:13:53,080 - INFO - Generated weekly stats placeholder
2025-07-21 11:13:53,080 - INFO - Generated game history placeholder with 10 records
2025-07-21 11:13:53,080 - INFO - Generated summary stats placeholder
2025-07-21 11:13:53,082 - INFO - Saved all placeholders to cache
2025-07-21 11:13:53,082 - INFO - Cleared all caches
2025-07-21 11:13:53,091 - INFO - Got weekly stats: 7 days
2025-07-21 11:13:53,092 - INFO - Weekly stats loaded
2025-07-21 11:13:53,093 - INFO - Task weekly_stats completed in 0.0134s
2025-07-21 11:13:53,094 - INFO - Got game history page 0: 0 records
2025-07-21 11:13:53,097 - INFO - Game history page 0 loaded
2025-07-21 11:13:53,097 - INFO - Task game_history_page_0 completed in 0.0010s
2025-07-21 11:13:53,098 - INFO - Got summary stats
2025-07-21 11:13:53,099 - INFO - Summary stats loaded
2025-07-21 11:13:53,099 - INFO - Task summary_stats completed in 0.0010s
2025-07-21 11:13:53,577 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-21 11:13:53,577 - INFO - Stats performance monitor initialized
2025-07-21 11:13:53,595 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-21 11:13:53,931 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 11:13:53,932 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 22:30:28,624 - INFO - Loaded 0 items from cache
2025-07-21 22:30:28,682 - INFO - Started background data loading
2025-07-21 22:30:28,683 - INFO - OptimizedStatsLoader initialized
2025-07-21 22:30:28,683 - INFO - Using optimized stats loader for integration
2025-07-21 22:30:29,040 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:30:29,042 - INFO - Loaded summary data
2025-07-21 22:30:29,042 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:30:29,042 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:30:29,043 - INFO - Saved 7 items to cache
2025-07-21 22:30:29,043 - INFO - Background data loading completed
2025-07-21 22:30:29,095 - INFO - Database schema initialized successfully
2025-07-21 22:30:29,106 - INFO - Database schema initialized successfully
2025-07-21 22:30:29,111 - INFO - Stats database initialized successfully
2025-07-21 22:30:29,121 - INFO - Database schema initialized successfully
2025-07-21 22:30:29,132 - INFO - Stats database initialized successfully
2025-07-21 22:30:29,139 - INFO - Game stats integration module available
2025-07-21 22:30:29,142 - INFO - Started stats event worker thread
2025-07-21 22:30:29,142 - INFO - Stats event hooks initialized
2025-07-21 22:30:30,474 - INFO - Database security initialized successfully
2025-07-21 22:30:30,474 - INFO - DB Operation: {"timestamp": "2025-07-21 22:30:30", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:30:30,474 - INFO - Created secure database connection
2025-07-21 22:30:30,475 - INFO - Database schema initialized successfully
2025-07-21 22:30:35,342 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:30:35,343 - INFO - Sync thread started
2025-07-21 22:30:35,343 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 22:30:35,345 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 22:30:39,898 - INFO - Hooked into game's start_game method
2025-07-21 22:30:39,899 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:30:57,383 - INFO - Hooked into game's start_game method
2025-07-21 22:30:57,385 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:31:19,576 - INFO - Loaded 7 items from persistent cache
2025-07-21 22:31:19,576 - INFO - Stats cache initialized
2025-07-21 22:31:19,576 - INFO - DB Operation: {"timestamp": "2025-07-21 22:31:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:31:19,576 - INFO - DB Operation: {"timestamp": "2025-07-21 22:31:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:31:19,577 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-21 22:31:19,577 - INFO - Stats preloader initialized
2025-07-21 22:31:19,577 - INFO - Starting stats data preloading
2025-07-21 22:31:19,577 - INFO - Loading data using optimized functions
2025-07-21 22:31:19,578 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:31:19,578 - INFO - Started stats data preloading
2025-07-21 22:31:19,578 - INFO - DB Operation: {"timestamp": "2025-07-21 22:31:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:31:19,687 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:31:19,688 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:31:19,688 - INFO - DB Operation: {"timestamp": "2025-07-21 22:31:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:31:19,690 - INFO - Preloaded summary data
2025-07-21 22:31:19,690 - INFO - Preloaded game history (0 records)
2025-07-21 22:31:19,690 - INFO - Preloaded wallet data
2025-07-21 22:31:19,690 - INFO - Stats data preloaded successfully in 0.11 seconds
2025-07-21 22:31:19,690 - INFO - Saved 9 items to persistent cache
2025-07-21 22:31:29,969 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-21 22:31:29,969 - INFO - Stats performance monitor initialized
2025-07-21 22:31:30,071 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-21 22:31:34,366 - INFO - draw_background: 0.0861s
2025-07-21 22:31:34,466 - INFO - Background thread started
2025-07-21 22:31:34,466 - INFO - Hybrid database integration initialized
2025-07-21 22:31:35,250 - INFO - draw_stats_page: 0.9701s
2025-07-21 22:31:35,279 - INFO - draw_background: 0.0065s
2025-07-21 22:31:35,356 - INFO - draw_stats_page: 0.0832s
2025-07-21 22:31:35,435 - INFO - draw_background: 0.0010s
2025-07-21 22:31:35,514 - INFO - draw_stats_page: 0.0799s
2025-07-21 22:31:35,612 - INFO - draw_background: 0.0010s
2025-07-21 22:31:35,671 - INFO - draw_stats_page: 0.0598s
2025-07-21 22:31:35,765 - INFO - draw_background: 0.0000s
2025-07-21 22:31:35,782 - INFO - Admin button added to stats page
2025-07-21 22:31:35,809 - INFO - draw_stats_page: 0.0449s
2025-07-21 22:31:35,810 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:35,914 - INFO - draw_background: 0.0010s
2025-07-21 22:31:35,974 - INFO - draw_stats_page: 0.0612s
2025-07-21 22:31:36,068 - INFO - draw_background: 0.0010s
2025-07-21 22:31:36,116 - INFO - draw_stats_page: 0.0491s
2025-07-21 22:31:36,209 - INFO - draw_background: 0.0010s
2025-07-21 22:31:36,250 - INFO - draw_stats_page: 0.0426s
2025-07-21 22:31:36,342 - INFO - draw_background: 0.0000s
2025-07-21 22:31:36,385 - INFO - draw_stats_page: 0.0450s
2025-07-21 22:31:36,482 - INFO - draw_background: 0.0000s
2025-07-21 22:31:36,529 - INFO - draw_stats_page: 0.0478s
2025-07-21 22:31:36,531 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:36,624 - INFO - draw_background: 0.0020s
2025-07-21 22:31:36,670 - INFO - draw_stats_page: 0.0476s
2025-07-21 22:31:36,765 - INFO - draw_background: 0.0005s
2025-07-21 22:31:36,813 - INFO - draw_stats_page: 0.0481s
2025-07-21 22:31:36,907 - INFO - draw_background: 0.0000s
2025-07-21 22:31:36,952 - INFO - draw_stats_page: 0.0459s
2025-07-21 22:31:37,046 - INFO - draw_background: 0.0000s
2025-07-21 22:31:37,091 - INFO - draw_stats_page: 0.0462s
2025-07-21 22:31:37,185 - INFO - draw_background: 0.0010s
2025-07-21 22:31:37,230 - INFO - draw_stats_page: 0.0460s
2025-07-21 22:31:37,232 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:37,338 - INFO - draw_background: 0.0005s
2025-07-21 22:31:37,380 - INFO - draw_stats_page: 0.0427s
2025-07-21 22:31:37,477 - INFO - draw_background: 0.0000s
2025-07-21 22:31:37,522 - INFO - draw_stats_page: 0.0461s
2025-07-21 22:31:37,612 - INFO - draw_background: 0.0000s
2025-07-21 22:31:37,663 - INFO - draw_stats_page: 0.0511s
2025-07-21 22:31:37,754 - INFO - draw_background: 0.0000s
2025-07-21 22:31:37,800 - INFO - draw_stats_page: 0.0450s
2025-07-21 22:31:37,890 - INFO - draw_background: 0.0000s
2025-07-21 22:31:37,933 - INFO - draw_stats_page: 0.0425s
2025-07-21 22:31:37,934 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:38,025 - INFO - draw_background: 0.0010s
2025-07-21 22:31:38,080 - INFO - draw_stats_page: 0.0562s
2025-07-21 22:31:38,173 - INFO - draw_background: 0.0010s
2025-07-21 22:31:38,352 - INFO - draw_stats_page: 0.1799s
2025-07-21 22:31:38,440 - INFO - draw_background: 0.0338s
2025-07-21 22:31:38,485 - INFO - draw_stats_page: 0.0794s
2025-07-21 22:31:38,510 - INFO - draw_background: 0.0000s
2025-07-21 22:31:38,546 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:31:38,546 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:31:38,565 - INFO - draw_stats_page: 0.0546s
2025-07-21 22:31:38,657 - INFO - draw_background: 0.0010s
2025-07-21 22:31:38,710 - INFO - draw_stats_page: 0.0545s
2025-07-21 22:31:38,712 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:38,807 - INFO - draw_background: 0.0010s
2025-07-21 22:31:38,853 - INFO - draw_stats_page: 0.0471s
2025-07-21 22:31:38,948 - INFO - draw_background: 0.0010s
2025-07-21 22:31:38,989 - INFO - draw_stats_page: 0.0420s
2025-07-21 22:31:39,083 - INFO - draw_background: 0.0010s
2025-07-21 22:31:39,128 - INFO - draw_stats_page: 0.0462s
2025-07-21 22:31:39,220 - INFO - draw_background: 0.0000s
2025-07-21 22:31:39,269 - INFO - draw_stats_page: 0.0476s
2025-07-21 22:31:39,360 - INFO - draw_background: 0.0015s
2025-07-21 22:31:39,420 - INFO - draw_stats_page: 0.0620s
2025-07-21 22:31:39,422 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:39,514 - INFO - draw_background: 0.0010s
2025-07-21 22:31:39,526 - INFO - draw_stats_page: 0.0131s
2025-07-21 22:31:39,648 - INFO - draw_background: 0.0010s
2025-07-21 22:31:39,653 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:39,781 - INFO - draw_background: 0.0010s
2025-07-21 22:31:39,785 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:39,913 - INFO - draw_background: 0.0005s
2025-07-21 22:31:39,919 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:31:40,046 - INFO - draw_background: 0.0010s
2025-07-21 22:31:40,052 - INFO - draw_stats_page: 0.0071s
2025-07-21 22:31:40,053 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:40,178 - INFO - draw_background: 0.0000s
2025-07-21 22:31:40,185 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:40,312 - INFO - draw_background: 0.0015s
2025-07-21 22:31:40,318 - INFO - draw_stats_page: 0.0071s
2025-07-21 22:31:40,446 - INFO - draw_background: 0.0010s
2025-07-21 22:31:40,453 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:40,579 - INFO - draw_background: 0.0010s
2025-07-21 22:31:40,584 - INFO - draw_stats_page: 0.0063s
2025-07-21 22:31:40,710 - INFO - draw_background: 0.0000s
2025-07-21 22:31:40,715 - INFO - draw_stats_page: 0.0061s
2025-07-21 22:31:40,717 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:40,787 - INFO - draw_background: 0.0012s
2025-07-21 22:31:40,794 - INFO - draw_stats_page: 0.0073s
2025-07-21 22:31:40,909 - INFO - draw_background: 0.0010s
2025-07-21 22:31:40,925 - INFO - draw_stats_page: 0.0168s
2025-07-21 22:31:41,042 - INFO - draw_background: 0.0010s
2025-07-21 22:31:41,045 - INFO - draw_stats_page: 0.0046s
2025-07-21 22:31:41,176 - INFO - draw_background: 0.0000s
2025-07-21 22:31:41,181 - INFO - draw_stats_page: 0.0060s
2025-07-21 22:31:41,307 - INFO - draw_background: 0.0000s
2025-07-21 22:31:41,313 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:31:41,314 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:41,440 - INFO - draw_background: 0.0000s
2025-07-21 22:31:41,445 - INFO - draw_stats_page: 0.0061s
2025-07-21 22:31:41,574 - INFO - draw_background: 0.0010s
2025-07-21 22:31:41,578 - INFO - draw_stats_page: 0.0035s
2025-07-21 22:31:41,706 - INFO - draw_background: 0.0000s
2025-07-21 22:31:41,710 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:31:41,840 - INFO - draw_background: 0.0000s
2025-07-21 22:31:41,845 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:41,973 - INFO - draw_background: 0.0012s
2025-07-21 22:31:41,979 - INFO - draw_stats_page: 0.0067s
2025-07-21 22:31:41,980 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:42,040 - INFO - draw_background: 0.0006s
2025-07-21 22:31:42,045 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:31:42,173 - INFO - draw_background: 0.0016s
2025-07-21 22:31:42,177 - INFO - draw_stats_page: 0.0056s
2025-07-21 22:31:42,306 - INFO - draw_background: 0.0010s
2025-07-21 22:31:42,311 - INFO - draw_stats_page: 0.0055s
2025-07-21 22:31:42,372 - INFO - draw_background: 0.0005s
2025-07-21 22:31:42,376 - INFO - draw_stats_page: 0.0053s
2025-07-21 22:31:42,504 - INFO - draw_background: 0.0010s
2025-07-21 22:31:42,508 - INFO - draw_stats_page: 0.0050s
2025-07-21 22:31:42,522 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:42,637 - INFO - draw_background: 0.0005s
2025-07-21 22:31:42,643 - INFO - draw_stats_page: 0.0056s
2025-07-21 22:31:42,770 - INFO - draw_background: 0.0000s
2025-07-21 22:31:42,775 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:42,906 - INFO - draw_background: 0.0000s
2025-07-21 22:31:42,909 - INFO - draw_stats_page: 0.0045s
2025-07-21 22:31:43,038 - INFO - draw_background: 0.0000s
2025-07-21 22:31:43,043 - INFO - draw_stats_page: 0.0055s
2025-07-21 22:31:43,170 - INFO - draw_background: 0.0000s
2025-07-21 22:31:43,176 - INFO - draw_stats_page: 0.0056s
2025-07-21 22:31:43,177 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:43,304 - INFO - draw_background: 0.0005s
2025-07-21 22:31:43,309 - INFO - draw_stats_page: 0.0055s
2025-07-21 22:31:43,438 - INFO - draw_background: 0.0010s
2025-07-21 22:31:43,443 - INFO - draw_stats_page: 0.0057s
2025-07-21 22:31:43,506 - INFO - draw_background: 0.0010s
2025-07-21 22:31:43,512 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:31:43,640 - INFO - draw_background: 0.0000s
2025-07-21 22:31:43,644 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:43,773 - INFO - draw_background: 0.0010s
2025-07-21 22:31:43,779 - INFO - draw_stats_page: 0.0060s
2025-07-21 22:31:43,780 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:43,906 - INFO - draw_background: 0.0010s
2025-07-21 22:31:43,909 - INFO - draw_stats_page: 0.0045s
2025-07-21 22:31:44,038 - INFO - draw_background: 0.0000s
2025-07-21 22:31:44,045 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:44,107 - INFO - draw_background: 0.0010s
2025-07-21 22:31:44,113 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:44,243 - INFO - draw_background: 0.0010s
2025-07-21 22:31:44,248 - INFO - draw_stats_page: 0.0060s
2025-07-21 22:31:44,374 - INFO - draw_background: 0.0010s
2025-07-21 22:31:44,378 - INFO - draw_stats_page: 0.0055s
2025-07-21 22:31:44,380 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:44,507 - INFO - draw_background: 0.0000s
2025-07-21 22:31:44,511 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:44,573 - INFO - draw_background: 0.0011s
2025-07-21 22:31:44,577 - INFO - draw_stats_page: 0.0046s
2025-07-21 22:31:44,705 - INFO - draw_background: 0.0010s
2025-07-21 22:31:44,709 - INFO - draw_stats_page: 0.0050s
2025-07-21 22:31:44,836 - INFO - draw_background: 0.0000s
2025-07-21 22:31:44,842 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:44,905 - INFO - draw_background: 0.0010s
2025-07-21 22:31:44,910 - INFO - draw_stats_page: 0.0060s
2025-07-21 22:31:44,912 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:45,036 - INFO - draw_background: 0.0005s
2025-07-21 22:31:45,040 - INFO - draw_stats_page: 0.0048s
2025-07-21 22:31:45,170 - INFO - draw_background: 0.0010s
2025-07-21 22:31:45,175 - INFO - draw_stats_page: 0.0056s
2025-07-21 22:31:45,236 - INFO - draw_background: 0.0010s
2025-07-21 22:31:45,242 - INFO - draw_stats_page: 0.0068s
2025-07-21 22:31:45,372 - INFO - draw_background: 0.0017s
2025-07-21 22:31:45,376 - INFO - draw_stats_page: 0.0057s
2025-07-21 22:31:45,437 - INFO - draw_background: 0.0000s
2025-07-21 22:31:45,442 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:31:45,444 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:45,571 - INFO - draw_background: 0.0011s
2025-07-21 22:31:45,576 - INFO - draw_stats_page: 0.0057s
2025-07-21 22:31:45,703 - INFO - draw_background: 0.0010s
2025-07-21 22:31:45,708 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:31:45,835 - INFO - draw_background: 0.0000s
2025-07-21 22:31:45,838 - INFO - draw_stats_page: 0.0040s
2025-07-21 22:31:45,900 - INFO - draw_background: 0.0000s
2025-07-21 22:31:45,905 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:31:46,033 - INFO - draw_background: 0.0012s
2025-07-21 22:31:46,037 - INFO - draw_stats_page: 0.0053s
2025-07-21 22:31:46,038 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:46,167 - INFO - draw_background: 0.0020s
2025-07-21 22:31:46,174 - INFO - draw_stats_page: 0.0096s
2025-07-21 22:31:46,299 - INFO - draw_background: 0.0000s
2025-07-21 22:31:46,302 - INFO - draw_stats_page: 0.0041s
2025-07-21 22:31:46,433 - INFO - draw_background: 0.0010s
2025-07-21 22:31:46,439 - INFO - draw_stats_page: 0.0073s
2025-07-21 22:31:46,565 - INFO - draw_background: 0.0010s
2025-07-21 22:31:46,569 - INFO - draw_stats_page: 0.0040s
2025-07-21 22:31:46,700 - INFO - draw_background: 0.0017s
2025-07-21 22:31:46,709 - INFO - draw_stats_page: 0.0106s
2025-07-21 22:31:46,712 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:46,833 - INFO - draw_background: 0.0010s
2025-07-21 22:31:46,841 - INFO - draw_stats_page: 0.0090s
2025-07-21 22:31:46,971 - INFO - load_statistics: 0.0015s
2025-07-21 22:31:46,973 - INFO - draw_background: 0.0010s
2025-07-21 22:31:46,979 - INFO - draw_stats_page: 0.0071s
2025-07-21 22:31:47,104 - INFO - draw_background: 0.0010s
2025-07-21 22:31:47,111 - INFO - draw_stats_page: 0.0081s
2025-07-21 22:31:47,236 - INFO - draw_background: 0.0000s
2025-07-21 22:31:47,241 - INFO - draw_stats_page: 0.0050s
2025-07-21 22:31:47,372 - INFO - draw_background: 0.0006s
2025-07-21 22:31:47,373 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:47,378 - INFO - draw_stats_page: 0.0076s
2025-07-21 22:31:47,504 - INFO - draw_background: 0.0010s
2025-07-21 22:31:47,507 - INFO - draw_stats_page: 0.0040s
2025-07-21 22:31:47,636 - INFO - draw_background: 0.0010s
2025-07-21 22:31:47,647 - INFO - draw_stats_page: 0.0129s
2025-07-21 22:31:47,768 - INFO - draw_background: 0.0000s
2025-07-21 22:31:47,772 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:31:47,900 - INFO - draw_background: 0.0000s
2025-07-21 22:31:47,906 - INFO - draw_stats_page: 0.0057s
2025-07-21 22:31:48,035 - INFO - draw_background: 0.0000s
2025-07-21 22:31:48,036 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:48,042 - INFO - draw_stats_page: 0.0071s
2025-07-21 22:31:48,168 - INFO - draw_background: 0.0010s
2025-07-21 22:31:48,176 - INFO - draw_stats_page: 0.0091s
2025-07-21 22:31:48,301 - INFO - draw_background: 0.0005s
2025-07-21 22:31:48,307 - INFO - draw_stats_page: 0.0071s
2025-07-21 22:31:48,436 - INFO - draw_background: 0.0010s
2025-07-21 22:31:48,440 - INFO - draw_stats_page: 0.0055s
2025-07-21 22:31:48,570 - INFO - draw_background: 0.0016s
2025-07-21 22:31:48,596 - INFO - draw_stats_page: 0.0273s
2025-07-21 22:31:48,717 - INFO - draw_background: 0.0010s
2025-07-21 22:31:48,718 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:48,723 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:31:48,851 - INFO - draw_background: 0.0020s
2025-07-21 22:31:48,859 - INFO - draw_stats_page: 0.0102s
2025-07-21 22:31:48,983 - INFO - draw_background: 0.0010s
2025-07-21 22:31:48,994 - INFO - draw_stats_page: 0.0122s
2025-07-21 22:31:49,116 - INFO - draw_background: 0.0010s
2025-07-21 22:31:49,122 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:31:49,249 - INFO - draw_background: 0.0015s
2025-07-21 22:31:49,260 - INFO - draw_stats_page: 0.0124s
2025-07-21 22:31:49,384 - INFO - draw_background: 0.0015s
2025-07-21 22:31:49,386 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:49,396 - INFO - draw_stats_page: 0.0132s
2025-07-21 22:31:49,517 - INFO - draw_background: 0.0009s
2025-07-21 22:31:49,523 - INFO - draw_stats_page: 0.0062s
2025-07-21 22:31:49,650 - INFO - draw_background: 0.0010s
2025-07-21 22:31:49,698 - INFO - draw_stats_page: 0.0497s
2025-07-21 22:31:49,792 - INFO - draw_background: 0.0016s
2025-07-21 22:31:49,845 - INFO - draw_stats_page: 0.0554s
2025-07-21 22:31:49,938 - INFO - draw_background: 0.0010s
2025-07-21 22:31:49,977 - INFO - draw_stats_page: 0.0403s
2025-07-21 22:31:50,070 - INFO - draw_background: 0.0005s
2025-07-21 22:31:50,072 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:50,116 - INFO - draw_stats_page: 0.0465s
2025-07-21 22:31:50,209 - INFO - draw_background: 0.0010s
2025-07-21 22:31:50,250 - INFO - draw_stats_page: 0.0421s
2025-07-21 22:31:50,343 - INFO - draw_background: 0.0000s
2025-07-21 22:31:50,390 - INFO - draw_stats_page: 0.0482s
2025-07-21 22:31:50,490 - INFO - draw_background: 0.0000s
2025-07-21 22:31:50,529 - INFO - draw_stats_page: 0.0391s
2025-07-21 22:31:50,625 - INFO - draw_background: 0.0010s
2025-07-21 22:31:50,828 - INFO - draw_stats_page: 0.2054s
2025-07-21 22:31:50,855 - INFO - draw_background: 0.0010s
2025-07-21 22:31:50,856 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:50,935 - INFO - draw_stats_page: 0.0811s
2025-07-21 22:31:50,965 - INFO - draw_background: 0.0000s
2025-07-21 22:31:51,007 - INFO - draw_stats_page: 0.0434s
2025-07-21 22:31:51,097 - INFO - draw_background: 0.0000s
2025-07-21 22:31:51,143 - INFO - draw_stats_page: 0.0457s
2025-07-21 22:31:51,236 - INFO - draw_background: 0.0010s
2025-07-21 22:31:51,276 - INFO - draw_stats_page: 0.0407s
2025-07-21 22:31:51,368 - INFO - draw_background: 0.0000s
2025-07-21 22:31:51,408 - INFO - draw_stats_page: 0.0391s
2025-07-21 22:31:51,505 - INFO - draw_background: 0.0010s
2025-07-21 22:31:51,507 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:51,619 - INFO - draw_stats_page: 0.1156s
2025-07-21 22:31:51,647 - INFO - draw_background: 0.0010s
2025-07-21 22:31:51,695 - INFO - draw_stats_page: 0.0486s
2025-07-21 22:31:51,789 - INFO - draw_background: 0.0030s
2025-07-21 22:31:51,916 - INFO - draw_stats_page: 0.1303s
2025-07-21 22:31:51,959 - INFO - draw_background: 0.0000s
2025-07-21 22:31:52,042 - INFO - draw_stats_page: 0.0839s
2025-07-21 22:31:52,069 - INFO - draw_background: 0.0020s
2025-07-21 22:31:52,116 - INFO - draw_stats_page: 0.0484s
2025-07-21 22:31:52,210 - INFO - draw_background: 0.0010s
2025-07-21 22:31:52,212 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:52,265 - INFO - draw_stats_page: 0.0555s
2025-07-21 22:31:52,359 - INFO - draw_background: 0.0000s
2025-07-21 22:31:52,427 - INFO - draw_stats_page: 0.0694s
2025-07-21 22:31:52,460 - INFO - draw_background: 0.0011s
2025-07-21 22:31:52,552 - INFO - draw_stats_page: 0.0926s
2025-07-21 22:31:52,585 - INFO - draw_background: 0.0031s
2025-07-21 22:31:52,657 - INFO - draw_stats_page: 0.0745s
2025-07-21 22:31:52,685 - INFO - draw_background: 0.0010s
2025-07-21 22:31:52,736 - INFO - draw_stats_page: 0.0520s
2025-07-21 22:31:52,827 - INFO - draw_background: 0.0020s
2025-07-21 22:31:52,830 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:52,896 - INFO - draw_stats_page: 0.0703s
2025-07-21 22:31:52,987 - INFO - draw_background: 0.0010s
2025-07-21 22:31:53,047 - INFO - draw_stats_page: 0.0611s
2025-07-21 22:31:53,138 - INFO - draw_background: 0.0000s
2025-07-21 22:31:53,180 - INFO - draw_stats_page: 0.0407s
2025-07-21 22:31:53,280 - INFO - draw_background: 0.0020s
2025-07-21 22:31:53,380 - INFO - draw_stats_page: 0.1022s
2025-07-21 22:31:53,404 - INFO - draw_background: 0.0010s
2025-07-21 22:31:53,457 - INFO - draw_stats_page: 0.0532s
2025-07-21 22:31:53,552 - INFO - draw_background: 0.0005s
2025-07-21 22:31:53,553 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:53,632 - INFO - draw_stats_page: 0.0798s
2025-07-21 22:31:53,657 - INFO - draw_background: 0.0010s
2025-07-21 22:31:53,704 - INFO - draw_stats_page: 0.0482s
2025-07-21 22:31:53,799 - INFO - draw_background: 0.0010s
2025-07-21 22:31:53,872 - INFO - draw_stats_page: 0.0738s
2025-07-21 22:31:53,903 - INFO - draw_background: 0.0010s
2025-07-21 22:31:53,999 - INFO - draw_stats_page: 0.0969s
2025-07-21 22:31:54,037 - INFO - draw_background: 0.0010s
2025-07-21 22:31:54,110 - INFO - draw_stats_page: 0.0745s
2025-07-21 22:31:54,139 - INFO - draw_background: 0.0000s
2025-07-21 22:31:54,140 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:54,177 - INFO - draw_stats_page: 0.0384s
2025-07-21 22:31:54,273 - INFO - draw_background: 0.0010s
2025-07-21 22:31:54,328 - INFO - draw_stats_page: 0.0562s
2025-07-21 22:31:54,420 - INFO - draw_background: 0.0020s
2025-07-21 22:31:54,498 - INFO - draw_stats_page: 0.0803s
2025-07-21 22:31:54,527 - INFO - draw_background: 0.0010s
2025-07-21 22:31:54,579 - INFO - draw_stats_page: 0.0525s
2025-07-21 22:31:54,630 - INFO - draw_background: 0.0015s
2025-07-21 22:31:54,677 - INFO - draw_stats_page: 0.0486s
2025-07-21 22:31:54,741 - INFO - draw_background: 0.0010s
2025-07-21 22:31:54,742 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:54,822 - INFO - draw_stats_page: 0.0814s
2025-07-21 22:31:54,853 - INFO - draw_background: 0.0015s
2025-07-21 22:31:54,921 - INFO - draw_stats_page: 0.0693s
2025-07-21 22:31:54,953 - INFO - draw_background: 0.0010s
2025-07-21 22:31:55,011 - INFO - draw_stats_page: 0.0591s
2025-07-21 22:31:55,060 - INFO - draw_background: 0.0010s
2025-07-21 22:31:55,121 - INFO - draw_stats_page: 0.0627s
2025-07-21 22:31:55,170 - INFO - draw_background: 0.0011s
2025-07-21 22:31:55,212 - INFO - draw_stats_page: 0.0425s
2025-07-21 22:31:55,276 - INFO - draw_background: 0.0010s
2025-07-21 22:31:55,277 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:55,369 - INFO - draw_stats_page: 0.0936s
2025-07-21 22:31:55,385 - INFO - draw_background: 0.0010s
2025-07-21 22:31:55,466 - INFO - draw_stats_page: 0.0821s
2025-07-21 22:31:55,499 - INFO - draw_background: 0.0000s
2025-07-21 22:31:55,549 - INFO - draw_stats_page: 0.0499s
2025-07-21 22:31:55,601 - INFO - draw_background: 0.0020s
2025-07-21 22:31:55,644 - INFO - draw_stats_page: 0.0450s
2025-07-21 22:31:55,745 - INFO - draw_background: 0.0000s
2025-07-21 22:31:55,780 - INFO - draw_stats_page: 0.0360s
2025-07-21 22:31:55,877 - INFO - draw_background: 0.0000s
2025-07-21 22:31:55,878 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:55,922 - INFO - draw_stats_page: 0.0454s
2025-07-21 22:31:56,016 - INFO - draw_background: 0.0010s
2025-07-21 22:31:56,058 - INFO - draw_stats_page: 0.0430s
2025-07-21 22:31:56,150 - INFO - draw_background: 0.0000s
2025-07-21 22:31:56,185 - INFO - draw_stats_page: 0.0359s
2025-07-21 22:31:56,284 - INFO - draw_background: 0.0010s
2025-07-21 22:31:56,328 - INFO - draw_stats_page: 0.0456s
2025-07-21 22:31:56,425 - INFO - draw_background: 0.0010s
2025-07-21 22:31:56,488 - INFO - draw_stats_page: 0.0638s
2025-07-21 22:31:56,579 - INFO - draw_background: 0.0010s
2025-07-21 22:31:56,581 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:56,633 - INFO - draw_stats_page: 0.0552s
2025-07-21 22:31:56,739 - INFO - draw_background: 0.0010s
2025-07-21 22:31:56,888 - INFO - draw_stats_page: 0.1499s
2025-07-21 22:31:56,933 - INFO - draw_background: 0.0026s
2025-07-21 22:31:56,975 - INFO - draw_stats_page: 0.0453s
2025-07-21 22:31:57,066 - INFO - draw_background: 0.0010s
2025-07-21 22:31:57,133 - INFO - draw_stats_page: 0.0676s
2025-07-21 22:31:57,226 - INFO - draw_background: 0.0010s
2025-07-21 22:31:57,273 - INFO - draw_stats_page: 0.0481s
2025-07-21 22:31:57,371 - INFO - draw_background: 0.0010s
2025-07-21 22:31:57,372 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:57,442 - INFO - draw_stats_page: 0.0733s
2025-07-21 22:31:57,471 - INFO - draw_background: 0.0000s
2025-07-21 22:31:57,516 - INFO - draw_stats_page: 0.0447s
2025-07-21 22:31:57,610 - INFO - draw_background: 0.0000s
2025-07-21 22:31:57,650 - INFO - draw_stats_page: 0.0404s
2025-07-21 22:31:57,748 - INFO - draw_background: 0.0000s
2025-07-21 22:31:57,789 - INFO - draw_stats_page: 0.0419s
2025-07-21 22:31:57,885 - INFO - draw_background: 0.0020s
2025-07-21 22:31:57,975 - INFO - draw_stats_page: 0.0913s
2025-07-21 22:31:58,002 - INFO - draw_background: 0.0010s
2025-07-21 22:31:58,004 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:58,055 - INFO - draw_stats_page: 0.0544s
2025-07-21 22:31:58,149 - INFO - draw_background: 0.0010s
2025-07-21 22:31:58,225 - INFO - draw_stats_page: 0.0766s
2025-07-21 22:31:58,260 - INFO - draw_background: 0.0005s
2025-07-21 22:31:58,325 - INFO - draw_stats_page: 0.0661s
2025-07-21 22:31:58,429 - INFO - draw_background: 0.0010s
2025-07-21 22:31:58,487 - INFO - draw_stats_page: 0.0594s
2025-07-21 22:31:58,580 - INFO - draw_background: 0.0011s
2025-07-21 22:31:58,633 - INFO - draw_stats_page: 0.0537s
2025-07-21 22:31:58,728 - INFO - draw_background: 0.0020s
2025-07-21 22:31:58,729 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:31:58,768 - INFO - draw_stats_page: 0.0422s
2025-07-21 22:31:58,860 - INFO - draw_background: 0.0005s
2025-07-21 22:31:58,910 - INFO - draw_stats_page: 0.0508s
2025-07-21 22:31:59,004 - INFO - draw_background: 0.0010s
2025-07-21 22:31:59,048 - INFO - draw_stats_page: 0.0453s
2025-07-21 22:31:59,145 - INFO - draw_background: 0.0010s
2025-07-21 22:31:59,244 - INFO - draw_stats_page: 0.0992s
2025-07-21 22:32:08,995 - INFO - Game start event recorded in database (players: 3)
2025-07-21 22:32:08,996 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:08,997 - INFO - Started background data loading
2025-07-21 22:32:08,998 - INFO - Forced data refresh
2025-07-21 22:32:08,998 - INFO - Game start recorded with optimized integration: True
2025-07-21 22:32:09,008 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:09,023 - INFO - Loaded summary data
2025-07-21 22:32:09,026 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:09,026 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:09,030 - INFO - Saved 7 items to cache
2025-07-21 22:32:09,031 - INFO - Background data loading completed
2025-07-21 22:32:09,043 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:09,047 - INFO - Started background data loading
2025-07-21 22:32:09,047 - INFO - Forced data refresh
2025-07-21 22:32:09,047 - INFO - Forced refresh of optimized loader data
2025-07-21 22:32:09,049 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:09,049 - INFO - Loaded summary data
2025-07-21 22:32:09,052 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:09,054 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:09,056 - INFO - Saved 7 items to cache
2025-07-21 22:32:09,056 - INFO - Background data loading completed
2025-07-21 22:32:09,063 - INFO - Forced refresh of thread_safe_db data
2025-07-21 22:32:09,073 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:09,074 - INFO - Cache cleared
2025-07-21 22:32:09,074 - INFO - Cleared all preloader cache data
2025-07-21 22:32:09,076 - INFO - Starting stats data preloading
2025-07-21 22:32:09,077 - INFO - Started stats data preloading
2025-07-21 22:32:09,077 - INFO - Loading data using optimized functions
2025-07-21 22:32:09,077 - INFO - Cleared stats preloader cache
2025-07-21 22:32:09,077 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:09,078 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:09,080 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:09,080 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:09,081 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:09,083 - INFO - Preloaded summary data
2025-07-21 22:32:09,083 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:09,085 - INFO - Preloaded wallet data
2025-07-21 22:32:09,085 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:32:09,087 - INFO - Saved 8 items to persistent cache
2025-07-21 22:32:09,118 - INFO - Got summary stats
2025-07-21 22:32:09,176 - INFO - Posted refresh_stats event
2025-07-21 22:32:09,178 - INFO - Processed game_started event: True
2025-07-21 22:32:09,184 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-07-21 22:32:09,185 - INFO - Loaded weekly stats from GameStatsIntegration
2025-07-21 22:32:17,549 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:17,549 - INFO - Cache cleared
2025-07-21 22:32:17,549 - INFO - Cleared all preloader cache data
2025-07-21 22:32:17,551 - INFO - Starting stats data preloading
2025-07-21 22:32:17,551 - INFO - Started stats data preloading
2025-07-21 22:32:17,551 - INFO - Loading data using optimized functions
2025-07-21 22:32:17,553 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:17,555 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:17,557 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:17,557 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:17,558 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:17,562 - INFO - Preloaded summary data
2025-07-21 22:32:17,565 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:17,566 - INFO - Preloaded wallet data
2025-07-21 22:32:17,566 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:32:17,569 - INFO - Saved 8 items to persistent cache
2025-07-21 22:32:17,579 - INFO - BaseLoader initialized with cache directory: data/cache
2025-07-21 22:32:17,580 - INFO - PlaceholderGenerator initialized
2025-07-21 22:32:17,582 - INFO - Loaded cache metadata with 0 entries
2025-07-21 22:32:17,585 - INFO - Started cache cleanup thread
2025-07-21 22:32:17,585 - INFO - DiskCache initialized with max age: 3600s
2025-07-21 22:32:17,586 - INFO - Background worker started
2025-07-21 22:32:17,586 - INFO - BackgroundLoader initialized
2025-07-21 22:32:17,587 - INFO - UIUpdater initialized
2025-07-21 22:32:17,587 - INFO - Loaded placeholders from cache
2025-07-21 22:32:17,587 - INFO - Loaded placeholder data
2025-07-21 22:32:17,606 - INFO - Queued task weekly_stats
2025-07-21 22:32:17,606 - INFO - Queued task game_history_page_0
2025-07-21 22:32:17,606 - INFO - Queued task summary_stats
2025-07-21 22:32:17,606 - INFO - Started background loading
2025-07-21 22:32:17,606 - INFO - StatsLoader initialized
2025-07-21 22:32:17,607 - INFO - Saved cache metadata with 0 entries
2025-07-21 22:32:17,607 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 22:32:17,607 - INFO - Cleared all function caches
2025-07-21 22:32:17,608 - INFO - Generated weekly stats placeholder
2025-07-21 22:32:17,608 - INFO - Generated game history placeholder with 10 records
2025-07-21 22:32:17,608 - INFO - Generated summary stats placeholder
2025-07-21 22:32:17,608 - INFO - Got weekly stats: 7 days
2025-07-21 22:32:17,610 - INFO - Saved all placeholders to cache
2025-07-21 22:32:17,610 - INFO - Cleared all caches
2025-07-21 22:32:17,610 - INFO - Weekly stats loaded
2025-07-21 22:32:17,610 - INFO - Task weekly_stats completed in 0.0032s
2025-07-21 22:32:17,611 - INFO - Got game history page 0: 0 records
2025-07-21 22:32:17,613 - INFO - Game history page 0 loaded
2025-07-21 22:32:17,614 - INFO - Task game_history_page_0 completed in 0.0006s
2025-07-21 22:32:17,614 - INFO - Got summary stats
2025-07-21 22:32:17,615 - INFO - Summary stats loaded
2025-07-21 22:32:17,616 - INFO - Task summary_stats completed in 0.0010s
2025-07-21 22:32:17,618 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 22:32:17,620 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 22:32:30,281 - ERROR - Error recording game start event in database: Cannot operate on a closed database.
2025-07-21 22:32:30,286 - INFO - Statistics saved to data\stats.json
2025-07-21 22:32:30,286 - INFO - Game start event added to JSON stats (fallback)
2025-07-21 22:32:30,291 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:30,294 - INFO - Started background data loading
2025-07-21 22:32:30,294 - INFO - Forced data refresh
2025-07-21 22:32:30,294 - INFO - Game start recorded with optimized integration: True
2025-07-21 22:32:30,296 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:30,297 - INFO - Loaded summary data
2025-07-21 22:32:30,298 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:30,299 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:30,302 - INFO - Saved 7 items to cache
2025-07-21 22:32:30,302 - INFO - Background data loading completed
2025-07-21 22:32:30,309 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:30,311 - INFO - Started background data loading
2025-07-21 22:32:30,312 - INFO - Forced data refresh
2025-07-21 22:32:30,312 - INFO - Forced refresh of optimized loader data
2025-07-21 22:32:30,313 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:30,316 - INFO - Loaded summary data
2025-07-21 22:32:30,318 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:30,320 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:30,323 - INFO - Saved 7 items to cache
2025-07-21 22:32:30,323 - INFO - Background data loading completed
2025-07-21 22:32:30,330 - INFO - Forced refresh of thread_safe_db data
2025-07-21 22:32:30,332 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:30,332 - INFO - Cache cleared
2025-07-21 22:32:30,332 - INFO - Cleared all preloader cache data
2025-07-21 22:32:30,333 - INFO - Starting stats data preloading
2025-07-21 22:32:30,333 - INFO - Started stats data preloading
2025-07-21 22:32:30,333 - INFO - Loading data using optimized functions
2025-07-21 22:32:30,336 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:30,335 - INFO - Cleared stats preloader cache
2025-07-21 22:32:30,336 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:30", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:30,340 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:30,342 - INFO - Posted refresh_stats event
2025-07-21 22:32:30,343 - INFO - Processed game_started event: True
2025-07-21 22:32:30,343 - INFO - Preloaded summary data
2025-07-21 22:32:30,344 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:30,346 - INFO - Preloaded wallet data
2025-07-21 22:32:30,347 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:32:30,348 - INFO - Saved 8 items to persistent cache
2025-07-21 22:32:36,154 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:36,154 - INFO - Cache cleared
2025-07-21 22:32:36,154 - INFO - Cleared all preloader cache data
2025-07-21 22:32:36,155 - INFO - Starting stats data preloading
2025-07-21 22:32:36,155 - INFO - Loading data using optimized functions
2025-07-21 22:32:36,156 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:36,156 - INFO - Started stats data preloading
2025-07-21 22:32:36,156 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:36,158 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:36,159 - INFO - Preloaded summary data
2025-07-21 22:32:36,159 - INFO - Saved cache metadata with 0 entries
2025-07-21 22:32:36,159 - INFO - Cleared 4 entries from cache in namespace stats
2025-07-21 22:32:36,160 - INFO - Cleared all function caches
2025-07-21 22:32:36,160 - INFO - Generated weekly stats placeholder
2025-07-21 22:32:36,161 - INFO - Generated game history placeholder with 10 records
2025-07-21 22:32:36,161 - INFO - Generated summary stats placeholder
2025-07-21 22:32:36,165 - INFO - Saved all placeholders to cache
2025-07-21 22:32:36,165 - INFO - Cleared all caches
2025-07-21 22:32:36,192 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:36,192 - INFO - Preloaded wallet data
2025-07-21 22:32:36,193 - INFO - Stats data preloaded successfully in 0.04 seconds
2025-07-21 22:32:36,194 - INFO - Saved 8 items to persistent cache
2025-07-21 22:32:36,268 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 22:32:36,269 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 22:32:38,549 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:32:39,426 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:32:48,437 - ERROR - Error recording game start event in database: Cannot operate on a closed database.
2025-07-21 22:32:48,452 - INFO - Statistics saved to data\stats.json
2025-07-21 22:32:48,452 - INFO - Game start event added to JSON stats (fallback)
2025-07-21 22:32:48,452 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:48,453 - INFO - Started background data loading
2025-07-21 22:32:48,454 - INFO - Forced data refresh
2025-07-21 22:32:48,454 - INFO - Game start recorded with optimized integration: True
2025-07-21 22:32:48,457 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:48,458 - INFO - Loaded summary data
2025-07-21 22:32:48,460 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:48,461 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:48,462 - INFO - Saved 7 items to cache
2025-07-21 22:32:48,463 - INFO - Background data loading completed
2025-07-21 22:32:48,474 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:32:48,476 - INFO - Started background data loading
2025-07-21 22:32:48,476 - INFO - Forced data refresh
2025-07-21 22:32:48,476 - INFO - Forced refresh of optimized loader data
2025-07-21 22:32:48,478 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:32:48,478 - INFO - Loaded summary data
2025-07-21 22:32:48,479 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:32:48,479 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:32:48,481 - INFO - Saved 7 items to cache
2025-07-21 22:32:48,482 - INFO - Background data loading completed
2025-07-21 22:32:48,484 - INFO - Forced refresh of thread_safe_db data
2025-07-21 22:32:48,494 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:48,494 - INFO - Cache cleared
2025-07-21 22:32:48,494 - INFO - Cleared all preloader cache data
2025-07-21 22:32:48,501 - INFO - Starting stats data preloading
2025-07-21 22:32:48,502 - INFO - Loading data using optimized functions
2025-07-21 22:32:48,502 - INFO - Started stats data preloading
2025-07-21 22:32:48,504 - INFO - Cleared stats preloader cache
2025-07-21 22:32:48,507 - INFO - Posted refresh_stats event
2025-07-21 22:32:48,509 - INFO - Processed game_started event: True
2025-07-21 22:32:53,505 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:53", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:53,508 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:53,509 - INFO - Preloaded summary data
2025-07-21 22:32:53,510 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:53,511 - INFO - Preloaded wallet data
2025-07-21 22:32:53,511 - INFO - Stats data preloaded successfully in 5.01 seconds
2025-07-21 22:32:53,515 - INFO - Saved 8 items to persistent cache
2025-07-21 22:32:58,578 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:58,578 - INFO - Cache cleared
2025-07-21 22:32:58,578 - INFO - Cleared all preloader cache data
2025-07-21 22:32:58,579 - INFO - Starting stats data preloading
2025-07-21 22:32:58,579 - INFO - Loading data using optimized functions
2025-07-21 22:32:58,579 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:32:58,579 - INFO - Started stats data preloading
2025-07-21 22:32:58,580 - INFO - DB Operation: {"timestamp": "2025-07-21 22:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:32:58,581 - INFO - Saved cache metadata with 0 entries
2025-07-21 22:32:58,581 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 22:32:58,581 - INFO - Cleared all function caches
2025-07-21 22:32:58,581 - INFO - Generated weekly stats placeholder
2025-07-21 22:32:58,582 - INFO - Generated game history placeholder with 10 records
2025-07-21 22:32:58,582 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:32:58,582 - INFO - Generated summary stats placeholder
2025-07-21 22:32:58,585 - INFO - Preloaded summary data
2025-07-21 22:32:58,585 - INFO - Saved all placeholders to cache
2025-07-21 22:32:58,585 - INFO - Cleared all caches
2025-07-21 22:32:58,587 - INFO - Preloaded game history (0 records)
2025-07-21 22:32:58,587 - INFO - Preloaded wallet data
2025-07-21 22:32:58,587 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:32:58,588 - INFO - Saved 0 items to persistent cache
2025-07-21 22:32:58,590 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 22:32:58,592 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 22:33:01,585 - INFO - draw_background: 0.0363s
2025-07-21 22:33:01,680 - INFO - draw_stats_page: 0.1316s
2025-07-21 22:33:01,694 - INFO - draw_background: 0.0000s
2025-07-21 22:33:01,696 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:01,734 - INFO - draw_stats_page: 0.0409s
2025-07-21 22:33:01,804 - INFO - draw_background: 0.0009s
2025-07-21 22:33:01,843 - INFO - draw_stats_page: 0.0399s
2025-07-21 22:33:01,911 - INFO - draw_background: 0.0010s
2025-07-21 22:33:01,948 - INFO - draw_stats_page: 0.0380s
2025-07-21 22:33:02,018 - INFO - draw_background: 0.0010s
2025-07-21 22:33:02,053 - INFO - draw_stats_page: 0.0367s
2025-07-21 22:33:02,123 - INFO - draw_background: 0.0000s
2025-07-21 22:33:02,160 - INFO - draw_stats_page: 0.0374s
2025-07-21 22:33:02,229 - INFO - draw_background: 0.0008s
2025-07-21 22:33:02,230 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:02,305 - INFO - draw_stats_page: 0.0773s
2025-07-21 22:33:02,344 - INFO - draw_background: 0.0016s
2025-07-21 22:33:02,429 - INFO - draw_stats_page: 0.0866s
2025-07-21 22:33:02,449 - INFO - draw_background: 0.0005s
2025-07-21 22:33:02,534 - INFO - draw_stats_page: 0.0855s
2025-07-21 22:33:02,553 - INFO - draw_background: 0.0010s
2025-07-21 22:33:02,612 - INFO - draw_stats_page: 0.0600s
2025-07-21 22:33:02,666 - INFO - draw_background: 0.0017s
2025-07-21 22:33:02,820 - INFO - draw_stats_page: 0.1563s
2025-07-21 22:33:02,859 - INFO - draw_background: 0.0030s
2025-07-21 22:33:02,861 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:02,997 - INFO - draw_stats_page: 0.1407s
2025-07-21 22:33:03,023 - INFO - draw_background: 0.0037s
2025-07-21 22:33:03,049 - INFO - Admin button added to stats page
2025-07-21 22:33:03,073 - INFO - draw_stats_page: 0.0541s
2025-07-21 22:33:03,124 - INFO - draw_background: 0.0020s
2025-07-21 22:33:03,182 - INFO - draw_stats_page: 0.0599s
2025-07-21 22:33:03,230 - INFO - draw_background: 0.0000s
2025-07-21 22:33:03,371 - INFO - draw_stats_page: 0.1414s
2025-07-21 22:33:03,408 - INFO - draw_background: 0.0027s
2025-07-21 22:33:03,547 - INFO - draw_stats_page: 0.1420s
2025-07-21 22:33:03,568 - INFO - draw_background: 0.0015s
2025-07-21 22:33:03,572 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:03,650 - INFO - draw_stats_page: 0.0834s
2025-07-21 22:33:03,679 - INFO - draw_background: 0.0020s
2025-07-21 22:33:03,760 - INFO - draw_stats_page: 0.0831s
2025-07-21 22:33:03,792 - INFO - draw_background: 0.0000s
2025-07-21 22:33:03,864 - INFO - draw_stats_page: 0.0725s
2025-07-21 22:33:03,903 - INFO - draw_background: 0.0015s
2025-07-21 22:33:03,987 - INFO - draw_stats_page: 0.0858s
2025-07-21 22:33:04,009 - INFO - draw_background: 0.0010s
2025-07-21 22:33:04,090 - INFO - draw_stats_page: 0.0829s
2025-07-21 22:33:04,127 - INFO - draw_background: 0.0015s
2025-07-21 22:33:04,128 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:04,180 - INFO - draw_stats_page: 0.0544s
2025-07-21 22:33:04,273 - INFO - draw_background: 0.0010s
2025-07-21 22:33:04,372 - INFO - draw_stats_page: 0.0998s
2025-07-21 22:33:04,405 - INFO - draw_background: 0.0020s
2025-07-21 22:33:04,523 - INFO - draw_stats_page: 0.1195s
2025-07-21 22:33:04,549 - INFO - draw_background: 0.0010s
2025-07-21 22:33:04,598 - INFO - draw_stats_page: 0.0493s
2025-07-21 22:33:04,693 - INFO - draw_background: 0.0010s
2025-07-21 22:33:04,736 - INFO - draw_stats_page: 0.0439s
2025-07-21 22:33:04,833 - INFO - draw_background: 0.0000s
2025-07-21 22:33:04,834 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:04,880 - INFO - draw_stats_page: 0.0488s
2025-07-21 22:33:04,975 - INFO - draw_background: 0.0020s
2025-07-21 22:33:05,086 - INFO - draw_stats_page: 0.1133s
2025-07-21 22:33:05,119 - INFO - draw_background: 0.0020s
2025-07-21 22:33:05,195 - INFO - draw_stats_page: 0.0778s
2025-07-21 22:33:05,227 - INFO - draw_background: 0.0010s
2025-07-21 22:33:05,305 - INFO - draw_stats_page: 0.0803s
2025-07-21 22:33:05,337 - INFO - draw_background: 0.0011s
2025-07-21 22:33:05,412 - INFO - draw_stats_page: 0.0758s
2025-07-21 22:33:05,444 - INFO - draw_background: 0.0010s
2025-07-21 22:33:05,446 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:05,528 - INFO - draw_stats_page: 0.0850s
2025-07-21 22:33:05,564 - INFO - draw_background: 0.0010s
2025-07-21 22:33:05,644 - INFO - draw_stats_page: 0.0800s
2025-07-21 22:33:05,676 - INFO - draw_background: 0.0010s
2025-07-21 22:33:05,747 - INFO - draw_stats_page: 0.0724s
2025-07-21 22:33:05,780 - INFO - draw_background: 0.0000s
2025-07-21 22:33:05,865 - INFO - draw_stats_page: 0.0864s
2025-07-21 22:33:05,888 - INFO - draw_background: 0.0000s
2025-07-21 22:33:05,932 - INFO - draw_stats_page: 0.0440s
2025-07-21 22:33:06,025 - INFO - draw_background: 0.0009s
2025-07-21 22:33:06,026 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:06,066 - INFO - draw_stats_page: 0.0421s
2025-07-21 22:33:06,158 - INFO - draw_background: 0.0010s
2025-07-21 22:33:06,252 - INFO - draw_stats_page: 0.0951s
2025-07-21 22:33:06,276 - INFO - draw_background: 0.0010s
2025-07-21 22:33:06,313 - INFO - draw_stats_page: 0.0371s
2025-07-21 22:33:06,410 - INFO - draw_background: 0.0010s
2025-07-21 22:33:06,508 - INFO - draw_stats_page: 0.0992s
2025-07-21 22:33:06,546 - INFO - draw_background: 0.0010s
2025-07-21 22:33:06,608 - INFO - draw_stats_page: 0.0631s
2025-07-21 22:33:06,709 - INFO - draw_background: 0.0020s
2025-07-21 22:33:06,712 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:06,737 - INFO - draw_stats_page: 0.0310s
2025-07-21 22:33:06,852 - INFO - draw_background: 0.0025s
2025-07-21 22:33:06,866 - INFO - draw_stats_page: 0.0166s
2025-07-21 22:33:06,983 - INFO - draw_background: 0.0010s
2025-07-21 22:33:06,996 - INFO - draw_stats_page: 0.0143s
2025-07-21 22:33:07,119 - INFO - draw_background: 0.0019s
2025-07-21 22:33:07,134 - INFO - draw_stats_page: 0.0183s
2025-07-21 22:33:07,253 - INFO - draw_background: 0.0019s
2025-07-21 22:33:07,266 - INFO - draw_stats_page: 0.0163s
2025-07-21 22:33:07,386 - INFO - draw_background: 0.0025s
2025-07-21 22:33:07,389 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:07,408 - INFO - draw_stats_page: 0.0237s
2025-07-21 22:33:07,528 - INFO - draw_background: 0.0009s
2025-07-21 22:33:07,538 - INFO - draw_stats_page: 0.0103s
2025-07-21 22:33:07,666 - INFO - draw_background: 0.0020s
2025-07-21 22:33:07,679 - INFO - draw_stats_page: 0.0162s
2025-07-21 22:33:07,798 - INFO - draw_background: 0.0019s
2025-07-21 22:33:07,812 - INFO - draw_stats_page: 0.0168s
2025-07-21 22:33:07,932 - INFO - draw_background: 0.0017s
2025-07-21 22:33:07,949 - INFO - draw_stats_page: 0.0204s
2025-07-21 22:33:08,080 - INFO - draw_background: 0.0022s
2025-07-21 22:33:08,085 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:08,100 - INFO - draw_stats_page: 0.0231s
2025-07-21 22:33:08,224 - INFO - draw_background: 0.0031s
2025-07-21 22:33:08,241 - INFO - draw_stats_page: 0.0200s
2025-07-21 22:33:08,362 - INFO - draw_background: 0.0029s
2025-07-21 22:33:08,376 - INFO - draw_stats_page: 0.0174s
2025-07-21 22:33:08,495 - INFO - draw_background: 0.0020s
2025-07-21 22:33:08,505 - INFO - draw_stats_page: 0.0118s
2025-07-21 22:33:08,628 - INFO - draw_background: 0.0020s
2025-07-21 22:33:08,643 - INFO - draw_stats_page: 0.0175s
2025-07-21 22:33:08,762 - INFO - draw_background: 0.0027s
2025-07-21 22:33:08,765 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:08,780 - INFO - draw_stats_page: 0.0208s
2025-07-21 22:33:08,895 - INFO - draw_background: 0.0027s
2025-07-21 22:33:08,908 - INFO - draw_stats_page: 0.0160s
2025-07-21 22:33:09,028 - INFO - draw_background: 0.0019s
2025-07-21 22:33:09,043 - INFO - draw_stats_page: 0.0179s
2025-07-21 22:33:09,164 - INFO - draw_background: 0.0021s
2025-07-21 22:33:09,177 - INFO - draw_stats_page: 0.0159s
2025-07-21 22:33:09,228 - INFO - draw_background: 0.0005s
2025-07-21 22:33:09,234 - INFO - draw_stats_page: 0.0061s
2025-07-21 22:33:09,365 - INFO - draw_background: 0.0019s
2025-07-21 22:33:09,367 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:09,378 - INFO - draw_stats_page: 0.0146s
2025-07-21 22:33:09,434 - INFO - draw_background: 0.0020s
2025-07-21 22:33:09,444 - INFO - draw_stats_page: 0.0117s
2025-07-21 22:33:09,565 - INFO - draw_background: 0.0020s
2025-07-21 22:33:09,579 - INFO - draw_stats_page: 0.0157s
2025-07-21 22:33:09,696 - INFO - draw_background: 0.0009s
2025-07-21 22:33:09,704 - INFO - draw_stats_page: 0.0087s
2025-07-21 22:33:09,830 - INFO - draw_background: 0.0026s
2025-07-21 22:33:09,856 - INFO - draw_stats_page: 0.0278s
2025-07-21 22:33:09,975 - INFO - draw_background: 0.0011s
2025-07-21 22:33:09,977 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:09,984 - INFO - draw_stats_page: 0.0108s
2025-07-21 22:33:10,109 - INFO - draw_background: 0.0029s
2025-07-21 22:33:10,128 - INFO - draw_stats_page: 0.0210s
2025-07-21 22:33:10,248 - INFO - draw_background: 0.0031s
2025-07-21 22:33:10,262 - INFO - draw_stats_page: 0.0170s
2025-07-21 22:33:10,378 - INFO - draw_background: 0.0010s
2025-07-21 22:33:10,387 - INFO - draw_stats_page: 0.0092s
2025-07-21 22:33:10,514 - INFO - draw_background: 0.0026s
2025-07-21 22:33:10,530 - INFO - draw_stats_page: 0.0173s
2025-07-21 22:33:10,649 - INFO - draw_background: 0.0032s
2025-07-21 22:33:10,652 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:10,666 - INFO - draw_stats_page: 0.0203s
2025-07-21 22:33:10,781 - INFO - draw_background: 0.0010s
2025-07-21 22:33:10,788 - INFO - draw_stats_page: 0.0088s
2025-07-21 22:33:10,915 - INFO - draw_background: 0.0021s
2025-07-21 22:33:10,930 - INFO - draw_stats_page: 0.0176s
2025-07-21 22:33:11,049 - INFO - draw_background: 0.0019s
2025-07-21 22:33:11,063 - INFO - draw_stats_page: 0.0167s
2025-07-21 22:33:11,182 - INFO - draw_background: 0.0021s
2025-07-21 22:33:11,199 - INFO - draw_stats_page: 0.0185s
2025-07-21 22:33:11,323 - INFO - draw_background: 0.0026s
2025-07-21 22:33:11,325 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:11,339 - INFO - draw_stats_page: 0.0196s
2025-07-21 22:33:11,454 - INFO - draw_background: 0.0026s
2025-07-21 22:33:11,469 - INFO - draw_stats_page: 0.0169s
2025-07-21 22:33:11,589 - INFO - draw_background: 0.0026s
2025-07-21 22:33:11,603 - INFO - draw_stats_page: 0.0165s
2025-07-21 22:33:11,721 - INFO - draw_background: 0.0022s
2025-07-21 22:33:11,735 - INFO - draw_stats_page: 0.0163s
2025-07-21 22:33:11,787 - INFO - draw_background: 0.0014s
2025-07-21 22:33:11,795 - INFO - draw_stats_page: 0.0089s
2025-07-21 22:33:11,922 - INFO - draw_background: 0.0016s
2025-07-21 22:33:11,924 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:11,930 - INFO - draw_stats_page: 0.0101s
2025-07-21 22:33:12,054 - INFO - draw_background: 0.0010s
2025-07-21 22:33:12,062 - INFO - draw_stats_page: 0.0090s
2025-07-21 22:33:12,120 - INFO - draw_background: 0.0010s
2025-07-21 22:33:12,129 - INFO - draw_stats_page: 0.0096s
2025-07-21 22:33:12,253 - INFO - draw_background: 0.0011s
2025-07-21 22:33:12,260 - INFO - draw_stats_page: 0.0084s
2025-07-21 22:33:12,387 - INFO - draw_background: 0.0009s
2025-07-21 22:33:12,395 - INFO - draw_stats_page: 0.0096s
2025-07-21 22:33:12,454 - INFO - draw_background: 0.0009s
2025-07-21 22:33:12,455 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:12,460 - INFO - draw_stats_page: 0.0075s
2025-07-21 22:33:12,591 - INFO - draw_background: 0.0035s
2025-07-21 22:33:12,606 - INFO - draw_stats_page: 0.0183s
2025-07-21 22:33:12,662 - INFO - draw_background: 0.0040s
2025-07-21 22:33:12,677 - INFO - draw_stats_page: 0.0192s
2025-07-21 22:33:12,727 - INFO - draw_background: 0.0020s
2025-07-21 22:33:12,736 - INFO - draw_stats_page: 0.0106s
2025-07-21 22:33:12,860 - INFO - draw_background: 0.0026s
2025-07-21 22:33:12,873 - INFO - draw_stats_page: 0.0160s
2025-07-21 22:33:13,000 - INFO - draw_background: 0.0029s
2025-07-21 22:33:13,004 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:13,023 - INFO - draw_stats_page: 0.0257s
2025-07-21 22:33:13,140 - INFO - draw_background: 0.0016s
2025-07-21 22:33:13,148 - INFO - draw_stats_page: 0.0082s
2025-07-21 22:33:13,274 - INFO - draw_background: 0.0009s
2025-07-21 22:33:13,283 - INFO - draw_stats_page: 0.0090s
2025-07-21 22:33:13,406 - INFO - draw_background: 0.0009s
2025-07-21 22:33:13,415 - INFO - draw_stats_page: 0.0092s
2025-07-21 22:33:13,474 - INFO - draw_background: 0.0009s
2025-07-21 22:33:13,482 - INFO - draw_stats_page: 0.0095s
2025-07-21 22:33:13,608 - INFO - draw_background: 0.0010s
2025-07-21 22:33:13,609 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:13,617 - INFO - draw_stats_page: 0.0112s
2025-07-21 22:33:13,740 - INFO - draw_background: 0.0017s
2025-07-21 22:33:13,755 - INFO - draw_stats_page: 0.0165s
2025-07-21 22:33:13,874 - INFO - draw_background: 0.0026s
2025-07-21 22:33:13,888 - INFO - draw_stats_page: 0.0168s
2025-07-21 22:33:13,940 - INFO - draw_background: 0.0019s
2025-07-21 22:33:13,948 - INFO - draw_stats_page: 0.0090s
2025-07-21 22:33:14,073 - INFO - draw_background: 0.0010s
2025-07-21 22:33:14,078 - INFO - draw_stats_page: 0.0062s
2025-07-21 22:33:14,206 - INFO - draw_background: 0.0010s
2025-07-21 22:33:14,207 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:14,211 - INFO - draw_stats_page: 0.0061s
2025-07-21 22:33:14,341 - INFO - draw_background: 0.0023s
2025-07-21 22:33:14,356 - INFO - draw_stats_page: 0.0156s
2025-07-21 22:33:14,475 - INFO - draw_background: 0.0027s
2025-07-21 22:33:14,486 - INFO - draw_stats_page: 0.0140s
2025-07-21 22:33:14,542 - INFO - load_statistics: 0.0010s
2025-07-21 22:33:14,543 - INFO - draw_background: 0.0010s
2025-07-21 22:33:14,544 - INFO - Starting stats data preloading
2025-07-21 22:33:14,544 - INFO - Started stats data preloading
2025-07-21 22:33:14,544 - INFO - Loading data using optimized functions
2025-07-21 22:33:14,549 - INFO - draw_stats_page: 0.0065s
2025-07-21 22:33:14,552 - INFO - FIRST STARTUP: Created new time reference
2025-07-21 22:33:14,556 - INFO - Time Manager initialized. Reference: 2025-07-21T19:33:14.552181+00:00
2025-07-21 22:33:14,672 - INFO - draw_background: 0.0007s
2025-07-21 22:33:14,676 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:33:14,677 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:14,809 - INFO - draw_background: 0.0024s
2025-07-21 22:33:14,827 - INFO - draw_stats_page: 0.0210s
2025-07-21 22:33:14,944 - INFO - draw_background: 0.0015s
2025-07-21 22:33:14,959 - INFO - draw_stats_page: 0.0149s
2025-07-21 22:33:15,079 - INFO - draw_background: 0.0029s
2025-07-21 22:33:15,095 - INFO - draw_stats_page: 0.0183s
2025-07-21 22:33:15,212 - INFO - draw_background: 0.0022s
2025-07-21 22:33:15,228 - INFO - draw_stats_page: 0.0184s
2025-07-21 22:33:15,355 - INFO - draw_background: 0.0027s
2025-07-21 22:33:15,367 - INFO - draw_stats_page: 0.0155s
2025-07-21 22:33:15,370 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:15,486 - INFO - draw_background: 0.0010s
2025-07-21 22:33:15,495 - INFO - draw_stats_page: 0.0101s
2025-07-21 22:33:15,620 - INFO - draw_background: 0.0009s
2025-07-21 22:33:15,629 - INFO - draw_stats_page: 0.0096s
2025-07-21 22:33:15,755 - INFO - draw_background: 0.0018s
2025-07-21 22:33:15,771 - INFO - draw_stats_page: 0.0180s
2025-07-21 22:33:15,887 - INFO - draw_background: 0.0015s
2025-07-21 22:33:15,902 - INFO - draw_stats_page: 0.0171s
2025-07-21 22:33:16,024 - INFO - draw_background: 0.0009s
2025-07-21 22:33:16,032 - INFO - draw_stats_page: 0.0085s
2025-07-21 22:33:16,034 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:16,161 - INFO - draw_background: 0.0025s
2025-07-21 22:33:16,174 - INFO - draw_stats_page: 0.0162s
2025-07-21 22:33:16,292 - INFO - draw_background: 0.0017s
2025-07-21 22:33:16,308 - INFO - draw_stats_page: 0.0174s
2025-07-21 22:33:16,427 - INFO - draw_background: 0.0019s
2025-07-21 22:33:16,440 - INFO - draw_stats_page: 0.0149s
2025-07-21 22:33:16,559 - INFO - draw_background: 0.0020s
2025-07-21 22:33:16,577 - INFO - draw_stats_page: 0.0197s
2025-07-21 22:33:16,697 - INFO - draw_background: 0.0019s
2025-07-21 22:33:16,710 - INFO - draw_stats_page: 0.0153s
2025-07-21 22:33:16,713 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:16,830 - INFO - draw_background: 0.0026s
2025-07-21 22:33:16,922 - INFO - draw_stats_page: 0.0943s
2025-07-21 22:33:16,959 - INFO - draw_background: 0.0022s
2025-07-21 22:33:17,035 - INFO - draw_stats_page: 0.0789s
2025-07-21 22:33:17,069 - INFO - draw_background: 0.0010s
2025-07-21 22:33:17,149 - INFO - draw_stats_page: 0.0813s
2025-07-21 22:33:17,182 - INFO - draw_background: 0.0016s
2025-07-21 22:33:17,264 - INFO - draw_stats_page: 0.0835s
2025-07-21 22:33:17,295 - INFO - draw_background: 0.0010s
2025-07-21 22:33:17,371 - INFO - draw_stats_page: 0.0760s
2025-07-21 22:33:17,373 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:17,406 - INFO - draw_background: 0.0019s
2025-07-21 22:33:17,484 - INFO - draw_stats_page: 0.0798s
2025-07-21 22:33:17,510 - INFO - draw_background: 0.0006s
2025-07-21 22:33:17,555 - INFO - draw_stats_page: 0.0459s
2025-07-21 22:33:17,651 - INFO - draw_background: 0.0020s
2025-07-21 22:33:17,774 - INFO - draw_stats_page: 0.1261s
2025-07-21 22:33:17,818 - INFO - draw_background: 0.0019s
2025-07-21 22:33:17,935 - INFO - draw_stats_page: 0.1178s
2025-07-21 22:33:17,966 - INFO - draw_background: 0.0020s
2025-07-21 22:33:18,129 - INFO - draw_stats_page: 0.1643s
2025-07-21 22:33:18,131 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:18,165 - INFO - draw_background: 0.0010s
2025-07-21 22:33:18,213 - INFO - draw_stats_page: 0.0497s
2025-07-21 22:33:18,306 - INFO - draw_background: 0.0015s
2025-07-21 22:33:18,412 - INFO - draw_stats_page: 0.1067s
2025-07-21 22:33:18,449 - INFO - draw_background: 0.0015s
2025-07-21 22:33:18,535 - INFO - draw_stats_page: 0.0873s
2025-07-21 22:33:18,564 - INFO - draw_background: 0.0005s
2025-07-21 22:33:18,621 - INFO - draw_stats_page: 0.0580s
2025-07-21 22:33:18,718 - INFO - draw_background: 0.0020s
2025-07-21 22:33:18,828 - INFO - draw_stats_page: 0.1125s
2025-07-21 22:33:18,832 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:18,876 - INFO - draw_background: 0.0010s
2025-07-21 22:33:18,948 - INFO - draw_stats_page: 0.0735s
2025-07-21 22:33:18,976 - INFO - draw_background: 0.0009s
2025-07-21 22:33:19,071 - INFO - draw_stats_page: 0.0947s
2025-07-21 22:33:19,110 - INFO - draw_background: 0.0016s
2025-07-21 22:33:19,218 - INFO - draw_stats_page: 0.1083s
2025-07-21 22:33:19,257 - INFO - draw_background: 0.0020s
2025-07-21 22:33:19,363 - INFO - draw_stats_page: 0.1083s
2025-07-21 22:33:19,402 - INFO - draw_background: 0.0017s
2025-07-21 22:33:19,502 - INFO - draw_stats_page: 0.1023s
2025-07-21 22:33:19,506 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:19,543 - INFO - draw_background: 0.0009s
2025-07-21 22:33:19,552 - INFO - DB Operation: {"timestamp": "2025-07-21 22:33:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:33:19,556 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:33:19,556 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:33:19,559 - INFO - DB Operation: {"timestamp": "2025-07-21 22:33:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:33:19,598 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:33:19,601 - INFO - DB Operation: {"timestamp": "2025-07-21 22:33:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:33:19,605 - INFO - Preloaded summary data
2025-07-21 22:33:19,610 - INFO - Preloaded game history (0 records)
2025-07-21 22:33:19,612 - INFO - Preloaded wallet data
2025-07-21 22:33:19,613 - INFO - Stats data preloaded successfully in 5.07 seconds
2025-07-21 22:33:19,617 - INFO - Saved 8 items to persistent cache
2025-07-21 22:33:19,652 - INFO - draw_stats_page: 0.1097s
2025-07-21 22:33:19,682 - INFO - draw_background: 0.0010s
2025-07-21 22:33:19,746 - INFO - draw_stats_page: 0.0641s
2025-07-21 22:33:19,836 - INFO - draw_background: 0.0009s
2025-07-21 22:33:19,892 - INFO - draw_stats_page: 0.0555s
2025-07-21 22:33:19,986 - INFO - draw_background: 0.0010s
2025-07-21 22:33:20,042 - INFO - draw_stats_page: 0.0562s
2025-07-21 22:33:20,138 - INFO - draw_background: 0.0020s
2025-07-21 22:33:20,289 - INFO - draw_stats_page: 0.1531s
2025-07-21 22:33:20,293 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:20,342 - INFO - draw_background: 0.0020s
2025-07-21 22:33:20,455 - INFO - draw_stats_page: 0.1144s
2025-07-21 22:33:20,489 - INFO - draw_background: 0.0010s
2025-07-21 22:33:20,571 - INFO - draw_stats_page: 0.0831s
2025-07-21 22:33:20,608 - INFO - draw_background: 0.0009s
2025-07-21 22:33:20,693 - INFO - draw_stats_page: 0.0853s
2025-07-21 22:33:20,727 - INFO - draw_background: 0.0015s
2025-07-21 22:33:20,813 - INFO - draw_stats_page: 0.0885s
2025-07-21 22:33:20,846 - INFO - draw_background: 0.0010s
2025-07-21 22:33:20,927 - INFO - draw_stats_page: 0.0823s
2025-07-21 22:33:20,929 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:20,962 - INFO - draw_background: 0.0014s
2025-07-21 22:33:21,067 - INFO - draw_stats_page: 0.1067s
2025-07-21 22:33:21,092 - INFO - draw_background: 0.0010s
2025-07-21 22:33:21,238 - INFO - draw_stats_page: 0.1463s
2025-07-21 22:33:21,269 - INFO - draw_background: 0.0010s
2025-07-21 22:33:21,309 - INFO - draw_stats_page: 0.0412s
2025-07-21 22:33:21,401 - INFO - draw_background: 0.0010s
2025-07-21 22:33:21,435 - INFO - draw_stats_page: 0.0347s
2025-07-21 22:33:21,537 - INFO - draw_background: 0.0010s
2025-07-21 22:33:21,627 - INFO - draw_stats_page: 0.0910s
2025-07-21 22:33:21,629 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:21,666 - INFO - draw_background: 0.0015s
2025-07-21 22:33:21,757 - INFO - draw_stats_page: 0.0918s
2025-07-21 22:33:21,786 - INFO - draw_background: 0.0020s
2025-07-21 22:33:21,845 - INFO - draw_stats_page: 0.0617s
2025-07-21 22:33:21,940 - INFO - draw_background: 0.0010s
2025-07-21 22:33:21,995 - INFO - draw_stats_page: 0.0556s
2025-07-21 22:33:22,090 - INFO - draw_background: 0.0005s
2025-07-21 22:33:22,146 - INFO - draw_stats_page: 0.0558s
2025-07-21 22:33:22,240 - INFO - draw_background: 0.0010s
2025-07-21 22:33:22,297 - INFO - draw_stats_page: 0.0578s
2025-07-21 22:33:22,299 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:22,396 - INFO - draw_background: 0.0020s
2025-07-21 22:33:22,508 - INFO - draw_stats_page: 0.1144s
2025-07-21 22:33:22,542 - INFO - draw_background: 0.0005s
2025-07-21 22:33:22,593 - INFO - draw_stats_page: 0.0520s
2025-07-21 22:33:22,687 - INFO - draw_background: 0.0010s
2025-07-21 22:33:22,752 - INFO - draw_stats_page: 0.0664s
2025-07-21 22:33:22,788 - INFO - draw_background: 0.0016s
2025-07-21 22:33:22,869 - INFO - draw_stats_page: 0.0819s
2025-07-21 22:33:22,906 - INFO - draw_background: 0.0010s
2025-07-21 22:33:22,973 - INFO - draw_stats_page: 0.0681s
2025-07-21 22:33:22,975 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:23,066 - INFO - draw_background: 0.0011s
2025-07-21 22:33:23,108 - INFO - draw_stats_page: 0.0440s
2025-07-21 22:33:23,204 - INFO - draw_background: 0.0010s
2025-07-21 22:33:23,248 - INFO - draw_stats_page: 0.0455s
2025-07-21 22:33:23,341 - INFO - draw_background: 0.0005s
2025-07-21 22:33:23,386 - INFO - draw_stats_page: 0.0453s
2025-07-21 22:33:23,479 - INFO - draw_background: 0.0025s
2025-07-21 22:33:23,590 - INFO - draw_stats_page: 0.1137s
2025-07-21 22:33:23,617 - INFO - draw_background: 0.0010s
2025-07-21 22:33:23,664 - INFO - draw_stats_page: 0.0474s
2025-07-21 22:33:23,665 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:23,759 - INFO - draw_background: 0.0010s
2025-07-21 22:33:23,810 - INFO - draw_stats_page: 0.0517s
2025-07-21 22:33:23,904 - INFO - draw_background: 0.0015s
2025-07-21 22:33:24,020 - INFO - draw_stats_page: 0.1178s
2025-07-21 22:33:24,046 - INFO - draw_background: 0.0010s
2025-07-21 22:33:24,120 - INFO - draw_stats_page: 0.0755s
2025-07-21 22:33:24,154 - INFO - draw_background: 0.0010s
2025-07-21 22:33:24,215 - INFO - draw_stats_page: 0.0621s
2025-07-21 22:33:24,310 - INFO - draw_background: 0.0019s
2025-07-21 22:33:24,446 - INFO - draw_stats_page: 0.1391s
2025-07-21 22:33:24,450 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:24,493 - INFO - draw_background: 0.0009s
2025-07-21 22:33:24,575 - INFO - draw_stats_page: 0.0839s
2025-07-21 22:33:24,603 - INFO - draw_background: 0.0010s
2025-07-21 22:33:24,656 - INFO - draw_stats_page: 0.0531s
2025-07-21 22:33:24,753 - INFO - draw_background: 0.0024s
2025-07-21 22:33:24,876 - INFO - draw_stats_page: 0.1254s
2025-07-21 22:33:24,928 - INFO - draw_background: 0.0020s
2025-07-21 22:33:25,029 - INFO - draw_stats_page: 0.1033s
2025-07-21 22:33:25,052 - INFO - draw_background: 0.0005s
2025-07-21 22:33:25,097 - INFO - draw_stats_page: 0.0453s
2025-07-21 22:33:25,098 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:25,191 - INFO - draw_background: 0.0000s
2025-07-21 22:33:25,288 - INFO - draw_stats_page: 0.0976s
2025-07-21 22:33:25,320 - INFO - draw_background: 0.0016s
2025-07-21 22:33:25,394 - INFO - draw_stats_page: 0.0748s
2025-07-21 22:33:25,427 - INFO - draw_background: 0.0010s
2025-07-21 22:33:25,505 - INFO - draw_stats_page: 0.0791s
2025-07-21 22:33:25,541 - INFO - draw_background: 0.0010s
2025-07-21 22:33:25,621 - INFO - draw_stats_page: 0.0805s
2025-07-21 22:33:25,646 - INFO - draw_background: 0.0010s
2025-07-21 22:33:25,691 - INFO - draw_stats_page: 0.0469s
2025-07-21 22:33:25,692 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:25,786 - INFO - draw_background: 0.0010s
2025-07-21 22:33:25,830 - INFO - draw_stats_page: 0.0452s
2025-07-21 22:33:25,922 - INFO - draw_background: 0.0005s
2025-07-21 22:33:25,966 - INFO - draw_stats_page: 0.0447s
2025-07-21 22:33:26,060 - INFO - draw_background: 0.0006s
2025-07-21 22:33:26,157 - INFO - draw_stats_page: 0.0973s
2025-07-21 22:33:26,183 - INFO - draw_background: 0.0010s
2025-07-21 22:33:26,224 - INFO - draw_stats_page: 0.0421s
2025-07-21 22:33:26,315 - INFO - draw_background: 0.0010s
2025-07-21 22:33:26,378 - INFO - draw_stats_page: 0.0644s
2025-07-21 22:33:26,381 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:26,480 - INFO - draw_background: 0.0016s
2025-07-21 22:33:26,550 - INFO - draw_stats_page: 0.0717s
2025-07-21 22:33:26,640 - INFO - draw_background: 0.0016s
2025-07-21 22:33:26,777 - INFO - draw_stats_page: 0.1380s
2025-07-21 22:33:26,822 - INFO - draw_background: 0.0025s
2025-07-21 22:33:27,015 - INFO - draw_stats_page: 0.1959s
2025-07-21 22:33:27,043 - INFO - draw_background: 0.0000s
2025-07-21 22:33:27,080 - INFO - draw_stats_page: 0.0379s
2025-07-21 22:33:27,175 - INFO - draw_background: 0.0010s
2025-07-21 22:33:27,270 - INFO - draw_stats_page: 0.0963s
2025-07-21 22:33:27,272 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:27,300 - INFO - draw_background: 0.0011s
2025-07-21 22:33:27,357 - INFO - draw_stats_page: 0.0573s
2025-07-21 22:33:27,456 - INFO - draw_background: 0.0012s
2025-07-21 22:33:27,508 - INFO - draw_stats_page: 0.0536s
2025-07-21 22:33:27,613 - INFO - draw_background: 0.0010s
2025-07-21 22:33:27,670 - INFO - draw_stats_page: 0.0582s
2025-07-21 22:33:27,765 - INFO - draw_background: 0.0005s
2025-07-21 22:33:27,826 - INFO - draw_stats_page: 0.0610s
2025-07-21 22:33:27,925 - INFO - draw_background: 0.0018s
2025-07-21 22:33:28,028 - INFO - draw_stats_page: 0.1059s
2025-07-21 22:33:28,029 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:28,053 - INFO - draw_background: 0.0010s
2025-07-21 22:33:28,103 - INFO - draw_stats_page: 0.0504s
2025-07-21 22:33:28,198 - INFO - draw_background: 0.0010s
2025-07-21 22:33:28,251 - INFO - draw_stats_page: 0.0542s
2025-07-21 22:33:28,346 - INFO - draw_background: 0.0011s
2025-07-21 22:33:28,392 - INFO - draw_stats_page: 0.0467s
2025-07-21 22:33:28,484 - INFO - draw_background: 0.0005s
2025-07-21 22:33:28,532 - INFO - draw_stats_page: 0.0487s
2025-07-21 22:33:28,623 - INFO - draw_background: 0.0011s
2025-07-21 22:33:28,668 - INFO - draw_stats_page: 0.0453s
2025-07-21 22:33:28,669 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:28,764 - INFO - draw_background: 0.0011s
2025-07-21 22:33:28,810 - INFO - draw_stats_page: 0.0469s
2025-07-21 22:33:28,904 - INFO - draw_background: 0.0010s
2025-07-21 22:33:28,952 - INFO - draw_stats_page: 0.0486s
2025-07-21 22:33:29,045 - INFO - draw_background: 0.0005s
2025-07-21 22:33:29,087 - INFO - draw_stats_page: 0.0429s
2025-07-21 22:33:29,180 - INFO - draw_background: 0.0010s
2025-07-21 22:33:29,222 - INFO - draw_stats_page: 0.0436s
2025-07-21 22:33:29,313 - INFO - draw_background: 0.0007s
2025-07-21 22:33:29,362 - INFO - draw_stats_page: 0.0491s
2025-07-21 22:33:29,364 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:29,453 - INFO - draw_background: 0.0007s
2025-07-21 22:33:29,498 - INFO - draw_stats_page: 0.0450s
2025-07-21 22:33:29,592 - INFO - draw_background: 0.0005s
2025-07-21 22:33:29,628 - INFO - draw_stats_page: 0.0365s
2025-07-21 22:33:29,727 - INFO - draw_background: 0.0000s
2025-07-21 22:33:29,762 - INFO - draw_stats_page: 0.0358s
2025-07-21 22:33:29,860 - INFO - draw_background: 0.0017s
2025-07-21 22:33:29,948 - INFO - draw_stats_page: 0.0880s
2025-07-21 22:33:29,984 - INFO - draw_background: 0.0011s
2025-07-21 22:33:30,077 - INFO - draw_stats_page: 0.0960s
2025-07-21 22:33:30,079 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:30,117 - INFO - draw_background: 0.0010s
2025-07-21 22:33:30,214 - INFO - draw_stats_page: 0.0996s
2025-07-21 22:33:30,240 - INFO - draw_background: 0.0010s
2025-07-21 22:33:30,291 - INFO - draw_stats_page: 0.0524s
2025-07-21 22:33:30,385 - INFO - draw_background: 0.0010s
2025-07-21 22:33:30,450 - INFO - draw_stats_page: 0.0672s
2025-07-21 22:33:30,547 - INFO - draw_background: 0.0010s
2025-07-21 22:33:30,638 - INFO - draw_stats_page: 0.0937s
2025-07-21 22:33:30,665 - INFO - draw_background: 0.0010s
2025-07-21 22:33:30,722 - INFO - draw_stats_page: 0.0580s
2025-07-21 22:33:30,724 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:30,820 - INFO - draw_background: 0.0016s
2025-07-21 22:33:30,917 - INFO - draw_stats_page: 0.0985s
2025-07-21 22:33:30,954 - INFO - draw_background: 0.0015s
2025-07-21 22:33:31,068 - INFO - draw_stats_page: 0.1161s
2025-07-21 22:33:31,103 - INFO - draw_background: 0.0012s
2025-07-21 22:33:31,233 - INFO - draw_stats_page: 0.1312s
2025-07-21 22:33:31,266 - INFO - draw_background: 0.0010s
2025-07-21 22:33:31,316 - INFO - draw_stats_page: 0.0499s
2025-07-21 22:33:31,409 - INFO - draw_background: 0.0010s
2025-07-21 22:33:31,513 - INFO - draw_stats_page: 0.1042s
2025-07-21 22:33:31,514 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:31,553 - INFO - draw_background: 0.0010s
2025-07-21 22:33:31,638 - INFO - draw_stats_page: 0.0858s
2025-07-21 22:33:31,672 - INFO - draw_background: 0.0006s
2025-07-21 22:33:31,810 - INFO - draw_stats_page: 0.1387s
2025-07-21 22:33:31,860 - INFO - draw_background: 0.0034s
2025-07-21 22:33:31,965 - INFO - draw_stats_page: 0.1069s
2025-07-21 22:33:31,997 - INFO - draw_background: 0.0010s
2025-07-21 22:33:32,072 - INFO - draw_stats_page: 0.0768s
2025-07-21 22:33:32,108 - INFO - draw_background: 0.0019s
2025-07-21 22:33:32,188 - INFO - draw_stats_page: 0.0812s
2025-07-21 22:33:32,189 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:32,222 - INFO - draw_background: 0.0010s
2025-07-21 22:33:32,311 - INFO - draw_stats_page: 0.0900s
2025-07-21 22:33:32,336 - INFO - draw_background: 0.0006s
2025-07-21 22:33:32,378 - INFO - draw_stats_page: 0.0417s
2025-07-21 22:33:32,469 - INFO - draw_background: 0.0009s
2025-07-21 22:33:32,560 - INFO - draw_stats_page: 0.0922s
2025-07-21 22:33:32,583 - INFO - draw_background: 0.0010s
2025-07-21 22:33:32,622 - INFO - draw_stats_page: 0.0392s
2025-07-21 22:33:32,716 - INFO - draw_background: 0.0011s
2025-07-21 22:33:32,805 - INFO - draw_stats_page: 0.0901s
2025-07-21 22:33:32,808 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:32,844 - INFO - draw_background: 0.0010s
2025-07-21 22:33:32,939 - INFO - draw_stats_page: 0.0962s
2025-07-21 22:33:32,977 - INFO - draw_background: 0.0010s
2025-07-21 22:33:33,070 - INFO - draw_stats_page: 0.0942s
2025-07-21 22:33:33,107 - INFO - draw_background: 0.0019s
2025-07-21 22:33:33,200 - INFO - draw_stats_page: 0.0950s
2025-07-21 22:33:33,227 - INFO - draw_background: 0.0010s
2025-07-21 22:33:33,294 - INFO - draw_stats_page: 0.0666s
2025-07-21 22:33:33,411 - INFO - draw_background: 0.0015s
2025-07-21 22:33:33,468 - INFO - draw_stats_page: 0.0586s
2025-07-21 22:33:33,468 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:33,564 - INFO - draw_background: 0.0025s
2025-07-21 22:33:33,663 - INFO - draw_stats_page: 0.1024s
2025-07-21 22:33:33,694 - INFO - draw_background: 0.0010s
2025-07-21 22:33:33,769 - INFO - draw_stats_page: 0.0749s
2025-07-21 22:33:33,800 - INFO - draw_background: 0.0012s
2025-07-21 22:33:33,882 - INFO - draw_stats_page: 0.0824s
2025-07-21 22:33:33,913 - INFO - draw_background: 0.0015s
2025-07-21 22:33:33,997 - INFO - draw_stats_page: 0.0854s
2025-07-21 22:33:34,028 - INFO - draw_background: 0.0005s
2025-07-21 22:33:34,101 - INFO - draw_stats_page: 0.0746s
2025-07-21 22:33:34,103 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:34,137 - INFO - draw_background: 0.0010s
2025-07-21 22:33:34,228 - INFO - draw_stats_page: 0.0928s
2025-07-21 22:33:34,257 - INFO - draw_background: 0.0010s
2025-07-21 22:33:34,302 - INFO - draw_stats_page: 0.0455s
2025-07-21 22:33:34,394 - INFO - draw_background: 0.0010s
2025-07-21 22:33:34,521 - INFO - draw_stats_page: 0.1278s
2025-07-21 22:33:34,572 - INFO - draw_background: 0.0016s
2025-07-21 22:33:34,649 - INFO - draw_stats_page: 0.0792s
2025-07-21 22:33:34,687 - INFO - draw_background: 0.0010s
2025-07-21 22:33:34,786 - INFO - draw_stats_page: 0.1004s
2025-07-21 22:33:34,788 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:34,823 - INFO - draw_background: 0.0025s
2025-07-21 22:33:34,909 - INFO - draw_stats_page: 0.0878s
2025-07-21 22:33:34,942 - INFO - draw_background: 0.0000s
2025-07-21 22:33:35,016 - INFO - draw_stats_page: 0.0760s
2025-07-21 22:33:35,048 - INFO - draw_background: 0.0009s
2025-07-21 22:33:35,093 - INFO - draw_stats_page: 0.0460s
2025-07-21 22:33:35,188 - INFO - draw_background: 0.0025s
2025-07-21 22:33:35,311 - INFO - draw_stats_page: 0.1257s
2025-07-21 22:33:35,347 - INFO - draw_background: 0.0010s
2025-07-21 22:33:35,424 - INFO - draw_stats_page: 0.0791s
2025-07-21 22:33:35,427 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:35,458 - INFO - draw_background: 0.0012s
2025-07-21 22:33:35,541 - INFO - draw_stats_page: 0.0844s
2025-07-21 22:33:35,566 - INFO - draw_background: 0.0000s
2025-07-21 22:33:35,617 - INFO - draw_stats_page: 0.0520s
2025-07-21 22:33:35,710 - INFO - draw_background: 0.0009s
2025-07-21 22:33:35,759 - INFO - draw_stats_page: 0.0501s
2025-07-21 22:33:35,850 - INFO - draw_background: 0.0015s
2025-07-21 22:33:35,923 - INFO - draw_stats_page: 0.0735s
2025-07-21 22:33:35,959 - INFO - draw_background: 0.0015s
2025-07-21 22:33:36,023 - INFO - draw_stats_page: 0.0660s
2025-07-21 22:33:36,024 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:36,117 - INFO - draw_background: 0.0010s
2025-07-21 22:33:36,177 - INFO - draw_stats_page: 0.0610s
2025-07-21 22:33:36,268 - INFO - draw_background: 0.0010s
2025-07-21 22:33:36,357 - INFO - draw_stats_page: 0.0887s
2025-07-21 22:33:36,379 - INFO - draw_background: 0.0010s
2025-07-21 22:33:36,418 - INFO - draw_stats_page: 0.0387s
2025-07-21 22:33:36,512 - INFO - draw_background: 0.0010s
2025-07-21 22:33:36,581 - INFO - draw_stats_page: 0.0699s
2025-07-21 22:33:36,673 - INFO - draw_background: 0.0025s
2025-07-21 22:33:36,814 - INFO - draw_stats_page: 0.1429s
2025-07-21 22:33:36,816 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:36,857 - INFO - draw_background: 0.0016s
2025-07-21 22:33:36,935 - INFO - draw_stats_page: 0.0792s
2025-07-21 22:33:36,964 - INFO - draw_background: 0.0010s
2025-07-21 22:33:37,017 - INFO - draw_stats_page: 0.0537s
2025-07-21 22:33:37,113 - INFO - draw_background: 0.0009s
2025-07-21 22:33:37,166 - INFO - draw_stats_page: 0.0541s
2025-07-21 22:33:37,263 - INFO - draw_background: 0.0010s
2025-07-21 22:33:37,318 - INFO - draw_stats_page: 0.0554s
2025-07-21 22:33:37,415 - INFO - draw_background: 0.0009s
2025-07-21 22:33:37,472 - INFO - draw_stats_page: 0.0580s
2025-07-21 22:33:37,473 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:37,570 - INFO - draw_background: 0.0020s
2025-07-21 22:33:37,669 - INFO - draw_stats_page: 0.1013s
2025-07-21 22:33:37,704 - INFO - draw_background: 0.0025s
2025-07-21 22:33:37,764 - INFO - draw_stats_page: 0.0630s
2025-07-21 22:33:37,857 - INFO - draw_background: 0.0010s
2025-07-21 22:33:37,936 - INFO - draw_stats_page: 0.0805s
2025-07-21 22:33:37,963 - INFO - draw_background: 0.0009s
2025-07-21 22:33:38,011 - INFO - draw_stats_page: 0.0488s
2025-07-21 22:33:38,106 - INFO - draw_background: 0.0016s
2025-07-21 22:33:38,218 - INFO - draw_stats_page: 0.1142s
2025-07-21 22:33:38,221 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:38,265 - INFO - draw_background: 0.0019s
2025-07-21 22:33:38,372 - INFO - draw_stats_page: 0.1089s
2025-07-21 22:33:38,404 - INFO - draw_background: 0.0010s
2025-07-21 22:33:38,479 - INFO - draw_stats_page: 0.0760s
2025-07-21 22:33:38,512 - INFO - draw_background: 0.0016s
2025-07-21 22:33:38,591 - INFO - draw_stats_page: 0.0812s
2025-07-21 22:33:38,622 - INFO - draw_background: 0.0010s
2025-07-21 22:33:38,695 - INFO - draw_stats_page: 0.0738s
2025-07-21 22:33:38,726 - INFO - draw_background: 0.0009s
2025-07-21 22:33:38,801 - INFO - draw_stats_page: 0.0757s
2025-07-21 22:33:38,803 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:38,834 - INFO - draw_background: 0.0011s
2025-07-21 22:33:38,916 - INFO - draw_stats_page: 0.0821s
2025-07-21 22:33:38,947 - INFO - draw_background: 0.0010s
2025-07-21 22:33:38,990 - INFO - draw_stats_page: 0.0438s
2025-07-21 22:33:39,083 - INFO - draw_background: 0.0026s
2025-07-21 22:33:39,290 - INFO - draw_stats_page: 0.2099s
2025-07-21 22:33:39,313 - INFO - draw_background: 0.0010s
2025-07-21 22:33:39,353 - INFO - draw_stats_page: 0.0412s
2025-07-21 22:33:39,447 - INFO - draw_background: 0.0010s
2025-07-21 22:33:39,543 - INFO - draw_stats_page: 0.0970s
2025-07-21 22:33:39,545 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:39,581 - INFO - draw_background: 0.0011s
2025-07-21 22:33:39,700 - INFO - draw_stats_page: 0.1198s
2025-07-21 22:33:39,734 - INFO - draw_background: 0.0009s
2025-07-21 22:33:39,865 - INFO - draw_stats_page: 0.1333s
2025-07-21 22:33:39,921 - INFO - draw_background: 0.0030s
2025-07-21 22:33:40,028 - INFO - draw_stats_page: 0.1110s
2025-07-21 22:33:40,060 - INFO - draw_background: 0.0010s
2025-07-21 22:33:40,119 - INFO - draw_stats_page: 0.0595s
2025-07-21 22:33:40,217 - INFO - draw_background: 0.0020s
2025-07-21 22:33:40,370 - INFO - draw_stats_page: 0.1565s
2025-07-21 22:33:40,374 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:40,430 - INFO - draw_background: 0.0020s
2025-07-21 22:33:40,526 - INFO - draw_stats_page: 0.0992s
2025-07-21 22:33:40,553 - INFO - draw_background: 0.0010s
2025-07-21 22:33:40,597 - INFO - draw_stats_page: 0.0438s
2025-07-21 22:33:40,691 - INFO - draw_background: 0.0016s
2025-07-21 22:33:40,764 - INFO - draw_stats_page: 0.0753s
2025-07-21 22:33:40,803 - INFO - draw_background: 0.0069s
2025-07-21 22:33:40,896 - INFO - draw_stats_page: 0.1007s
2025-07-21 22:33:40,929 - INFO - draw_background: 0.0005s
2025-07-21 22:33:41,025 - INFO - draw_stats_page: 0.0961s
2025-07-21 22:33:41,026 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:41,050 - INFO - draw_background: 0.0016s
2025-07-21 22:33:41,096 - INFO - draw_stats_page: 0.0460s
2025-07-21 22:33:41,188 - INFO - draw_background: 0.0005s
2025-07-21 22:33:41,223 - INFO - draw_stats_page: 0.0361s
2025-07-21 22:33:41,324 - INFO - draw_background: 0.0000s
2025-07-21 22:33:41,358 - INFO - draw_stats_page: 0.0353s
2025-07-21 22:33:41,457 - INFO - draw_background: 0.0014s
2025-07-21 22:33:41,539 - INFO - draw_stats_page: 0.0835s
2025-07-21 22:33:41,573 - INFO - draw_background: 0.0010s
2025-07-21 22:33:41,649 - INFO - draw_stats_page: 0.0771s
2025-07-21 22:33:41,651 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:41,684 - INFO - draw_background: 0.0010s
2025-07-21 22:33:41,765 - INFO - draw_stats_page: 0.0810s
2025-07-21 22:33:41,798 - INFO - draw_background: 0.0010s
2025-07-21 22:33:41,878 - INFO - draw_stats_page: 0.0820s
2025-07-21 22:33:41,913 - INFO - draw_background: 0.0009s
2025-07-21 22:33:41,992 - INFO - draw_stats_page: 0.0801s
2025-07-21 22:33:42,025 - INFO - draw_background: 0.0011s
2025-07-21 22:33:42,101 - INFO - draw_stats_page: 0.0763s
2025-07-21 22:33:42,133 - INFO - draw_background: 0.0010s
2025-07-21 22:33:42,216 - INFO - draw_stats_page: 0.0835s
2025-07-21 22:33:42,219 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:42,252 - INFO - draw_background: 0.0019s
2025-07-21 22:33:42,332 - INFO - draw_stats_page: 0.0817s
2025-07-21 22:33:42,358 - INFO - draw_background: 0.0010s
2025-07-21 22:33:42,462 - INFO - draw_stats_page: 0.1056s
2025-07-21 22:33:42,570 - INFO - draw_background: 0.0020s
2025-07-21 22:33:42,655 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:33:42,656 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:33:42,657 - INFO - draw_stats_page: 0.0891s
2025-07-21 22:33:42,685 - INFO - draw_background: 0.0010s
2025-07-21 22:33:42,737 - INFO - draw_stats_page: 0.0533s
2025-07-21 22:33:42,830 - INFO - draw_background: 0.0010s
2025-07-21 22:33:42,867 - INFO - draw_stats_page: 0.0381s
2025-07-21 22:33:42,869 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:42,964 - INFO - draw_background: 0.0010s
2025-07-21 22:33:43,001 - INFO - draw_stats_page: 0.0379s
2025-07-21 22:33:43,097 - INFO - draw_background: 0.0011s
2025-07-21 22:33:43,201 - INFO - draw_stats_page: 0.1062s
2025-07-21 22:33:43,234 - INFO - draw_background: 0.0000s
2025-07-21 22:33:43,274 - INFO - draw_stats_page: 0.0410s
2025-07-21 22:33:43,369 - INFO - draw_background: 0.0021s
2025-07-21 22:33:43,458 - INFO - draw_stats_page: 0.0904s
2025-07-21 22:33:43,489 - INFO - draw_background: 0.0000s
2025-07-21 22:33:43,567 - INFO - draw_stats_page: 0.0783s
2025-07-21 22:33:43,569 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:43,604 - INFO - draw_background: 0.0019s
2025-07-21 22:33:43,684 - INFO - draw_stats_page: 0.0826s
2025-07-21 22:33:43,714 - INFO - draw_background: 0.0020s
2025-07-21 22:33:43,797 - INFO - draw_stats_page: 0.0849s
2025-07-21 22:33:43,827 - INFO - draw_background: 0.0009s
2025-07-21 22:33:43,905 - INFO - draw_stats_page: 0.0787s
2025-07-21 22:33:43,935 - INFO - draw_background: 0.0010s
2025-07-21 22:33:43,999 - INFO - draw_stats_page: 0.0652s
2025-07-21 22:33:44,089 - INFO - draw_background: 0.0006s
2025-07-21 22:33:44,183 - INFO - draw_stats_page: 0.0950s
2025-07-21 22:33:44,184 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:44,206 - INFO - draw_background: 0.0000s
2025-07-21 22:33:44,286 - INFO - draw_stats_page: 0.0810s
2025-07-21 22:33:44,314 - INFO - draw_background: 0.0026s
2025-07-21 22:33:44,355 - INFO - draw_stats_page: 0.0432s
2025-07-21 22:33:44,445 - INFO - draw_background: 0.0015s
2025-07-21 22:33:44,496 - INFO - draw_stats_page: 0.0526s
2025-07-21 22:33:44,603 - INFO - draw_background: 0.0018s
2025-07-21 22:33:44,669 - INFO - draw_stats_page: 0.0675s
2025-07-21 22:33:44,762 - INFO - draw_background: 0.0005s
2025-07-21 22:33:44,798 - INFO - draw_stats_page: 0.0369s
2025-07-21 22:33:44,800 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:44,896 - INFO - draw_background: 0.0010s
2025-07-21 22:33:44,930 - INFO - draw_stats_page: 0.0353s
2025-07-21 22:33:45,029 - INFO - draw_background: 0.0000s
2025-07-21 22:33:45,065 - INFO - draw_stats_page: 0.0359s
2025-07-21 22:33:45,162 - INFO - draw_background: 0.0005s
2025-07-21 22:33:45,198 - INFO - draw_stats_page: 0.0365s
2025-07-21 22:33:45,297 - INFO - draw_background: 0.0020s
2025-07-21 22:33:45,390 - INFO - draw_stats_page: 0.0946s
2025-07-21 22:33:45,419 - INFO - draw_background: 0.0000s
2025-07-21 22:33:45,459 - INFO - draw_stats_page: 0.0411s
2025-07-21 22:33:45,460 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:45,553 - INFO - draw_background: 0.0010s
2025-07-21 22:33:45,601 - INFO - draw_stats_page: 0.0497s
2025-07-21 22:33:45,694 - INFO - draw_background: 0.0010s
2025-07-21 22:33:45,847 - INFO - draw_stats_page: 0.1507s
2025-07-21 22:33:45,875 - INFO - draw_background: 0.0000s
2025-07-21 22:33:45,912 - INFO - draw_stats_page: 0.0384s
2025-07-21 22:33:46,008 - INFO - draw_background: 0.0010s
2025-07-21 22:33:46,047 - INFO - draw_stats_page: 0.0383s
2025-07-21 22:33:46,142 - INFO - draw_background: 0.0016s
2025-07-21 22:33:46,230 - INFO - draw_stats_page: 0.0892s
2025-07-21 22:33:46,233 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:46,272 - INFO - draw_background: 0.0016s
2025-07-21 22:33:46,364 - INFO - draw_stats_page: 0.0934s
2025-07-21 22:33:46,395 - INFO - draw_background: 0.0010s
2025-07-21 22:33:46,460 - INFO - draw_stats_page: 0.0657s
2025-07-21 22:33:46,556 - INFO - draw_background: 0.0028s
2025-07-21 22:33:46,716 - INFO - draw_stats_page: 0.1630s
2025-07-21 22:33:46,767 - INFO - draw_background: 0.0025s
2025-07-21 22:33:46,902 - INFO - draw_stats_page: 0.1375s
2025-07-21 22:33:46,950 - INFO - draw_background: 0.0017s
2025-07-21 22:33:47,097 - INFO - draw_stats_page: 0.1491s
2025-07-21 22:33:47,101 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:47,149 - INFO - draw_background: 0.0020s
2025-07-21 22:33:47,269 - INFO - draw_stats_page: 0.1216s
2025-07-21 22:33:47,301 - INFO - draw_background: 0.0016s
2025-07-21 22:33:47,368 - INFO - draw_stats_page: 0.0692s
2025-07-21 22:33:47,463 - INFO - draw_background: 0.0026s
2025-07-21 22:33:47,565 - INFO - draw_stats_page: 0.1044s
2025-07-21 22:33:47,589 - INFO - draw_background: 0.0000s
2025-07-21 22:33:47,633 - INFO - draw_stats_page: 0.0449s
2025-07-21 22:33:47,726 - INFO - draw_background: 0.0020s
2025-07-21 22:33:47,853 - INFO - draw_stats_page: 0.1288s
2025-07-21 22:33:47,856 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:47,899 - INFO - draw_background: 0.0019s
2025-07-21 22:33:48,036 - INFO - draw_stats_page: 0.1389s
2025-07-21 22:33:48,082 - INFO - draw_background: 0.0022s
2025-07-21 22:33:48,209 - INFO - draw_stats_page: 0.1283s
2025-07-21 22:33:48,249 - INFO - draw_background: 0.0012s
2025-07-21 22:33:48,329 - INFO - draw_stats_page: 0.0818s
2025-07-21 22:33:48,354 - INFO - draw_background: 0.0010s
2025-07-21 22:33:48,390 - INFO - draw_stats_page: 0.0367s
2025-07-21 22:33:48,488 - INFO - draw_background: 0.0020s
2025-07-21 22:33:48,598 - INFO - draw_stats_page: 0.1122s
2025-07-21 22:33:48,600 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:48,634 - INFO - draw_background: 0.0010s
2025-07-21 22:33:48,683 - INFO - draw_stats_page: 0.0492s
2025-07-21 22:33:48,778 - INFO - draw_background: 0.0020s
2025-07-21 22:33:48,878 - INFO - draw_stats_page: 0.1019s
2025-07-21 22:33:48,911 - INFO - draw_background: 0.0010s
2025-07-21 22:33:48,989 - INFO - draw_stats_page: 0.0796s
2025-07-21 22:33:49,020 - INFO - draw_background: 0.0015s
2025-07-21 22:33:49,093 - INFO - draw_stats_page: 0.0740s
2025-07-21 22:33:49,123 - INFO - draw_background: 0.0011s
2025-07-21 22:33:49,196 - INFO - draw_stats_page: 0.0726s
2025-07-21 22:33:49,198 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:49,230 - INFO - draw_background: 0.0015s
2025-07-21 22:33:49,306 - INFO - draw_stats_page: 0.0773s
2025-07-21 22:33:49,340 - INFO - draw_background: 0.0009s
2025-07-21 22:33:49,415 - INFO - draw_stats_page: 0.0762s
2025-07-21 22:33:49,447 - INFO - draw_background: 0.0010s
2025-07-21 22:33:49,531 - INFO - draw_stats_page: 0.0849s
2025-07-21 22:33:49,564 - INFO - draw_background: 0.0010s
2025-07-21 22:33:49,606 - INFO - draw_stats_page: 0.0419s
2025-07-21 22:33:49,697 - INFO - draw_background: 0.0010s
2025-07-21 22:33:49,744 - INFO - draw_stats_page: 0.0477s
2025-07-21 22:33:49,746 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:49,838 - INFO - draw_background: 0.0009s
2025-07-21 22:33:49,928 - INFO - draw_stats_page: 0.0912s
2025-07-21 22:33:49,954 - INFO - draw_background: 0.0010s
2025-07-21 22:33:50,006 - INFO - draw_stats_page: 0.0531s
2025-07-21 22:33:50,101 - INFO - draw_background: 0.0027s
2025-07-21 22:33:50,202 - INFO - draw_stats_page: 0.1037s
2025-07-21 22:33:50,234 - INFO - draw_background: 0.0010s
2025-07-21 22:33:50,341 - INFO - draw_stats_page: 0.1081s
2025-07-21 22:33:50,378 - INFO - draw_background: 0.0016s
2025-07-21 22:33:50,471 - INFO - draw_stats_page: 0.0943s
2025-07-21 22:33:50,473 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:50,509 - INFO - draw_background: 0.0010s
2025-07-21 22:33:50,592 - INFO - draw_stats_page: 0.0845s
2025-07-21 22:33:50,619 - INFO - draw_background: 0.0010s
2025-07-21 22:33:50,678 - INFO - draw_stats_page: 0.0596s
2025-07-21 22:33:50,777 - INFO - draw_background: 0.0011s
2025-07-21 22:33:50,826 - INFO - draw_stats_page: 0.0493s
2025-07-21 22:33:50,921 - INFO - draw_background: 0.0015s
2025-07-21 22:33:51,008 - INFO - draw_stats_page: 0.0882s
2025-07-21 22:33:51,044 - INFO - draw_background: 0.0009s
2025-07-21 22:33:51,142 - INFO - draw_stats_page: 0.0987s
2025-07-21 22:33:51,144 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:51,177 - INFO - draw_background: 0.0015s
2025-07-21 22:33:51,263 - INFO - draw_stats_page: 0.0874s
2025-07-21 22:33:51,297 - INFO - draw_background: 0.0011s
2025-07-21 22:33:51,374 - INFO - draw_stats_page: 0.0780s
2025-07-21 22:33:51,398 - INFO - draw_background: 0.0000s
2025-07-21 22:33:51,434 - INFO - draw_stats_page: 0.0359s
2025-07-21 22:33:51,535 - INFO - draw_background: 0.0019s
2025-07-21 22:33:51,681 - INFO - draw_stats_page: 0.1483s
2025-07-21 22:33:51,723 - INFO - draw_background: 0.0016s
2025-07-21 22:33:51,918 - INFO - draw_stats_page: 0.1980s
2025-07-21 22:33:51,920 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:51,945 - INFO - draw_background: 0.0010s
2025-07-21 22:33:51,982 - INFO - draw_stats_page: 0.0386s
2025-07-21 22:33:52,079 - INFO - draw_background: 0.0009s
2025-07-21 22:33:52,116 - INFO - draw_stats_page: 0.0383s
2025-07-21 22:33:52,214 - INFO - draw_background: 0.0010s
2025-07-21 22:33:52,310 - INFO - draw_stats_page: 0.0984s
2025-07-21 22:33:52,347 - INFO - draw_background: 0.0020s
2025-07-21 22:33:52,445 - INFO - draw_stats_page: 0.1004s
2025-07-21 22:33:52,483 - INFO - draw_background: 0.0017s
2025-07-21 22:33:52,583 - INFO - draw_stats_page: 0.1023s
2025-07-21 22:33:52,586 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:52,623 - INFO - draw_background: 0.0019s
2025-07-21 22:33:52,718 - INFO - draw_stats_page: 0.0971s
2025-07-21 22:33:52,745 - INFO - draw_background: 0.0009s
2025-07-21 22:33:52,800 - INFO - draw_stats_page: 0.0565s
2025-07-21 22:33:52,895 - INFO - draw_background: 0.0010s
2025-07-21 22:33:52,985 - INFO - draw_stats_page: 0.0911s
2025-07-21 22:33:53,014 - INFO - draw_background: 0.0010s
2025-07-21 22:33:53,069 - INFO - draw_stats_page: 0.0566s
2025-07-21 22:33:53,164 - INFO - draw_background: 0.0009s
2025-07-21 22:33:53,260 - INFO - draw_stats_page: 0.0976s
2025-07-21 22:33:53,263 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:53,300 - INFO - draw_background: 0.0015s
2025-07-21 22:33:53,408 - INFO - draw_stats_page: 0.1084s
2025-07-21 22:33:53,435 - INFO - draw_background: 0.0010s
2025-07-21 22:33:53,492 - INFO - draw_stats_page: 0.0558s
2025-07-21 22:33:53,589 - INFO - draw_background: 0.0029s
2025-07-21 22:33:53,740 - INFO - draw_stats_page: 0.1534s
2025-07-21 22:33:53,791 - INFO - draw_background: 0.0026s
2025-07-21 22:33:53,894 - INFO - draw_stats_page: 0.1055s
2025-07-21 22:33:53,930 - INFO - draw_background: 0.0017s
2025-07-21 22:33:54,040 - INFO - draw_stats_page: 0.1110s
2025-07-21 22:33:54,042 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:54,075 - INFO - draw_background: 0.0010s
2025-07-21 22:33:54,184 - INFO - draw_stats_page: 0.1105s
2025-07-21 22:33:54,214 - INFO - draw_background: 0.0010s
2025-07-21 22:33:54,264 - INFO - draw_stats_page: 0.0511s
2025-07-21 22:33:54,357 - INFO - draw_background: 0.0010s
2025-07-21 22:33:54,442 - INFO - draw_stats_page: 0.0855s
2025-07-21 22:33:54,475 - INFO - draw_background: 0.0020s
2025-07-21 22:33:54,748 - INFO - draw_stats_page: 0.2754s
2025-07-21 22:33:54,774 - INFO - draw_background: 0.0010s
2025-07-21 22:33:54,815 - INFO - draw_stats_page: 0.0424s
2025-07-21 22:33:54,816 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:54,910 - INFO - draw_background: 0.0000s
2025-07-21 22:33:54,947 - INFO - draw_stats_page: 0.0382s
2025-07-21 22:33:55,044 - INFO - draw_background: 0.0009s
2025-07-21 22:33:55,086 - INFO - draw_stats_page: 0.0433s
2025-07-21 22:33:55,176 - INFO - draw_background: 0.0000s
2025-07-21 22:33:55,215 - INFO - draw_stats_page: 0.0396s
2025-07-21 22:33:55,312 - INFO - draw_background: 0.0010s
2025-07-21 22:33:55,374 - INFO - draw_stats_page: 0.0621s
2025-07-21 22:33:55,464 - INFO - draw_background: 0.0010s
2025-07-21 22:33:55,528 - INFO - draw_stats_page: 0.0649s
2025-07-21 22:33:55,529 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:55,623 - INFO - draw_background: 0.0010s
2025-07-21 22:33:55,663 - INFO - draw_stats_page: 0.0416s
2025-07-21 22:33:55,756 - INFO - draw_background: 0.0009s
2025-07-21 22:33:55,792 - INFO - draw_stats_page: 0.0367s
2025-07-21 22:33:55,890 - INFO - draw_background: 0.0016s
2025-07-21 22:33:55,979 - INFO - draw_stats_page: 0.0901s
2025-07-21 22:33:56,021 - INFO - draw_background: 0.0010s
2025-07-21 22:33:56,077 - INFO - draw_stats_page: 0.0573s
2025-07-21 22:33:56,176 - INFO - draw_background: 0.0009s
2025-07-21 22:33:56,267 - INFO - draw_stats_page: 0.0916s
2025-07-21 22:33:56,269 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:56,302 - INFO - draw_background: 0.0005s
2025-07-21 22:33:56,381 - INFO - draw_stats_page: 0.0818s
2025-07-21 22:33:56,412 - INFO - draw_background: 0.0009s
2025-07-21 22:33:56,479 - INFO - draw_stats_page: 0.0685s
2025-07-21 22:33:56,577 - INFO - draw_background: 0.0009s
2025-07-21 22:33:56,640 - INFO - draw_stats_page: 0.0643s
2025-07-21 22:33:56,735 - INFO - draw_background: 0.0005s
2025-07-21 22:33:56,798 - INFO - draw_stats_page: 0.0636s
2025-07-21 22:33:56,896 - INFO - draw_background: 0.0010s
2025-07-21 22:33:56,957 - INFO - draw_stats_page: 0.0619s
2025-07-21 22:33:56,959 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:57,054 - INFO - draw_background: 0.0010s
2025-07-21 22:33:57,227 - INFO - draw_stats_page: 0.1738s
2025-07-21 22:33:57,251 - INFO - draw_background: 0.0016s
2025-07-21 22:33:57,308 - INFO - draw_stats_page: 0.0567s
2025-07-21 22:33:57,403 - INFO - draw_background: 0.0010s
2025-07-21 22:33:57,455 - INFO - draw_stats_page: 0.0528s
2025-07-21 22:33:57,552 - INFO - draw_background: 0.0032s
2025-07-21 22:33:57,632 - INFO - draw_stats_page: 0.0843s
2025-07-21 22:33:57,660 - INFO - draw_background: 0.0000s
2025-07-21 22:33:57,724 - INFO - draw_stats_page: 0.0644s
2025-07-21 22:33:57,725 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:57,822 - INFO - draw_background: 0.0036s
2025-07-21 22:33:57,946 - INFO - draw_stats_page: 0.1275s
2025-07-21 22:33:57,984 - INFO - draw_background: 0.0021s
2025-07-21 22:33:58,061 - INFO - draw_stats_page: 0.0797s
2025-07-21 22:33:58,092 - INFO - draw_background: 0.0010s
2025-07-21 22:33:58,162 - INFO - draw_stats_page: 0.0703s
2025-07-21 22:33:58,255 - INFO - draw_background: 0.0020s
2025-07-21 22:33:58,319 - INFO - draw_stats_page: 0.0663s
2025-07-21 22:33:58,417 - INFO - draw_background: 0.0020s
2025-07-21 22:33:58,475 - INFO - draw_stats_page: 0.0609s
2025-07-21 22:33:58,478 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:58,574 - INFO - draw_background: 0.0018s
2025-07-21 22:33:58,664 - INFO - draw_stats_page: 0.0923s
2025-07-21 22:33:58,695 - INFO - draw_background: 0.0010s
2025-07-21 22:33:58,775 - INFO - draw_stats_page: 0.0803s
2025-07-21 22:33:58,811 - INFO - draw_background: 0.0021s
2025-07-21 22:33:58,909 - INFO - draw_stats_page: 0.0995s
2025-07-21 22:33:58,945 - INFO - draw_background: 0.0019s
2025-07-21 22:33:58,997 - INFO - draw_stats_page: 0.0529s
2025-07-21 22:33:59,090 - INFO - draw_background: 0.0010s
2025-07-21 22:33:59,140 - INFO - draw_stats_page: 0.0507s
2025-07-21 22:33:59,141 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:59,232 - INFO - draw_background: 0.0005s
2025-07-21 22:33:59,316 - INFO - draw_stats_page: 0.0843s
2025-07-21 22:33:59,344 - INFO - draw_background: 0.0010s
2025-07-21 22:33:59,429 - INFO - draw_stats_page: 0.0875s
2025-07-21 22:33:59,458 - INFO - draw_background: 0.0005s
2025-07-21 22:33:59,526 - INFO - draw_stats_page: 0.0693s
2025-07-21 22:33:59,622 - INFO - draw_background: 0.0010s
2025-07-21 22:33:59,669 - INFO - draw_stats_page: 0.0476s
2025-07-21 22:33:59,760 - INFO - draw_background: 0.0010s
2025-07-21 22:33:59,804 - INFO - draw_stats_page: 0.0442s
2025-07-21 22:33:59,804 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:33:59,898 - INFO - draw_background: 0.0010s
2025-07-21 22:33:59,963 - INFO - draw_stats_page: 0.0660s
2025-07-21 22:34:00,062 - INFO - draw_background: 0.0026s
2025-07-21 22:34:00,178 - INFO - draw_stats_page: 0.1179s
2025-07-21 22:34:00,205 - INFO - draw_background: 0.0019s
2025-07-21 22:34:00,272 - INFO - draw_stats_page: 0.0687s
2025-07-21 22:34:00,367 - INFO - draw_background: 0.0000s
2025-07-21 22:34:00,406 - INFO - draw_stats_page: 0.0404s
2025-07-21 22:34:00,500 - INFO - draw_background: 0.0010s
2025-07-21 22:34:00,605 - INFO - draw_stats_page: 0.1068s
2025-07-21 22:34:00,608 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:00,653 - INFO - draw_background: 0.0015s
2025-07-21 22:34:00,738 - INFO - draw_stats_page: 0.0883s
2025-07-21 22:34:00,766 - INFO - draw_background: 0.0000s
2025-07-21 22:34:00,807 - INFO - draw_stats_page: 0.0410s
2025-07-21 22:34:00,902 - INFO - draw_background: 0.0010s
2025-07-21 22:34:01,018 - INFO - draw_stats_page: 0.1176s
2025-07-21 22:34:01,051 - INFO - draw_background: 0.0012s
2025-07-21 22:34:01,126 - INFO - draw_stats_page: 0.0760s
2025-07-21 22:34:01,154 - INFO - draw_background: 0.0010s
2025-07-21 22:34:01,210 - INFO - draw_stats_page: 0.0574s
2025-07-21 22:34:01,214 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:01,305 - INFO - draw_background: 0.0020s
2025-07-21 22:34:01,411 - INFO - draw_stats_page: 0.1088s
2025-07-21 22:34:01,439 - INFO - draw_background: 0.0010s
2025-07-21 22:34:01,492 - INFO - draw_stats_page: 0.0534s
2025-07-21 22:34:01,590 - INFO - draw_background: 0.0029s
2025-07-21 22:34:01,724 - INFO - draw_stats_page: 0.1367s
2025-07-21 22:34:01,764 - INFO - draw_background: 0.0037s
2025-07-21 22:34:01,857 - INFO - draw_stats_page: 0.0971s
2025-07-21 22:34:01,904 - INFO - draw_background: 0.0010s
2025-07-21 22:34:02,025 - INFO - draw_stats_page: 0.1228s
2025-07-21 22:34:02,030 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:02,073 - INFO - draw_background: 0.0015s
2025-07-21 22:34:02,170 - INFO - draw_stats_page: 0.0985s
2025-07-21 22:34:02,193 - INFO - draw_background: 0.0010s
2025-07-21 22:34:02,236 - INFO - draw_stats_page: 0.0431s
2025-07-21 22:34:02,332 - INFO - draw_background: 0.0016s
2025-07-21 22:34:02,388 - INFO - draw_stats_page: 0.0577s
2025-07-21 22:34:02,480 - INFO - draw_background: 0.0010s
2025-07-21 22:34:02,537 - INFO - draw_stats_page: 0.0586s
2025-07-21 22:34:02,656 - INFO - draw_background: 0.0010s
2025-07-21 22:34:02,776 - INFO - draw_stats_page: 0.1224s
2025-07-21 22:34:02,777 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:02,808 - INFO - draw_background: 0.0010s
2025-07-21 22:34:02,890 - INFO - draw_stats_page: 0.0829s
2025-07-21 22:34:02,920 - INFO - draw_background: 0.0006s
2025-07-21 22:34:03,180 - INFO - draw_stats_page: 0.2610s
2025-07-21 22:34:03,206 - INFO - draw_background: 0.0000s
2025-07-21 22:34:03,257 - INFO - draw_stats_page: 0.0516s
2025-07-21 22:34:03,349 - INFO - draw_background: 0.0010s
2025-07-21 22:34:03,508 - INFO - draw_stats_page: 0.1603s
2025-07-21 22:34:03,556 - INFO - draw_background: 0.0016s
2025-07-21 22:34:03,614 - INFO - draw_stats_page: 0.0583s
2025-07-21 22:34:03,616 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:03,712 - INFO - draw_background: 0.0010s
2025-07-21 22:34:03,769 - INFO - draw_stats_page: 0.0574s
2025-07-21 22:34:03,865 - INFO - draw_background: 0.0033s
2025-07-21 22:34:03,917 - INFO - draw_stats_page: 0.0543s
2025-07-21 22:34:03,943 - INFO - draw_background: 0.0010s
2025-07-21 22:34:03,992 - INFO - draw_stats_page: 0.0504s
2025-07-21 22:34:04,090 - INFO - draw_background: 0.0000s
2025-07-21 22:34:04,126 - INFO - draw_stats_page: 0.0357s
2025-07-21 22:34:04,224 - INFO - draw_background: 0.0009s
2025-07-21 22:34:04,262 - INFO - draw_stats_page: 0.0390s
2025-07-21 22:34:04,263 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:04,356 - INFO - draw_background: 0.0000s
2025-07-21 22:34:04,437 - INFO - draw_stats_page: 0.0809s
2025-07-21 22:34:04,466 - INFO - draw_background: 0.0010s
2025-07-21 22:34:04,518 - INFO - draw_stats_page: 0.0533s
2025-07-21 22:34:04,617 - INFO - draw_background: 0.0010s
2025-07-21 22:34:04,670 - INFO - draw_stats_page: 0.0544s
2025-07-21 22:34:04,718 - INFO - draw_background: 0.0000s
2025-07-21 22:34:04,756 - INFO - draw_stats_page: 0.0372s
2025-07-21 22:34:04,835 - INFO - draw_background: 0.0006s
2025-07-21 22:34:04,869 - INFO - draw_stats_page: 0.0346s
2025-07-21 22:34:04,871 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:34:04,952 - INFO - draw_background: 0.0014s
2025-07-21 22:34:05,044 - INFO - draw_stats_page: 0.0936s
2025-07-21 22:34:05,063 - INFO - draw_background: 0.0010s
2025-07-21 22:34:05,115 - INFO - draw_stats_page: 0.0526s
2025-07-21 22:34:05,168 - INFO - draw_background: 0.0010s
2025-07-21 22:34:05,218 - INFO - draw_stats_page: 0.0517s
2025-07-21 22:34:05,268 - INFO - draw_background: 0.0010s
2025-07-21 22:34:05,326 - INFO - draw_stats_page: 0.0591s
2025-07-21 22:34:05,344 - INFO - draw_background: 0.0026s
2025-07-21 22:34:05,412 - INFO - draw_stats_page: 0.0710s
2025-07-21 22:34:05,413 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:35:50,416 - INFO - Loaded 8 items from cache
2025-07-21 22:35:50,418 - INFO - Started background data loading
2025-07-21 22:35:50,418 - INFO - OptimizedStatsLoader initialized
2025-07-21 22:35:50,418 - INFO - Using optimized stats loader for integration
2025-07-21 22:35:50,419 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:35:50,420 - INFO - Loaded summary data
2025-07-21 22:35:50,420 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:35:50,420 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:35:50,421 - INFO - Saved 9 items to cache
2025-07-21 22:35:50,423 - INFO - Background data loading completed
2025-07-21 22:35:50,427 - INFO - Database schema initialized successfully
2025-07-21 22:35:50,432 - INFO - Database schema initialized successfully
2025-07-21 22:35:50,433 - INFO - Stats database initialized successfully
2025-07-21 22:35:50,438 - INFO - Database schema initialized successfully
2025-07-21 22:35:50,439 - INFO - Stats database initialized successfully
2025-07-21 22:35:50,441 - INFO - Game stats integration module available
2025-07-21 22:35:50,442 - INFO - Started stats event worker thread
2025-07-21 22:35:50,442 - INFO - Stats event hooks initialized
2025-07-21 22:35:50,516 - INFO - Database security initialized successfully
2025-07-21 22:35:50,516 - INFO - DB Operation: {"timestamp": "2025-07-21 22:35:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:35:50,516 - INFO - Created secure database connection
2025-07-21 22:35:50,517 - INFO - Database schema initialized successfully
2025-07-21 22:35:54,606 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:35:54,607 - INFO - Sync thread started
2025-07-21 22:35:54,608 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 22:35:54,610 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 22:35:55,134 - INFO - Hooked into game's start_game method
2025-07-21 22:35:55,135 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:35:59,958 - INFO - Hooked into game's start_game method
2025-07-21 22:35:59,959 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:36:01,800 - INFO - Loaded 9 items from persistent cache
2025-07-21 22:36:01,800 - INFO - Stats cache initialized
2025-07-21 22:36:01,801 - INFO - DB Operation: {"timestamp": "2025-07-21 22:36:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:36:01,801 - INFO - DB Operation: {"timestamp": "2025-07-21 22:36:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:36:01,801 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-21 22:36:01,801 - INFO - Stats preloader initialized
2025-07-21 22:36:01,802 - INFO - Starting stats data preloading
2025-07-21 22:36:01,802 - INFO - Started stats data preloading
2025-07-21 22:36:01,802 - INFO - Loading data using optimized functions
2025-07-21 22:36:01,803 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:36:01,803 - INFO - DB Operation: {"timestamp": "2025-07-21 22:36:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:36:01,813 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:36:01,813 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:36:01,813 - INFO - DB Operation: {"timestamp": "2025-07-21 22:36:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:36:01,814 - INFO - Preloaded summary data
2025-07-21 22:36:01,815 - INFO - Preloaded game history (0 records)
2025-07-21 22:36:01,816 - INFO - Preloaded wallet data
2025-07-21 22:36:01,816 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:36:01,817 - INFO - Saved 9 items to persistent cache
2025-07-21 22:36:02,309 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-21 22:36:02,309 - INFO - Stats performance monitor initialized
2025-07-21 22:36:02,320 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-21 22:36:02,719 - INFO - draw_background: 0.0304s
2025-07-21 22:36:02,768 - INFO - Background thread started
2025-07-21 22:36:02,768 - INFO - Hybrid database integration initialized
2025-07-21 22:36:02,872 - INFO - draw_stats_page: 0.1834s
2025-07-21 22:36:02,884 - INFO - draw_background: 0.0009s
2025-07-21 22:36:02,934 - INFO - draw_stats_page: 0.0508s
2025-07-21 22:36:03,019 - INFO - draw_background: 0.0000s
2025-07-21 22:36:03,061 - INFO - draw_stats_page: 0.0418s
2025-07-21 22:36:03,152 - INFO - draw_background: 0.0019s
2025-07-21 22:36:03,249 - INFO - draw_stats_page: 0.0992s
2025-07-21 22:36:03,254 - INFO - draw_background: 0.0018s
2025-07-21 22:36:03,357 - INFO - draw_stats_page: 0.1047s
2025-07-21 22:36:03,359 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:03,364 - INFO - draw_background: 0.0010s
2025-07-21 22:36:03,473 - INFO - draw_stats_page: 0.1108s
2025-07-21 22:36:03,478 - INFO - draw_background: 0.0016s
2025-07-21 22:36:03,578 - INFO - draw_stats_page: 0.1018s
2025-07-21 22:36:03,584 - INFO - draw_background: 0.0016s
2025-07-21 22:36:03,672 - INFO - draw_stats_page: 0.0897s
2025-07-21 22:36:03,693 - INFO - draw_background: 0.0010s
2025-07-21 22:36:03,762 - INFO - draw_stats_page: 0.0684s
2025-07-21 22:36:03,799 - INFO - draw_background: 0.0010s
2025-07-21 22:36:03,861 - INFO - draw_stats_page: 0.0631s
2025-07-21 22:36:03,864 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:03,900 - INFO - draw_background: 0.0000s
2025-07-21 22:36:03,960 - INFO - draw_stats_page: 0.0605s
2025-07-21 22:36:04,031 - INFO - draw_background: 0.0020s
2025-07-21 22:36:04,139 - INFO - draw_stats_page: 0.1104s
2025-07-21 22:36:04,163 - INFO - draw_background: 0.0010s
2025-07-21 22:36:04,189 - INFO - Admin button added to stats page
2025-07-21 22:36:04,202 - INFO - draw_stats_page: 0.0401s
2025-07-21 22:36:04,296 - INFO - draw_background: 0.0010s
2025-07-21 22:36:04,334 - INFO - draw_stats_page: 0.0395s
2025-07-21 22:36:04,426 - INFO - draw_background: 0.0000s
2025-07-21 22:36:04,464 - INFO - draw_stats_page: 0.0380s
2025-07-21 22:36:04,465 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:04,562 - INFO - draw_background: 0.0000s
2025-07-21 22:36:04,599 - INFO - draw_stats_page: 0.0382s
2025-07-21 22:36:04,695 - INFO - draw_background: 0.0011s
2025-07-21 22:36:04,774 - INFO - draw_stats_page: 0.0794s
2025-07-21 22:36:04,809 - INFO - draw_background: 0.0010s
2025-07-21 22:36:04,893 - INFO - draw_stats_page: 0.0857s
2025-07-21 22:36:04,923 - INFO - draw_background: 0.0030s
2025-07-21 22:36:05,014 - INFO - draw_stats_page: 0.0943s
2025-07-21 22:36:05,051 - INFO - draw_background: 0.0010s
2025-07-21 22:36:05,120 - INFO - draw_stats_page: 0.0690s
2025-07-21 22:36:05,121 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:05,216 - INFO - draw_background: 0.0026s
2025-07-21 22:36:05,335 - INFO - draw_stats_page: 0.1224s
2025-07-21 22:36:05,373 - INFO - draw_background: 0.0017s
2025-07-21 22:36:05,448 - INFO - draw_stats_page: 0.0778s
2025-07-21 22:36:05,483 - INFO - draw_background: 0.0009s
2025-07-21 22:36:05,580 - INFO - draw_stats_page: 0.0988s
2025-07-21 22:36:05,620 - INFO - draw_background: 0.0020s
2025-07-21 22:36:05,709 - INFO - draw_stats_page: 0.0907s
2025-07-21 22:36:05,743 - INFO - draw_background: 0.0010s
2025-07-21 22:36:05,823 - INFO - draw_stats_page: 0.0814s
2025-07-21 22:36:05,825 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:05,862 - INFO - draw_background: 0.0015s
2025-07-21 22:36:05,954 - INFO - draw_stats_page: 0.0923s
2025-07-21 22:36:05,981 - INFO - draw_background: 0.0011s
2025-07-21 22:36:06,035 - INFO - draw_stats_page: 0.0548s
2025-07-21 22:36:06,130 - INFO - draw_background: 0.0030s
2025-07-21 22:36:06,243 - INFO - draw_stats_page: 0.1156s
2025-07-21 22:36:06,279 - INFO - draw_background: 0.0010s
2025-07-21 22:36:06,377 - INFO - draw_stats_page: 0.0991s
2025-07-21 22:36:06,417 - INFO - draw_background: 0.0010s
2025-07-21 22:36:06,509 - INFO - draw_stats_page: 0.0935s
2025-07-21 22:36:06,512 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:06,549 - INFO - draw_background: 0.0010s
2025-07-21 22:36:06,633 - INFO - draw_stats_page: 0.0858s
2025-07-21 22:36:06,662 - INFO - draw_background: 0.0010s
2025-07-21 22:36:06,712 - INFO - draw_stats_page: 0.0516s
2025-07-21 22:36:06,806 - INFO - draw_background: 0.0006s
2025-07-21 22:36:06,848 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:36:06,848 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:36:06,850 - INFO - draw_stats_page: 0.0444s
2025-07-21 22:36:06,944 - INFO - draw_background: 0.0017s
2025-07-21 22:36:07,055 - INFO - draw_stats_page: 0.1125s
2025-07-21 22:36:07,099 - INFO - draw_background: 0.0009s
2025-07-21 22:36:07,205 - INFO - draw_stats_page: 0.1069s
2025-07-21 22:36:07,209 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:07,271 - INFO - draw_background: 0.0020s
2025-07-21 22:36:07,349 - INFO - draw_stats_page: 0.0805s
2025-07-21 22:36:07,381 - INFO - draw_background: 0.0010s
2025-07-21 22:36:07,475 - INFO - draw_stats_page: 0.0949s
2025-07-21 22:36:07,506 - INFO - draw_background: 0.0015s
2025-07-21 22:36:07,580 - INFO - draw_stats_page: 0.0758s
2025-07-21 22:36:07,733 - INFO - draw_background: 0.0670s
2025-07-21 22:36:07,748 - INFO - draw_stats_page: 0.0816s
2025-07-21 22:36:07,787 - INFO - draw_background: 0.0005s
2025-07-21 22:36:07,793 - INFO - draw_stats_page: 0.0066s
2025-07-21 22:36:07,793 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:07,922 - INFO - draw_background: 0.0026s
2025-07-21 22:36:07,926 - INFO - draw_stats_page: 0.0072s
2025-07-21 22:36:08,054 - INFO - draw_background: 0.0011s
2025-07-21 22:36:08,060 - INFO - draw_stats_page: 0.0070s
2025-07-21 22:36:08,187 - INFO - draw_background: 0.0010s
2025-07-21 22:36:08,192 - INFO - draw_stats_page: 0.0050s
2025-07-21 22:36:08,320 - INFO - draw_background: 0.0020s
2025-07-21 22:36:08,326 - INFO - draw_stats_page: 0.0081s
2025-07-21 22:36:08,450 - INFO - draw_background: 0.0005s
2025-07-21 22:36:08,454 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:36:08,455 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:08,586 - INFO - draw_background: 0.0026s
2025-07-21 22:36:08,603 - INFO - draw_stats_page: 0.0187s
2025-07-21 22:36:08,726 - INFO - draw_background: 0.0036s
2025-07-21 22:36:08,739 - INFO - draw_stats_page: 0.0167s
2025-07-21 22:36:08,858 - INFO - draw_background: 0.0032s
2025-07-21 22:36:08,875 - INFO - draw_stats_page: 0.0201s
2025-07-21 22:36:08,993 - INFO - draw_background: 0.0020s
2025-07-21 22:36:09,014 - INFO - draw_stats_page: 0.0225s
2025-07-21 22:36:09,138 - INFO - draw_background: 0.0022s
2025-07-21 22:36:09,152 - INFO - draw_stats_page: 0.0161s
2025-07-21 22:36:09,156 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:09,274 - INFO - draw_background: 0.0026s
2025-07-21 22:36:09,295 - INFO - draw_stats_page: 0.0234s
2025-07-21 22:36:09,407 - INFO - draw_background: 0.0000s
2025-07-21 22:36:09,414 - INFO - draw_stats_page: 0.0056s
2025-07-21 22:36:09,541 - INFO - draw_background: 0.0009s
2025-07-21 22:36:09,545 - INFO - draw_stats_page: 0.0051s
2025-07-21 22:36:09,676 - INFO - draw_background: 0.0015s
2025-07-21 22:36:09,696 - INFO - draw_stats_page: 0.0229s
2025-07-21 22:36:09,810 - INFO - draw_background: 0.0010s
2025-07-21 22:36:09,821 - INFO - draw_stats_page: 0.0124s
2025-07-21 22:36:09,824 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:09,945 - INFO - draw_background: 0.0047s
2025-07-21 22:36:09,959 - INFO - draw_stats_page: 0.0185s
2025-07-21 22:36:10,077 - INFO - draw_background: 0.0021s
2025-07-21 22:36:10,091 - INFO - draw_stats_page: 0.0164s
2025-07-21 22:36:10,215 - INFO - draw_background: 0.0015s
2025-07-21 22:36:10,229 - INFO - draw_stats_page: 0.0160s
2025-07-21 22:36:10,353 - INFO - draw_background: 0.0020s
2025-07-21 22:36:10,369 - INFO - draw_stats_page: 0.0183s
2025-07-21 22:36:10,492 - INFO - draw_background: 0.0005s
2025-07-21 22:36:10,498 - INFO - draw_stats_page: 0.0052s
2025-07-21 22:36:10,499 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:10,626 - INFO - draw_background: 0.0000s
2025-07-21 22:36:10,630 - INFO - draw_stats_page: 0.0047s
2025-07-21 22:36:10,757 - INFO - draw_background: 0.0000s
2025-07-21 22:36:10,762 - INFO - draw_stats_page: 0.0045s
2025-07-21 22:36:10,894 - INFO - draw_background: 0.0021s
2025-07-21 22:36:10,907 - INFO - draw_stats_page: 0.0160s
2025-07-21 22:36:11,028 - INFO - draw_background: 0.0026s
2025-07-21 22:36:11,043 - INFO - draw_stats_page: 0.0175s
2025-07-21 22:36:11,163 - INFO - draw_background: 0.0019s
2025-07-21 22:36:11,183 - INFO - draw_stats_page: 0.0225s
2025-07-21 22:36:11,187 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:11,308 - INFO - draw_background: 0.0020s
2025-07-21 22:36:11,325 - INFO - draw_stats_page: 0.0183s
2025-07-21 22:36:11,443 - INFO - draw_background: 0.0000s
2025-07-21 22:36:11,450 - INFO - draw_stats_page: 0.0067s
2025-07-21 22:36:11,577 - INFO - draw_background: 0.0006s
2025-07-21 22:36:11,581 - INFO - draw_stats_page: 0.0041s
2025-07-21 22:36:11,709 - INFO - draw_background: 0.0006s
2025-07-21 22:36:11,713 - INFO - draw_stats_page: 0.0046s
2025-07-21 22:36:11,843 - INFO - draw_background: 0.0019s
2025-07-21 22:36:11,857 - INFO - draw_stats_page: 0.0148s
2025-07-21 22:36:11,860 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:11,975 - INFO - draw_background: 0.0016s
2025-07-21 22:36:11,987 - INFO - draw_stats_page: 0.0142s
2025-07-21 22:36:12,109 - INFO - draw_background: 0.0020s
2025-07-21 22:36:12,123 - INFO - draw_stats_page: 0.0153s
2025-07-21 22:36:12,244 - INFO - draw_background: 0.0017s
2025-07-21 22:36:12,258 - INFO - draw_stats_page: 0.0166s
2025-07-21 22:36:12,378 - INFO - draw_background: 0.0022s
2025-07-21 22:36:12,383 - INFO - draw_stats_page: 0.0073s
2025-07-21 22:36:12,578 - INFO - draw_background: 0.0015s
2025-07-21 22:36:12,594 - INFO - draw_stats_page: 0.0173s
2025-07-21 22:36:12,595 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:12,629 - INFO - draw_background: 0.0020s
2025-07-21 22:36:12,640 - INFO - draw_stats_page: 0.0126s
2025-07-21 22:36:12,761 - INFO - draw_background: 0.0010s
2025-07-21 22:36:12,765 - INFO - draw_stats_page: 0.0042s
2025-07-21 22:36:12,893 - INFO - draw_background: 0.0010s
2025-07-21 22:36:12,897 - INFO - draw_stats_page: 0.0046s
2025-07-21 22:36:13,025 - INFO - draw_background: 0.0005s
2025-07-21 22:36:13,029 - INFO - draw_stats_page: 0.0046s
2025-07-21 22:36:13,157 - INFO - draw_background: 0.0009s
2025-07-21 22:36:13,161 - INFO - draw_stats_page: 0.0044s
2025-07-21 22:36:13,163 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:13,228 - INFO - draw_background: 0.0040s
2025-07-21 22:36:13,246 - INFO - draw_stats_page: 0.0217s
2025-07-21 22:36:13,370 - INFO - draw_background: 0.0015s
2025-07-21 22:36:13,377 - INFO - draw_stats_page: 0.0081s
2025-07-21 22:36:13,504 - INFO - draw_background: 0.0025s
2025-07-21 22:36:13,519 - INFO - draw_stats_page: 0.0172s
2025-07-21 22:36:13,639 - INFO - draw_background: 0.0016s
2025-07-21 22:36:13,661 - INFO - draw_stats_page: 0.0249s
2025-07-21 22:36:13,794 - INFO - draw_background: 0.0030s
2025-07-21 22:36:13,809 - INFO - draw_stats_page: 0.0177s
2025-07-21 22:36:13,813 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:13,932 - INFO - draw_background: 0.0020s
2025-07-21 22:36:13,948 - INFO - draw_stats_page: 0.0178s
2025-07-21 22:36:14,067 - INFO - draw_background: 0.0025s
2025-07-21 22:36:14,080 - INFO - draw_stats_page: 0.0158s
2025-07-21 22:36:14,199 - INFO - draw_background: 0.0026s
2025-07-21 22:36:14,214 - INFO - draw_stats_page: 0.0177s
2025-07-21 22:36:14,332 - INFO - draw_background: 0.0029s
2025-07-21 22:36:14,347 - INFO - draw_stats_page: 0.0180s
2025-07-21 22:36:14,464 - INFO - draw_background: 0.0030s
2025-07-21 22:36:14,479 - INFO - draw_stats_page: 0.0177s
2025-07-21 22:36:14,482 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:14,601 - INFO - draw_background: 0.0030s
2025-07-21 22:36:14,619 - INFO - draw_stats_page: 0.0207s
2025-07-21 22:36:14,739 - INFO - draw_background: 0.0030s
2025-07-21 22:36:14,760 - INFO - draw_stats_page: 0.0225s
2025-07-21 22:36:14,814 - INFO - draw_background: 0.0030s
2025-07-21 22:36:14,831 - INFO - draw_stats_page: 0.0203s
2025-07-21 22:36:14,946 - INFO - draw_background: 0.0026s
2025-07-21 22:36:14,960 - INFO - draw_stats_page: 0.0169s
2025-07-21 22:36:15,079 - INFO - draw_background: 0.0026s
2025-07-21 22:36:15,092 - INFO - draw_stats_page: 0.0163s
2025-07-21 22:36:15,097 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:36:15,220 - INFO - draw_background: 0.0031s
2025-07-21 22:36:15,237 - INFO - draw_stats_page: 0.0199s
2025-07-21 22:36:15,353 - INFO - draw_background: 0.0024s
2025-07-21 22:36:15,367 - INFO - draw_stats_page: 0.0164s
2025-07-21 22:36:15,490 - INFO - draw_background: 0.0027s
2025-07-21 22:36:15,504 - INFO - draw_stats_page: 0.0150s
2025-07-21 22:36:58,688 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:37:10,942 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:37:10,943 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:38:02,803 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:38:15,044 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:38:15,045 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:51:27,556 - INFO - Loaded 7 items from cache
2025-07-21 22:51:27,557 - INFO - Started background data loading
2025-07-21 22:51:27,557 - INFO - OptimizedStatsLoader initialized
2025-07-21 22:51:27,557 - INFO - Using optimized stats loader for integration
2025-07-21 22:51:27,559 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:51:27,559 - INFO - Loaded summary data
2025-07-21 22:51:27,559 - INFO - Loaded game history page 0 (2 records)
2025-07-21 22:51:27,559 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:51:27,561 - INFO - Saved 7 items to cache
2025-07-21 22:51:27,561 - INFO - Background data loading completed
2025-07-21 22:51:27,622 - INFO - Database schema initialized successfully
2025-07-21 22:51:27,625 - INFO - Database schema initialized successfully
2025-07-21 22:51:27,627 - INFO - Stats database initialized successfully
2025-07-21 22:51:27,629 - INFO - Database schema initialized successfully
2025-07-21 22:51:27,631 - INFO - Stats database initialized successfully
2025-07-21 22:51:27,632 - INFO - Game stats integration module available
2025-07-21 22:51:27,633 - INFO - Started stats event worker thread
2025-07-21 22:51:27,633 - INFO - Stats event hooks initialized
2025-07-21 22:51:27,715 - INFO - Database security initialized successfully
2025-07-21 22:51:27,716 - INFO - DB Operation: {"timestamp": "2025-07-21 22:51:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:51:27,716 - INFO - Created secure database connection
2025-07-21 22:51:27,719 - INFO - Database schema initialized successfully
2025-07-21 22:51:31,813 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:51:31,816 - INFO - Sync thread started
2025-07-21 22:51:31,816 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 22:51:31,817 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 22:58:58,465 - INFO - Loaded 8 items from cache
2025-07-21 22:58:58,466 - INFO - Started background data loading
2025-07-21 22:58:58,466 - INFO - OptimizedStatsLoader initialized
2025-07-21 22:58:58,466 - INFO - Using optimized stats loader for integration
2025-07-21 22:58:58,469 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:58:58,470 - INFO - Loaded summary data
2025-07-21 22:58:58,470 - INFO - Loaded game history page 0 (2 records)
2025-07-21 22:58:58,470 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:58:58,472 - INFO - Saved 9 items to cache
2025-07-21 22:58:58,473 - INFO - Background data loading completed
2025-07-21 22:58:58,479 - INFO - Database schema initialized successfully
2025-07-21 22:58:58,501 - INFO - Database schema initialized successfully
2025-07-21 22:58:58,504 - INFO - Stats database initialized successfully
2025-07-21 22:58:58,508 - INFO - Database schema initialized successfully
2025-07-21 22:58:58,511 - INFO - Stats database initialized successfully
2025-07-21 22:58:58,512 - INFO - Game stats integration module available
2025-07-21 22:58:58,513 - INFO - Started stats event worker thread
2025-07-21 22:58:58,513 - INFO - Stats event hooks initialized
2025-07-21 22:58:58,597 - INFO - Database security initialized successfully
2025-07-21 22:58:58,597 - INFO - DB Operation: {"timestamp": "2025-07-21 22:58:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:58:58,597 - INFO - Created secure database connection
2025-07-21 22:58:58,598 - INFO - Database schema initialized successfully
2025-07-21 22:59:02,686 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:59:02,687 - INFO - Sync thread started
2025-07-21 22:59:02,687 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-21 22:59:02,688 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-21 22:59:03,231 - INFO - Hooked into game's start_game method
2025-07-21 22:59:03,232 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:59:08,336 - INFO - Hooked into game's start_game method
2025-07-21 22:59:08,337 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-21 22:59:17,243 - INFO - Loaded 9 items from persistent cache
2025-07-21 22:59:17,244 - INFO - Stats cache initialized
2025-07-21 22:59:17,244 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:17,244 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:17,245 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-21 22:59:17,245 - INFO - Stats preloader initialized
2025-07-21 22:59:17,245 - INFO - Starting stats data preloading
2025-07-21 22:59:17,245 - INFO - Started stats data preloading
2025-07-21 22:59:17,246 - INFO - Loading data using optimized functions
2025-07-21 22:59:17,246 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:17,246 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:17,256 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:59:17,256 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:17,257 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:17,259 - INFO - Preloaded summary data
2025-07-21 22:59:17,260 - INFO - Preloaded game history (2 records)
2025-07-21 22:59:17,260 - INFO - Preloaded wallet data
2025-07-21 22:59:17,260 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-21 22:59:17,263 - INFO - Saved 9 items to persistent cache
2025-07-21 22:59:17,966 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-21 22:59:17,967 - INFO - Stats performance monitor initialized
2025-07-21 22:59:17,989 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-21 22:59:18,519 - INFO - draw_background: 0.0368s
2025-07-21 22:59:18,636 - INFO - Background thread started
2025-07-21 22:59:18,637 - INFO - Hybrid database integration initialized
2025-07-21 22:59:18,770 - INFO - draw_stats_page: 0.2879s
2025-07-21 22:59:18,783 - INFO - draw_background: 0.0013s
2025-07-21 22:59:18,847 - INFO - draw_stats_page: 0.0653s
2025-07-21 22:59:18,928 - INFO - draw_background: 0.0010s
2025-07-21 22:59:18,984 - INFO - draw_stats_page: 0.0575s
2025-07-21 22:59:19,067 - INFO - draw_background: 0.0010s
2025-07-21 22:59:19,121 - INFO - draw_stats_page: 0.0553s
2025-07-21 22:59:19,203 - INFO - draw_background: 0.0011s
2025-07-21 22:59:19,266 - INFO - draw_stats_page: 0.0633s
2025-07-21 22:59:19,267 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:59:19,350 - INFO - draw_background: 0.0010s
2025-07-21 22:59:19,421 - INFO - draw_stats_page: 0.0722s
2025-07-21 22:59:19,500 - INFO - draw_background: 0.0010s
2025-07-21 22:59:19,546 - INFO - draw_stats_page: 0.0465s
2025-07-21 22:59:19,638 - INFO - draw_background: 0.0010s
2025-07-21 22:59:19,696 - INFO - draw_stats_page: 0.0589s
2025-07-21 22:59:19,790 - INFO - draw_background: 0.0010s
2025-07-21 22:59:19,845 - INFO - draw_stats_page: 0.0554s
2025-07-21 22:59:19,944 - INFO - draw_background: 0.0005s
2025-07-21 22:59:20,013 - INFO - Admin button added to stats page
2025-07-21 22:59:20,108 - INFO - draw_stats_page: 0.1641s
2025-07-21 22:59:20,110 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:59:20,139 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,179 - INFO - draw_stats_page: 0.0410s
2025-07-21 22:59:20,275 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,331 - INFO - draw_stats_page: 0.0570s
2025-07-21 22:59:20,423 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,465 - INFO - draw_stats_page: 0.0424s
2025-07-21 22:59:20,557 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,598 - INFO - draw_stats_page: 0.0427s
2025-07-21 22:59:20,690 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,742 - INFO - draw_stats_page: 0.0524s
2025-07-21 22:59:20,742 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:59:20,833 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,869 - INFO - draw_stats_page: 0.0378s
2025-07-21 22:59:20,967 - INFO - draw_background: 0.0010s
2025-07-21 22:59:20,999 - INFO - draw_stats_page: 0.0318s
2025-07-21 22:59:21,100 - INFO - draw_background: 0.0009s
2025-07-21 22:59:21,134 - INFO - draw_stats_page: 0.0344s
2025-07-21 22:59:21,234 - INFO - draw_background: 0.0005s
2025-07-21 22:59:21,316 - INFO - draw_stats_page: 0.0834s
2025-07-21 22:59:21,361 - INFO - draw_background: 0.0020s
2025-07-21 22:59:22,061 - INFO - draw_stats_page: 0.7021s
2025-07-21 22:59:22,063 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 22:59:22,079 - INFO - draw_background: 0.0010s
2025-07-21 22:59:22,754 - INFO - draw_stats_page: 0.6759s
2025-07-21 22:59:22,782 - INFO - draw_background: 0.0026s
2025-07-21 22:59:23,042 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 22:59:23,042 - WARNING - Failed to connect to RethinkDB
2025-07-21 22:59:23,448 - INFO - draw_stats_page: 0.6681s
2025-07-21 22:59:23,465 - INFO - draw_background: 0.0021s
2025-07-21 22:59:23,904 - INFO - draw_stats_page: 0.4414s
2025-07-21 22:59:35,559 - INFO - Game start event recorded in database (players: 4)
2025-07-21 22:59:35,560 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:59:35,561 - INFO - Started background data loading
2025-07-21 22:59:35,561 - INFO - Forced data refresh
2025-07-21 22:59:35,561 - INFO - Game start recorded with optimized integration: True
2025-07-21 22:59:35,608 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:59:35,608 - INFO - Loaded summary data
2025-07-21 22:59:35,609 - INFO - Loaded game history page 0 (2 records)
2025-07-21 22:59:35,617 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:59:35,617 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:59:35,623 - INFO - Forced data refresh
2025-07-21 22:59:35,623 - INFO - Forced refresh of optimized loader data
2025-07-21 22:59:35,623 - INFO - Saved 0 items to cache
2025-07-21 22:59:35,624 - INFO - Background data loading completed
2025-07-21 22:59:35,626 - INFO - Forced refresh of thread_safe_db data
2025-07-21 22:59:35,628 - INFO - Saved 0 items to persistent cache
2025-07-21 22:59:35,628 - INFO - Cache cleared
2025-07-21 22:59:35,628 - INFO - Cleared all preloader cache data
2025-07-21 22:59:35,629 - INFO - Starting stats data preloading
2025-07-21 22:59:35,629 - INFO - Started stats data preloading
2025-07-21 22:59:35,629 - INFO - Loading data using optimized functions
2025-07-21 22:59:35,629 - INFO - Cleared stats preloader cache
2025-07-21 22:59:35,629 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:35,630 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:35", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:35,631 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:59:35,631 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:35,631 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:35", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:35,631 - INFO - Loaded summary data
2025-07-21 22:59:35,632 - INFO - Preloaded summary data
2025-07-21 22:59:35,633 - INFO - Preloaded game history (2 records)
2025-07-21 22:59:35,633 - INFO - Preloaded wallet data
2025-07-21 22:59:35,633 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-07-21 22:59:35,638 - INFO - Saved 8 items to persistent cache
2025-07-21 22:59:35,645 - INFO - Got summary stats
2025-07-21 22:59:35,681 - INFO - Posted refresh_stats event
2025-07-21 22:59:35,681 - INFO - Processed game_started event: True
2025-07-21 22:59:35,688 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 900.
2025-07-21 22:59:35,689 - ERROR - Error loading game history page 0: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 900.
2025-07-21 22:59:35,689 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-07-21 22:59:35,689 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 900.
2025-07-21 22:59:35,690 - INFO - Loaded weekly stats from GameStatsIntegration
2025-07-21 22:59:47,880 - INFO - Saved 0 items to persistent cache
2025-07-21 22:59:47,881 - INFO - Cache cleared
2025-07-21 22:59:47,881 - INFO - Cleared all preloader cache data
2025-07-21 22:59:47,881 - INFO - Starting stats data preloading
2025-07-21 22:59:47,881 - INFO - Started stats data preloading
2025-07-21 22:59:47,882 - INFO - Loading data using optimized functions
2025-07-21 22:59:47,882 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:47,882 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:47,884 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:59:47,884 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:47,885 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:47,887 - INFO - Preloaded summary data
2025-07-21 22:59:47,889 - INFO - Preloaded game history (2 records)
2025-07-21 22:59:47,891 - INFO - Preloaded wallet data
2025-07-21 22:59:47,891 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 22:59:47,893 - INFO - Saved 8 items to persistent cache
2025-07-21 22:59:47,897 - INFO - BaseLoader initialized with cache directory: data/cache
2025-07-21 22:59:47,898 - INFO - PlaceholderGenerator initialized
2025-07-21 22:59:47,898 - INFO - Loaded cache metadata with 0 entries
2025-07-21 22:59:47,899 - INFO - Started cache cleanup thread
2025-07-21 22:59:47,899 - INFO - DiskCache initialized with max age: 3600s
2025-07-21 22:59:47,899 - INFO - Background worker started
2025-07-21 22:59:47,899 - INFO - BackgroundLoader initialized
2025-07-21 22:59:47,899 - INFO - UIUpdater initialized
2025-07-21 22:59:47,900 - INFO - Loaded placeholders from cache
2025-07-21 22:59:47,900 - INFO - Loaded placeholder data
2025-07-21 22:59:47,900 - INFO - Queued task weekly_stats
2025-07-21 22:59:47,900 - INFO - Queued task game_history_page_0
2025-07-21 22:59:47,900 - INFO - Queued task summary_stats
2025-07-21 22:59:47,900 - INFO - Started background loading
2025-07-21 22:59:47,900 - INFO - StatsLoader initialized
2025-07-21 22:59:47,901 - INFO - Saved cache metadata with 0 entries
2025-07-21 22:59:47,901 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 22:59:47,902 - INFO - Cleared all function caches
2025-07-21 22:59:47,902 - INFO - Generated weekly stats placeholder
2025-07-21 22:59:47,902 - INFO - Generated game history placeholder with 10 records
2025-07-21 22:59:47,903 - INFO - Generated summary stats placeholder
2025-07-21 22:59:47,903 - INFO - Got weekly stats: 7 days
2025-07-21 22:59:47,905 - INFO - Saved all placeholders to cache
2025-07-21 22:59:47,905 - INFO - Weekly stats loaded
2025-07-21 22:59:47,905 - INFO - Cleared all caches
2025-07-21 22:59:47,906 - INFO - Task weekly_stats completed in 0.0021s
2025-07-21 22:59:47,906 - INFO - Got game history page 0: 2 records
2025-07-21 22:59:47,908 - INFO - Game history page 0 loaded
2025-07-21 22:59:47,908 - INFO - Task game_history_page_0 completed in 0.0010s
2025-07-21 22:59:47,909 - INFO - Got summary stats
2025-07-21 22:59:47,910 - INFO - Summary stats loaded
2025-07-21 22:59:47,910 - INFO - Task summary_stats completed in 0.0010s
2025-07-21 22:59:47,916 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 22:59:47,916 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 22:59:48,091 - INFO - Saved 0 items to persistent cache
2025-07-21 22:59:48,091 - INFO - Cache cleared
2025-07-21 22:59:48,091 - INFO - Cleared all preloader cache data
2025-07-21 22:59:48,091 - INFO - Starting stats data preloading
2025-07-21 22:59:48,091 - INFO - Started stats data preloading
2025-07-21 22:59:48,091 - INFO - Loading data using optimized functions
2025-07-21 22:59:48,092 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:48,092 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:48", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:48,095 - INFO - Saved cache metadata with 0 entries
2025-07-21 22:59:48,095 - INFO - Cleared 4 entries from cache in namespace stats
2025-07-21 22:59:48,096 - INFO - Cleared all function caches
2025-07-21 22:59:48,096 - INFO - Generated weekly stats placeholder
2025-07-21 22:59:48,096 - INFO - Generated game history placeholder with 10 records
2025-07-21 22:59:48,097 - INFO - Generated summary stats placeholder
2025-07-21 22:59:48,099 - INFO - Saved all placeholders to cache
2025-07-21 22:59:48,099 - INFO - Cleared all caches
2025-07-21 22:59:48,118 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:59:48,228 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 22:59:48,229 - INFO - Game data: {'winner_name': 'Credit Deduction Game', 'winner_cartella': 0, 'claim_type': 'credit_based_recording', 'game_duration': 301.0895745754242, 'player_count': 1, 'prize_amount': 40, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 22:59:48', 'stake': 25, 'bet_amount': 25, 'completion_type': 'credit_deduction', 'credits_used': 1, 'share_percentage': 15, 'referee_commission': 8.0, 'game_id': 'retroactive_game_1753127987', 'total_bets': 40, 'timestamp': '2025-07-21T22:59:48.090572'}
2025-07-21 22:59:48,229 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 22:59:48,231 - INFO - Statistics saved to data\stats.json
2025-07-21 22:59:48,231 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 22:59:48,290 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 22:59:48,290 - INFO - Game data: {'winner_name': 'Credit-Based Game', 'winner_cartella': None, 'claim_type': 'credit_based', 'game_duration': 0, 'player_count': 4, 'prize_amount': 32, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 22:59:48', 'stake': 10, 'bet_amount': 10, 'completion_type': 'credit_based', 'timestamp': '2025-07-21T22:59:48.135685'}
2025-07-21 22:59:48,292 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 22:59:48,302 - INFO - Statistics saved to data\stats.json
2025-07-21 22:59:48,303 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 22:59:53,119 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:53", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:53,119 - INFO - Preloaded summary data
2025-07-21 22:59:53,120 - INFO - Preloaded game history (2 records)
2025-07-21 22:59:53,120 - INFO - Preloaded wallet data
2025-07-21 22:59:53,120 - INFO - Stats data preloaded successfully in 5.03 seconds
2025-07-21 22:59:53,121 - INFO - Saved 8 items to persistent cache
2025-07-21 22:59:56,489 - ERROR - Error recording game start event in database: Cannot operate on a closed database.
2025-07-21 22:59:56,500 - INFO - Statistics saved to data\stats.json
2025-07-21 22:59:56,500 - INFO - Game start event added to JSON stats (fallback)
2025-07-21 22:59:56,500 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:59:56,503 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 11684.
2025-07-21 22:59:56,503 - INFO - Started background data loading
2025-07-21 22:59:56,504 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 11684.
2025-07-21 22:59:56,504 - INFO - Forced data refresh
2025-07-21 22:59:56,504 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 11684.
2025-07-21 22:59:56,504 - INFO - Game start recorded with optimized integration: True
2025-07-21 22:59:56,505 - INFO - Saved 0 items to cache
2025-07-21 22:59:56,505 - INFO - Background data loading completed
2025-07-21 22:59:56,505 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 11684.
2025-07-21 22:59:56,509 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 22:59:56,510 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15040.
2025-07-21 22:59:56,510 - INFO - Started background data loading
2025-07-21 22:59:56,510 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15040.
2025-07-21 22:59:56,510 - INFO - Forced data refresh
2025-07-21 22:59:56,511 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15040.
2025-07-21 22:59:56,511 - INFO - Forced refresh of optimized loader data
2025-07-21 22:59:56,511 - INFO - Saved 0 items to cache
2025-07-21 22:59:56,512 - INFO - Background data loading completed
2025-07-21 22:59:56,512 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15040.
2025-07-21 22:59:56,520 - INFO - Forced refresh of thread_safe_db data
2025-07-21 22:59:56,522 - INFO - Saved 0 items to persistent cache
2025-07-21 22:59:56,522 - INFO - Cache cleared
2025-07-21 22:59:56,522 - INFO - Cleared all preloader cache data
2025-07-21 22:59:56,522 - INFO - Starting stats data preloading
2025-07-21 22:59:56,523 - INFO - Started stats data preloading
2025-07-21 22:59:56,523 - INFO - Loading data using optimized functions
2025-07-21 22:59:56,523 - INFO - Cleared stats preloader cache
2025-07-21 22:59:56,523 - WARNING - Connection validation failed, creating new connection
2025-07-21 22:59:56,524 - INFO - DB Operation: {"timestamp": "2025-07-21 22:59:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 22:59:56,525 - INFO - Posted refresh_stats event
2025-07-21 22:59:56,525 - INFO - Preloaded weekly stats for 7 days
2025-07-21 22:59:56,526 - INFO - Preloaded summary data
2025-07-21 22:59:56,527 - INFO - Preloaded game history (2 records)
2025-07-21 22:59:56,527 - INFO - Processed game_started event: True
2025-07-21 22:59:56,527 - INFO - Preloaded wallet data
2025-07-21 22:59:56,527 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-07-21 22:59:56,529 - INFO - Saved 8 items to persistent cache
2025-07-21 23:00:06,777 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:00:09,318 - INFO - Saved 0 items to persistent cache
2025-07-21 23:00:09,318 - INFO - Cache cleared
2025-07-21 23:00:09,318 - INFO - Cleared all preloader cache data
2025-07-21 23:00:09,319 - INFO - Starting stats data preloading
2025-07-21 23:00:09,319 - INFO - Started stats data preloading
2025-07-21 23:00:09,319 - INFO - Loading data using optimized functions
2025-07-21 23:00:09,319 - WARNING - Connection validation failed, creating new connection
2025-07-21 23:00:09,320 - INFO - DB Operation: {"timestamp": "2025-07-21 23:00:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:00:09,322 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:00:09,322 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:00:09,322 - INFO - Cleared all function caches
2025-07-21 23:00:09,323 - INFO - Generated weekly stats placeholder
2025-07-21 23:00:09,324 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:00:09,324 - INFO - Generated summary stats placeholder
2025-07-21 23:00:09,324 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:00:09,327 - INFO - Saved all placeholders to cache
2025-07-21 23:00:09,327 - INFO - Cleared all caches
2025-07-21 23:00:09,327 - INFO - Preloaded summary data
2025-07-21 23:00:09,328 - INFO - Preloaded game history (2 records)
2025-07-21 23:00:09,328 - INFO - Preloaded wallet data
2025-07-21 23:00:09,329 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 23:00:09,330 - INFO - Saved 1 items to persistent cache
2025-07-21 23:00:09,330 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 23:00:09,331 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 23:00:09,861 - INFO - Saved 0 items to persistent cache
2025-07-21 23:00:09,862 - INFO - Cache cleared
2025-07-21 23:00:09,862 - INFO - Cleared all preloader cache data
2025-07-21 23:00:09,863 - INFO - Starting stats data preloading
2025-07-21 23:00:09,863 - INFO - Started stats data preloading
2025-07-21 23:00:09,863 - INFO - Loading data using optimized functions
2025-07-21 23:00:09,864 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:00:09,864 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:00:09,864 - INFO - Cleared all function caches
2025-07-21 23:00:09,865 - INFO - Generated weekly stats placeholder
2025-07-21 23:00:09,865 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:00:09,865 - INFO - Generated summary stats placeholder
2025-07-21 23:00:09,867 - INFO - Saved all placeholders to cache
2025-07-21 23:00:09,868 - INFO - Cleared all caches
2025-07-21 23:00:10,000 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:00:10,000 - INFO - Game data: {'winner_name': 'Credit Deduction Game', 'winner_cartella': 0, 'claim_type': 'credit_based_recording', 'game_duration': 300.8600118160248, 'player_count': 1, 'prize_amount': 40, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:00:09', 'stake': 25, 'bet_amount': 25, 'completion_type': 'credit_deduction', 'credits_used': 1, 'share_percentage': 15, 'referee_commission': 8.0, 'game_id': 'retroactive_game_1753128009', 'total_bets': 40, 'timestamp': '2025-07-21T23:00:09.861001'}
2025-07-21 23:00:10,002 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:00:10,003 - INFO - Statistics saved to data\stats.json
2025-07-21 23:00:10,003 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:00:14,864 - INFO - DB Operation: {"timestamp": "2025-07-21 23:00:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:00:14,865 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:00:14,865 - ERROR - Error getting connection from pool: 
2025-07-21 23:00:14,865 - WARNING - Falling back to direct connection
2025-07-21 23:00:14,865 - INFO - DB Operation: {"timestamp": "2025-07-21 23:00:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:00:14,866 - INFO - Preloaded summary data
2025-07-21 23:00:14,866 - INFO - Preloaded game history (2 records)
2025-07-21 23:00:14,867 - INFO - Preloaded wallet data
2025-07-21 23:00:14,867 - INFO - Stats data preloaded successfully in 5.00 seconds
2025-07-21 23:00:14,867 - INFO - Loaded time reference (startup #2)
2025-07-21 23:00:14,868 - INFO - Saved 8 items to persistent cache
2025-07-21 23:00:14,868 - INFO - Time Manager initialized. Reference: 2025-07-21T19:33:14.552181+00:00
2025-07-21 23:00:19,869 - ERROR - Error getting connection from pool: 
2025-07-21 23:00:19,870 - WARNING - Falling back to direct connection
2025-07-21 23:00:19,870 - INFO - DB Operation: {"timestamp": "2025-07-21 23:00:19", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:00:24,872 - ERROR - Error getting connection from pool: 
2025-07-21 23:00:24,872 - WARNING - Falling back to direct connection
2025-07-21 23:00:24,873 - INFO - DB Operation: {"timestamp": "2025-07-21 23:00:24", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:00:25,444 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:00:25,444 - INFO - Game data: {'winner_name': 'Credit-Based Game', 'winner_cartella': None, 'claim_type': 'credit_based', 'game_duration': 0, 'player_count': 4, 'prize_amount': 32, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:00:24', 'stake': 10, 'bet_amount': 10, 'completion_type': 'credit_based', 'timestamp': '2025-07-21T23:00:24.995155'}
2025-07-21 23:00:25,446 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:00:25,448 - INFO - Statistics saved to data\stats.json
2025-07-21 23:00:25,448 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:00:27,138 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:00:27,140 - WARNING - Failed to connect to RethinkDB
2025-07-21 23:00:29,040 - INFO - draw_background: 0.0308s
2025-07-21 23:00:29,123 - INFO - draw_stats_page: 0.1137s
2025-07-21 23:00:29,137 - INFO - draw_background: 0.0000s
2025-07-21 23:00:29,172 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:00:29,173 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:29,242 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,277 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:00:29,346 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,437 - INFO - draw_stats_page: 0.0926s
2025-07-21 23:00:29,456 - INFO - draw_background: 0.0000s
2025-07-21 23:00:29,493 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:00:29,563 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,598 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:00:29,668 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,704 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:00:29,705 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:29,775 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,912 - INFO - draw_stats_page: 0.1385s
2025-07-21 23:00:29,926 - INFO - draw_background: 0.0010s
2025-07-21 23:00:29,969 - INFO - draw_stats_page: 0.0440s
2025-07-21 23:00:30,060 - INFO - draw_background: 0.0010s
2025-07-21 23:00:30,096 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:00:30,193 - INFO - draw_background: 0.0011s
2025-07-21 23:00:30,228 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:00:30,327 - INFO - draw_background: 0.0010s
2025-07-21 23:00:30,360 - INFO - draw_stats_page: 0.0339s
2025-07-21 23:00:30,361 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:30,460 - INFO - draw_background: 0.0010s
2025-07-21 23:00:30,495 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:00:30,510 - INFO - Admin button added to stats page
2025-07-21 23:00:30,593 - INFO - draw_background: 0.0000s
2025-07-21 23:00:30,625 - INFO - draw_stats_page: 0.0320s
2025-07-21 23:00:30,727 - INFO - draw_background: 0.0010s
2025-07-21 23:00:30,760 - INFO - draw_stats_page: 0.0342s
2025-07-21 23:00:30,860 - INFO - draw_background: 0.0009s
2025-07-21 23:00:30,892 - INFO - draw_stats_page: 0.0334s
2025-07-21 23:00:30,993 - INFO - draw_background: 0.0006s
2025-07-21 23:00:31,027 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:00:31,027 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:31,126 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,160 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:00:31,258 - INFO - draw_background: 0.0009s
2025-07-21 23:00:31,291 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:00:31,392 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,427 - INFO - draw_stats_page: 0.0351s
2025-07-21 23:00:31,524 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,556 - INFO - draw_stats_page: 0.0329s
2025-07-21 23:00:31,656 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,690 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:00:31,691 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:31,790 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,821 - INFO - draw_stats_page: 0.0317s
2025-07-21 23:00:31,925 - INFO - draw_background: 0.0010s
2025-07-21 23:00:31,960 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:00:32,058 - INFO - draw_background: 0.0009s
2025-07-21 23:00:32,088 - INFO - draw_stats_page: 0.0312s
2025-07-21 23:00:32,190 - INFO - draw_background: 0.0010s
2025-07-21 23:00:32,223 - INFO - draw_stats_page: 0.0340s
2025-07-21 23:00:32,323 - INFO - draw_background: 0.0010s
2025-07-21 23:00:32,357 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:00:32,358 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:32,457 - INFO - draw_background: 0.0009s
2025-07-21 23:00:32,487 - INFO - draw_stats_page: 0.0312s
2025-07-21 23:00:32,591 - INFO - draw_background: 0.0009s
2025-07-21 23:00:32,623 - INFO - draw_stats_page: 0.0338s
2025-07-21 23:00:32,723 - INFO - draw_background: 0.0010s
2025-07-21 23:00:32,754 - INFO - draw_stats_page: 0.0316s
2025-07-21 23:00:32,855 - INFO - draw_background: 0.0000s
2025-07-21 23:00:32,888 - INFO - draw_stats_page: 0.0331s
2025-07-21 23:00:32,989 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,024 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:00:33,025 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:33,122 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,155 - INFO - draw_stats_page: 0.0341s
2025-07-21 23:00:33,256 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,287 - INFO - draw_stats_page: 0.0328s
2025-07-21 23:00:33,388 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,421 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:00:33,521 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,551 - INFO - draw_stats_page: 0.0310s
2025-07-21 23:00:33,656 - INFO - draw_background: 0.0010s
2025-07-21 23:00:33,690 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:00:33,691 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:33,788 - INFO - draw_background: 0.0010s
2025-07-21 23:00:33,821 - INFO - draw_stats_page: 0.0334s
2025-07-21 23:00:33,920 - INFO - draw_background: 0.0009s
2025-07-21 23:00:33,955 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:00:34,053 - INFO - draw_background: 0.0005s
2025-07-21 23:00:34,059 - INFO - draw_stats_page: 0.0068s
2025-07-21 23:00:34,185 - INFO - draw_background: 0.0009s
2025-07-21 23:00:34,188 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:34,318 - INFO - draw_background: 0.0010s
2025-07-21 23:00:34,322 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:00:34,323 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:34,450 - INFO - draw_background: 0.0000s
2025-07-21 23:00:34,454 - INFO - draw_stats_page: 0.0031s
2025-07-21 23:00:34,583 - INFO - draw_background: 0.0005s
2025-07-21 23:00:34,586 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:34,714 - INFO - draw_background: 0.0000s
2025-07-21 23:00:34,718 - INFO - draw_stats_page: 0.0042s
2025-07-21 23:00:34,846 - INFO - draw_background: 0.0000s
2025-07-21 23:00:34,851 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:00:34,980 - INFO - draw_background: 0.0000s
2025-07-21 23:00:34,986 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:00:34,987 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:35,115 - INFO - draw_background: 0.0009s
2025-07-21 23:00:35,119 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:35,246 - INFO - draw_background: 0.0000s
2025-07-21 23:00:35,251 - INFO - draw_stats_page: 0.0042s
2025-07-21 23:00:35,381 - INFO - draw_background: 0.0009s
2025-07-21 23:00:35,385 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:35,513 - INFO - draw_background: 0.0010s
2025-07-21 23:00:35,517 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:00:35,646 - INFO - draw_background: 0.0009s
2025-07-21 23:00:35,649 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:35,650 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:35,779 - INFO - draw_background: 0.0010s
2025-07-21 23:00:35,783 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:35,911 - INFO - draw_background: 0.0000s
2025-07-21 23:00:35,916 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:00:36,045 - INFO - draw_background: 0.0009s
2025-07-21 23:00:36,049 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:36,177 - INFO - draw_background: 0.0000s
2025-07-21 23:00:36,180 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:36,310 - INFO - draw_background: 0.0010s
2025-07-21 23:00:36,314 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:36,315 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:36,443 - INFO - draw_background: 0.0010s
2025-07-21 23:00:36,446 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:36,576 - INFO - draw_background: 0.0000s
2025-07-21 23:00:36,579 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:36,708 - INFO - draw_background: 0.0009s
2025-07-21 23:00:36,711 - INFO - draw_stats_page: 0.0035s
2025-07-21 23:00:36,841 - INFO - draw_background: 0.0000s
2025-07-21 23:00:36,845 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:36,975 - INFO - draw_background: 0.0010s
2025-07-21 23:00:36,978 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:36,980 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:37,109 - INFO - draw_background: 0.0009s
2025-07-21 23:00:37,112 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:37,240 - INFO - draw_background: 0.0009s
2025-07-21 23:00:37,244 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:37,373 - INFO - draw_background: 0.0006s
2025-07-21 23:00:37,377 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:00:37,506 - INFO - draw_background: 0.0010s
2025-07-21 23:00:37,509 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:37,640 - INFO - draw_background: 0.0000s
2025-07-21 23:00:37,643 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:37,644 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:37,774 - INFO - draw_background: 0.0010s
2025-07-21 23:00:37,777 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:00:37,909 - INFO - draw_background: 0.0009s
2025-07-21 23:00:37,912 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:38,041 - INFO - draw_background: 0.0010s
2025-07-21 23:00:38,045 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:38,175 - INFO - draw_background: 0.0009s
2025-07-21 23:00:38,178 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:38,307 - INFO - draw_background: 0.0009s
2025-07-21 23:00:38,311 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:00:38,312 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:38,440 - INFO - draw_background: 0.0009s
2025-07-21 23:00:38,444 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:38,507 - INFO - draw_background: 0.0010s
2025-07-21 23:00:38,511 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:38,641 - INFO - draw_background: 0.0010s
2025-07-21 23:00:38,645 - INFO - draw_stats_page: 0.0056s
2025-07-21 23:00:38,707 - INFO - draw_background: 0.0011s
2025-07-21 23:00:38,710 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:00:38,837 - INFO - draw_background: 0.0010s
2025-07-21 23:00:38,841 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:38,843 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:38,971 - INFO - draw_background: 0.0000s
2025-07-21 23:00:38,975 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:39,105 - INFO - draw_background: 0.0010s
2025-07-21 23:00:39,109 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:39,238 - INFO - draw_background: 0.0009s
2025-07-21 23:00:39,243 - INFO - draw_stats_page: 0.0054s
2025-07-21 23:00:39,305 - INFO - draw_background: 0.0010s
2025-07-21 23:00:39,309 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:39,439 - INFO - draw_background: 0.0010s
2025-07-21 23:00:39,443 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:39,444 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:39,506 - INFO - draw_background: 0.0010s
2025-07-21 23:00:39,510 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:39,641 - INFO - draw_background: 0.0011s
2025-07-21 23:00:39,645 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:00:39,774 - INFO - draw_background: 0.0009s
2025-07-21 23:00:39,778 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:39,840 - INFO - draw_background: 0.0009s
2025-07-21 23:00:39,844 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:39,973 - INFO - draw_background: 0.0012s
2025-07-21 23:00:39,977 - INFO - draw_stats_page: 0.0052s
2025-07-21 23:00:39,979 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:40,105 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,109 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:40,239 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,244 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:00:40,373 - INFO - draw_background: 0.0011s
2025-07-21 23:00:40,376 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:40,507 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,511 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:40,640 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,643 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:40,644 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:40,707 - INFO - draw_background: 0.0009s
2025-07-21 23:00:40,710 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:40,839 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,842 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:40,906 - INFO - draw_background: 0.0010s
2025-07-21 23:00:40,909 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:41,040 - INFO - load_statistics: 0.0010s
2025-07-21 23:00:41,041 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,045 - INFO - draw_stats_page: 0.0055s
2025-07-21 23:00:41,172 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,173 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:41,176 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:00:41,305 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,308 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:41,439 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,442 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:00:41,573 - INFO - draw_background: 0.0005s
2025-07-21 23:00:41,576 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:41,706 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,709 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:41,837 - INFO - draw_background: 0.0010s
2025-07-21 23:00:41,838 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:41,841 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:00:41,970 - INFO - draw_background: 0.0009s
2025-07-21 23:00:41,974 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:00:42,103 - INFO - draw_background: 0.0000s
2025-07-21 23:00:42,106 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:42,235 - INFO - draw_background: 0.0000s
2025-07-21 23:00:42,240 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:42,368 - INFO - draw_background: 0.0010s
2025-07-21 23:00:42,371 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:42,501 - INFO - draw_background: 0.0010s
2025-07-21 23:00:42,501 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:42,505 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:00:42,634 - INFO - draw_background: 0.0010s
2025-07-21 23:00:42,637 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:00:42,767 - INFO - draw_background: 0.0000s
2025-07-21 23:00:42,770 - INFO - draw_stats_page: 0.0035s
2025-07-21 23:00:42,900 - INFO - draw_background: 0.0000s
2025-07-21 23:00:42,905 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:43,036 - INFO - draw_background: 0.0010s
2025-07-21 23:00:43,041 - INFO - draw_stats_page: 0.0060s
2025-07-21 23:00:43,169 - INFO - draw_background: 0.0000s
2025-07-21 23:00:43,170 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:43,174 - INFO - draw_stats_page: 0.0056s
2025-07-21 23:00:43,303 - INFO - draw_background: 0.0010s
2025-07-21 23:00:43,307 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:00:43,435 - INFO - draw_background: 0.0010s
2025-07-21 23:00:43,438 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:43,568 - INFO - draw_background: 0.0010s
2025-07-21 23:00:43,572 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:00:43,703 - INFO - draw_background: 0.0005s
2025-07-21 23:00:43,707 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:00:43,837 - INFO - draw_background: 0.0009s
2025-07-21 23:00:43,838 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:43,843 - INFO - draw_stats_page: 0.0065s
2025-07-21 23:00:43,970 - INFO - draw_background: 0.0010s
2025-07-21 23:00:43,973 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:00:44,102 - INFO - draw_background: 0.0011s
2025-07-21 23:00:44,106 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:00:44,235 - INFO - draw_background: 0.0009s
2025-07-21 23:00:44,280 - INFO - draw_stats_page: 0.0455s
2025-07-21 23:00:44,304 - INFO - draw_background: 0.0011s
2025-07-21 23:00:44,348 - INFO - draw_stats_page: 0.0457s
2025-07-21 23:00:44,373 - INFO - draw_background: 0.0005s
2025-07-21 23:00:44,374 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:44,411 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:00:44,507 - INFO - draw_background: 0.0010s
2025-07-21 23:00:44,547 - INFO - draw_stats_page: 0.0418s
2025-07-21 23:00:44,641 - INFO - draw_background: 0.0010s
2025-07-21 23:00:44,672 - INFO - draw_stats_page: 0.0327s
2025-07-21 23:00:44,775 - INFO - draw_background: 0.0009s
2025-07-21 23:00:44,812 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:00:44,908 - INFO - draw_background: 0.0009s
2025-07-21 23:00:44,943 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:00:45,042 - INFO - draw_background: 0.0010s
2025-07-21 23:00:45,042 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:45,079 - INFO - draw_stats_page: 0.0386s
2025-07-21 23:00:45,174 - INFO - draw_background: 0.0005s
2025-07-21 23:00:45,212 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:00:45,318 - INFO - draw_background: 0.0010s
2025-07-21 23:00:45,371 - INFO - draw_stats_page: 0.0535s
2025-07-21 23:00:45,466 - INFO - draw_background: 0.0010s
2025-07-21 23:00:45,503 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:00:45,598 - INFO - draw_background: 0.0005s
2025-07-21 23:00:45,637 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:00:45,666 - INFO - draw_background: 0.0016s
2025-07-21 23:00:45,667 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:45,707 - INFO - draw_stats_page: 0.0418s
2025-07-21 23:00:45,798 - INFO - draw_background: 0.0000s
2025-07-21 23:00:45,836 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:00:45,932 - INFO - draw_background: 0.0010s
2025-07-21 23:00:45,970 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:00:46,066 - INFO - draw_background: 0.0005s
2025-07-21 23:00:46,100 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:00:46,196 - INFO - draw_background: 0.0005s
2025-07-21 23:00:46,232 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:00:46,330 - INFO - draw_background: 0.0010s
2025-07-21 23:00:46,331 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:46,367 - INFO - draw_stats_page: 0.0387s
2025-07-21 23:00:46,463 - INFO - draw_background: 0.0006s
2025-07-21 23:00:46,503 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:00:46,597 - INFO - draw_background: 0.0000s
2025-07-21 23:00:46,629 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:00:46,728 - INFO - draw_background: 0.0009s
2025-07-21 23:00:46,763 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:00:46,862 - INFO - draw_background: 0.0005s
2025-07-21 23:00:46,895 - INFO - draw_stats_page: 0.0331s
2025-07-21 23:00:46,963 - INFO - draw_background: 0.0015s
2025-07-21 23:00:46,965 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:47,094 - INFO - draw_stats_page: 0.1322s
2025-07-21 23:00:47,116 - INFO - draw_background: 0.0010s
2025-07-21 23:00:47,150 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:00:47,248 - INFO - draw_background: 0.0010s
2025-07-21 23:00:47,284 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:00:47,382 - INFO - draw_background: 0.0010s
2025-07-21 23:00:47,418 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:00:47,516 - INFO - draw_background: 0.0010s
2025-07-21 23:00:47,548 - INFO - draw_stats_page: 0.0334s
2025-07-21 23:00:47,647 - INFO - draw_background: 0.0010s
2025-07-21 23:00:47,648 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:47,682 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:00:47,781 - INFO - draw_background: 0.0005s
2025-07-21 23:00:47,819 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:00:47,914 - INFO - draw_background: 0.0009s
2025-07-21 23:00:47,950 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:00:48,046 - INFO - draw_background: 0.0010s
2025-07-21 23:00:48,085 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:00:48,178 - INFO - draw_background: 0.0010s
2025-07-21 23:00:48,218 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:00:48,311 - INFO - draw_background: 0.0000s
2025-07-21 23:00:48,312 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:48,348 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:00:48,443 - INFO - draw_background: 0.0010s
2025-07-21 23:00:48,477 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:00:48,577 - INFO - draw_background: 0.0010s
2025-07-21 23:00:48,618 - INFO - draw_stats_page: 0.0411s
2025-07-21 23:00:48,710 - INFO - draw_background: 0.0000s
2025-07-21 23:00:48,744 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:00:48,844 - INFO - draw_background: 0.0005s
2025-07-21 23:00:48,880 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:00:48,980 - INFO - draw_background: 0.0010s
2025-07-21 23:00:48,981 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:49,016 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:00:49,113 - INFO - draw_background: 0.0008s
2025-07-21 23:00:49,159 - INFO - draw_stats_page: 0.0463s
2025-07-21 23:00:49,246 - INFO - draw_background: 0.0005s
2025-07-21 23:00:49,279 - INFO - draw_stats_page: 0.0339s
2025-07-21 23:00:49,381 - INFO - draw_background: 0.0000s
2025-07-21 23:00:49,416 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:00:49,512 - INFO - draw_background: 0.0009s
2025-07-21 23:00:49,551 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:00:49,646 - INFO - draw_background: 0.0010s
2025-07-21 23:00:49,647 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:49,687 - INFO - draw_stats_page: 0.0415s
2025-07-21 23:00:49,781 - INFO - draw_background: 0.0010s
2025-07-21 23:00:49,818 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:00:49,914 - INFO - draw_background: 0.0000s
2025-07-21 23:00:49,948 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:00:50,047 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,085 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:00:50,180 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,215 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:00:50,314 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,314 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:50,354 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:00:50,446 - INFO - draw_background: 0.0005s
2025-07-21 23:00:50,482 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:00:50,578 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,613 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:00:50,713 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,750 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:00:50,846 - INFO - draw_background: 0.0009s
2025-07-21 23:00:50,881 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:00:50,980 - INFO - draw_background: 0.0010s
2025-07-21 23:00:50,981 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:51,017 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:00:51,113 - INFO - draw_background: 0.0005s
2025-07-21 23:00:51,150 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:00:51,247 - INFO - draw_background: 0.0010s
2025-07-21 23:00:51,282 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:00:51,380 - INFO - draw_background: 0.0010s
2025-07-21 23:00:51,416 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:00:51,513 - INFO - draw_background: 0.0010s
2025-07-21 23:00:51,553 - INFO - draw_stats_page: 0.0403s
2025-07-21 23:00:51,649 - INFO - draw_background: 0.0010s
2025-07-21 23:00:51,650 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:51,683 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:00:51,781 - INFO - draw_background: 0.0000s
2025-07-21 23:00:51,819 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:00:51,916 - INFO - draw_background: 0.0010s
2025-07-21 23:00:51,948 - INFO - draw_stats_page: 0.0329s
2025-07-21 23:00:52,050 - INFO - draw_background: 0.0010s
2025-07-21 23:00:52,093 - INFO - draw_stats_page: 0.0435s
2025-07-21 23:00:52,183 - INFO - draw_background: 0.0005s
2025-07-21 23:00:52,220 - INFO - draw_stats_page: 0.0374s
2025-07-21 23:00:52,318 - INFO - draw_background: 0.0000s
2025-07-21 23:00:52,319 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:52,356 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:00:52,450 - INFO - draw_background: 0.0009s
2025-07-21 23:00:52,486 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:00:52,584 - INFO - draw_background: 0.0010s
2025-07-21 23:00:52,624 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:00:52,718 - INFO - draw_background: 0.0010s
2025-07-21 23:00:52,756 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:00:52,850 - INFO - draw_background: 0.0009s
2025-07-21 23:00:52,886 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:00:52,984 - INFO - draw_background: 0.0010s
2025-07-21 23:00:52,985 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:53,020 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:00:53,117 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,152 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:00:53,251 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,286 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:00:53,384 - INFO - draw_background: 0.0011s
2025-07-21 23:00:53,417 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:00:53,517 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,549 - INFO - draw_stats_page: 0.0322s
2025-07-21 23:00:53,650 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,650 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:53,686 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:00:53,785 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,820 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:00:53,919 - INFO - draw_background: 0.0010s
2025-07-21 23:00:53,953 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:00:54,050 - INFO - draw_background: 0.0010s
2025-07-21 23:00:54,163 - INFO - draw_stats_page: 0.1134s
2025-07-21 23:00:54,200 - INFO - draw_background: 0.0000s
2025-07-21 23:00:54,240 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:00:54,335 - INFO - draw_background: 0.0010s
2025-07-21 23:00:54,336 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:54,372 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:00:54,468 - INFO - draw_background: 0.0009s
2025-07-21 23:00:54,502 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:00:54,601 - INFO - draw_background: 0.0009s
2025-07-21 23:00:54,637 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:00:54,734 - INFO - draw_background: 0.0010s
2025-07-21 23:00:54,772 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:00:54,867 - INFO - draw_background: 0.0009s
2025-07-21 23:00:54,901 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:00:55,000 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,001 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:55,037 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:00:55,134 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,168 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:00:55,267 - INFO - draw_background: 0.0005s
2025-07-21 23:00:55,302 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:00:55,400 - INFO - draw_background: 0.0009s
2025-07-21 23:00:55,436 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:00:55,533 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,567 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:00:55,665 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,666 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:55,701 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:00:55,798 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,833 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:00:55,931 - INFO - draw_background: 0.0010s
2025-07-21 23:00:55,968 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:00:56,065 - INFO - draw_background: 0.0010s
2025-07-21 23:00:56,099 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:00:56,198 - INFO - draw_background: 0.0010s
2025-07-21 23:00:56,234 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:00:56,330 - INFO - draw_background: 0.0000s
2025-07-21 23:00:56,331 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:56,370 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:00:56,463 - INFO - draw_background: 0.0005s
2025-07-21 23:00:56,500 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:00:56,599 - INFO - draw_background: 0.0000s
2025-07-21 23:00:56,635 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:00:56,730 - INFO - draw_background: 0.0010s
2025-07-21 23:00:56,765 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:00:56,865 - INFO - draw_background: 0.0000s
2025-07-21 23:00:56,901 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:00:56,998 - INFO - draw_background: 0.0010s
2025-07-21 23:00:56,999 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:57,034 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:00:57,130 - INFO - draw_background: 0.0011s
2025-07-21 23:00:57,165 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:00:57,263 - INFO - draw_background: 0.0000s
2025-07-21 23:00:57,298 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:00:57,397 - INFO - draw_background: 0.0009s
2025-07-21 23:00:57,431 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:00:57,531 - INFO - draw_background: 0.0009s
2025-07-21 23:00:57,565 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:00:57,665 - INFO - draw_background: 0.0009s
2025-07-21 23:00:57,666 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:57,699 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:00:57,797 - INFO - draw_background: 0.0009s
2025-07-21 23:00:57,839 - INFO - draw_stats_page: 0.0427s
2025-07-21 23:00:57,931 - INFO - draw_background: 0.0000s
2025-07-21 23:00:57,968 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:00:58,064 - INFO - draw_background: 0.0010s
2025-07-21 23:00:58,103 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:00:58,198 - INFO - draw_background: 0.0000s
2025-07-21 23:00:58,234 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:00:58,330 - INFO - draw_background: 0.0010s
2025-07-21 23:00:58,331 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:58,366 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:00:58,464 - INFO - draw_background: 0.0010s
2025-07-21 23:00:58,493 - INFO - draw_stats_page: 0.0309s
2025-07-21 23:00:58,599 - INFO - draw_background: 0.0009s
2025-07-21 23:00:58,634 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:00:58,734 - INFO - draw_background: 0.0010s
2025-07-21 23:00:58,775 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:00:58,867 - INFO - draw_background: 0.0009s
2025-07-21 23:00:58,899 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:00:59,001 - INFO - draw_background: 0.0010s
2025-07-21 23:00:59,001 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:59,041 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:00:59,133 - INFO - draw_background: 0.0006s
2025-07-21 23:00:59,169 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:00:59,268 - INFO - draw_background: 0.0009s
2025-07-21 23:00:59,302 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:00:59,401 - INFO - draw_background: 0.0000s
2025-07-21 23:00:59,434 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:00:59,534 - INFO - draw_background: 0.0000s
2025-07-21 23:00:59,568 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:00:59,668 - INFO - draw_background: 0.0009s
2025-07-21 23:00:59,669 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:00:59,702 - INFO - draw_stats_page: 0.0351s
2025-07-21 23:00:59,801 - INFO - draw_background: 0.0009s
2025-07-21 23:00:59,836 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:00:59,936 - INFO - draw_background: 0.0000s
2025-07-21 23:00:59,974 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:01:00,068 - INFO - draw_background: 0.0000s
2025-07-21 23:01:00,103 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:01:00,201 - INFO - draw_background: 0.0009s
2025-07-21 23:01:00,233 - INFO - draw_stats_page: 0.0328s
2025-07-21 23:01:00,334 - INFO - draw_background: 0.0000s
2025-07-21 23:01:00,336 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:00,375 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:01:00,468 - INFO - draw_background: 0.0009s
2025-07-21 23:01:00,504 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:01:00,600 - INFO - draw_background: 0.0010s
2025-07-21 23:01:00,637 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:01:00,733 - INFO - draw_background: 0.0006s
2025-07-21 23:01:00,774 - INFO - draw_stats_page: 0.0421s
2025-07-21 23:01:00,868 - INFO - draw_background: 0.0009s
2025-07-21 23:01:00,904 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:01:01,001 - INFO - draw_background: 0.0009s
2025-07-21 23:01:01,002 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:01,039 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:01:01,133 - INFO - draw_background: 0.0006s
2025-07-21 23:01:01,175 - INFO - draw_stats_page: 0.0420s
2025-07-21 23:01:01,268 - INFO - draw_background: 0.0010s
2025-07-21 23:01:01,304 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:01:01,399 - INFO - draw_background: 0.0010s
2025-07-21 23:01:01,431 - INFO - draw_stats_page: 0.0331s
2025-07-21 23:01:01,466 - INFO - draw_background: 0.0009s
2025-07-21 23:01:01,505 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:01:01,598 - INFO - draw_background: 0.0010s
2025-07-21 23:01:01,599 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:01,633 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:01:01,732 - INFO - draw_background: 0.0010s
2025-07-21 23:01:01,769 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:01:01,890 - INFO - draw_background: 0.0000s
2025-07-21 23:01:01,939 - INFO - draw_stats_page: 0.0483s
2025-07-21 23:01:02,027 - INFO - draw_background: 0.0009s
2025-07-21 23:01:02,061 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:01:02,160 - INFO - draw_background: 0.0005s
2025-07-21 23:01:02,197 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:01:02,293 - INFO - draw_background: 0.0010s
2025-07-21 23:01:02,293 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:02,331 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:01:02,427 - INFO - draw_background: 0.0009s
2025-07-21 23:01:02,470 - INFO - draw_stats_page: 0.0439s
2025-07-21 23:01:02,559 - INFO - draw_background: 0.0010s
2025-07-21 23:01:02,597 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:01:02,693 - INFO - draw_background: 0.0006s
2025-07-21 23:01:02,732 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:01:02,825 - INFO - draw_background: 0.0000s
2025-07-21 23:01:02,863 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:01:02,958 - INFO - draw_background: 0.0000s
2025-07-21 23:01:02,960 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:02,994 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:01:03,091 - INFO - draw_background: 0.0009s
2025-07-21 23:01:03,128 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:01:03,225 - INFO - draw_background: 0.0009s
2025-07-21 23:01:03,260 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:03,356 - INFO - draw_background: 0.0006s
2025-07-21 23:01:03,391 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:01:03,488 - INFO - draw_background: 0.0005s
2025-07-21 23:01:03,523 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:01:03,622 - INFO - draw_background: 0.0005s
2025-07-21 23:01:03,622 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:03,659 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:01:03,756 - INFO - draw_background: 0.0009s
2025-07-21 23:01:03,793 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:01:03,823 - INFO - draw_background: 0.0012s
2025-07-21 23:01:03,863 - INFO - draw_stats_page: 0.0415s
2025-07-21 23:01:03,957 - INFO - draw_background: 0.0010s
2025-07-21 23:01:03,993 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:01:04,090 - INFO - draw_background: 0.0010s
2025-07-21 23:01:04,126 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:01:04,224 - INFO - draw_background: 0.0010s
2025-07-21 23:01:04,224 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:04,264 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:01:04,358 - INFO - draw_background: 0.0010s
2025-07-21 23:01:04,396 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:01:04,488 - INFO - draw_background: 0.0005s
2025-07-21 23:01:04,527 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:01:04,620 - INFO - draw_background: 0.0005s
2025-07-21 23:01:04,656 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:01:04,755 - INFO - draw_background: 0.0011s
2025-07-21 23:01:04,790 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:01:04,887 - INFO - draw_background: 0.0000s
2025-07-21 23:01:04,889 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:04,927 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:01:05,021 - INFO - draw_background: 0.0010s
2025-07-21 23:01:05,058 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:01:05,156 - INFO - draw_background: 0.0000s
2025-07-21 23:01:05,193 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:01:05,287 - INFO - draw_background: 0.0000s
2025-07-21 23:01:05,323 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:01:05,421 - INFO - draw_background: 0.0000s
2025-07-21 23:01:05,458 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:01:05,554 - INFO - draw_background: 0.0011s
2025-07-21 23:01:05,555 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:05,591 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:01:05,687 - INFO - draw_background: 0.0009s
2025-07-21 23:01:05,725 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:01:05,818 - INFO - draw_background: 0.0009s
2025-07-21 23:01:05,858 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:01:05,951 - INFO - draw_background: 0.0000s
2025-07-21 23:01:05,985 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:01:06,085 - INFO - draw_background: 0.0013s
2025-07-21 23:01:06,126 - INFO - draw_stats_page: 0.0426s
2025-07-21 23:01:06,217 - INFO - draw_background: 0.0010s
2025-07-21 23:01:06,218 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:06,256 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:01:06,348 - INFO - draw_background: 0.0009s
2025-07-21 23:01:06,385 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:01:06,481 - INFO - draw_background: 0.0000s
2025-07-21 23:01:06,520 - INFO - draw_stats_page: 0.0396s
2025-07-21 23:01:06,616 - INFO - draw_background: 0.0010s
2025-07-21 23:01:06,662 - INFO - draw_stats_page: 0.0473s
2025-07-21 23:01:06,752 - INFO - draw_background: 0.0016s
2025-07-21 23:01:06,790 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:01:06,883 - INFO - draw_background: 0.0005s
2025-07-21 23:01:06,884 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:06,922 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:01:07,017 - INFO - draw_background: 0.0010s
2025-07-21 23:01:07,059 - INFO - draw_stats_page: 0.0426s
2025-07-21 23:01:07,150 - INFO - draw_background: 0.0000s
2025-07-21 23:01:07,192 - INFO - draw_stats_page: 0.0425s
2025-07-21 23:01:07,282 - INFO - draw_background: 0.0006s
2025-07-21 23:01:07,324 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:01:07,416 - INFO - draw_background: 0.0009s
2025-07-21 23:01:07,458 - INFO - draw_stats_page: 0.0435s
2025-07-21 23:01:07,548 - INFO - draw_background: 0.0009s
2025-07-21 23:01:07,549 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:07,588 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:01:07,682 - INFO - draw_background: 0.0010s
2025-07-21 23:01:07,720 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:01:07,750 - INFO - draw_background: 0.0000s
2025-07-21 23:01:07,792 - INFO - draw_stats_page: 0.0423s
2025-07-21 23:01:07,816 - INFO - draw_background: 0.0010s
2025-07-21 23:01:07,858 - INFO - draw_stats_page: 0.0431s
2025-07-21 23:01:07,883 - INFO - draw_background: 0.0005s
2025-07-21 23:01:07,925 - INFO - draw_stats_page: 0.0432s
2025-07-21 23:01:07,951 - INFO - draw_background: 0.0010s
2025-07-21 23:01:07,951 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:07,998 - INFO - draw_stats_page: 0.0481s
2025-07-21 23:01:08,085 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,123 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:01:08,217 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,258 - INFO - draw_stats_page: 0.0412s
2025-07-21 23:01:08,350 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,385 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:01:08,482 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,520 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:01:08,616 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,617 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:08,651 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:01:08,749 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,790 - INFO - draw_stats_page: 0.0416s
2025-07-21 23:01:08,880 - INFO - draw_background: 0.0010s
2025-07-21 23:01:08,922 - INFO - draw_stats_page: 0.0429s
2025-07-21 23:01:09,013 - INFO - draw_background: 0.0005s
2025-07-21 23:01:09,052 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:01:09,146 - INFO - draw_background: 0.0010s
2025-07-21 23:01:09,187 - INFO - draw_stats_page: 0.0418s
2025-07-21 23:01:09,280 - INFO - draw_background: 0.0010s
2025-07-21 23:01:09,281 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:09,431 - INFO - draw_stats_page: 0.1515s
2025-07-21 23:01:09,453 - INFO - draw_background: 0.0011s
2025-07-21 23:01:09,493 - INFO - draw_stats_page: 0.0407s
2025-07-21 23:01:09,587 - INFO - draw_background: 0.0009s
2025-07-21 23:01:09,630 - INFO - draw_stats_page: 0.0434s
2025-07-21 23:01:09,720 - INFO - draw_background: 0.0000s
2025-07-21 23:01:09,760 - INFO - draw_stats_page: 0.0407s
2025-07-21 23:01:09,853 - INFO - draw_background: 0.0005s
2025-07-21 23:01:09,897 - INFO - draw_stats_page: 0.0452s
2025-07-21 23:01:09,920 - INFO - draw_background: 0.0010s
2025-07-21 23:01:09,920 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:09,964 - INFO - draw_stats_page: 0.0458s
2025-07-21 23:01:09,986 - INFO - draw_background: 0.0009s
2025-07-21 23:01:10,034 - INFO - draw_stats_page: 0.0490s
2025-07-21 23:01:10,057 - INFO - draw_background: 0.0009s
2025-07-21 23:01:10,100 - INFO - draw_stats_page: 0.0439s
2025-07-21 23:01:10,127 - INFO - draw_background: 0.0026s
2025-07-21 23:01:10,170 - INFO - draw_stats_page: 0.0456s
2025-07-21 23:01:10,195 - INFO - draw_background: 0.0020s
2025-07-21 23:01:10,239 - INFO - draw_stats_page: 0.0455s
2025-07-21 23:01:10,333 - INFO - draw_background: 0.0005s
2025-07-21 23:01:10,335 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:10,378 - INFO - draw_stats_page: 0.0458s
2025-07-21 23:01:10,466 - INFO - draw_background: 0.0009s
2025-07-21 23:01:10,507 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:01:10,600 - INFO - draw_background: 0.0010s
2025-07-21 23:01:10,636 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:01:10,733 - INFO - draw_background: 0.0010s
2025-07-21 23:01:10,776 - INFO - draw_stats_page: 0.0449s
2025-07-21 23:01:10,865 - INFO - draw_background: 0.0010s
2025-07-21 23:01:10,875 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:01:10,905 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:01:10,998 - INFO - draw_background: 0.0010s
2025-07-21 23:01:10,999 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:11,038 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:01:11,131 - INFO - draw_background: 0.0005s
2025-07-21 23:01:11,169 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:01:11,266 - INFO - draw_background: 0.0010s
2025-07-21 23:01:11,304 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:01:11,402 - INFO - draw_background: 0.0010s
2025-07-21 23:01:11,438 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:01:11,534 - INFO - draw_background: 0.0010s
2025-07-21 23:01:11,564 - INFO - draw_stats_page: 0.0318s
2025-07-21 23:01:11,667 - INFO - draw_background: 0.0009s
2025-07-21 23:01:11,668 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:11,705 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:01:11,801 - INFO - draw_background: 0.0009s
2025-07-21 23:01:11,840 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:01:11,934 - INFO - draw_background: 0.0000s
2025-07-21 23:01:11,974 - INFO - draw_stats_page: 0.0411s
2025-07-21 23:01:12,067 - INFO - draw_background: 0.0010s
2025-07-21 23:01:12,108 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:01:12,200 - INFO - draw_background: 0.0005s
2025-07-21 23:01:12,236 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:01:12,332 - INFO - draw_background: 0.0000s
2025-07-21 23:01:12,334 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:12,370 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:01:12,467 - INFO - draw_background: 0.0010s
2025-07-21 23:01:12,500 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:01:12,600 - INFO - draw_background: 0.0010s
2025-07-21 23:01:12,635 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:01:12,734 - INFO - draw_background: 0.0010s
2025-07-21 23:01:12,768 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:01:12,866 - INFO - draw_background: 0.0010s
2025-07-21 23:01:12,901 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:01:13,000 - INFO - draw_background: 0.0009s
2025-07-21 23:01:13,001 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:13,036 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:01:13,133 - INFO - draw_background: 0.0010s
2025-07-21 23:01:13,167 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:01:13,268 - INFO - draw_background: 0.0011s
2025-07-21 23:01:13,300 - INFO - draw_stats_page: 0.0330s
2025-07-21 23:01:13,400 - INFO - draw_background: 0.0000s
2025-07-21 23:01:13,437 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:01:13,534 - INFO - draw_background: 0.0010s
2025-07-21 23:01:13,571 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:01:13,668 - INFO - draw_background: 0.0010s
2025-07-21 23:01:13,669 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:13,709 - INFO - draw_stats_page: 0.0415s
2025-07-21 23:01:13,801 - INFO - draw_background: 0.0009s
2025-07-21 23:01:13,835 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:01:13,935 - INFO - draw_background: 0.0010s
2025-07-21 23:01:13,970 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:01:14,067 - INFO - draw_background: 0.0000s
2025-07-21 23:01:14,103 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:01:14,201 - INFO - draw_background: 0.0010s
2025-07-21 23:01:14,236 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:01:14,333 - INFO - draw_background: 0.0010s
2025-07-21 23:01:14,334 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:14,369 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:01:14,467 - INFO - draw_background: 0.0005s
2025-07-21 23:01:14,501 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:01:14,601 - INFO - draw_background: 0.0009s
2025-07-21 23:01:14,633 - INFO - draw_stats_page: 0.0327s
2025-07-21 23:01:14,733 - INFO - draw_background: 0.0005s
2025-07-21 23:01:14,767 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:01:14,867 - INFO - draw_background: 0.0009s
2025-07-21 23:01:14,902 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:01:14,999 - INFO - draw_background: 0.0010s
2025-07-21 23:01:15,000 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:15,038 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:01:15,133 - INFO - draw_background: 0.0006s
2025-07-21 23:01:15,169 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:01:15,266 - INFO - draw_background: 0.0009s
2025-07-21 23:01:15,301 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:01:15,400 - INFO - draw_background: 0.0000s
2025-07-21 23:01:15,437 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:01:15,536 - INFO - draw_background: 0.0010s
2025-07-21 23:01:15,571 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:01:15,694 - INFO - draw_background: 0.0011s
2025-07-21 23:01:15,695 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:15,730 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:01:15,827 - INFO - draw_background: 0.0009s
2025-07-21 23:01:15,860 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:01:15,959 - INFO - draw_background: 0.0000s
2025-07-21 23:01:15,994 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:01:16,091 - INFO - draw_background: 0.0010s
2025-07-21 23:01:16,126 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:16,222 - INFO - draw_background: 0.0005s
2025-07-21 23:01:16,264 - INFO - draw_stats_page: 0.0420s
2025-07-21 23:01:16,356 - INFO - draw_background: 0.0010s
2025-07-21 23:01:16,356 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:16,391 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:01:16,487 - INFO - draw_background: 0.0006s
2025-07-21 23:01:16,522 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:01:16,619 - INFO - draw_background: 0.0000s
2025-07-21 23:01:16,657 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:01:16,753 - INFO - draw_background: 0.0011s
2025-07-21 23:01:16,801 - INFO - draw_stats_page: 0.0488s
2025-07-21 23:01:16,893 - INFO - draw_background: 0.0011s
2025-07-21 23:01:16,934 - INFO - draw_stats_page: 0.0422s
2025-07-21 23:01:17,027 - INFO - draw_background: 0.0006s
2025-07-21 23:01:17,028 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:17,073 - INFO - draw_stats_page: 0.0462s
2025-07-21 23:01:17,174 - INFO - draw_background: 0.0010s
2025-07-21 23:01:17,280 - INFO - draw_stats_page: 0.1068s
2025-07-21 23:01:17,305 - INFO - draw_background: 0.0019s
2025-07-21 23:01:17,351 - INFO - draw_stats_page: 0.0480s
2025-07-21 23:01:17,443 - INFO - draw_background: 0.0006s
2025-07-21 23:01:17,489 - INFO - draw_stats_page: 0.0477s
2025-07-21 23:01:17,578 - INFO - draw_background: 0.0010s
2025-07-21 23:01:17,624 - INFO - draw_stats_page: 0.0476s
2025-07-21 23:01:17,712 - INFO - draw_background: 0.0011s
2025-07-21 23:01:17,713 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:17,759 - INFO - draw_stats_page: 0.0481s
2025-07-21 23:01:17,849 - INFO - draw_background: 0.0012s
2025-07-21 23:01:17,898 - INFO - draw_stats_page: 0.0501s
2025-07-21 23:01:17,988 - INFO - draw_background: 0.0005s
2025-07-21 23:01:18,032 - INFO - draw_stats_page: 0.0448s
2025-07-21 23:01:18,122 - INFO - draw_background: 0.0006s
2025-07-21 23:01:18,158 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:01:18,256 - INFO - draw_background: 0.0010s
2025-07-21 23:01:18,290 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:01:18,390 - INFO - draw_background: 0.0000s
2025-07-21 23:01:18,390 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:18,425 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:01:18,524 - INFO - draw_background: 0.0000s
2025-07-21 23:01:18,556 - INFO - draw_stats_page: 0.0334s
2025-07-21 23:01:18,657 - INFO - draw_background: 0.0005s
2025-07-21 23:01:18,694 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:01:18,790 - INFO - draw_background: 0.0010s
2025-07-21 23:01:18,823 - INFO - draw_stats_page: 0.0338s
2025-07-21 23:01:18,925 - INFO - draw_background: 0.0010s
2025-07-21 23:01:18,959 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:01:19,057 - INFO - draw_background: 0.0000s
2025-07-21 23:01:19,058 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:19,092 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:01:19,190 - INFO - draw_background: 0.0009s
2025-07-21 23:01:19,227 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:01:19,324 - INFO - draw_background: 0.0000s
2025-07-21 23:01:19,359 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:19,456 - INFO - draw_background: 0.0009s
2025-07-21 23:01:19,487 - INFO - draw_stats_page: 0.0326s
2025-07-21 23:01:19,589 - INFO - draw_background: 0.0009s
2025-07-21 23:01:19,625 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:01:19,724 - INFO - draw_background: 0.0000s
2025-07-21 23:01:19,725 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:19,759 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:19,857 - INFO - draw_background: 0.0010s
2025-07-21 23:01:19,891 - INFO - draw_stats_page: 0.0341s
2025-07-21 23:01:19,989 - INFO - draw_background: 0.0009s
2025-07-21 23:01:20,023 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:01:20,124 - INFO - draw_background: 0.0010s
2025-07-21 23:01:20,159 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:01:20,257 - INFO - draw_background: 0.0011s
2025-07-21 23:01:20,295 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:01:20,390 - INFO - draw_background: 0.0010s
2025-07-21 23:01:20,391 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:20,425 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:01:20,524 - INFO - draw_background: 0.0005s
2025-07-21 23:01:20,559 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:01:20,658 - INFO - draw_background: 0.0000s
2025-07-21 23:01:20,692 - INFO - draw_stats_page: 0.0335s
2025-07-21 23:01:20,790 - INFO - draw_background: 0.0000s
2025-07-21 23:01:20,824 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:01:20,923 - INFO - draw_background: 0.0000s
2025-07-21 23:01:20,958 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:21,059 - INFO - draw_background: 0.0009s
2025-07-21 23:01:21,060 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:21,094 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:01:21,190 - INFO - draw_background: 0.0000s
2025-07-21 23:01:21,228 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:01:21,325 - INFO - draw_background: 0.0009s
2025-07-21 23:01:21,358 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:01:21,457 - INFO - draw_background: 0.0010s
2025-07-21 23:01:21,493 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:01:21,591 - INFO - draw_background: 0.0000s
2025-07-21 23:01:21,631 - INFO - draw_stats_page: 0.0404s
2025-07-21 23:01:21,726 - INFO - draw_background: 0.0010s
2025-07-21 23:01:21,727 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:21,762 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:01:21,859 - INFO - draw_background: 0.0009s
2025-07-21 23:01:21,889 - INFO - draw_stats_page: 0.0313s
2025-07-21 23:01:21,992 - INFO - draw_background: 0.0006s
2025-07-21 23:01:22,027 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:01:22,126 - INFO - draw_background: 0.0000s
2025-07-21 23:01:22,162 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:01:22,259 - INFO - draw_background: 0.0005s
2025-07-21 23:01:22,300 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:01:22,394 - INFO - draw_background: 0.0010s
2025-07-21 23:01:22,395 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:22,439 - INFO - draw_stats_page: 0.0466s
2025-07-21 23:01:22,531 - INFO - draw_background: 0.0010s
2025-07-21 23:01:22,565 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:01:22,663 - INFO - draw_background: 0.0000s
2025-07-21 23:01:22,699 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:01:22,797 - INFO - draw_background: 0.0010s
2025-07-21 23:01:22,833 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:01:22,928 - INFO - draw_background: 0.0010s
2025-07-21 23:01:22,962 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:01:23,061 - INFO - draw_background: 0.0010s
2025-07-21 23:01:23,062 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:23,097 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:01:23,196 - INFO - draw_background: 0.0000s
2025-07-21 23:01:23,236 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:01:23,328 - INFO - draw_background: 0.0000s
2025-07-21 23:01:23,363 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:01:23,461 - INFO - draw_background: 0.0000s
2025-07-21 23:01:23,501 - INFO - draw_stats_page: 0.0402s
2025-07-21 23:01:23,595 - INFO - draw_background: 0.0010s
2025-07-21 23:01:23,631 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:01:23,728 - INFO - draw_background: 0.0009s
2025-07-21 23:01:23,729 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:23,766 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:01:23,797 - INFO - draw_background: 0.0020s
2025-07-21 23:01:23,833 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:01:23,869 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,009 - INFO - draw_stats_page: 0.1417s
2025-07-21 23:01:24,032 - INFO - draw_background: 0.0005s
2025-07-21 23:01:24,068 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:01:24,147 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,188 - INFO - draw_stats_page: 0.0420s
2025-07-21 23:01:24,251 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,252 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:24,295 - INFO - draw_stats_page: 0.0452s
2025-07-21 23:01:24,359 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,405 - INFO - draw_stats_page: 0.0473s
2025-07-21 23:01:24,470 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,509 - INFO - draw_stats_page: 0.0406s
2025-07-21 23:01:24,574 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,615 - INFO - draw_stats_page: 0.0429s
2025-07-21 23:01:24,679 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,728 - INFO - draw_stats_page: 0.0485s
2025-07-21 23:01:24,741 - INFO - draw_background: 0.0010s
2025-07-21 23:01:24,742 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:01:24,791 - INFO - draw_stats_page: 0.0510s
2025-07-21 23:01:31,233 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:01:31,234 - WARNING - Failed to connect to RethinkDB
2025-07-21 23:01:51,417 - ERROR - Error recording game start event in database: Cannot operate on a closed database.
2025-07-21 23:01:51,425 - INFO - Statistics saved to data\stats.json
2025-07-21 23:01:51,425 - INFO - Game start event added to JSON stats (fallback)
2025-07-21 23:01:51,425 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 23:01:51,436 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15080.
2025-07-21 23:01:51,436 - INFO - Started background data loading
2025-07-21 23:01:51,436 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15080.
2025-07-21 23:01:51,436 - INFO - Forced data refresh
2025-07-21 23:01:51,436 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15080.
2025-07-21 23:01:51,436 - INFO - Game start recorded with optimized integration: True
2025-07-21 23:01:51,438 - INFO - Saved 0 items to cache
2025-07-21 23:01:51,438 - INFO - Background data loading completed
2025-07-21 23:01:51,439 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 15080.
2025-07-21 23:01:51,443 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 23:01:51,445 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 6204.
2025-07-21 23:01:51,445 - INFO - Started background data loading
2025-07-21 23:01:51,445 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 6204.
2025-07-21 23:01:51,445 - INFO - Forced data refresh
2025-07-21 23:01:51,446 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 6204.
2025-07-21 23:01:51,446 - INFO - Forced refresh of optimized loader data
2025-07-21 23:01:51,446 - INFO - Saved 0 items to cache
2025-07-21 23:01:51,447 - INFO - Background data loading completed
2025-07-21 23:01:51,447 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 6204.
2025-07-21 23:01:51,450 - INFO - Forced refresh of thread_safe_db data
2025-07-21 23:01:51,451 - INFO - Saved 0 items to persistent cache
2025-07-21 23:01:51,452 - INFO - Cache cleared
2025-07-21 23:01:51,452 - INFO - Cleared all preloader cache data
2025-07-21 23:01:51,456 - INFO - Starting stats data preloading
2025-07-21 23:01:51,456 - INFO - Started stats data preloading
2025-07-21 23:01:51,456 - INFO - Loading data using optimized functions
2025-07-21 23:01:51,456 - INFO - Cleared stats preloader cache
2025-07-21 23:01:51,458 - INFO - Posted refresh_stats event
2025-07-21 23:01:51,458 - INFO - Processed game_started event: True
2025-07-21 23:01:56,457 - INFO - DB Operation: {"timestamp": "2025-07-21 23:01:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:01:56,457 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:01:56,458 - INFO - Preloaded summary data
2025-07-21 23:01:56,458 - INFO - Preloaded game history (2 records)
2025-07-21 23:01:56,458 - INFO - Preloaded wallet data
2025-07-21 23:01:56,458 - INFO - Stats data preloaded successfully in 5.00 seconds
2025-07-21 23:01:56,459 - INFO - Saved 8 items to persistent cache
2025-07-21 23:02:01,462 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:01,462 - INFO - Cache cleared
2025-07-21 23:02:01,462 - INFO - Cleared all preloader cache data
2025-07-21 23:02:01,462 - INFO - Starting stats data preloading
2025-07-21 23:02:01,463 - INFO - Started stats data preloading
2025-07-21 23:02:01,463 - INFO - Loading data using optimized functions
2025-07-21 23:02:01,463 - WARNING - Connection validation failed, creating new connection
2025-07-21 23:02:01,464 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:01,465 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:02:01,465 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:02:01,465 - INFO - Cleared all function caches
2025-07-21 23:02:01,465 - INFO - Generated weekly stats placeholder
2025-07-21 23:02:01,466 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:02:01,466 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:02:01,466 - INFO - Generated summary stats placeholder
2025-07-21 23:02:01,467 - INFO - Preloaded summary data
2025-07-21 23:02:01,469 - INFO - Saved all placeholders to cache
2025-07-21 23:02:01,469 - INFO - Cleared all caches
2025-07-21 23:02:01,469 - INFO - Preloaded game history (2 records)
2025-07-21 23:02:01,471 - INFO - Preloaded wallet data
2025-07-21 23:02:01,471 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 23:02:01,472 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:01,473 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 23:02:01,473 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 23:02:01,705 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:01,705 - INFO - Cache cleared
2025-07-21 23:02:01,705 - INFO - Cleared all preloader cache data
2025-07-21 23:02:01,705 - INFO - Starting stats data preloading
2025-07-21 23:02:01,705 - INFO - Started stats data preloading
2025-07-21 23:02:01,705 - INFO - Loading data using optimized functions
2025-07-21 23:02:01,706 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:02:01,706 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:02:01,706 - INFO - Cleared all function caches
2025-07-21 23:02:01,707 - INFO - Generated weekly stats placeholder
2025-07-21 23:02:01,707 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:02:01,707 - INFO - Generated summary stats placeholder
2025-07-21 23:02:01,710 - INFO - Saved all placeholders to cache
2025-07-21 23:02:01,710 - INFO - Cleared all caches
2025-07-21 23:02:01,773 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:02:01,773 - INFO - Game data: {'winner_name': 'Credit Deduction Game', 'winner_cartella': 0, 'claim_type': 'credit_based_recording', 'game_duration': 300.7031252384186, 'player_count': 4, 'prize_amount': 120, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:02:01', 'stake': 25, 'bet_amount': 25, 'completion_type': 'credit_deduction', 'credits_used': 4, 'share_percentage': 15, 'referee_commission': 24.0, 'game_id': 'retroactive_game_1753128121', 'total_bets': 120, 'timestamp': '2025-07-21T23:02:01.703125'}
2025-07-21 23:02:01,775 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:02:01,777 - INFO - Statistics saved to data\stats.json
2025-07-21 23:02:01,777 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:02:06,706 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:06,706 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:02:06,707 - ERROR - Error getting connection from pool: 
2025-07-21 23:02:06,707 - WARNING - Falling back to direct connection
2025-07-21 23:02:06,707 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:06,707 - INFO - Preloaded summary data
2025-07-21 23:02:06,708 - INFO - Preloaded game history (2 records)
2025-07-21 23:02:06,708 - INFO - Preloaded wallet data
2025-07-21 23:02:06,708 - INFO - Stats data preloaded successfully in 5.00 seconds
2025-07-21 23:02:06,710 - INFO - Saved 8 items to persistent cache
2025-07-21 23:02:11,710 - ERROR - Error getting connection from pool: 
2025-07-21 23:02:11,710 - WARNING - Falling back to direct connection
2025-07-21 23:02:11,710 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:14,979 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:02:16,712 - ERROR - Error getting connection from pool: 
2025-07-21 23:02:16,712 - WARNING - Falling back to direct connection
2025-07-21 23:02:16,713 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:16", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:16,939 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:02:16,940 - INFO - Game data: {'winner_name': 'Credit-Based Game', 'winner_cartella': None, 'claim_type': 'credit_based', 'game_duration': 0, 'player_count': 4, 'prize_amount': 96, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:02:16', 'stake': 30, 'bet_amount': 30, 'completion_type': 'credit_based', 'timestamp': '2025-07-21T23:02:16.716243'}
2025-07-21 23:02:16,963 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:02:16,983 - INFO - Statistics saved to data\stats.json
2025-07-21 23:02:16,984 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:02:35,317 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:02:35,317 - WARNING - Failed to connect to RethinkDB
2025-07-21 23:02:35,639 - ERROR - Error recording game start event in database: Cannot operate on a closed database.
2025-07-21 23:02:35,642 - INFO - Statistics saved to data\stats.json
2025-07-21 23:02:35,642 - INFO - Game start event added to JSON stats (fallback)
2025-07-21 23:02:35,644 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 23:02:35,647 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 13536.
2025-07-21 23:02:35,647 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 13536.
2025-07-21 23:02:35,647 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 13536.
2025-07-21 23:02:35,648 - INFO - Started background data loading
2025-07-21 23:02:35,648 - INFO - Forced data refresh
2025-07-21 23:02:35,648 - INFO - Game start recorded with optimized integration: True
2025-07-21 23:02:35,648 - INFO - Saved 0 items to cache
2025-07-21 23:02:35,649 - INFO - Background data loading completed
2025-07-21 23:02:35,649 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 13536.
2025-07-21 23:02:35,659 - INFO - Posted refresh_stats event to trigger UI update
2025-07-21 23:02:35,664 - ERROR - Error loading weekly stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 7384.
2025-07-21 23:02:35,664 - ERROR - Error loading summary data: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 7384.
2025-07-21 23:02:35,665 - ERROR - Error loading game history: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 7384.
2025-07-21 23:02:35,666 - INFO - Started background data loading
2025-07-21 23:02:35,667 - INFO - Forced data refresh
2025-07-21 23:02:35,669 - INFO - Forced refresh of optimized loader data
2025-07-21 23:02:35,670 - INFO - Saved 0 items to cache
2025-07-21 23:02:35,670 - INFO - Background data loading completed
2025-07-21 23:02:35,673 - ERROR - Error closing database connection: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11688 and this is thread id 7384.
2025-07-21 23:02:35,686 - INFO - Forced refresh of thread_safe_db data
2025-07-21 23:02:35,687 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:35,687 - INFO - Cache cleared
2025-07-21 23:02:35,687 - INFO - Cleared all preloader cache data
2025-07-21 23:02:35,688 - INFO - Starting stats data preloading
2025-07-21 23:02:35,688 - INFO - Loading data using optimized functions
2025-07-21 23:02:35,688 - INFO - Started stats data preloading
2025-07-21 23:02:35,688 - INFO - Cleared stats preloader cache
2025-07-21 23:02:35,689 - INFO - Posted refresh_stats event
2025-07-21 23:02:35,689 - INFO - Processed game_started event: True
2025-07-21 23:02:40,688 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:40", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:40,690 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:02:40,694 - INFO - Preloaded summary data
2025-07-21 23:02:40,696 - INFO - Preloaded game history (2 records)
2025-07-21 23:02:40,697 - INFO - Preloaded wallet data
2025-07-21 23:02:40,697 - INFO - Stats data preloaded successfully in 5.01 seconds
2025-07-21 23:02:40,699 - INFO - Saved 8 items to persistent cache
2025-07-21 23:02:49,715 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:49,715 - INFO - Cache cleared
2025-07-21 23:02:49,715 - INFO - Cleared all preloader cache data
2025-07-21 23:02:49,716 - INFO - Starting stats data preloading
2025-07-21 23:02:49,716 - INFO - Loading data using optimized functions
2025-07-21 23:02:49,716 - WARNING - Connection validation failed, creating new connection
2025-07-21 23:02:49,716 - INFO - Started stats data preloading
2025-07-21 23:02:49,717 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:49,718 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:02:49,718 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:02:49,718 - INFO - Cleared all function caches
2025-07-21 23:02:49,718 - INFO - Generated weekly stats placeholder
2025-07-21 23:02:49,719 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:02:49,719 - INFO - Preloaded weekly stats for 7 days
2025-07-21 23:02:49,719 - INFO - Generated summary stats placeholder
2025-07-21 23:02:49,721 - INFO - Preloaded summary data
2025-07-21 23:02:49,722 - INFO - Saved all placeholders to cache
2025-07-21 23:02:49,722 - INFO - Cleared all caches
2025-07-21 23:02:49,722 - INFO - Preloaded game history (2 records)
2025-07-21 23:02:49,723 - INFO - Preloaded wallet data
2025-07-21 23:02:49,723 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-21 23:02:49,724 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:49,726 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-21 23:02:49,726 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-21 23:02:49,932 - INFO - Saved 0 items to persistent cache
2025-07-21 23:02:49,932 - INFO - Cache cleared
2025-07-21 23:02:49,932 - INFO - Cleared all preloader cache data
2025-07-21 23:02:49,933 - INFO - Starting stats data preloading
2025-07-21 23:02:49,933 - INFO - Started stats data preloading
2025-07-21 23:02:49,933 - INFO - Loading data using optimized functions
2025-07-21 23:02:49,934 - INFO - Saved cache metadata with 0 entries
2025-07-21 23:02:49,934 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-21 23:02:49,934 - INFO - Cleared all function caches
2025-07-21 23:02:49,934 - INFO - Generated weekly stats placeholder
2025-07-21 23:02:49,934 - INFO - Generated game history placeholder with 10 records
2025-07-21 23:02:49,936 - INFO - Generated summary stats placeholder
2025-07-21 23:02:49,938 - INFO - Saved all placeholders to cache
2025-07-21 23:02:49,938 - INFO - Cleared all caches
2025-07-21 23:02:49,985 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:02:49,986 - INFO - Game data: {'winner_name': 'Credit Deduction Game', 'winner_cartella': 0, 'claim_type': 'credit_based_recording', 'game_duration': 300.90253829956055, 'player_count': 4, 'prize_amount': 120, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:02:49', 'stake': 25, 'bet_amount': 25, 'completion_type': 'credit_deduction', 'credits_used': 4, 'share_percentage': 15, 'referee_commission': 24.0, 'game_id': 'retroactive_game_1753128169', 'total_bets': 120, 'timestamp': '2025-07-21T23:02:49.903045'}
2025-07-21 23:02:49,986 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:02:49,988 - INFO - Statistics saved to data\stats.json
2025-07-21 23:02:49,988 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:02:54,939 - ERROR - Error getting connection from pool: 
2025-07-21 23:02:54,939 - WARNING - Falling back to direct connection
2025-07-21 23:02:54,939 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:02:59,935 - ERROR - Error in connection pool: Timed out waiting for database connection
2025-07-21 23:02:59,935 - ERROR - Error getting optimized weekly stats: Timed out waiting for database connection
2025-07-21 23:02:59,935 - INFO - Preloaded weekly stats for 0 days
2025-07-21 23:02:59,942 - ERROR - Error getting connection from pool: 
2025-07-21 23:02:59,942 - WARNING - Falling back to direct connection
2025-07-21 23:02:59,942 - INFO - DB Operation: {"timestamp": "2025-07-21 23:02:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:04,943 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:04,944 - WARNING - Falling back to direct connection
2025-07-21 23:03:04,944 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:09,937 - ERROR - Error in connection pool: Timed out waiting for database connection
2025-07-21 23:03:09,937 - ERROR - Error preloading summary data: Timed out waiting for database connection
2025-07-21 23:03:09,946 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:09,946 - WARNING - Falling back to direct connection
2025-07-21 23:03:09,946 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:10,147 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 23:03:10,147 - INFO - Game data: {'winner_name': 'Credit-Based Game', 'winner_cartella': None, 'claim_type': 'credit_based', 'game_duration': 0, 'player_count': 4, 'prize_amount': 96, 'commission_percentage': 20.0, 'called_numbers': [], 'is_demo_mode': False, 'date_time': '2025-07-21 23:03:09', 'stake': 30, 'bet_amount': 30, 'completion_type': 'credit_based', 'timestamp': '2025-07-21T23:03:09.950993'}
2025-07-21 23:03:10,149 - ERROR - Error recording game statistics in database: Cannot operate on a closed database.
2025-07-21 23:03:10,151 - INFO - Statistics saved to data\stats.json
2025-07-21 23:03:10,151 - INFO - Game statistics updated in JSON stats (fallback)
2025-07-21 23:03:12,834 - INFO - draw_background: 0.0319s
2025-07-21 23:03:12,922 - INFO - draw_stats_page: 0.1207s
2025-07-21 23:03:12,935 - INFO - draw_background: 0.0000s
2025-07-21 23:03:12,972 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:03:13,046 - INFO - draw_background: 0.0010s
2025-07-21 23:03:13,128 - INFO - draw_stats_page: 0.0833s
2025-07-21 23:03:13,147 - INFO - draw_background: 0.0010s
2025-07-21 23:03:13,183 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:13,253 - INFO - draw_background: 0.0015s
2025-07-21 23:03:13,254 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:13,292 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:03:13,361 - INFO - draw_background: 0.0010s
2025-07-21 23:03:13,395 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:13,464 - INFO - draw_background: 0.0009s
2025-07-21 23:03:13,498 - INFO - draw_stats_page: 0.0342s
2025-07-21 23:03:13,568 - INFO - draw_background: 0.0010s
2025-07-21 23:03:13,606 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:03:13,711 - INFO - draw_background: 0.0000s
2025-07-21 23:03:13,746 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:13,846 - INFO - draw_background: 0.0010s
2025-07-21 23:03:13,847 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:13,882 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:13,981 - INFO - draw_background: 0.0009s
2025-07-21 23:03:14,015 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:03:14,114 - INFO - draw_background: 0.0011s
2025-07-21 23:03:14,147 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:14,247 - INFO - draw_background: 0.0009s
2025-07-21 23:03:14,284 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:03:14,302 - INFO - Admin button added to stats page
2025-07-21 23:03:14,382 - INFO - draw_background: 0.0007s
2025-07-21 23:03:14,415 - INFO - draw_stats_page: 0.0337s
2025-07-21 23:03:14,514 - INFO - draw_background: 0.0009s
2025-07-21 23:03:14,515 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:14,551 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:03:14,650 - INFO - draw_background: 0.0009s
2025-07-21 23:03:14,684 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:03:14,782 - INFO - draw_background: 0.0005s
2025-07-21 23:03:14,817 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:14,916 - INFO - draw_background: 0.0010s
2025-07-21 23:03:14,950 - INFO - draw_stats_page: 0.0341s
2025-07-21 23:03:15,048 - INFO - draw_background: 0.0005s
2025-07-21 23:03:15,081 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:03:15,180 - INFO - draw_background: 0.0000s
2025-07-21 23:03:15,183 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:15,215 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:03:15,315 - INFO - draw_background: 0.0010s
2025-07-21 23:03:15,349 - INFO - draw_stats_page: 0.0340s
2025-07-21 23:03:15,449 - INFO - draw_background: 0.0010s
2025-07-21 23:03:15,482 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:03:15,580 - INFO - draw_background: 0.0000s
2025-07-21 23:03:15,615 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:03:15,714 - INFO - draw_background: 0.0000s
2025-07-21 23:03:15,747 - INFO - draw_stats_page: 0.0325s
2025-07-21 23:03:15,849 - INFO - draw_background: 0.0000s
2025-07-21 23:03:15,850 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:15,883 - INFO - draw_stats_page: 0.0341s
2025-07-21 23:03:15,983 - INFO - draw_background: 0.0012s
2025-07-21 23:03:16,017 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:03:16,116 - INFO - draw_background: 0.0009s
2025-07-21 23:03:16,148 - INFO - draw_stats_page: 0.0337s
2025-07-21 23:03:16,247 - INFO - draw_background: 0.0011s
2025-07-21 23:03:16,280 - INFO - draw_stats_page: 0.0342s
2025-07-21 23:03:16,380 - INFO - draw_background: 0.0009s
2025-07-21 23:03:16,412 - INFO - draw_stats_page: 0.0330s
2025-07-21 23:03:16,515 - INFO - draw_background: 0.0010s
2025-07-21 23:03:16,516 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:16,555 - INFO - draw_stats_page: 0.0417s
2025-07-21 23:03:16,648 - INFO - draw_background: 0.0010s
2025-07-21 23:03:16,679 - INFO - draw_stats_page: 0.0319s
2025-07-21 23:03:16,780 - INFO - draw_background: 0.0000s
2025-07-21 23:03:16,811 - INFO - draw_stats_page: 0.0305s
2025-07-21 23:03:16,913 - INFO - draw_background: 0.0000s
2025-07-21 23:03:16,957 - INFO - draw_stats_page: 0.0446s
2025-07-21 23:03:17,047 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,078 - INFO - draw_stats_page: 0.0319s
2025-07-21 23:03:17,179 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,180 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:17,250 - INFO - draw_stats_page: 0.0713s
2025-07-21 23:03:17,340 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,377 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:17,474 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,508 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:17,607 - INFO - draw_background: 0.0009s
2025-07-21 23:03:17,640 - INFO - draw_stats_page: 0.0340s
2025-07-21 23:03:17,739 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,773 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:03:17,872 - INFO - draw_background: 0.0010s
2025-07-21 23:03:17,873 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:17,878 - INFO - draw_stats_page: 0.0075s
2025-07-21 23:03:18,006 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,009 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:18,139 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,143 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:18,270 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,273 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:18,403 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,407 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:03:18,536 - INFO - draw_background: 0.0000s
2025-07-21 23:03:18,537 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:18,540 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:03:18,670 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,674 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:03:18,801 - INFO - draw_background: 0.0010s
2025-07-21 23:03:18,805 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:03:18,933 - INFO - draw_background: 0.0005s
2025-07-21 23:03:18,937 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:19,059 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:03:19,067 - INFO - draw_background: 0.0009s
2025-07-21 23:03:19,070 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:03:19,199 - INFO - draw_background: 0.0010s
2025-07-21 23:03:19,200 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:19,204 - INFO - draw_stats_page: 0.0055s
2025-07-21 23:03:19,333 - INFO - draw_background: 0.0010s
2025-07-21 23:03:19,336 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:19,400 - INFO - draw_background: 0.0010s
2025-07-21 23:03:19,403 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:19,532 - INFO - draw_background: 0.0016s
2025-07-21 23:03:19,537 - INFO - draw_stats_page: 0.0071s
2025-07-21 23:03:19,665 - INFO - draw_background: 0.0010s
2025-07-21 23:03:19,669 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:03:19,797 - INFO - draw_background: 0.0011s
2025-07-21 23:03:19,799 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:19,804 - INFO - draw_stats_page: 0.0072s
2025-07-21 23:03:19,932 - INFO - draw_background: 0.0011s
2025-07-21 23:03:19,937 - INFO - draw_stats_page: 0.0061s
2025-07-21 23:03:19,938 - ERROR - Error in connection pool: Timed out waiting for database connection
2025-07-21 23:03:19,940 - ERROR - Error preloading game history: Timed out waiting for database connection
2025-07-21 23:03:19,997 - INFO - draw_background: 0.0010s
2025-07-21 23:03:20,002 - INFO - draw_stats_page: 0.0060s
2025-07-21 23:03:20,130 - INFO - draw_background: 0.0011s
2025-07-21 23:03:20,135 - INFO - draw_stats_page: 0.0061s
2025-07-21 23:03:20,196 - INFO - draw_background: 0.0010s
2025-07-21 23:03:20,200 - INFO - draw_stats_page: 0.0052s
2025-07-21 23:03:20,329 - INFO - draw_background: 0.0005s
2025-07-21 23:03:20,330 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:20,334 - INFO - draw_stats_page: 0.0059s
2025-07-21 23:03:20,464 - INFO - draw_background: 0.0014s
2025-07-21 23:03:20,470 - INFO - draw_stats_page: 0.0068s
2025-07-21 23:03:20,530 - INFO - draw_background: 0.0012s
2025-07-21 23:03:20,534 - INFO - draw_stats_page: 0.0055s
2025-07-21 23:03:20,662 - INFO - draw_background: 0.0015s
2025-07-21 23:03:20,666 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:03:20,794 - INFO - draw_background: 0.0010s
2025-07-21 23:03:20,799 - INFO - draw_stats_page: 0.0061s
2025-07-21 23:03:20,928 - INFO - draw_background: 0.0010s
2025-07-21 23:03:20,928 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:20,932 - INFO - draw_stats_page: 0.0056s
2025-07-21 23:03:20,995 - INFO - draw_background: 0.0013s
2025-07-21 23:03:20,998 - INFO - draw_stats_page: 0.0043s
2025-07-21 23:03:21,128 - INFO - draw_background: 0.0015s
2025-07-21 23:03:21,132 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:03:21,194 - INFO - draw_background: 0.0011s
2025-07-21 23:03:21,197 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:21,327 - INFO - draw_background: 0.0010s
2025-07-21 23:03:21,330 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:03:21,458 - INFO - draw_background: 0.0009s
2025-07-21 23:03:21,459 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:21,463 - INFO - draw_stats_page: 0.0060s
2025-07-21 23:03:21,592 - INFO - draw_background: 0.0015s
2025-07-21 23:03:21,596 - INFO - draw_stats_page: 0.0055s
2025-07-21 23:03:21,724 - INFO - draw_background: 0.0009s
2025-07-21 23:03:21,727 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:21,857 - INFO - draw_background: 0.0010s
2025-07-21 23:03:21,860 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:21,990 - INFO - draw_background: 0.0000s
2025-07-21 23:03:21,993 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:03:22,123 - INFO - draw_background: 0.0005s
2025-07-21 23:03:22,124 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:22,128 - INFO - draw_stats_page: 0.0056s
2025-07-21 23:03:22,256 - INFO - draw_background: 0.0009s
2025-07-21 23:03:22,260 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:03:22,390 - INFO - draw_background: 0.0009s
2025-07-21 23:03:22,393 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:22,524 - INFO - draw_background: 0.0010s
2025-07-21 23:03:22,528 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:22,657 - INFO - draw_background: 0.0010s
2025-07-21 23:03:22,660 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:22,790 - INFO - draw_background: 0.0009s
2025-07-21 23:03:22,791 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:22,794 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:03:22,922 - INFO - draw_background: 0.0010s
2025-07-21 23:03:22,925 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:03:22,988 - INFO - draw_background: 0.0005s
2025-07-21 23:03:22,992 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:23,122 - INFO - draw_background: 0.0009s
2025-07-21 23:03:23,125 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:23,255 - INFO - draw_background: 0.0005s
2025-07-21 23:03:23,258 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:03:23,323 - INFO - draw_background: 0.0010s
2025-07-21 23:03:23,324 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:23,328 - INFO - draw_stats_page: 0.0056s
2025-07-21 23:03:23,456 - INFO - draw_background: 0.0009s
2025-07-21 23:03:23,461 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:23,590 - INFO - draw_background: 0.0010s
2025-07-21 23:03:23,595 - INFO - draw_stats_page: 0.0042s
2025-07-21 23:03:23,722 - INFO - draw_background: 0.0009s
2025-07-21 23:03:23,726 - INFO - draw_stats_page: 0.0048s
2025-07-21 23:03:23,858 - INFO - load_statistics: 0.0010s
2025-07-21 23:03:23,859 - INFO - draw_background: 0.0011s
2025-07-21 23:03:23,860 - INFO - Preload thread already running
2025-07-21 23:03:23,863 - INFO - draw_stats_page: 0.0058s
2025-07-21 23:03:23,895 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:23,989 - INFO - draw_background: 0.0010s
2025-07-21 23:03:23,992 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:24,123 - INFO - draw_background: 0.0010s
2025-07-21 23:03:24,127 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:24,257 - INFO - draw_background: 0.0010s
2025-07-21 23:03:24,261 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:24,389 - INFO - draw_background: 0.0009s
2025-07-21 23:03:24,393 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:24,521 - INFO - draw_background: 0.0009s
2025-07-21 23:03:24,525 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:24,526 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:24,655 - INFO - draw_background: 0.0010s
2025-07-21 23:03:24,658 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:24,786 - INFO - draw_background: 0.0009s
2025-07-21 23:03:24,789 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:24,919 - INFO - draw_background: 0.0000s
2025-07-21 23:03:24,922 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:25,053 - INFO - draw_background: 0.0000s
2025-07-21 23:03:25,056 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:25,184 - INFO - draw_background: 0.0000s
2025-07-21 23:03:25,187 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:25,188 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:25,318 - INFO - draw_background: 0.0000s
2025-07-21 23:03:25,321 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:25,451 - INFO - draw_background: 0.0010s
2025-07-21 23:03:25,454 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:03:25,584 - INFO - draw_background: 0.0010s
2025-07-21 23:03:25,587 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:25,718 - INFO - draw_background: 0.0000s
2025-07-21 23:03:25,722 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:25,852 - INFO - draw_background: 0.0010s
2025-07-21 23:03:25,855 - INFO - draw_stats_page: 0.0036s
2025-07-21 23:03:25,855 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:25,983 - INFO - draw_background: 0.0000s
2025-07-21 23:03:25,987 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:26,118 - INFO - draw_background: 0.0009s
2025-07-21 23:03:26,120 - INFO - draw_stats_page: 0.0034s
2025-07-21 23:03:26,252 - INFO - draw_background: 0.0000s
2025-07-21 23:03:26,255 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:03:26,384 - INFO - draw_background: 0.0010s
2025-07-21 23:03:26,387 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:26,516 - INFO - draw_background: 0.0010s
2025-07-21 23:03:26,519 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:03:26,520 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:26,651 - INFO - draw_background: 0.0000s
2025-07-21 23:03:26,654 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:26,784 - INFO - draw_background: 0.0010s
2025-07-21 23:03:26,788 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:26,918 - INFO - draw_background: 0.0000s
2025-07-21 23:03:26,921 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:27,051 - INFO - draw_background: 0.0010s
2025-07-21 23:03:27,054 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:27,184 - INFO - draw_background: 0.0010s
2025-07-21 23:03:27,188 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:03:27,189 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:27,318 - INFO - draw_background: 0.0010s
2025-07-21 23:03:27,321 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:03:27,450 - INFO - draw_background: 0.0005s
2025-07-21 23:03:27,454 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:03:27,583 - INFO - draw_background: 0.0011s
2025-07-21 23:03:27,586 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:27,716 - INFO - draw_background: 0.0000s
2025-07-21 23:03:27,719 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:03:27,848 - INFO - draw_background: 0.0000s
2025-07-21 23:03:27,853 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:03:27,854 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:27,984 - INFO - draw_background: 0.0010s
2025-07-21 23:03:28,025 - INFO - draw_stats_page: 0.0422s
2025-07-21 23:03:28,117 - INFO - draw_background: 0.0010s
2025-07-21 23:03:28,153 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:28,249 - INFO - draw_background: 0.0009s
2025-07-21 23:03:28,285 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:28,382 - INFO - draw_background: 0.0006s
2025-07-21 23:03:28,419 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:28,514 - INFO - draw_background: 0.0005s
2025-07-21 23:03:28,552 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:03:28,553 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:28,648 - INFO - draw_background: 0.0010s
2025-07-21 23:03:28,683 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:28,782 - INFO - draw_background: 0.0005s
2025-07-21 23:03:28,820 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:03:28,915 - INFO - draw_background: 0.0009s
2025-07-21 23:03:28,950 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:29,047 - INFO - draw_background: 0.0010s
2025-07-21 23:03:29,081 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:03:29,180 - INFO - draw_background: 0.0000s
2025-07-21 23:03:29,215 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:03:29,216 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:29,316 - INFO - draw_background: 0.0000s
2025-07-21 23:03:29,353 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:03:29,448 - INFO - draw_background: 0.0000s
2025-07-21 23:03:29,479 - INFO - draw_stats_page: 0.0327s
2025-07-21 23:03:29,580 - INFO - draw_background: 0.0000s
2025-07-21 23:03:29,620 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:03:29,715 - INFO - draw_background: 0.0009s
2025-07-21 23:03:29,749 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:29,847 - INFO - draw_background: 0.0010s
2025-07-21 23:03:29,882 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:03:29,883 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:29,941 - ERROR - Error in connection pool: Timed out waiting for database connection
2025-07-21 23:03:29,941 - ERROR - Error preloading wallet data: Timed out waiting for database connection
2025-07-21 23:03:29,941 - INFO - Stats data preloaded successfully in 40.01 seconds
2025-07-21 23:03:29,942 - INFO - Saved 1 items to persistent cache
2025-07-21 23:03:29,979 - INFO - draw_background: 0.0010s
2025-07-21 23:03:30,013 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:03:30,113 - INFO - draw_background: 0.0005s
2025-07-21 23:03:30,147 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:30,245 - INFO - draw_background: 0.0011s
2025-07-21 23:03:30,395 - INFO - draw_stats_page: 0.1513s
2025-07-21 23:03:30,417 - INFO - draw_background: 0.0000s
2025-07-21 23:03:30,455 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:03:30,549 - INFO - draw_background: 0.0000s
2025-07-21 23:03:30,584 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:30,585 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:30,683 - INFO - draw_background: 0.0000s
2025-07-21 23:03:30,718 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:30,815 - INFO - draw_background: 0.0000s
2025-07-21 23:03:30,850 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:03:30,948 - INFO - draw_background: 0.0010s
2025-07-21 23:03:30,982 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:31,083 - INFO - draw_background: 0.0000s
2025-07-21 23:03:31,117 - INFO - draw_stats_page: 0.0351s
2025-07-21 23:03:31,216 - INFO - draw_background: 0.0010s
2025-07-21 23:03:31,250 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:31,252 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:31,350 - INFO - draw_background: 0.0010s
2025-07-21 23:03:31,389 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:03:31,482 - INFO - draw_background: 0.0006s
2025-07-21 23:03:31,517 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:31,615 - INFO - draw_background: 0.0005s
2025-07-21 23:03:31,650 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:31,749 - INFO - draw_background: 0.0010s
2025-07-21 23:03:31,783 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:31,882 - INFO - draw_background: 0.0010s
2025-07-21 23:03:31,917 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:31,918 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:32,014 - INFO - draw_background: 0.0010s
2025-07-21 23:03:32,048 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:03:32,148 - INFO - draw_background: 0.0009s
2025-07-21 23:03:32,185 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:32,280 - INFO - draw_background: 0.0000s
2025-07-21 23:03:32,319 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:32,413 - INFO - draw_background: 0.0005s
2025-07-21 23:03:32,451 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:03:32,546 - INFO - draw_background: 0.0010s
2025-07-21 23:03:32,580 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:03:32,581 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:32,679 - INFO - draw_background: 0.0009s
2025-07-21 23:03:32,715 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:32,812 - INFO - draw_background: 0.0010s
2025-07-21 23:03:32,846 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:03:32,946 - INFO - draw_background: 0.0010s
2025-07-21 23:03:32,982 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:03:33,077 - INFO - draw_background: 0.0000s
2025-07-21 23:03:33,113 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:33,211 - INFO - draw_background: 0.0010s
2025-07-21 23:03:33,246 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:03:33,246 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:33,344 - INFO - draw_background: 0.0010s
2025-07-21 23:03:33,378 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:03:33,477 - INFO - draw_background: 0.0010s
2025-07-21 23:03:33,513 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:33,609 - INFO - draw_background: 0.0010s
2025-07-21 23:03:33,645 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:03:33,743 - INFO - draw_background: 0.0005s
2025-07-21 23:03:33,779 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:33,875 - INFO - draw_background: 0.0000s
2025-07-21 23:03:33,909 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:33,910 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:34,008 - INFO - draw_background: 0.0010s
2025-07-21 23:03:34,043 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:03:34,140 - INFO - draw_background: 0.0010s
2025-07-21 23:03:34,175 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:34,274 - INFO - draw_background: 0.0000s
2025-07-21 23:03:34,310 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:34,407 - INFO - draw_background: 0.0000s
2025-07-21 23:03:34,443 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:03:34,541 - INFO - draw_background: 0.0010s
2025-07-21 23:03:34,582 - INFO - draw_stats_page: 0.0426s
2025-07-21 23:03:34,583 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:34,676 - INFO - draw_background: 0.0010s
2025-07-21 23:03:34,716 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:03:34,808 - INFO - draw_background: 0.0010s
2025-07-21 23:03:34,844 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:03:34,941 - INFO - draw_background: 0.0009s
2025-07-21 23:03:34,942 - ERROR - Error in connection pool: Timed out waiting for database connection
2025-07-21 23:03:34,942 - ERROR - Error using connection pool: Timed out waiting for database connection
2025-07-21 23:03:34,942 - WARNING - Falling back to direct connection
2025-07-21 23:03:34,977 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:35,076 - INFO - draw_background: 0.0009s
2025-07-21 23:03:35,111 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:03:35,208 - INFO - draw_background: 0.0005s
2025-07-21 23:03:35,245 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:03:35,246 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:35,343 - INFO - draw_background: 0.0006s
2025-07-21 23:03:35,377 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:35,477 - INFO - draw_background: 0.0000s
2025-07-21 23:03:35,514 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:03:35,608 - INFO - draw_background: 0.0000s
2025-07-21 23:03:35,645 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:03:35,743 - INFO - draw_background: 0.0005s
2025-07-21 23:03:35,773 - INFO - draw_stats_page: 0.0313s
2025-07-21 23:03:35,875 - INFO - draw_background: 0.0009s
2025-07-21 23:03:35,911 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:35,912 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:36,009 - INFO - draw_background: 0.0000s
2025-07-21 23:03:36,048 - INFO - draw_stats_page: 0.0402s
2025-07-21 23:03:36,142 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,175 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:36,276 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,313 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:36,409 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,444 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:36,543 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,577 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:36,578 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:36,678 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,713 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:36,813 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,847 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:03:36,946 - INFO - draw_background: 0.0010s
2025-07-21 23:03:36,982 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:03:37,080 - INFO - draw_background: 0.0010s
2025-07-21 23:03:37,116 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:03:37,212 - INFO - draw_background: 0.0000s
2025-07-21 23:03:37,248 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:03:37,249 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:37,345 - INFO - draw_background: 0.0009s
2025-07-21 23:03:37,379 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:03:37,479 - INFO - draw_background: 0.0009s
2025-07-21 23:03:37,519 - INFO - draw_stats_page: 0.0413s
2025-07-21 23:03:37,610 - INFO - draw_background: 0.0000s
2025-07-21 23:03:37,649 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:03:37,746 - INFO - draw_background: 0.0009s
2025-07-21 23:03:37,785 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:03:37,879 - INFO - draw_background: 0.0010s
2025-07-21 23:03:37,915 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:03:37,916 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:38,012 - INFO - draw_background: 0.0010s
2025-07-21 23:03:38,146 - INFO - draw_stats_page: 0.1355s
2025-07-21 23:03:38,173 - INFO - draw_background: 0.0005s
2025-07-21 23:03:38,209 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:38,304 - INFO - draw_background: 0.0009s
2025-07-21 23:03:38,339 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:03:38,438 - INFO - draw_background: 0.0010s
2025-07-21 23:03:38,473 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:03:38,570 - INFO - draw_background: 0.0009s
2025-07-21 23:03:38,610 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:03:38,610 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:38,703 - INFO - draw_background: 0.0006s
2025-07-21 23:03:38,739 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:38,837 - INFO - draw_background: 0.0010s
2025-07-21 23:03:38,872 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:03:38,969 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,006 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:03:39,103 - INFO - draw_background: 0.0005s
2025-07-21 23:03:39,138 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:03:39,235 - INFO - draw_background: 0.0009s
2025-07-21 23:03:39,267 - INFO - draw_stats_page: 0.0317s
2025-07-21 23:03:39,267 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:39,367 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,404 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:03:39,412 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:03:39,413 - WARNING - Failed to connect to RethinkDB
2025-07-21 23:03:39,500 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,535 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:39,634 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,670 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:39,767 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,805 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:03:39,899 - INFO - draw_background: 0.0010s
2025-07-21 23:03:39,931 - INFO - draw_stats_page: 0.0322s
2025-07-21 23:03:39,932 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:39,943 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:39,943 - WARNING - Falling back to direct connection
2025-07-21 23:03:39,944 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:40,032 - INFO - draw_background: 0.0006s
2025-07-21 23:03:40,065 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:03:40,166 - INFO - draw_background: 0.0009s
2025-07-21 23:03:40,203 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:40,299 - INFO - draw_background: 0.0009s
2025-07-21 23:03:40,335 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:03:40,433 - INFO - draw_background: 0.0005s
2025-07-21 23:03:40,467 - INFO - draw_stats_page: 0.0351s
2025-07-21 23:03:40,567 - INFO - draw_background: 0.0010s
2025-07-21 23:03:40,602 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:03:40,603 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:40,700 - INFO - draw_background: 0.0000s
2025-07-21 23:03:40,736 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:03:40,835 - INFO - draw_background: 0.0010s
2025-07-21 23:03:40,872 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:03:40,968 - INFO - draw_background: 0.0010s
2025-07-21 23:03:41,004 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:03:41,099 - INFO - draw_background: 0.0010s
2025-07-21 23:03:41,135 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:03:41,231 - INFO - draw_background: 0.0005s
2025-07-21 23:03:41,269 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:41,270 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:41,365 - INFO - draw_background: 0.0013s
2025-07-21 23:03:41,403 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:03:41,497 - INFO - draw_background: 0.0009s
2025-07-21 23:03:41,528 - INFO - draw_stats_page: 0.0324s
2025-07-21 23:03:41,630 - INFO - draw_background: 0.0010s
2025-07-21 23:03:41,669 - INFO - draw_stats_page: 0.0403s
2025-07-21 23:03:41,762 - INFO - draw_background: 0.0005s
2025-07-21 23:03:41,797 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:03:41,895 - INFO - draw_background: 0.0000s
2025-07-21 23:03:41,929 - INFO - draw_stats_page: 0.0349s
2025-07-21 23:03:41,930 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:42,028 - INFO - draw_background: 0.0010s
2025-07-21 23:03:42,067 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:03:42,161 - INFO - draw_background: 0.0010s
2025-07-21 23:03:42,196 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:03:42,295 - INFO - draw_background: 0.0010s
2025-07-21 23:03:42,328 - INFO - draw_stats_page: 0.0338s
2025-07-21 23:03:42,427 - INFO - draw_background: 0.0009s
2025-07-21 23:03:42,465 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:42,559 - INFO - draw_background: 0.0000s
2025-07-21 23:03:42,590 - INFO - draw_stats_page: 0.0322s
2025-07-21 23:03:42,593 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:42,690 - INFO - draw_background: 0.0000s
2025-07-21 23:03:42,732 - INFO - draw_stats_page: 0.0418s
2025-07-21 23:03:42,823 - INFO - draw_background: 0.0000s
2025-07-21 23:03:42,856 - INFO - draw_stats_page: 0.0335s
2025-07-21 23:03:42,956 - INFO - draw_background: 0.0010s
2025-07-21 23:03:42,994 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:03:43,089 - INFO - draw_background: 0.0005s
2025-07-21 23:03:43,128 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:03:43,222 - INFO - draw_background: 0.0010s
2025-07-21 23:03:43,257 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:03:43,258 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:43,355 - INFO - draw_background: 0.0010s
2025-07-21 23:03:43,389 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:03:43,487 - INFO - draw_background: 0.0000s
2025-07-21 23:03:43,521 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:03:43,620 - INFO - draw_background: 0.0010s
2025-07-21 23:03:43,656 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:03:43,753 - INFO - draw_background: 0.0010s
2025-07-21 23:03:43,789 - INFO - draw_stats_page: 0.0374s
2025-07-21 23:03:43,885 - INFO - draw_background: 0.0000s
2025-07-21 23:03:43,922 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:03:43,924 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:44,018 - INFO - draw_background: 0.0010s
2025-07-21 23:03:44,055 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:03:44,153 - INFO - draw_background: 0.0006s
2025-07-21 23:03:44,187 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:03:44,286 - INFO - draw_background: 0.0010s
2025-07-21 23:03:44,320 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:03:44,420 - INFO - draw_background: 0.0010s
2025-07-21 23:03:44,454 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:44,553 - INFO - draw_background: 0.0000s
2025-07-21 23:03:44,590 - INFO - draw_stats_page: 0.0374s
2025-07-21 23:03:44,592 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:44,688 - INFO - draw_background: 0.0010s
2025-07-21 23:03:44,729 - INFO - draw_stats_page: 0.0425s
2025-07-21 23:03:44,821 - INFO - draw_background: 0.0000s
2025-07-21 23:03:44,854 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:03:44,950 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:44,950 - WARNING - Falling back to direct connection
2025-07-21 23:03:44,950 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:44", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:44,953 - INFO - draw_background: 0.0005s
2025-07-21 23:03:44,986 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:03:45,089 - INFO - draw_background: 0.0000s
2025-07-21 23:03:45,124 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:03:45,222 - INFO - draw_background: 0.0009s
2025-07-21 23:03:45,277 - INFO - draw_stats_page: 0.0565s
2025-07-21 23:03:45,280 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:45,373 - INFO - draw_background: 0.0010s
2025-07-21 23:03:45,449 - INFO - draw_stats_page: 0.0773s
2025-07-21 23:03:45,472 - INFO - draw_background: 0.0010s
2025-07-21 23:03:45,512 - INFO - draw_stats_page: 0.0402s
2025-07-21 23:03:45,606 - INFO - draw_background: 0.0009s
2025-07-21 23:03:45,646 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:03:45,738 - INFO - draw_background: 0.0000s
2025-07-21 23:03:45,771 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:03:45,872 - INFO - draw_background: 0.0010s
2025-07-21 23:03:45,958 - INFO - draw_stats_page: 0.0871s
2025-07-21 23:03:45,959 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:45,979 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,016 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:46,113 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,146 - INFO - draw_stats_page: 0.0340s
2025-07-21 23:03:46,245 - INFO - draw_background: 0.0005s
2025-07-21 23:03:46,280 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:03:46,378 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,417 - INFO - draw_stats_page: 0.0405s
2025-07-21 23:03:46,511 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,547 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:46,548 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:46,645 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,683 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:03:46,778 - INFO - draw_background: 0.0010s
2025-07-21 23:03:46,810 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:03:46,912 - INFO - draw_background: 0.0011s
2025-07-21 23:03:46,950 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:03:47,045 - INFO - draw_background: 0.0009s
2025-07-21 23:03:47,080 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:03:47,178 - INFO - draw_background: 0.0010s
2025-07-21 23:03:47,215 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:03:47,216 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:47,312 - INFO - draw_background: 0.0010s
2025-07-21 23:03:47,353 - INFO - draw_stats_page: 0.0425s
2025-07-21 23:03:47,445 - INFO - draw_background: 0.0009s
2025-07-21 23:03:47,480 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:03:47,579 - INFO - draw_background: 0.0009s
2025-07-21 23:03:47,614 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:47,711 - INFO - draw_background: 0.0010s
2025-07-21 23:03:47,748 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:03:47,845 - INFO - draw_background: 0.0010s
2025-07-21 23:03:47,882 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:03:47,884 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:47,979 - INFO - draw_background: 0.0010s
2025-07-21 23:03:48,019 - INFO - draw_stats_page: 0.0415s
2025-07-21 23:03:48,112 - INFO - draw_background: 0.0009s
2025-07-21 23:03:48,148 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:48,245 - INFO - draw_background: 0.0009s
2025-07-21 23:03:48,285 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:03:48,312 - INFO - draw_background: 0.0015s
2025-07-21 23:03:48,353 - INFO - draw_stats_page: 0.0434s
2025-07-21 23:03:48,445 - INFO - draw_background: 0.0010s
2025-07-21 23:03:48,485 - INFO - draw_stats_page: 0.0417s
2025-07-21 23:03:48,486 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:48,576 - INFO - draw_background: 0.0010s
2025-07-21 23:03:48,615 - INFO - draw_stats_page: 0.0407s
2025-07-21 23:03:48,708 - INFO - draw_background: 0.0010s
2025-07-21 23:03:48,744 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:48,842 - INFO - draw_background: 0.0009s
2025-07-21 23:03:48,880 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:03:48,976 - INFO - draw_background: 0.0009s
2025-07-21 23:03:49,013 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:49,108 - INFO - draw_background: 0.0010s
2025-07-21 23:03:49,144 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:03:49,145 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:49,242 - INFO - draw_background: 0.0005s
2025-07-21 23:03:49,279 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:49,376 - INFO - draw_background: 0.0010s
2025-07-21 23:03:49,412 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:49,442 - INFO - draw_background: 0.0010s
2025-07-21 23:03:49,482 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:03:49,575 - INFO - draw_background: 0.0000s
2025-07-21 23:03:49,609 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:03:49,708 - INFO - draw_background: 0.0010s
2025-07-21 23:03:49,747 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:03:49,748 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:49,841 - INFO - draw_background: 0.0013s
2025-07-21 23:03:49,881 - INFO - draw_stats_page: 0.0416s
2025-07-21 23:03:49,909 - INFO - draw_background: 0.0000s
2025-07-21 23:03:49,952 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:49,952 - WARNING - Falling back to direct connection
2025-07-21 23:03:49,953 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:49,957 - INFO - draw_stats_page: 0.0490s
2025-07-21 23:03:50,045 - INFO - draw_background: 0.0009s
2025-07-21 23:03:50,083 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:03:50,179 - INFO - draw_background: 0.0010s
2025-07-21 23:03:50,214 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:50,310 - INFO - draw_background: 0.0000s
2025-07-21 23:03:50,347 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:03:50,348 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:50,442 - INFO - draw_background: 0.0005s
2025-07-21 23:03:50,479 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:03:50,576 - INFO - draw_background: 0.0009s
2025-07-21 23:03:50,613 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:03:50,709 - INFO - draw_background: 0.0009s
2025-07-21 23:03:50,740 - INFO - draw_stats_page: 0.0317s
2025-07-21 23:03:50,843 - INFO - draw_background: 0.0006s
2025-07-21 23:03:50,882 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:03:50,975 - INFO - draw_background: 0.0005s
2025-07-21 23:03:51,016 - INFO - draw_stats_page: 0.0412s
2025-07-21 23:03:51,018 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:51,112 - INFO - draw_background: 0.0010s
2025-07-21 23:03:51,150 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:03:51,245 - INFO - draw_background: 0.0005s
2025-07-21 23:03:51,284 - INFO - draw_stats_page: 0.0402s
2025-07-21 23:03:51,377 - INFO - draw_background: 0.0000s
2025-07-21 23:03:51,412 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:51,509 - INFO - draw_background: 0.0005s
2025-07-21 23:03:51,543 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:03:51,642 - INFO - draw_background: 0.0005s
2025-07-21 23:03:51,682 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:03:51,683 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:51,775 - INFO - draw_background: 0.0012s
2025-07-21 23:03:51,811 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:03:51,907 - INFO - draw_background: 0.0010s
2025-07-21 23:03:51,943 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:03:52,040 - INFO - draw_background: 0.0009s
2025-07-21 23:03:52,077 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:03:52,174 - INFO - draw_background: 0.0010s
2025-07-21 23:03:52,210 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:03:52,306 - INFO - draw_background: 0.0009s
2025-07-21 23:03:52,342 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:03:52,343 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:52,441 - INFO - draw_background: 0.0010s
2025-07-21 23:03:52,478 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:52,574 - INFO - draw_background: 0.0011s
2025-07-21 23:03:52,614 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:03:52,707 - INFO - draw_background: 0.0010s
2025-07-21 23:03:52,744 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:03:52,840 - INFO - draw_background: 0.0010s
2025-07-21 23:03:52,876 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:52,973 - INFO - draw_background: 0.0011s
2025-07-21 23:03:53,009 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:03:53,010 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:53,106 - INFO - draw_background: 0.0010s
2025-07-21 23:03:53,244 - INFO - draw_stats_page: 0.1392s
2025-07-21 23:03:53,270 - INFO - draw_background: 0.0010s
2025-07-21 23:03:53,308 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:53,403 - INFO - draw_background: 0.0010s
2025-07-21 23:03:53,440 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:03:53,538 - INFO - draw_background: 0.0010s
2025-07-21 23:03:53,570 - INFO - draw_stats_page: 0.0329s
2025-07-21 23:03:53,673 - INFO - draw_background: 0.0009s
2025-07-21 23:03:53,708 - INFO - draw_stats_page: 0.0363s
2025-07-21 23:03:53,709 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:53,807 - INFO - draw_background: 0.0005s
2025-07-21 23:03:53,842 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:03:53,942 - INFO - draw_background: 0.0010s
2025-07-21 23:03:53,976 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:54,075 - INFO - draw_background: 0.0011s
2025-07-21 23:03:54,111 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:54,210 - INFO - draw_background: 0.0000s
2025-07-21 23:03:54,253 - INFO - draw_stats_page: 0.0449s
2025-07-21 23:03:54,344 - INFO - draw_background: 0.0010s
2025-07-21 23:03:54,380 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:03:54,382 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:54,477 - INFO - draw_background: 0.0010s
2025-07-21 23:03:54,512 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:03:54,610 - INFO - draw_background: 0.0010s
2025-07-21 23:03:54,650 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:03:54,743 - INFO - draw_background: 0.0010s
2025-07-21 23:03:54,778 - INFO - draw_stats_page: 0.0353s
2025-07-21 23:03:54,876 - INFO - draw_background: 0.0009s
2025-07-21 23:03:54,912 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:03:54,955 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:54,955 - WARNING - Falling back to direct connection
2025-07-21 23:03:54,955 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:03:55,009 - INFO - draw_background: 0.0009s
2025-07-21 23:03:55,044 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:03:55,045 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:55,140 - INFO - draw_background: 0.0005s
2025-07-21 23:03:55,178 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:03:55,274 - INFO - draw_background: 0.0010s
2025-07-21 23:03:55,306 - INFO - draw_stats_page: 0.0327s
2025-07-21 23:03:55,407 - INFO - draw_background: 0.0010s
2025-07-21 23:03:55,441 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:03:55,539 - INFO - draw_background: 0.0000s
2025-07-21 23:03:55,575 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:03:55,673 - INFO - draw_background: 0.0005s
2025-07-21 23:03:55,706 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:03:55,708 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:55,807 - INFO - draw_background: 0.0010s
2025-07-21 23:03:55,900 - INFO - draw_stats_page: 0.0945s
2025-07-21 23:03:55,923 - INFO - draw_background: 0.0010s
2025-07-21 23:03:55,960 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:56,053 - INFO - draw_background: 0.0005s
2025-07-21 23:03:56,092 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:56,188 - INFO - draw_background: 0.0000s
2025-07-21 23:03:56,224 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:56,321 - INFO - draw_background: 0.0010s
2025-07-21 23:03:56,356 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:03:56,357 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:56,453 - INFO - draw_background: 0.0005s
2025-07-21 23:03:56,489 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:03:56,586 - INFO - draw_background: 0.0010s
2025-07-21 23:03:56,619 - INFO - draw_stats_page: 0.0335s
2025-07-21 23:03:56,719 - INFO - draw_background: 0.0010s
2025-07-21 23:03:56,756 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:56,854 - INFO - draw_background: 0.0010s
2025-07-21 23:03:56,889 - INFO - draw_stats_page: 0.0365s
2025-07-21 23:03:56,986 - INFO - draw_background: 0.0010s
2025-07-21 23:03:57,021 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:03:57,022 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:57,118 - INFO - draw_background: 0.0005s
2025-07-21 23:03:57,158 - INFO - draw_stats_page: 0.0411s
2025-07-21 23:03:57,250 - INFO - draw_background: 0.0000s
2025-07-21 23:03:57,286 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:03:57,384 - INFO - draw_background: 0.0010s
2025-07-21 23:03:57,421 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:03:57,517 - INFO - draw_background: 0.0000s
2025-07-21 23:03:57,548 - INFO - draw_stats_page: 0.0313s
2025-07-21 23:03:57,650 - INFO - draw_background: 0.0000s
2025-07-21 23:03:57,688 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:03:57,689 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:57,784 - INFO - draw_background: 0.0010s
2025-07-21 23:03:57,824 - INFO - draw_stats_page: 0.0403s
2025-07-21 23:03:57,918 - INFO - draw_background: 0.0010s
2025-07-21 23:03:57,952 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:03:58,052 - INFO - draw_background: 0.0005s
2025-07-21 23:03:58,089 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:58,186 - INFO - draw_background: 0.0000s
2025-07-21 23:03:58,221 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:03:58,317 - INFO - draw_background: 0.0010s
2025-07-21 23:03:58,354 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:03:58,355 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:58,451 - INFO - draw_background: 0.0010s
2025-07-21 23:03:58,490 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:03:58,584 - INFO - draw_background: 0.0010s
2025-07-21 23:03:58,618 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:03:58,716 - INFO - draw_background: 0.0009s
2025-07-21 23:03:58,752 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:03:58,849 - INFO - draw_background: 0.0010s
2025-07-21 23:03:58,885 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:03:58,982 - INFO - draw_background: 0.0009s
2025-07-21 23:03:59,018 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:03:59,019 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:59,115 - INFO - draw_background: 0.0010s
2025-07-21 23:03:59,151 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:03:59,248 - INFO - draw_background: 0.0009s
2025-07-21 23:03:59,287 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:03:59,381 - INFO - draw_background: 0.0005s
2025-07-21 23:03:59,417 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:03:59,515 - INFO - draw_background: 0.0010s
2025-07-21 23:03:59,551 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:03:59,647 - INFO - draw_background: 0.0010s
2025-07-21 23:03:59,684 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:03:59,685 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:03:59,779 - INFO - draw_background: 0.0010s
2025-07-21 23:03:59,820 - INFO - draw_stats_page: 0.0420s
2025-07-21 23:03:59,912 - INFO - draw_background: 0.0005s
2025-07-21 23:03:59,950 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:03:59,958 - ERROR - Error getting connection from pool: 
2025-07-21 23:03:59,958 - WARNING - Falling back to direct connection
2025-07-21 23:03:59,963 - INFO - DB Operation: {"timestamp": "2025-07-21 23:03:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-21 23:04:00,046 - INFO - draw_background: 0.0010s
2025-07-21 23:04:00,084 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:04:00,177 - INFO - draw_background: 0.0000s
2025-07-21 23:04:00,218 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:04:00,310 - INFO - draw_background: 0.0000s
2025-07-21 23:04:00,350 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:00,352 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:00,444 - INFO - draw_background: 0.0010s
2025-07-21 23:04:00,481 - INFO - draw_stats_page: 0.0374s
2025-07-21 23:04:00,575 - INFO - draw_background: 0.0009s
2025-07-21 23:04:00,609 - INFO - draw_stats_page: 0.0344s
2025-07-21 23:04:00,709 - INFO - draw_background: 0.0009s
2025-07-21 23:04:00,748 - INFO - draw_stats_page: 0.0396s
2025-07-21 23:04:00,842 - INFO - draw_background: 0.0005s
2025-07-21 23:04:00,985 - INFO - draw_stats_page: 0.1433s
2025-07-21 23:04:01,005 - INFO - draw_background: 0.0000s
2025-07-21 23:04:01,045 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:04:01,047 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:01,140 - INFO - draw_background: 0.0010s
2025-07-21 23:04:01,179 - INFO - draw_stats_page: 0.0405s
2025-07-21 23:04:01,272 - INFO - draw_background: 0.0011s
2025-07-21 23:04:01,309 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:04:01,406 - INFO - draw_background: 0.0010s
2025-07-21 23:04:01,442 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:04:01,538 - INFO - draw_background: 0.0000s
2025-07-21 23:04:01,576 - INFO - draw_stats_page: 0.0386s
2025-07-21 23:04:01,672 - INFO - draw_background: 0.0000s
2025-07-21 23:04:01,706 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:04:01,707 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:01,803 - INFO - draw_background: 0.0000s
2025-07-21 23:04:01,844 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:04:01,938 - INFO - draw_background: 0.0009s
2025-07-21 23:04:01,972 - INFO - draw_stats_page: 0.0358s
2025-07-21 23:04:02,070 - INFO - draw_background: 0.0010s
2025-07-21 23:04:02,109 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:04:02,204 - INFO - draw_background: 0.0010s
2025-07-21 23:04:02,244 - INFO - draw_stats_page: 0.0411s
2025-07-21 23:04:02,338 - INFO - draw_background: 0.0009s
2025-07-21 23:04:02,378 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:04:02,380 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:02,470 - INFO - draw_background: 0.0000s
2025-07-21 23:04:02,504 - INFO - draw_stats_page: 0.0340s
2025-07-21 23:04:02,603 - INFO - draw_background: 0.0005s
2025-07-21 23:04:02,638 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:04:02,736 - INFO - draw_background: 0.0010s
2025-07-21 23:04:02,772 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:04:02,869 - INFO - draw_background: 0.0000s
2025-07-21 23:04:02,903 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:04:03,004 - INFO - draw_background: 0.0011s
2025-07-21 23:04:03,039 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:04:03,039 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:03,137 - INFO - draw_background: 0.0010s
2025-07-21 23:04:03,238 - INFO - draw_stats_page: 0.1018s
2025-07-21 23:04:03,261 - INFO - draw_background: 0.0010s
2025-07-21 23:04:03,298 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:04:03,393 - INFO - draw_background: 0.0010s
2025-07-21 23:04:03,433 - INFO - draw_stats_page: 0.0413s
2025-07-21 23:04:03,526 - INFO - draw_background: 0.0000s
2025-07-21 23:04:03,564 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:04:03,659 - INFO - draw_background: 0.0000s
2025-07-21 23:04:03,697 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:04:03,698 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:03,792 - INFO - draw_background: 0.0005s
2025-07-21 23:04:03,829 - INFO - draw_stats_page: 0.0371s
2025-07-21 23:04:03,926 - INFO - draw_background: 0.0010s
2025-07-21 23:04:03,962 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:04:04,059 - INFO - draw_background: 0.0010s
2025-07-21 23:04:04,095 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:04:04,192 - INFO - draw_background: 0.0006s
2025-07-21 23:04:04,227 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:04:04,326 - INFO - draw_background: 0.0010s
2025-07-21 23:04:04,366 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:04:04,367 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:04,460 - INFO - draw_background: 0.0010s
2025-07-21 23:04:04,496 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:04:04,592 - INFO - draw_background: 0.0000s
2025-07-21 23:04:04,627 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:04:04,724 - INFO - draw_background: 0.0010s
2025-07-21 23:04:04,763 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:04:04,857 - INFO - draw_background: 0.0010s
2025-07-21 23:04:04,893 - INFO - draw_stats_page: 0.0374s
2025-07-21 23:04:04,990 - INFO - draw_background: 0.0020s
2025-07-21 23:04:05,023 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:04:05,025 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:05,123 - INFO - draw_background: 0.0012s
2025-07-21 23:04:05,161 - INFO - draw_stats_page: 0.0396s
2025-07-21 23:04:05,257 - INFO - draw_background: 0.0010s
2025-07-21 23:04:05,293 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:04:05,390 - INFO - draw_background: 0.0011s
2025-07-21 23:04:05,428 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:04:05,524 - INFO - draw_background: 0.0010s
2025-07-21 23:04:05,558 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:04:05,657 - INFO - draw_background: 0.0010s
2025-07-21 23:04:05,697 - INFO - draw_stats_page: 0.0404s
2025-07-21 23:04:05,698 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:05,789 - INFO - draw_background: 0.0010s
2025-07-21 23:04:05,827 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:04:05,922 - INFO - draw_background: 0.0005s
2025-07-21 23:04:05,960 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:04:06,056 - INFO - draw_background: 0.0010s
2025-07-21 23:04:06,096 - INFO - draw_stats_page: 0.0406s
2025-07-21 23:04:06,190 - INFO - draw_background: 0.0010s
2025-07-21 23:04:06,225 - INFO - draw_stats_page: 0.0367s
2025-07-21 23:04:06,323 - INFO - draw_background: 0.0011s
2025-07-21 23:04:06,362 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:04:06,362 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:06,455 - INFO - draw_background: 0.0015s
2025-07-21 23:04:06,493 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:06,588 - INFO - draw_background: 0.0010s
2025-07-21 23:04:06,627 - INFO - draw_stats_page: 0.0393s
2025-07-21 23:04:06,723 - INFO - draw_background: 0.0010s
2025-07-21 23:04:06,760 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:04:06,856 - INFO - draw_background: 0.0000s
2025-07-21 23:04:06,892 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:04:06,988 - INFO - draw_background: 0.0010s
2025-07-21 23:04:07,022 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:04:07,024 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:07,121 - INFO - draw_background: 0.0020s
2025-07-21 23:04:07,160 - INFO - draw_stats_page: 0.0413s
2025-07-21 23:04:07,255 - INFO - draw_background: 0.0000s
2025-07-21 23:04:07,292 - INFO - draw_stats_page: 0.0387s
2025-07-21 23:04:07,388 - INFO - draw_background: 0.0010s
2025-07-21 23:04:07,427 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:04:07,520 - INFO - draw_background: 0.0010s
2025-07-21 23:04:07,558 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:04:07,587 - INFO - draw_background: 0.0010s
2025-07-21 23:04:07,747 - INFO - draw_stats_page: 0.1623s
2025-07-21 23:04:07,748 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:07,778 - INFO - draw_background: 0.0010s
2025-07-21 23:04:07,825 - INFO - draw_stats_page: 0.0476s
2025-07-21 23:04:07,913 - INFO - draw_background: 0.0011s
2025-07-21 23:04:07,949 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:04:07,979 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,020 - INFO - draw_stats_page: 0.0404s
2025-07-21 23:04:08,113 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,154 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:04:08,247 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,284 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:04:08,286 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:08,378 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,416 - INFO - draw_stats_page: 0.0385s
2025-07-21 23:04:08,510 - INFO - draw_background: 0.0000s
2025-07-21 23:04:08,547 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:04:08,643 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,682 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:08,776 - INFO - draw_background: 0.0000s
2025-07-21 23:04:08,818 - INFO - draw_stats_page: 0.0434s
2025-07-21 23:04:08,910 - INFO - draw_background: 0.0010s
2025-07-21 23:04:08,946 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:04:08,947 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:09,043 - INFO - draw_background: 0.0011s
2025-07-21 23:04:09,082 - INFO - draw_stats_page: 0.0404s
2025-07-21 23:04:09,107 - INFO - draw_background: 0.0000s
2025-07-21 23:04:09,154 - INFO - draw_stats_page: 0.0470s
2025-07-21 23:04:09,243 - INFO - draw_background: 0.0011s
2025-07-21 23:04:09,281 - INFO - draw_stats_page: 0.0387s
2025-07-21 23:04:09,375 - INFO - draw_background: 0.0010s
2025-07-21 23:04:09,412 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:04:09,507 - INFO - draw_background: 0.0010s
2025-07-21 23:04:09,560 - INFO - draw_stats_page: 0.0547s
2025-07-21 23:04:09,562 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:09,653 - INFO - draw_background: 0.0006s
2025-07-21 23:04:09,724 - INFO - draw_stats_page: 0.0722s
2025-07-21 23:04:09,816 - INFO - draw_background: 0.0014s
2025-07-21 23:04:09,855 - INFO - draw_stats_page: 0.0407s
2025-07-21 23:04:09,950 - INFO - draw_background: 0.0010s
2025-07-21 23:04:09,990 - INFO - draw_stats_page: 0.0411s
2025-07-21 23:04:10,084 - INFO - draw_background: 0.0015s
2025-07-21 23:04:10,123 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:10,218 - INFO - draw_background: 0.0021s
2025-07-21 23:04:10,256 - INFO - draw_stats_page: 0.0403s
2025-07-21 23:04:10,258 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:10,349 - INFO - draw_background: 0.0019s
2025-07-21 23:04:10,386 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:04:10,479 - INFO - draw_background: 0.0009s
2025-07-21 23:04:10,520 - INFO - draw_stats_page: 0.0417s
2025-07-21 23:04:10,612 - INFO - draw_background: 0.0006s
2025-07-21 23:04:10,650 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:04:10,746 - INFO - draw_background: 0.0010s
2025-07-21 23:04:10,788 - INFO - draw_stats_page: 0.0438s
2025-07-21 23:04:10,879 - INFO - draw_background: 0.0009s
2025-07-21 23:04:10,919 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:04:10,920 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:11,014 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,052 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:04:11,146 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,181 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:04:11,279 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,317 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:04:11,412 - INFO - draw_background: 0.0015s
2025-07-21 23:04:11,450 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:04:11,545 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,587 - INFO - draw_stats_page: 0.0430s
2025-07-21 23:04:11,588 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:11,612 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,655 - INFO - draw_stats_page: 0.0446s
2025-07-21 23:04:11,745 - INFO - draw_background: 0.0010s
2025-07-21 23:04:11,786 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:04:11,878 - INFO - draw_background: 0.0000s
2025-07-21 23:04:11,920 - INFO - draw_stats_page: 0.0414s
2025-07-21 23:04:12,011 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,049 - INFO - draw_stats_page: 0.0390s
2025-07-21 23:04:12,144 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,181 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:04:12,184 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:12,277 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,315 - INFO - draw_stats_page: 0.0396s
2025-07-21 23:04:12,409 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,454 - INFO - draw_stats_page: 0.0459s
2025-07-21 23:04:12,542 - INFO - draw_background: 0.0005s
2025-07-21 23:04:12,586 - INFO - draw_stats_page: 0.0439s
2025-07-21 23:04:12,676 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,716 - INFO - draw_stats_page: 0.0404s
2025-07-21 23:04:12,810 - INFO - draw_background: 0.0009s
2025-07-21 23:04:12,846 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:04:12,848 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:12,879 - INFO - draw_background: 0.0010s
2025-07-21 23:04:12,922 - INFO - draw_stats_page: 0.0446s
2025-07-21 23:04:13,013 - INFO - draw_background: 0.0015s
2025-07-21 23:04:13,050 - INFO - draw_stats_page: 0.0382s
2025-07-21 23:04:13,145 - INFO - draw_background: 0.0009s
2025-07-21 23:04:13,185 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:04:13,279 - INFO - draw_background: 0.0009s
2025-07-21 23:04:13,317 - INFO - draw_stats_page: 0.0387s
2025-07-21 23:04:13,413 - INFO - draw_background: 0.0010s
2025-07-21 23:04:13,448 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:04:13,449 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:13,546 - INFO - draw_background: 0.0010s
2025-07-21 23:04:13,589 - INFO - draw_stats_page: 0.0437s
2025-07-21 23:04:13,616 - INFO - draw_background: 0.0010s
2025-07-21 23:04:13,659 - INFO - draw_stats_page: 0.0447s
2025-07-21 23:04:13,751 - INFO - draw_background: 0.0010s
2025-07-21 23:04:13,794 - INFO - draw_stats_page: 0.0444s
2025-07-21 23:04:13,821 - INFO - draw_background: 0.0013s
2025-07-21 23:04:13,864 - INFO - draw_stats_page: 0.0448s
2025-07-21 23:04:13,956 - INFO - draw_background: 0.0000s
2025-07-21 23:04:13,998 - INFO - draw_stats_page: 0.0435s
2025-07-21 23:04:14,000 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:14,023 - INFO - draw_background: 0.0005s
2025-07-21 23:04:14,071 - INFO - draw_stats_page: 0.0493s
2025-07-21 23:04:14,162 - INFO - draw_background: 0.0012s
2025-07-21 23:04:14,199 - INFO - draw_stats_page: 0.0373s
2025-07-21 23:04:14,233 - INFO - draw_background: 0.0031s
2025-07-21 23:04:14,394 - INFO - draw_stats_page: 0.1647s
2025-07-21 23:04:14,417 - INFO - draw_background: 0.0010s
2025-07-21 23:04:14,470 - INFO - draw_stats_page: 0.0536s
2025-07-21 23:04:14,562 - INFO - draw_background: 0.0017s
2025-07-21 23:04:14,600 - INFO - draw_stats_page: 0.0398s
2025-07-21 23:04:14,601 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:14,629 - INFO - draw_background: 0.0020s
2025-07-21 23:04:14,674 - INFO - draw_stats_page: 0.0473s
2025-07-21 23:04:14,762 - INFO - draw_background: 0.0006s
2025-07-21 23:04:14,801 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:04:14,832 - INFO - draw_background: 0.0021s
2025-07-21 23:04:14,872 - INFO - draw_stats_page: 0.0425s
2025-07-21 23:04:14,962 - INFO - draw_background: 0.0009s
2025-07-21 23:04:15,002 - INFO - draw_stats_page: 0.0408s
2025-07-21 23:04:15,032 - INFO - draw_background: 0.0015s
2025-07-21 23:04:15,071 - INFO - draw_stats_page: 0.0407s
2025-07-21 23:04:15,072 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:15,161 - INFO - draw_background: 0.0010s
2025-07-21 23:04:15,202 - INFO - draw_stats_page: 0.0413s
2025-07-21 23:04:15,229 - INFO - draw_background: 0.0010s
2025-07-21 23:04:15,273 - INFO - draw_stats_page: 0.0450s
2025-07-21 23:04:15,363 - INFO - draw_background: 0.0005s
2025-07-21 23:04:15,404 - INFO - draw_stats_page: 0.0422s
2025-07-21 23:04:15,430 - INFO - draw_background: 0.0011s
2025-07-21 23:04:15,470 - INFO - draw_stats_page: 0.0412s
2025-07-21 23:04:15,563 - INFO - draw_background: 0.0006s
2025-07-21 23:04:15,601 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:04:15,602 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:15,630 - INFO - draw_background: 0.0020s
2025-07-21 23:04:15,672 - INFO - draw_stats_page: 0.0435s
2025-07-21 23:04:15,763 - INFO - draw_background: 0.0010s
2025-07-21 23:04:15,802 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:15,830 - INFO - draw_background: 0.0026s
2025-07-21 23:04:15,869 - INFO - draw_stats_page: 0.0416s
2025-07-21 23:04:15,962 - INFO - draw_background: 0.0000s
2025-07-21 23:04:16,003 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:04:16,097 - INFO - draw_background: 0.0010s
2025-07-21 23:04:16,136 - INFO - draw_stats_page: 0.0405s
2025-07-21 23:04:16,137 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:16,228 - INFO - draw_background: 0.0009s
2025-07-21 23:04:16,267 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:04:16,344 - INFO - draw_background: 0.0010s
2025-07-21 23:04:16,380 - INFO - draw_stats_page: 0.0376s
2025-07-21 23:04:16,457 - INFO - draw_background: 0.0010s
2025-07-21 23:04:16,493 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:04:16,570 - INFO - draw_background: 0.0000s
2025-07-21 23:04:16,607 - INFO - draw_stats_page: 0.0366s
2025-07-21 23:04:16,714 - INFO - draw_background: 0.0010s
2025-07-21 23:04:16,752 - INFO - draw_stats_page: 0.0384s
2025-07-21 23:04:16,752 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:16,816 - INFO - draw_background: 0.0005s
2025-07-21 23:04:16,856 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:04:16,918 - INFO - draw_background: 0.0005s
2025-07-21 23:04:16,957 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:04:17,018 - INFO - draw_background: 0.0000s
2025-07-21 23:04:17,055 - INFO - draw_stats_page: 0.0370s
2025-07-21 23:04:17,135 - INFO - draw_background: 0.0010s
2025-07-21 23:04:17,170 - INFO - draw_stats_page: 0.0359s
2025-07-21 23:04:17,250 - INFO - draw_background: 0.0009s
2025-07-21 23:04:17,286 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:04:17,287 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:17,365 - INFO - draw_background: 0.0009s
2025-07-21 23:04:17,400 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:04:17,480 - INFO - draw_background: 0.0010s
2025-07-21 23:04:17,517 - INFO - draw_stats_page: 0.0386s
2025-07-21 23:04:17,594 - INFO - draw_background: 0.0010s
2025-07-21 23:04:17,633 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:17,697 - INFO - draw_background: 0.0009s
2025-07-21 23:04:17,734 - INFO - draw_stats_page: 0.0378s
2025-07-21 23:04:17,798 - INFO - draw_background: 0.0010s
2025-07-21 23:04:17,834 - INFO - draw_stats_page: 0.0369s
2025-07-21 23:04:17,835 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:17,898 - INFO - draw_background: 0.0010s
2025-07-21 23:04:17,932 - INFO - draw_stats_page: 0.0355s
2025-07-21 23:04:18,012 - INFO - draw_background: 0.0005s
2025-07-21 23:04:18,054 - INFO - draw_stats_page: 0.0413s
2025-07-21 23:04:18,116 - INFO - draw_background: 0.0010s
2025-07-21 23:04:18,152 - INFO - draw_stats_page: 0.0364s
2025-07-21 23:04:18,230 - INFO - draw_background: 0.0000s
2025-07-21 23:04:18,269 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:04:18,346 - INFO - draw_background: 0.0010s
2025-07-21 23:04:18,385 - INFO - draw_stats_page: 0.0391s
2025-07-21 23:04:18,386 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:18,490 - INFO - draw_background: 0.0009s
2025-07-21 23:04:18,534 - INFO - draw_stats_page: 0.0454s
2025-07-21 23:04:18,622 - INFO - draw_background: 0.0005s
2025-07-21 23:04:18,662 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:04:18,690 - INFO - draw_background: 0.0010s
2025-07-21 23:04:18,732 - INFO - draw_stats_page: 0.0433s
2025-07-21 23:04:18,793 - INFO - draw_background: 0.0011s
2025-07-21 23:04:18,832 - INFO - draw_stats_page: 0.0401s
2025-07-21 23:04:18,877 - INFO - draw_background: 0.0010s
2025-07-21 23:04:18,917 - INFO - draw_stats_page: 0.0409s
2025-07-21 23:04:18,918 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:18,979 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,019 - INFO - draw_stats_page: 0.0394s
2025-07-21 23:04:19,066 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,104 - INFO - draw_stats_page: 0.0386s
2025-07-21 23:04:19,183 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,221 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:04:19,284 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,326 - INFO - draw_stats_page: 0.0431s
2025-07-21 23:04:19,429 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,466 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:04:19,467 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:19,562 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,595 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:04:19,694 - INFO - draw_background: 0.0010s
2025-07-21 23:04:19,734 - INFO - draw_stats_page: 0.0410s
2025-07-21 23:04:19,795 - INFO - draw_background: 0.0000s
2025-07-21 23:04:19,836 - INFO - draw_stats_page: 0.0406s
2025-07-21 23:04:19,900 - INFO - draw_background: 0.0011s
2025-07-21 23:04:19,936 - INFO - draw_stats_page: 0.0377s
2025-07-21 23:04:19,999 - INFO - draw_background: 0.0009s
2025-07-21 23:04:20,038 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:04:20,040 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:20,052 - INFO - draw_background: 0.0016s
2025-07-21 23:04:20,096 - INFO - draw_stats_page: 0.0457s
2025-07-21 23:04:22,060 - INFO - draw_background: 0.0349s
2025-07-21 23:04:22,275 - INFO - draw_stats_page: 0.2492s
2025-07-21 23:04:22,289 - INFO - draw_background: 0.0012s
2025-07-21 23:04:22,333 - INFO - draw_stats_page: 0.0451s
2025-07-21 23:04:22,401 - INFO - draw_background: 0.0012s
2025-07-21 23:04:22,444 - INFO - draw_stats_page: 0.0448s
2025-07-21 23:04:22,512 - INFO - draw_background: 0.0011s
2025-07-21 23:04:22,546 - INFO - draw_stats_page: 0.0357s
2025-07-21 23:04:22,547 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:22,616 - INFO - draw_background: 0.0010s
2025-07-21 23:04:22,647 - INFO - draw_stats_page: 0.0308s
2025-07-21 23:04:22,760 - INFO - draw_background: 0.0005s
2025-07-21 23:04:22,795 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:04:22,893 - INFO - draw_background: 0.0010s
2025-07-21 23:04:22,930 - INFO - draw_stats_page: 0.0379s
2025-07-21 23:04:23,025 - INFO - draw_background: 0.0005s
2025-07-21 23:04:23,066 - INFO - draw_stats_page: 0.0422s
2025-07-21 23:04:23,159 - INFO - draw_background: 0.0010s
2025-07-21 23:04:23,164 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-21 23:04:23,193 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:04:23,194 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:23,292 - INFO - draw_background: 0.0006s
2025-07-21 23:04:23,325 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:04:23,426 - INFO - draw_background: 0.0010s
2025-07-21 23:04:23,461 - INFO - draw_stats_page: 0.0360s
2025-07-21 23:04:23,526 - INFO - Admin button added to stats page
2025-07-21 23:04:23,558 - INFO - draw_background: 0.0005s
2025-07-21 23:04:23,591 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:04:23,692 - INFO - draw_background: 0.0011s
2025-07-21 23:04:23,731 - INFO - draw_stats_page: 0.0392s
2025-07-21 23:04:23,825 - INFO - draw_background: 0.0000s
2025-07-21 23:04:23,858 - INFO - draw_stats_page: 0.0329s
2025-07-21 23:04:23,859 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:23,958 - INFO - draw_background: 0.0009s
2025-07-21 23:04:23,993 - INFO - draw_stats_page: 0.0352s
2025-07-21 23:04:24,090 - INFO - draw_background: 0.0000s
2025-07-21 23:04:24,125 - INFO - draw_stats_page: 0.0347s
2025-07-21 23:04:24,225 - INFO - draw_background: 0.0010s
2025-07-21 23:04:24,256 - INFO - draw_stats_page: 0.0306s
2025-07-21 23:04:24,357 - INFO - draw_background: 0.0009s
2025-07-21 23:04:24,390 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:04:24,490 - INFO - draw_background: 0.0010s
2025-07-21 23:04:24,522 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:04:24,523 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:24,624 - INFO - draw_background: 0.0000s
2025-07-21 23:04:24,657 - INFO - draw_stats_page: 0.0342s
2025-07-21 23:04:24,757 - INFO - draw_background: 0.0009s
2025-07-21 23:04:24,788 - INFO - draw_stats_page: 0.0328s
2025-07-21 23:04:24,889 - INFO - draw_background: 0.0009s
2025-07-21 23:04:24,923 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:04:25,025 - INFO - draw_background: 0.0009s
2025-07-21 23:04:25,057 - INFO - draw_stats_page: 0.0333s
2025-07-21 23:04:25,158 - INFO - draw_background: 0.0010s
2025-07-21 23:04:25,189 - INFO - draw_stats_page: 0.0322s
2025-07-21 23:04:25,190 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:25,291 - INFO - draw_background: 0.0010s
2025-07-21 23:04:25,324 - INFO - draw_stats_page: 0.0338s
2025-07-21 23:04:25,424 - INFO - draw_background: 0.0011s
2025-07-21 23:04:25,454 - INFO - draw_stats_page: 0.0315s
2025-07-21 23:04:25,556 - INFO - draw_background: 0.0009s
2025-07-21 23:04:25,589 - INFO - draw_stats_page: 0.0338s
2025-07-21 23:04:25,690 - INFO - draw_background: 0.0009s
2025-07-21 23:04:25,733 - INFO - draw_stats_page: 0.0428s
2025-07-21 23:04:25,823 - INFO - draw_background: 0.0000s
2025-07-21 23:04:25,858 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:04:25,858 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:25,957 - INFO - draw_background: 0.0010s
2025-07-21 23:04:25,996 - INFO - draw_stats_page: 0.0399s
2025-07-21 23:04:26,088 - INFO - draw_background: 0.0009s
2025-07-21 23:04:26,134 - INFO - draw_stats_page: 0.0475s
2025-07-21 23:04:26,222 - INFO - draw_background: 0.0005s
2025-07-21 23:04:26,255 - INFO - draw_stats_page: 0.0315s
2025-07-21 23:04:26,357 - INFO - draw_background: 0.0010s
2025-07-21 23:04:26,390 - INFO - draw_stats_page: 0.0336s
2025-07-21 23:04:26,489 - INFO - draw_background: 0.0011s
2025-07-21 23:04:26,523 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:04:26,524 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:26,620 - INFO - draw_background: 0.0000s
2025-07-21 23:04:26,658 - INFO - draw_stats_page: 0.0380s
2025-07-21 23:04:26,754 - INFO - draw_background: 0.0000s
2025-07-21 23:04:26,786 - INFO - draw_stats_page: 0.0321s
2025-07-21 23:04:26,887 - INFO - draw_background: 0.0010s
2025-07-21 23:04:26,920 - INFO - draw_stats_page: 0.0331s
2025-07-21 23:04:27,020 - INFO - draw_background: 0.0000s
2025-07-21 23:04:27,056 - INFO - draw_stats_page: 0.0372s
2025-07-21 23:04:27,152 - INFO - draw_background: 0.0006s
2025-07-21 23:04:27,158 - INFO - draw_stats_page: 0.0061s
2025-07-21 23:04:27,160 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:27,285 - INFO - draw_background: 0.0010s
2025-07-21 23:04:27,288 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:27,418 - INFO - draw_background: 0.0009s
2025-07-21 23:04:27,422 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:27,550 - INFO - draw_background: 0.0000s
2025-07-21 23:04:27,553 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:04:27,683 - INFO - draw_background: 0.0005s
2025-07-21 23:04:27,687 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:27,817 - INFO - draw_background: 0.0005s
2025-07-21 23:04:27,821 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:27,822 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:27,950 - INFO - draw_background: 0.0009s
2025-07-21 23:04:27,954 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:28,085 - INFO - draw_background: 0.0010s
2025-07-21 23:04:28,088 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:28,218 - INFO - draw_background: 0.0010s
2025-07-21 23:04:28,221 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:04:28,350 - INFO - draw_background: 0.0010s
2025-07-21 23:04:28,355 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:28,483 - INFO - draw_background: 0.0005s
2025-07-21 23:04:28,487 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:28,488 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:28,617 - INFO - draw_background: 0.0000s
2025-07-21 23:04:28,620 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:28,682 - INFO - draw_background: 0.0011s
2025-07-21 23:04:28,685 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:28,816 - INFO - draw_background: 0.0010s
2025-07-21 23:04:28,820 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:04:28,948 - INFO - draw_background: 0.0009s
2025-07-21 23:04:28,953 - INFO - draw_stats_page: 0.0055s
2025-07-21 23:04:29,082 - INFO - draw_background: 0.0010s
2025-07-21 23:04:29,085 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:29,086 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:29,215 - INFO - draw_background: 0.0009s
2025-07-21 23:04:29,219 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:04:29,349 - INFO - draw_background: 0.0012s
2025-07-21 23:04:29,352 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:04:29,483 - INFO - draw_background: 0.0010s
2025-07-21 23:04:29,486 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:29,549 - INFO - draw_background: 0.0011s
2025-07-21 23:04:29,553 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:04:29,683 - INFO - draw_background: 0.0010s
2025-07-21 23:04:29,686 - INFO - draw_stats_page: 0.0048s
2025-07-21 23:04:29,687 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:29,749 - INFO - draw_background: 0.0011s
2025-07-21 23:04:29,752 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:29,883 - INFO - draw_background: 0.0011s
2025-07-21 23:04:29,887 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:29,949 - INFO - draw_background: 0.0011s
2025-07-21 23:04:29,953 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:30,082 - INFO - draw_background: 0.0016s
2025-07-21 23:04:30,086 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:30,147 - INFO - draw_background: 0.0010s
2025-07-21 23:04:30,151 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:30,152 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:30,280 - INFO - draw_background: 0.0009s
2025-07-21 23:04:30,284 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:04:30,348 - INFO - draw_background: 0.0007s
2025-07-21 23:04:30,352 - INFO - draw_stats_page: 0.0047s
2025-07-21 23:04:30,479 - INFO - draw_background: 0.0010s
2025-07-21 23:04:30,482 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:30,545 - INFO - draw_background: 0.0005s
2025-07-21 23:04:30,550 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:30,679 - INFO - draw_background: 0.0010s
2025-07-21 23:04:30,683 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:30,685 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:30,746 - INFO - draw_background: 0.0011s
2025-07-21 23:04:30,749 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:30,879 - INFO - draw_background: 0.0009s
2025-07-21 23:04:30,883 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:31,012 - INFO - draw_background: 0.0010s
2025-07-21 23:04:31,015 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:31,144 - INFO - draw_background: 0.0009s
2025-07-21 23:04:31,149 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:04:31,278 - INFO - draw_background: 0.0009s
2025-07-21 23:04:31,281 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:04:31,282 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:31,409 - INFO - draw_background: 0.0009s
2025-07-21 23:04:31,412 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:31,543 - INFO - draw_background: 0.0005s
2025-07-21 23:04:31,547 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:31,676 - INFO - draw_background: 0.0005s
2025-07-21 23:04:31,680 - INFO - draw_stats_page: 0.0036s
2025-07-21 23:04:31,810 - INFO - draw_background: 0.0010s
2025-07-21 23:04:31,813 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:31,942 - INFO - draw_background: 0.0005s
2025-07-21 23:04:31,947 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:31,948 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:32,075 - INFO - draw_background: 0.0010s
2025-07-21 23:04:32,078 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:32,209 - INFO - draw_background: 0.0010s
2025-07-21 23:04:32,212 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:32,344 - INFO - draw_background: 0.0009s
2025-07-21 23:04:32,347 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:32,478 - INFO - draw_background: 0.0000s
2025-07-21 23:04:32,481 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:32,609 - INFO - draw_background: 0.0009s
2025-07-21 23:04:32,613 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:32,613 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:32,742 - INFO - draw_background: 0.0006s
2025-07-21 23:04:32,745 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:32,809 - INFO - draw_background: 0.0009s
2025-07-21 23:04:32,812 - INFO - draw_stats_page: 0.0049s
2025-07-21 23:04:32,942 - INFO - draw_background: 0.0010s
2025-07-21 23:04:32,945 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:33,008 - INFO - draw_background: 0.0009s
2025-07-21 23:04:33,012 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:04:33,140 - INFO - draw_background: 0.0000s
2025-07-21 23:04:33,144 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:33,146 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:33,208 - INFO - draw_background: 0.0010s
2025-07-21 23:04:33,212 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:33,341 - INFO - load_statistics: 0.0010s
2025-07-21 23:04:33,343 - INFO - draw_background: 0.0013s
2025-07-21 23:04:33,347 - INFO - draw_stats_page: 0.0053s
2025-07-21 23:04:33,475 - INFO - draw_background: 0.0010s
2025-07-21 23:04:33,478 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:33,608 - INFO - draw_background: 0.0009s
2025-07-21 23:04:33,610 - INFO - draw_stats_page: 0.0034s
2025-07-21 23:04:33,740 - INFO - draw_background: 0.0009s
2025-07-21 23:04:33,741 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:33,743 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:33,870 - INFO - draw_background: 0.0000s
2025-07-21 23:04:33,874 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:34,005 - INFO - draw_background: 0.0009s
2025-07-21 23:04:34,008 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:34,137 - INFO - draw_background: 0.0009s
2025-07-21 23:04:34,140 - INFO - draw_stats_page: 0.0044s
2025-07-21 23:04:34,270 - INFO - draw_background: 0.0009s
2025-07-21 23:04:34,273 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:34,402 - INFO - draw_background: 0.0006s
2025-07-21 23:04:34,403 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:34,406 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:34,535 - INFO - draw_background: 0.0009s
2025-07-21 23:04:34,538 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:04:34,669 - INFO - draw_background: 0.0000s
2025-07-21 23:04:34,672 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:34,802 - INFO - draw_background: 0.0005s
2025-07-21 23:04:34,806 - INFO - draw_stats_page: 0.0036s
2025-07-21 23:04:34,935 - INFO - draw_background: 0.0010s
2025-07-21 23:04:34,938 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:35,068 - INFO - draw_background: 0.0010s
2025-07-21 23:04:35,069 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:35,073 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:35,200 - INFO - draw_background: 0.0009s
2025-07-21 23:04:35,203 - INFO - draw_stats_page: 0.0035s
2025-07-21 23:04:35,333 - INFO - draw_background: 0.0005s
2025-07-21 23:04:35,337 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:35,465 - INFO - draw_background: 0.0010s
2025-07-21 23:04:35,468 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:35,600 - INFO - draw_background: 0.0000s
2025-07-21 23:04:35,604 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:35,733 - INFO - draw_background: 0.0005s
2025-07-21 23:04:35,734 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:35,737 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:35,865 - INFO - draw_background: 0.0010s
2025-07-21 23:04:35,869 - INFO - draw_stats_page: 0.0045s
2025-07-21 23:04:35,997 - INFO - draw_background: 0.0010s
2025-07-21 23:04:36,000 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:04:36,130 - INFO - draw_background: 0.0009s
2025-07-21 23:04:36,133 - INFO - draw_stats_page: 0.0039s
2025-07-21 23:04:36,262 - INFO - draw_background: 0.0005s
2025-07-21 23:04:36,267 - INFO - draw_stats_page: 0.0059s
2025-07-21 23:04:36,397 - INFO - draw_background: 0.0000s
2025-07-21 23:04:36,398 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:36,401 - INFO - draw_stats_page: 0.0050s
2025-07-21 23:04:36,529 - INFO - draw_background: 0.0010s
2025-07-21 23:04:36,532 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:36,660 - INFO - draw_background: 0.0000s
2025-07-21 23:04:36,664 - INFO - draw_stats_page: 0.0041s
2025-07-21 23:04:36,794 - INFO - draw_background: 0.0000s
2025-07-21 23:04:36,797 - INFO - draw_stats_page: 0.0046s
2025-07-21 23:04:36,926 - INFO - draw_background: 0.0010s
2025-07-21 23:04:36,929 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:37,059 - INFO - draw_background: 0.0000s
2025-07-21 23:04:37,060 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:37,063 - INFO - draw_stats_page: 0.0051s
2025-07-21 23:04:37,192 - INFO - draw_background: 0.0010s
2025-07-21 23:04:37,195 - INFO - draw_stats_page: 0.0040s
2025-07-21 23:04:37,324 - INFO - draw_background: 0.0005s
2025-07-21 23:04:37,367 - INFO - draw_stats_page: 0.0434s
2025-07-21 23:04:37,458 - INFO - draw_background: 0.0000s
2025-07-21 23:04:37,495 - INFO - draw_stats_page: 0.0383s
2025-07-21 23:04:37,591 - INFO - draw_background: 0.0009s
2025-07-21 23:04:37,625 - INFO - draw_stats_page: 0.0350s
2025-07-21 23:04:37,724 - INFO - draw_background: 0.0010s
2025-07-21 23:04:37,725 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:37,764 - INFO - draw_stats_page: 0.0412s
2025-07-21 23:04:37,855 - INFO - draw_background: 0.0000s
2025-07-21 23:04:37,893 - INFO - draw_stats_page: 0.0375s
2025-07-21 23:04:37,990 - INFO - draw_background: 0.0010s
2025-07-21 23:04:38,029 - INFO - draw_stats_page: 0.0397s
2025-07-21 23:04:38,122 - INFO - draw_background: 0.0005s
2025-07-21 23:04:38,159 - INFO - draw_stats_page: 0.0368s
2025-07-21 23:04:38,256 - INFO - draw_background: 0.0005s
2025-07-21 23:04:38,291 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:04:38,389 - INFO - draw_background: 0.0009s
2025-07-21 23:04:38,389 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:38,426 - INFO - draw_stats_page: 0.0381s
2025-07-21 23:04:38,523 - INFO - draw_background: 0.0005s
2025-07-21 23:04:38,559 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:04:38,655 - INFO - draw_background: 0.0000s
2025-07-21 23:04:38,694 - INFO - draw_stats_page: 0.0389s
2025-07-21 23:04:38,790 - INFO - draw_background: 0.0010s
2025-07-21 23:04:38,830 - INFO - draw_stats_page: 0.0402s
2025-07-21 23:04:38,923 - INFO - draw_background: 0.0006s
2025-07-21 23:04:39,061 - INFO - draw_stats_page: 0.1394s
2025-07-21 23:04:39,088 - INFO - draw_background: 0.0010s
2025-07-21 23:04:39,088 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:39,126 - INFO - draw_stats_page: 0.0400s
2025-07-21 23:04:39,220 - INFO - draw_background: 0.0010s
2025-07-21 23:04:39,254 - INFO - draw_stats_page: 0.0354s
2025-07-21 23:04:39,354 - INFO - draw_background: 0.0000s
2025-07-21 23:04:39,394 - INFO - draw_stats_page: 0.0419s
2025-07-21 23:04:39,485 - INFO - draw_background: 0.0010s
2025-07-21 23:04:39,517 - INFO - draw_stats_page: 0.0332s
2025-07-21 23:04:39,619 - INFO - draw_background: 0.0010s
2025-07-21 23:04:39,659 - INFO - draw_stats_page: 0.0412s
2025-07-21 23:04:39,753 - INFO - draw_background: 0.0000s
2025-07-21 23:04:39,753 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:39,789 - INFO - draw_stats_page: 0.0362s
2025-07-21 23:04:39,885 - INFO - draw_background: 0.0005s
2025-07-21 23:04:39,924 - INFO - draw_stats_page: 0.0395s
2025-07-21 23:04:40,020 - INFO - draw_background: 0.0010s
2025-07-21 23:04:40,053 - INFO - draw_stats_page: 0.0346s
2025-07-21 23:04:40,150 - INFO - draw_background: 0.0000s
2025-07-21 23:04:40,186 - INFO - draw_stats_page: 0.0345s
2025-07-21 23:04:40,284 - INFO - draw_background: 0.0010s
2025-07-21 23:04:40,317 - INFO - draw_stats_page: 0.0335s
2025-07-21 23:04:40,417 - INFO - draw_background: 0.0009s
2025-07-21 23:04:40,418 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:40,455 - INFO - draw_stats_page: 0.0388s
2025-07-21 23:04:40,549 - INFO - draw_background: 0.0000s
2025-07-21 23:04:40,584 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:04:40,683 - INFO - draw_background: 0.0000s
2025-07-21 23:04:40,718 - INFO - draw_stats_page: 0.0356s
2025-07-21 23:04:40,816 - INFO - draw_background: 0.0000s
2025-07-21 23:04:40,850 - INFO - draw_stats_page: 0.0343s
2025-07-21 23:04:40,950 - INFO - draw_background: 0.0009s
2025-07-21 23:04:40,985 - INFO - draw_stats_page: 0.0348s
2025-07-21 23:04:41,082 - INFO - draw_background: 0.0005s
2025-07-21 23:04:41,083 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-21 23:04:41,126 - INFO - draw_stats_page: 0.0444s
2025-07-21 23:04:41,216 - INFO - draw_background: 0.0006s
2025-07-21 23:04:41,258 - INFO - draw_stats_page: 0.0424s
2025-07-21 23:04:41,348 - INFO - draw_background: 0.0005s
2025-07-21 23:04:41,382 - INFO - draw_stats_page: 0.0341s
2025-07-21 23:04:41,482 - INFO - draw_background: 0.0010s
2025-07-21 23:04:41,518 - INFO - draw_stats_page: 0.0361s
2025-07-21 23:04:41,614 - INFO - draw_background: 0.0010s
2025-07-21 23:04:41,650 - INFO - draw_stats_page: 0.0368s
