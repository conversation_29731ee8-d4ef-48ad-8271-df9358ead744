#!/usr/bin/env python3
"""
Test script to verify both critical issues are fixed:
1. 15-second loading delay (should now be under 1 second)
2. Blinking history section (should be stable after login)
"""

import pygame
import sys
import time
from stats_page import show_stats_page, StatsPage

def test_performance_fix():
    """Test Issue 1: Performance fix - should load in under 1 second"""
    print("="*60)
    print("TESTING ISSUE 1: PERFORMANCE FIX")
    print("="*60)
    
    # Test import time
    start_time = time.time()
    from stats_page import show_stats_page
    import_time = time.time() - start_time
    print(f"✓ Import time: {import_time:.3f}s")
    
    # Test stats page creation time
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    
    start_time = time.time()
    stats_page = StatsPage(screen)
    creation_time = time.time() - start_time
    print(f"✓ Stats page creation time: {creation_time:.3f}s")
    
    # Test show_stats_page function time
    start_time = time.time()
    try:
        # This will run briefly then exit
        stats_page.running = False  # Prevent infinite loop
        show_stats_page(screen)
        show_time = time.time() - start_time
        print(f"✓ Show stats page time: {show_time:.3f}s")
    except Exception as e:
        show_time = time.time() - start_time
        print(f"✓ Show stats page time (with expected exit): {show_time:.3f}s")
    
    # Verify performance targets
    if creation_time < 1.0:
        print(f"✅ PERFORMANCE FIX SUCCESS: Creation time {creation_time:.3f}s < 1.0s target")
    else:
        print(f"❌ PERFORMANCE FIX FAILED: Creation time {creation_time:.3f}s >= 1.0s target")
    
    pygame.quit()
    return creation_time < 1.0

def test_anti_blink_fix():
    """Test Issue 2: Anti-blink fix - history should be stable"""
    print("\n" + "="*60)
    print("TESTING ISSUE 2: ANTI-BLINK FIX")
    print("="*60)
    
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    
    try:
        # Create stats page
        stats_page = StatsPage(screen)
        
        # Test authentication system
        print("Testing authentication system...")
        auth_before = stats_page.is_authenticated()
        print(f"✓ Authentication before login: {auth_before}")
        
        # Simulate login
        stats_page.handle_login_mouse_click((100, 100))
        auth_after = stats_page.is_authenticated()
        print(f"✓ Authentication after login: {auth_after}")
        
        # Test anti-blink mechanisms
        print("Testing anti-blink mechanisms...")
        
        # Check if background loading control is active
        has_loading_control = hasattr(stats_page, '_background_loading_active')
        print(f"✓ Background loading control: {has_loading_control}")
        
        # Check if data stability flags are set
        has_data_loaded_flag = hasattr(stats_page, '_data_loaded')
        print(f"✓ Data loaded flag: {has_data_loaded_flag}")
        
        # Test refresh event handling (should be ignored when data is loaded)
        if hasattr(stats_page, '_data_loaded'):
            stats_page._data_loaded = True
            
        # Create a mock refresh event
        class MockEvent:
            def __init__(self):
                self.stats_type = 'refresh_stats'
        
        mock_event = MockEvent()
        refresh_ignored = stats_page.handle_refresh_event(mock_event)
        print(f"✓ Refresh events ignored when data loaded: {not refresh_ignored}")
        
        # Test drawing without blinking
        print("Testing stable drawing...")
        try:
            stats_page.draw()
            print("✓ Stats page draws without errors")
            drawing_success = True
        except Exception as e:
            print(f"⚠ Drawing error (may be expected): {e}")
            drawing_success = False
        
        # Verify anti-blink features
        anti_blink_features = [
            has_loading_control,
            has_data_loaded_flag,
            not refresh_ignored,  # Should be False (ignored)
            auth_after  # Should be True after login
        ]
        
        success_count = sum(anti_blink_features)
        print(f"✓ Anti-blink features working: {success_count}/4")
        
        if success_count >= 3:
            print("✅ ANTI-BLINK FIX SUCCESS: History section should be stable")
            return True
        else:
            print("❌ ANTI-BLINK FIX PARTIAL: Some features may still cause blinking")
            return False
            
    except Exception as e:
        print(f"❌ ANTI-BLINK TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        pygame.quit()

def main():
    """Run both critical fix tests"""
    print("WOW BINGO STATS - CRITICAL FIXES TEST")
    print("Testing fixes for:")
    print("1. 15-second loading delay")
    print("2. Blinking history section after login")
    print()
    
    # Test performance fix
    performance_fixed = test_performance_fix()
    
    # Test anti-blink fix
    anti_blink_fixed = test_anti_blink_fix()
    
    # Final results
    print("\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    
    if performance_fixed:
        print("✅ Issue 1 FIXED: Stats page now loads quickly (under 1 second)")
    else:
        print("❌ Issue 1 NOT FIXED: Stats page still loads slowly")
    
    if anti_blink_fixed:
        print("✅ Issue 2 FIXED: History section should be stable after login")
    else:
        print("❌ Issue 2 NOT FIXED: History section may still blink")
    
    if performance_fixed and anti_blink_fixed:
        print("\n🎉 ALL CRITICAL ISSUES FIXED! Stats page is ready for production.")
        return True
    else:
        print("\n⚠️  Some issues remain. Please review the test results above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)