#!/usr/bin/env python3
"""
Stats Page Fixes Validation Script

This script validates that the performance and flickering fixes are working correctly.
It tests both the 60-second loading issue and the UI flickering problem.
"""

import time
import threading
import os
import sys
import json
from datetime import datetime

def test_performance_fixes():
    """Test that the performance fixes resolve the 60-second loading issue"""
    print("=" * 60)
    print("TESTING PERFORMANCE FIXES")
    print("=" * 60)
    
    try:
        # Test 1: Fast stats loading
        print("Test 1: Testing fast stats loading...")
        start_time = time.time()
        
        # Import and create stats page
        from stats_page import StatsPage
        import pygame
        
        # Initialize pygame for testing
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        
        # Create stats page instance
        stats_page = StatsPage(screen)
        
        # Test load_statistics method
        stats_page.load_statistics()
        
        load_time = time.time() - start_time
        print(f"✓ Stats page loaded in {load_time:.2f} seconds")
        
        # PASS if loading takes less than 5 seconds (should be much faster now)
        if load_time < 5.0:
            print("✓ PERFORMANCE TEST PASSED: Loading time is acceptable")
        else:
            print("✗ PERFORMANCE TEST FAILED: Loading time still too slow")
            return False
            
        # Test 2: Fast refresh
        print("\nTest 2: Testing fast refresh...")
        start_time = time.time()
        
        stats_page.refresh_stats()
        
        refresh_time = time.time() - start_time
        print(f"✓ Stats refresh completed in {refresh_time:.2f} seconds")
        
        # PASS if refresh takes less than 2 seconds
        if refresh_time < 2.0:
            print("✓ REFRESH TEST PASSED: Refresh time is acceptable")
        else:
            print("✗ REFRESH TEST FAILED: Refresh time still too slow")
            return False
            
        # Test 3: Cache functionality
        print("\nTest 3: Testing cache functionality...")
        cache_file = os.path.join('data', 'stats_cache_instant.json')
        
        if os.path.exists(cache_file):
            print("✓ Cache file created successfully")
            
            # Check cache content
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
                
            if 'timestamp' in cache_data:
                print("✓ Cache contains timestamp")
            if 'weekly_stats' in cache_data:
                print("✓ Cache contains weekly stats")
                
            print("✓ CACHE TEST PASSED: Cache system working")
        else:
            print("! Cache file not created (may be normal for first run)")
            
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"✗ PERFORMANCE TEST ERROR: {e}")
        return False

def test_flickering_fixes():
    """Test that the flickering fixes resolve the UI blinking issue"""
    print("\n" + "=" * 60)
    print("TESTING FLICKERING FIXES")
    print("=" * 60)
    
    try:
        # Test 1: Loading state management
        print("Test 1: Testing loading state management...")
        
        from stats_page import StatsPage
        import pygame
        
        # Initialize pygame for testing
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        
        # Create stats page instance
        stats_page = StatsPage(screen)
        
        # Test that loading state doesn't get stuck
        initial_loading_state = getattr(stats_page, 'stats_loading_in_progress', False)
        print(f"✓ Initial loading state: {initial_loading_state}")
        
        # Test set_loading_state method
        stats_page.set_loading_state(True)
        state_after_true = stats_page.stats_loading_in_progress
        
        stats_page.set_loading_state(False)
        state_after_false = stats_page.stats_loading_in_progress
        
        print(f"✓ Loading state after True: {state_after_true}")
        print(f"✓ Loading state after False: {state_after_false}")
        
        if not state_after_false:
            print("✓ LOADING STATE TEST PASSED: State management working")
        else:
            print("✗ LOADING STATE TEST FAILED: State stuck in loading")
            return False
            
        # Test 2: Stable UI data management
        print("\nTest 2: Testing stable UI data management...")
        
        # Test stable data methods
        test_data = [{'id': 1, 'name': 'test'}]
        stats_page.set_stable_ui_data('test_section', test_data)
        retrieved_data = stats_page.get_stable_ui_data('test_section')
        
        if retrieved_data == test_data:
            print("✓ STABLE DATA TEST PASSED: Data management working")
        else:
            print("✗ STABLE DATA TEST FAILED: Data not preserved")
            return False
            
        # Test 3: Section update cooldown
        print("\nTest 3: Testing section update cooldown...")
        
        # First update should be allowed
        should_update_1 = stats_page.should_update_section('test_cooldown')
        print(f"✓ First update allowed: {should_update_1}")
        
        # Immediate second update should be blocked
        should_update_2 = stats_page.should_update_section('test_cooldown')
        print(f"✓ Immediate second update blocked: {not should_update_2}")
        
        if should_update_1 and not should_update_2:
            print("✓ COOLDOWN TEST PASSED: Update throttling working")
        else:
            print("✗ COOLDOWN TEST FAILED: Throttling not working")
            return False
            
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"✗ FLICKERING TEST ERROR: {e}")
        return False

def test_integration():
    """Test that the fixes work together without conflicts"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATION")
    print("=" * 60)
    
    try:
        print("Test 1: Testing combined performance and flickering fixes...")
        
        from stats_page import StatsPage
        import pygame
        
        # Initialize pygame for testing
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        
        # Create stats page instance
        stats_page = StatsPage(screen)
        
        # Test rapid operations that previously caused issues
        start_time = time.time()
        
        # Load statistics
        stats_page.load_statistics()
        
        # Wait a moment
        time.sleep(0.1)
        
        # Refresh stats
        stats_page.refresh_stats()
        
        # Wait a moment
        time.sleep(0.1)
        
        # Update UI
        stats_page.update()
        
        total_time = time.time() - start_time
        print(f"✓ Combined operations completed in {total_time:.2f} seconds")
        
        # Check final state
        loading_state = getattr(stats_page, 'stats_loading_in_progress', False)
        refresh_state = getattr(stats_page, '_refresh_in_progress', False)
        
        print(f"✓ Final loading state: {loading_state}")
        print(f"✓ Final refresh state: {refresh_state}")
        
        if total_time < 3.0:
            print("✓ INTEGRATION TEST PASSED: All fixes working together")
            pygame.quit()
            return True
        else:
            print("✗ INTEGRATION TEST FAILED: Combined operations too slow")
            pygame.quit()
            return False
            
    except Exception as e:
        print(f"✗ INTEGRATION TEST ERROR: {e}")
        return False

def main():
    """Run all validation tests"""
    print("STATS PAGE FIXES VALIDATION")
    print("Testing fixes for 60-second loading and UI flickering issues")
    print("Started at:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # Run tests
    performance_passed = test_performance_fixes()
    flickering_passed = test_flickering_fixes()
    integration_passed = test_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"Performance Fixes: {'PASSED' if performance_passed else 'FAILED'}")
    print(f"Flickering Fixes: {'PASSED' if flickering_passed else 'FAILED'}")
    print(f"Integration Test: {'PASSED' if integration_passed else 'FAILED'}")
    
    all_passed = performance_passed and flickering_passed and integration_passed
    
    if all_passed:
        print("\n✓ ALL TESTS PASSED: Fixes are working correctly!")
        print("The 60-second loading issue and UI flickering should be resolved.")
    else:
        print("\n✗ SOME TESTS FAILED: Please review the fixes.")
        
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
