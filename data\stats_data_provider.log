2025-07-21 11:10:31.629 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:10:31.631 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:10:31.641 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:10:31.641 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 11:10:31.642 - Initializing StatsDataProvider
2025-07-21 11:10:31.655 - Loaded 8 items from cache
2025-07-21 11:10:31.661 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 11:10:31.666 - Starting background data loading thread
2025-07-21 11:10:31.669 - Background data loading started
2025-07-21 11:10:31.669 - StatsDataProvider initialization completed
2025-07-21 11:10:31.669 - Loading summary statistics
2025-07-21 11:10:31.671 - Created singleton instance of StatsDataProvider on module import
2025-07-21 11:10:31.672 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 11:10:31.672 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 11:10:31.673 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:10:31.674 - get_stats_provider called, returning provider with initialized=True
2025-07-21 11:10:31.674 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 11:10:31.676 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:10:31.678 - Successfully loaded weekly stats: 7 days
2025-07-21 11:10:31.681 - Saved 10 items to cache
2025-07-21 11:10:31.682 - Background data loading completed
2025-07-21 11:11:00.805 - get_stats_provider called, returning provider with initialized=True
2025-07-21 11:13:47.344 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:47.345 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:47.361 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:47.361 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 11:13:47.362 - Initializing StatsDataProvider
2025-07-21 11:13:47.378 - Loaded 8 items from cache
2025-07-21 11:13:47.381 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 11:13:47.382 - Starting background data loading thread
2025-07-21 11:13:47.384 - Background data loading started
2025-07-21 11:13:47.384 - StatsDataProvider initialization completed
2025-07-21 11:13:47.386 - Loading summary statistics
2025-07-21 11:13:47.386 - Created singleton instance of StatsDataProvider on module import
2025-07-21 11:13:47.387 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 11:13:47.390 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 11:13:47.392 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:47.394 - get_stats_provider called, returning provider with initialized=True
2025-07-21 11:13:47.394 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 11:13:47.396 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:47.399 - Successfully loaded weekly stats: 7 days
2025-07-21 11:13:47.402 - Saved 10 items to cache
2025-07-21 11:13:47.403 - Background data loading completed
2025-07-21 22:32:09.082 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:09.089 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:09.124 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:09.125 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 22:32:09.126 - Initializing StatsDataProvider
2025-07-21 22:32:09.148 - Loaded 8 items from cache
2025-07-21 22:32:09.149 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 22:32:09.151 - Starting background data loading thread
2025-07-21 22:32:09.156 - Background data loading started
2025-07-21 22:32:09.156 - StatsDataProvider initialization completed
2025-07-21 22:32:09.158 - Loading summary statistics
2025-07-21 22:32:09.158 - Created singleton instance of StatsDataProvider on module import
2025-07-21 22:32:09.160 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 22:32:09.163 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 22:32:09.165 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:09.168 - get_stats_provider called, returning provider with initialized=True
2025-07-21 22:32:09.174 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 22:32:09.180 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:09.185 - Successfully loaded weekly stats: 7 days
2025-07-21 22:32:09.197 - Saved 10 items to cache
2025-07-21 22:32:09.198 - Background data loading completed
2025-07-21 22:32:30.339 - get_stats_provider called, returning provider with initialized=True
2025-07-21 22:32:48.505 - get_stats_provider called, returning provider with initialized=True
2025-07-21 22:59:35.632 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:35.633 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:35.646 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:35.647 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 22:59:35.648 - Initializing StatsDataProvider
2025-07-21 22:59:35.666 - Loaded 8 items from cache
2025-07-21 22:59:35.669 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 22:59:35.672 - Starting background data loading thread
2025-07-21 22:59:35.674 - Background data loading started
2025-07-21 22:59:35.674 - StatsDataProvider initialization completed
2025-07-21 22:59:35.675 - Loading summary statistics
2025-07-21 22:59:35.675 - Created singleton instance of StatsDataProvider on module import
2025-07-21 22:59:35.677 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 22:59:35.677 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 22:59:35.679 - get_stats_provider called, returning provider with initialized=True
2025-07-21 22:59:35.679 - Data from GameStatsIntegration: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:35.682 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 22:59:35.684 - Successfully loaded summary stats: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:35.691 - Saved 10 items to cache
2025-07-21 22:59:35.692 - Background data loading completed
2025-07-21 22:59:56.523 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:01:51.457 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:02:35.688 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:14:59.614 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:14:59.619 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:14:59.639 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:14:59.640 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 23:14:59.641 - Initializing StatsDataProvider
2025-07-21 23:14:59.664 - Loaded 8 items from cache
2025-07-21 23:14:59.668 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 23:14:59.678 - Starting background data loading thread
2025-07-21 23:14:59.680 - Background data loading started
2025-07-21 23:14:59.680 - StatsDataProvider initialization completed
2025-07-21 23:14:59.681 - Loading summary statistics
2025-07-21 23:14:59.681 - Created singleton instance of StatsDataProvider on module import
2025-07-21 23:14:59.682 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 23:14:59.683 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 23:14:59.683 - Data from GameStatsIntegration: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:14:59.688 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:14:59.688 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 23:14:59.689 - Successfully loaded summary stats: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:14:59.690 - Successfully loaded game history: 10 entries
2025-07-21 23:14:59.693 - Saved 10 items to cache
2025-07-21 23:14:59.694 - Background data loading completed
2025-07-21 23:15:21.382 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:24:57.698 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 336.0, 'daily_earnings': 336.0, 'daily_games': 17, 'wallet_balance': 60.0}
2025-07-21 23:24:57.703 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 336.0, 'daily_earnings': 336.0, 'daily_games': 17, 'wallet_balance': 60.0}
2025-07-21 23:24:57.723 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 336.0, 'daily_earnings': 336.0, 'daily_games': 17, 'wallet_balance': 60.0}
2025-07-21 23:24:57.724 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-21 23:24:57.725 - Initializing StatsDataProvider
2025-07-21 23:24:57.745 - Loaded 8 items from cache
2025-07-21 23:24:57.752 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-21 23:24:57.756 - Starting background data loading thread
2025-07-21 23:24:57.758 - Background data loading started
2025-07-21 23:24:57.758 - StatsDataProvider initialization completed
2025-07-21 23:24:57.760 - Loading summary statistics
2025-07-21 23:24:57.762 - Created singleton instance of StatsDataProvider on module import
2025-07-21 23:24:57.764 - Attempting to load summary stats from GameStatsIntegration
2025-07-21 23:24:57.764 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-21 23:24:57.766 - Data from GameStatsIntegration: {'total_earnings': 336.0, 'daily_earnings': 336.0, 'daily_games': 17, 'wallet_balance': 60.0}
2025-07-21 23:24:57.773 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:24:57.774 - Successfully loaded summary stats from GameStatsIntegration
2025-07-21 23:24:57.778 - Successfully loaded summary stats: {'total_earnings': 336.0, 'daily_earnings': 336.0, 'daily_games': 17, 'wallet_balance': 60.0}
2025-07-21 23:24:57.781 - Successfully loaded game history: 10 entries
2025-07-21 23:24:57.787 - Successfully loaded weekly stats: 7 days
2025-07-21 23:24:57.793 - Saved 10 items to cache
2025-07-21 23:24:57.794 - Background data loading completed
2025-07-21 23:25:30.909 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:27:05.709 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:28:23.357 - get_stats_provider called, returning provider with initialized=True
2025-07-21 23:29:41.170 - get_stats_provider called, returning provider with initialized=True
