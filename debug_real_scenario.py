#!/usr/bin/env python3
"""
Debug script to analyze the real scenario where pattern detection is failing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state_handler import GameState
from bingo_card import create_card_for_player

class MockPlayer:
    def __init__(self, cartela_no):
        self.cartela_no = cartela_no

class MockGame:
    def __init__(self):
        # Real scenario from the debug output
        self.called_numbers = [34, 2, 30, 49, 40, 20, 13, 37, 59, 51, 73, 71, 48, 62, 52, 65, 58, 33, 55, 32, 4, 70, 66, 8, 15, 21, 23, 5, 10, 9, 12, 6, 60, 44, 46, 29, 54, 43, 67, 27, 75, 69, 25, 31, 36, 24, 18, 57, 26, 7, 11, 22, 19, 38, 50, 39, 28, 35]
        self.current_number = 35
        self.players = [MockPlayer(1)]
        
    def play_winner_sound(self):
        print("WINNER SOUND PLAYED")
        return True
        
    def play_warning_sound(self):
        print("WARNING SOUND PLAYED")
        return True

def analyze_card_patterns():
    """Analyze the card and see what patterns should be detected"""
    print("ANALYZING REAL SCENARIO CARD PATTERNS")
    print("=" * 60)
    
    # Create mock game with real scenario data
    mock_game = MockGame()
    print(f"Called numbers: {len(mock_game.called_numbers)} calls")
    print(f"Current number: {mock_game.current_number}")
    print(f"Called numbers: {mock_game.called_numbers}")
    
    # Create game state handler to get the card
    game_state = GameState(mock_game)
    
    # Get the card for cartella 1
    try:
        card = game_state.game.bingo_logic.get_card_for_player(1)
        if not card:
            print("ERROR: Could not get card for cartella 1")
            return False
            
        print(f"\nCARD LAYOUT:")
        print("B   I   N   G   O")
        print("-------------------")
        for row in range(5):
            row_str = ""
            for col in range(5):
                num = card.grid[col][row]
                if num == 0:
                    row_str += "FREE*"
                else:
                    row_str += f"{num:2d}  "
            print(row_str)
        
        # Mark all called numbers on the card
        print(f"\nMARKING CALLED NUMBERS ON CARD:")
        for number in mock_game.called_numbers:
            was_marked = card.mark_number(number)
            if was_marked:
                print(f"  Marked: {number}")
        
        print(f"\nCARD MARKED NUMBERS: {sorted(card.marked)}")
        
        # Now let's manually check each pattern type
        print(f"\nMANUAL PATTERN ANALYSIS:")
        
        # Check rows
        print(f"\nROW ANALYSIS:")
        for row in range(5):
            row_numbers = []
            row_marked = []
            for col in range(5):
                num = card.grid[col][row]
                row_numbers.append(num)
                if num == 0:  # FREE space
                    row_marked.append(True)
                else:
                    row_marked.append(num in card.marked)
            
            all_marked = all(row_marked)
            print(f"  Row {row+1}: {row_numbers} -> Marked: {row_marked} -> Complete: {all_marked}")
        
        # Check columns
        print(f"\nCOLUMN ANALYSIS:")
        for col in range(5):
            col_numbers = []
            col_marked = []
            for row in range(5):
                num = card.grid[col][row]
                col_numbers.append(num)
                if num == 0:  # FREE space
                    col_marked.append(True)
                else:
                    col_marked.append(num in card.marked)
            
            all_marked = all(col_marked)
            print(f"  Column {col+1}: {col_numbers} -> Marked: {col_marked} -> Complete: {all_marked}")
        
        # Check diagonals
        print(f"\nDIAGONAL ANALYSIS:")
        
        # Main diagonal (top-left to bottom-right)
        diag1_numbers = []
        diag1_marked = []
        for i in range(5):
            num = card.grid[i][i]
            diag1_numbers.append(num)
            if num == 0:  # FREE space
                diag1_marked.append(True)
            else:
                diag1_marked.append(num in card.marked)
        
        all_marked = all(diag1_marked)
        print(f"  Main Diagonal: {diag1_numbers} -> Marked: {diag1_marked} -> Complete: {all_marked}")
        
        # Other diagonal (top-right to bottom-left)
        diag2_numbers = []
        diag2_marked = []
        for i in range(5):
            num = card.grid[i][4-i]
            diag2_numbers.append(num)
            if num == 0:  # FREE space
                diag2_marked.append(True)
            else:
                diag2_marked.append(num in card.marked)
        
        all_marked = all(diag2_marked)
        print(f"  Other Diagonal: {diag2_numbers} -> Marked: {diag2_marked} -> Complete: {all_marked}")
        
        # Check four corners
        print(f"\nFOUR CORNERS ANALYSIS:")
        corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
        corner_numbers = []
        corner_marked = []
        for col, row in corners:
            num = card.grid[col][row]
            corner_numbers.append(num)
            if num == 0:  # FREE space
                corner_marked.append(True)
            else:
                corner_marked.append(num in card.marked)
        
        all_marked = all(corner_marked)
        print(f"  Four Corners: {corner_numbers} -> Marked: {corner_marked} -> Complete: {all_marked}")
        
        # Now test the actual pattern detection
        print(f"\nTESTING ACTUAL PATTERN DETECTION:")
        has_patterns = card.check_winning_patterns(mock_game.called_numbers)
        print(f"check_winning_patterns() result: {has_patterns}")
        print(f"Detected patterns: {getattr(card, 'winning_patterns', [])}")
        
        return True
        
    except Exception as e:
        print(f"ERROR analyzing card: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the analysis"""
    print("REAL SCENARIO PATTERN DETECTION DEBUG")
    print("Analyzing why patterns are not being detected")
    print("=" * 80)
    
    success = analyze_card_patterns()
    
    if success:
        print(f"\nANALYSIS COMPLETED!")
    else:
        print(f"\nANALYSIS FAILED!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)