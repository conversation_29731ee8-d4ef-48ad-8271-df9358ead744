import sqlite3
import os

STATS_DB_PATH = os.path.join('data', 'stats.db')
TARGET_TOTAL = 39782.6667

conn = sqlite3.connect(STATS_DB_PATH)
cursor = conn.cursor()

# Get current total
cursor.execute('SELECT SUM(earnings) FROM daily_stats')
current_total = cursor.fetchone()[0]
print(f"Current total: {current_total}")
print(f"Target total: {TARGET_TOTAL}")

# Calculate adjustment needed
adjustment = TARGET_TOTAL - current_total
print(f"Adjustment needed: {adjustment}")

if abs(adjustment) > 0.001:
    # Find a day to adjust (not Wednesday)
    cursor.execute('''
    SELECT date, earnings FROM daily_stats 
    WHERE date != "2024-06-25" AND earnings > 0 
    ORDER BY earnings DESC LIMIT 1
    ''')
    
    result = cursor.fetchone()
    if result:
        adjust_date = result[0]
        current_earnings = result[1]
        new_earnings = current_earnings + adjustment
        
        print(f"Adjusting {adjust_date} from {current_earnings} to {new_earnings}")
        
        cursor.execute('UPDATE daily_stats SET earnings = ? WHERE date = ?', 
                      (new_earnings, adjust_date))
        
        conn.commit()
        
        # Verify
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        final_total = cursor.fetchone()[0]
        print(f"Final total: {final_total}")

conn.close()
print("Adjustment complete!")