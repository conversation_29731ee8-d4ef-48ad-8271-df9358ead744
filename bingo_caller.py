import random
import time
import threading
import os
import pygame
from announcer_language_manager import AnnouncerLanguageManager

class BingoCaller:
    """
    Manages the calling of bingo numbers with various calling patterns and timing options.
    """

    def __init__(self, total_numbers=75, callback=None):
        """
        Initialize a new BingoCaller instance.

        Args:
            total_numbers: The total number of bingo numbers (default: 75)
            callback: Optional callback function called when a new number is selected
        """
        self.total_numbers = total_numbers
        self.callback = callback
        self.called_numbers = []
        self.current_number = None
        self.last_call_time = 0  # Timestamp of the last number call
        self.calling_thread = None
        self.should_stop = False
        self.paused = False
        self.call_delay = 3.0  # Seconds between calls
        self.column_letters = ['B', 'I', 'N', 'G', 'O']

        # Signal to interrupt the current delay cycle
        self.interrupt_delay = False

        # Initialize audio cache for announcement sounds
        self.announcement_sounds = {}

        # Performance optimization: Track if we're in the middle of playing an announcement
        self.announcement_playing = False
        self.announcement_channel = None

        # Enhanced randomization system
        self._initialize_enhanced_randomization()

        # Try to reserve a dedicated channel for announcements
        try:
            self.announcement_channel = pygame.mixer.Channel(1)  # Use channel 1 for announcements
        except Exception as e:
            print(f"Could not reserve audio channel: {e}")

        # Preload common audio files in background
        self._preload_audio_files()

    def _initialize_enhanced_randomization(self):
        """Initialize the enhanced randomization system with hybrid techniques"""
        # Shuffled deck approach - pre-shuffle all numbers at game start
        self._shuffled_deck = list(range(1, self.total_numbers + 1))
        random.shuffle(self._shuffled_deck)
        self._deck_index = 0
        
        # Column tracking for balanced distribution (B=0, I=1, N=2, G=3, O=4)
        self._column_call_counts = [0, 0, 0, 0, 0]  # Track calls per column
        self._last_column_called = None
        
        # Recent number tracking to avoid clustering
        self._recent_numbers = []  # Track last 5-10 called numbers
        self._recent_ranges = []   # Track recent number ranges
        self._max_recent_tracking = 8  # Track last 8 numbers for pattern avoidance
        
        # Weighted selection pools for different phases of the game
        self._early_game_threshold = 15  # First 15 numbers use balanced approach
        self._mid_game_threshold = 45    # Numbers 16-45 use hybrid approach
        # Late game (46-75) uses more random approach for excitement
        
        # Consecutive number avoidance
        self._consecutive_avoidance_window = 3  # Avoid consecutive numbers within this range
        
        # Range balancing (divide 1-75 into 5 ranges of 15 numbers each)
        self._range_call_counts = [0, 0, 0, 0, 0]  # Track calls per range
        
        print("Enhanced randomization system initialized with hybrid techniques")

    def get_letter_for_number(self, number):
        """Get the BINGO letter for a given number"""
        if number < 1 or number > self.total_numbers:
            return ''

        # Calculate column (0-4) based on number range
        col = min(4, (number - 1) // 15)
        return self.column_letters[col]

    def _preload_audio_files(self):
        """
        Preload common audio files in the background to improve performance.
        This loads the first 10 numbers in each column (B1-B10, I16-I25, etc.)
        which are the most commonly called numbers.
        """
        try:
            # Get the selected announcer language from settings
            language = None
            try:
                from settings_manager import SettingsManager
                settings = SettingsManager()
                language = settings.get_setting('audio', 'announcer_language', 'Default (English)')
            except Exception as e:
                print(f"Error getting announcer language from settings: {e}")
                language = 'Default (English)'

            # Start a background thread to preload audio files
            threading.Thread(
                target=self._preload_audio_thread,
                args=(language,),
                daemon=True
            ).start()
        except Exception as e:
            print(f"Error starting preload thread: {e}")

    def _preload_audio_thread(self, language):
        """Background thread to preload audio files without blocking the main thread"""
        try:
            # Preload the first 10 numbers in each column
            for col in range(5):  # B, I, N, G, O
                start_num = col * 15 + 1
                for i in range(10):  # First 10 numbers in each column
                    num = start_num + i
                    if num <= self.total_numbers:
                        # Preload this number's announcement
                        self.get_announcement_sound(num, language, preloading=True)
                        # Small sleep to avoid overloading the system
                        time.sleep(0.01)
        except Exception as e:
            print(f"Error in preload thread: {e}")

    def get_announcement_sound(self, number, language=None, preloading=False):
        """
        Get the sound object for a number announcement.
        Caches sounds for better performance.

        Args:
            number: The bingo number to announce
            language: The language to use for the announcement (Default (English), Amharic, Oromo, etc.)
                     If None, uses English
            preloading: Whether this is being called during preloading (affects logging)
        """
        # Create a cache key that includes both number and language
        # Normalize language for caching
        cache_language = 'English'
        if language in ['Default', 'Default (English)', None]:
            cache_language = 'English'
        else:
            cache_language = language
            
        cache_key = f"{number}_{cache_language}"

        if cache_key in self.announcement_sounds:
            return self.announcement_sounds[cache_key]

        letter = self.get_letter_for_number(number)

        # Use the AnnouncerLanguageManager to get the audio path
        audio_path = AnnouncerLanguageManager.get_audio_path(number, letter, language)

        try:
            if os.path.exists(audio_path):
                sound = pygame.mixer.Sound(audio_path)
                self.announcement_sounds[cache_key] = sound
                if not preloading:
                    print(f"Loaded and cached sound for {letter}{number} in {language or 'English'}")
                return sound
            else:
                if not preloading:
                    print(f"Audio file not found: {audio_path}")
        except Exception as e:
            if not preloading:
                print(f"Error loading announcement sound {audio_path}: {e}")

        return None

    def play_announcement(self, number, language=None):
        """
        Play the audio announcement for a number

        Args:
            number: The bingo number to announce
            language: The language to use for the announcement (Default (English), Amharic, Oromo, etc.)
                     If None, uses the default language from settings
        """
        # Get the selected announcer language from settings if not specified
        if language is None:
            try:
                # Import here to avoid circular imports
                from settings_manager import SettingsManager
                settings = SettingsManager()
                language = settings.get_setting('audio', 'announcer_language', 'Default (English)')
            except Exception as e:
                print(f"Error getting announcer language from settings: {e}")
                language = 'Default (English)'

        # Get the sound
        sound = self.get_announcement_sound(number, language)
        if not sound:
            return False

        # Play the sound using the dedicated channel if available
        try:
            if self.announcement_channel:
                # Stop any currently playing announcement
                self.announcement_channel.stop()
                # Play on the dedicated channel
                self.announcement_channel.play(sound)
                self.announcement_playing = True
                return True
            else:
                # Fall back to regular play if no channel is available
                sound.play()
                self.announcement_playing = True
                return True
        except Exception as e:
            print(f"Error playing announcement: {e}")
            # Try fallback method
            try:
                sound.play()
                return True
            except:
                pass

        return False

    def is_announcement_playing(self):
        """Check if an announcement is currently playing"""
        if self.announcement_channel:
            return self.announcement_channel.get_busy()
        return False

    def call_next_number(self):
        """
        Call the next number using enhanced randomization techniques.
        Returns the newly called number or None if all numbers have been called.
        """
        # Check if all numbers have been called
        if len(self.called_numbers) >= self.total_numbers:
            return None

        # Use enhanced randomization to select the next number
        number = self._select_next_number_enhanced()
        
        if number is None:
            return None

        # Update state with the new number
        self.current_number = number
        self.called_numbers.append(number)
        
        # Update enhanced randomization tracking
        self._update_randomization_tracking(number)

        # Update the last call timestamp for animation purposes
        self.last_call_time = time.time()

        # Log the number being called for debugging (minimal logging)
        letter = self.get_letter_for_number(number)
        print(f"BingoCaller: Calling {letter}{number} (Enhanced)")

        # Play announcement for this number
        self.play_announcement(number)

        # Invoke callback if provided
        if self.callback:
            try:
                self.callback(self.current_number)
            except Exception as e:
                print(f"Error in bingo caller callback: {e}")

        return self.current_number

    def get_call_announcement(self, number=None):
        """Get a formatted announcement string for the called number"""
        if number is None:
            number = self.current_number

        if number is None:
            return "No number called"

        letter = self.get_letter_for_number(number)
        return f"{letter}-{number}"

    def _select_next_number_enhanced(self):
        """
        Enhanced number selection using hybrid randomization techniques
        """
        # Get uncalled numbers
        called_set = set(self.called_numbers)
        uncalled = [n for n in range(1, self.total_numbers + 1) if n not in called_set]
        
        if not uncalled:
            return None
        
        # Determine game phase
        calls_made = len(self.called_numbers)
        
        if calls_made < self._early_game_threshold:
            # Early game: Use balanced column approach
            return self._select_balanced_column_number(uncalled)
        elif calls_made < self._mid_game_threshold:
            # Mid game: Use hybrid approach
            return self._select_hybrid_number(uncalled)
        else:
            # Late game: Use weighted random with constraints
            return self._select_late_game_number(uncalled)
    
    def _select_balanced_column_number(self, uncalled):
        """Select number ensuring balanced column distribution"""
        # Group uncalled numbers by column
        column_numbers = [[], [], [], [], []]  # B, I, N, G, O
        
        for num in uncalled:
            col = min(4, (num - 1) // 15)
            column_numbers[col].append(num)
        
        # Find columns with least calls
        min_calls = min(self._column_call_counts)
        preferred_columns = [i for i, count in enumerate(self._column_call_counts) if count == min_calls]
        
        # Avoid same column as last call if possible
        if self._last_column_called is not None and len(preferred_columns) > 1:
            preferred_columns = [col for col in preferred_columns if col != self._last_column_called]
        
        # Select from preferred columns
        for col in preferred_columns:
            if column_numbers[col]:
                # Apply additional constraints within the column
                candidates = self._apply_constraints(column_numbers[col])
                if candidates:
                    return random.choice(candidates)
        
        # Fallback: select from any available column
        for col in range(5):
            if column_numbers[col]:
                candidates = self._apply_constraints(column_numbers[col])
                if candidates:
                    return random.choice(candidates)
        
        # Last resort: pure random
        return random.choice(uncalled)
    
    def _select_hybrid_number(self, uncalled):
        """Hybrid selection combining multiple techniques"""
        # Apply all constraints
        candidates = self._apply_constraints(uncalled)
        
        if not candidates:
            candidates = uncalled
        
        # Weight candidates based on multiple factors
        weighted_candidates = []
        
        for num in candidates:
            weight = 1.0
            
            # Column balancing weight
            col = min(4, (num - 1) // 15)
            col_calls = self._column_call_counts[col]
            avg_col_calls = sum(self._column_call_counts) / 5
            if col_calls < avg_col_calls:
                weight *= 1.5  # Prefer underrepresented columns
            
            # Range balancing weight
            range_idx = min(4, (num - 1) // 15)
            range_calls = self._range_call_counts[range_idx]
            avg_range_calls = sum(self._range_call_counts) / 5
            if range_calls < avg_range_calls:
                weight *= 1.3  # Prefer underrepresented ranges
            
            # Recent number avoidance weight
            if num in self._recent_numbers:
                weight *= 0.3  # Strongly avoid recent numbers
            
            # Add weighted instances
            for _ in range(int(weight * 10)):
                weighted_candidates.append(num)
        
        return random.choice(weighted_candidates) if weighted_candidates else random.choice(candidates)
    
    def _select_late_game_number(self, uncalled):
        """Late game selection with excitement factor"""
        # Apply basic constraints but allow more randomness
        candidates = self._apply_constraints(uncalled, strict=False)
        
        if not candidates:
            candidates = uncalled
        
        # Add some excitement by occasionally picking from shuffled deck
        if random.random() < 0.3:  # 30% chance to use shuffled deck
            return self._get_next_from_shuffled_deck(candidates)
        
        # Otherwise use weighted selection
        return self._select_hybrid_number(candidates)
    
    def _apply_constraints(self, numbers, strict=True):
        """Apply various constraints to filter number candidates"""
        candidates = numbers.copy()
        
        # Remove consecutive numbers if we have recent calls
        if self._recent_numbers and strict:
            filtered = []
            for num in candidates:
                is_consecutive = False
                for recent in self._recent_numbers[-self._consecutive_avoidance_window:]:
                    if abs(num - recent) <= 2:  # Avoid numbers within 2 of recent calls
                        is_consecutive = True
                        break
                if not is_consecutive:
                    filtered.append(num)
            if filtered:  # Only apply if we have alternatives
                candidates = filtered
        
        # Remove numbers from same column as last call (if strict)
        if self._last_column_called is not None and strict and len(candidates) > 5:
            filtered = []
            for num in candidates:
                col = min(4, (num - 1) // 15)
                if col != self._last_column_called:
                    filtered.append(num)
            if filtered:
                candidates = filtered
        
        return candidates
    
    def _get_next_from_shuffled_deck(self, available_numbers):
        """Get next number from pre-shuffled deck that's still available"""
        while self._deck_index < len(self._shuffled_deck):
            num = self._shuffled_deck[self._deck_index]
            self._deck_index += 1
            if num in available_numbers:
                return num
        
        # Deck exhausted, reshuffle remaining numbers
        remaining = [n for n in available_numbers if n not in self.called_numbers]
        if remaining:
            random.shuffle(remaining)
            return remaining[0]
        
        return random.choice(available_numbers) if available_numbers else None
    
    def _update_randomization_tracking(self, number):
        """Update tracking variables after a number is called"""
        # Update column tracking
        col = min(4, (number - 1) // 15)
        self._column_call_counts[col] += 1
        self._last_column_called = col
        
        # Update range tracking
        range_idx = min(4, (number - 1) // 15)
        self._range_call_counts[range_idx] += 1
        
        # Update recent numbers tracking
        self._recent_numbers.append(number)
        if len(self._recent_numbers) > self._max_recent_tracking:
            self._recent_numbers.pop(0)
        
        # Update recent ranges tracking
        self._recent_ranges.append(range_idx)
        if len(self._recent_ranges) > self._max_recent_tracking:
            self._recent_ranges.pop(0)

    def reset(self):
        """Reset the caller for a new game"""
        self.stop_calling()  # Stop any ongoing calling
        self.called_numbers = []
        self.current_number = None
        self.last_call_time = 0
        
        # Reset enhanced randomization system
        self._initialize_enhanced_randomization()

    def start_calling(self, delay=None):
        """
        Start automatically calling numbers with the specified delay.
        This runs in a separate thread to not block the main game loop.

        Args:
            delay: Time in seconds between calls (defaults to self.call_delay)
        """
        if delay is not None:
            self.call_delay = delay

        # Stop any existing thread
        self.stop_calling()

        # Start a new calling thread
        self.should_stop = False
        self.paused = False
        self.calling_thread = threading.Thread(target=self._calling_loop)
        self.calling_thread.daemon = True  # Thread won't prevent program exit
        self.calling_thread.start()

    def _calling_loop(self):
        """Internal method: Loop that calls numbers at timed intervals"""
        try:
            # Add a small initial delay before calling the first number
            # This ensures the UI is ready to display the first number
            initial_delay = 1.0  # 1 second initial delay
            print(f"BingoCaller: Adding {initial_delay}s initial delay before first number call")
            time.sleep(initial_delay)

            # Flag to track if this is the first number being called
            is_first_call = True

            # Performance optimization: Use a set for called numbers
            called_set = set()

            while not self.should_stop:
                try:
                    if not self.paused:
                        # Quick check if we've called all numbers
                        if len(called_set) >= self.total_numbers:
                            print(f"BingoCaller: All {self.total_numbers} numbers have been called. Stopping.")
                            self.paused = True
                            continue

                        # Wait for any current announcement to finish before calling next number
                        # This ensures announcements don't overlap and improves audio quality
                        if self.is_announcement_playing():
                            # Short sleep and continue the loop
                            time.sleep(0.1)
                            continue

                        # Call the next number
                        called_number = self.call_next_number()

                        # Verify the called number is valid
                        if called_number is not None:
                            # Update tracking
                            called_set.add(called_number)

                            # Log the first number call for debugging
                            if is_first_call:
                                print(f"BingoCaller: First number called: {called_number}")
                                is_first_call = False

                                # Force a small delay after the first number to ensure UI updates
                                time.sleep(0.2)  # Reduced from 0.5 to 0.2 for better performance
                        else:
                            # If no number was called, there might be an issue
                            print(f"BingoCaller: No number was called. Called numbers: {len(self.called_numbers)}/{self.total_numbers}")
                except Exception as e:
                    print(f"Error calling next number: {e}")
                    # Continue with the loop despite the error
                    time.sleep(0.5)  # Reduced from 1.0 to 0.5 for better performance
                    continue

                # Wait for the specified delay
                start_time = time.time()
                # Reset the interrupt flag at the start of each delay cycle
                self.interrupt_delay = False

                # More efficient delay loop with fewer checks
                check_interval = 0.1  # Check less frequently (100ms) for better performance
                next_check_time = start_time + check_interval

                while time.time() - start_time < self.call_delay:
                    # Only check at specific intervals rather than continuously
                    current_time = time.time()
                    if current_time >= next_check_time:
                        # Check for stop or interrupt signals
                        if self.should_stop or self.interrupt_delay:
                            if self.interrupt_delay:
                                print("BingoCaller: Delay cycle interrupted")
                            break  # Exit the delay loop immediately
                        next_check_time = current_time + check_interval

                    # Sleep for a short time to reduce CPU usage
                    time.sleep(0.05)
        except Exception as e:
            print(f"Fatal error in calling loop: {e}")
            # Try to reset the caller state
            self.should_stop = True
            self.paused = True

    def pause_calling(self):
        """Pause the automatic calling"""
        self.paused = True

    def resume_calling(self):
        """Resume the automatic calling"""
        self.paused = False

    def stop_calling(self):
        """Stop the automatic calling thread"""
        if self.calling_thread and self.calling_thread.is_alive():
            self.should_stop = True
            self.calling_thread.join(1.0)  # Wait up to 1 second for thread to end
            self.calling_thread = None

    def update_delay(self, new_delay):
        """Update the call delay and restart the calling thread if it's running

        Args:
            new_delay: New delay in seconds between number calls

        Returns:
            bool: True if the delay was updated and calling was restarted, False otherwise
        """
        # Store current state
        was_calling = self.calling_thread is not None and self.calling_thread.is_alive()

        # Update the delay
        old_delay = self.call_delay
        self.call_delay = new_delay
        print(f"BingoCaller: Updated call delay from {old_delay}s to {new_delay}s")

        # If the caller was running, interrupt the current delay cycle
        if was_calling:
            # Set the interrupt flag to break out of the current delay cycle
            self.interrupt_delay = True
            print(f"BingoCaller: Interrupting current delay cycle")

            # Give a short time for the interrupt to take effect
            time.sleep(0.1)

            # No need to restart the thread - the interrupt will cause the next cycle to use the new delay
            print(f"BingoCaller: Next number will be called with new delay: {new_delay}s")

            return True

        return False

    def get_called_count(self):
        """Get the count of called numbers"""
        return len(self.called_numbers)

    def is_number_called(self, number):
        """Check if a specific number has been called"""
        return number in self.called_numbers

    def is_recently_called(self, number, recent_timeframe=5.0):
        """
        Check if a number was called recently (within the specified timeframe).

        Args:
            number: The number to check
            recent_timeframe: Timeframe in seconds to consider "recent" (default: 5.0)

        Returns:
            bool: True if the number is the current number and was called within the timeframe
        """
        return (
            number == self.current_number and
            time.time() - self.last_call_time < recent_timeframe
        )

    def get_formatted_called_list(self):
        """Get a formatted string of all called numbers grouped by letter"""
        if not self.called_numbers:
            return "No numbers called"

        # Group by BINGO column
        columns = {letter: [] for letter in self.column_letters}

        for num in sorted(self.called_numbers):
            letter = self.get_letter_for_number(num)
            columns[letter].append(num)

        # Format the output
        result = []
        for letter in self.column_letters:
            nums = columns[letter]
            if nums:
                result.append(f"{letter}: {', '.join(str(n) for n in nums)}")

        return "\n".join(result)

    def get_randomization_stats(self):
        """Get statistics about the enhanced randomization system"""
        stats = {
            "column_distribution": {
                "B": self._column_call_counts[0],
                "I": self._column_call_counts[1], 
                "N": self._column_call_counts[2],
                "G": self._column_call_counts[3],
                "O": self._column_call_counts[4]
            },
            "range_distribution": {
                "1-15": self._range_call_counts[0],
                "16-30": self._range_call_counts[1],
                "31-45": self._range_call_counts[2], 
                "46-60": self._range_call_counts[3],
                "61-75": self._range_call_counts[4]
            },
            "recent_numbers": self._recent_numbers.copy(),
            "last_column": self.column_letters[self._last_column_called] if self._last_column_called is not None else None,
            "deck_progress": f"{self._deck_index}/{len(self._shuffled_deck)}",
            "calls_made": len(self.called_numbers),
            "game_phase": self._get_current_game_phase()
        }
        return stats
    
    def _get_current_game_phase(self):
        """Get the current game phase for statistics"""
        calls_made = len(self.called_numbers)
        if calls_made < self._early_game_threshold:
            return "Early Game (Balanced)"
        elif calls_made < self._mid_game_threshold:
            return "Mid Game (Hybrid)"
        else:
            return "Late Game (Weighted Random)"

    def call_specific_number(self, number):
        """
        Call a specific number (for testing or manual operation).
        Returns True if successful, False if number already called or invalid.
        """
        # Validate that the number is within the valid range (1-75)
        if not (1 <= number <= self.total_numbers):
            print(f"Error: Number {number} is outside valid range 1-{self.total_numbers}")
            return False

        # Check if all numbers have been called already
        if len(self.called_numbers) >= self.total_numbers:
            return False

        # Performance optimization: Direct check if number is in called_numbers
        if number in self.called_numbers:
            return False

        # Update state with the new number
        self.current_number = number
        self.called_numbers.append(number)
        
        # Update enhanced randomization tracking
        self._update_randomization_tracking(number)

        # Update the last call timestamp for animation purposes
        self.last_call_time = time.time()

        # Play announcement for this number using the selected language
        self.play_announcement(number)

        # Invoke callback if provided
        if self.callback:
            try:
                self.callback(number)
            except Exception as e:
                print(f"Error in bingo caller callback: {e}")

        return True