#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix tips values in the game_history table.

This script:
1. Recalculates tips for all existing records based on the requirement
2. Updates the database with the correct values
"""

import os
import sqlite3
import sys

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

def fix_tips_values():
    """Fix tips values in the game_history table."""
    print("Fixing tips values in game_history table...")
    
    # Check if database exists
    if not os.path.exists(STATS_DB_PATH):
        print(f"Database not found at: {STATS_DB_PATH}")
        return False
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get all records
        cursor.execute("""
        SELECT id, stake, players
        FROM game_history
        """)
        
        records = cursor.fetchall()
        updated_count = 0
        
        for record in records:
            record_id, stake, players = record
            
            # Calculate tips as the remainder when dividing by 10
            base_prize_pool = stake * players if stake and players else 0
            rounded_prize = (base_prize_pool // 10) * 10
            tips = max(0, base_prize_pool - rounded_prize)
            
            # Update the record
            cursor.execute("""
            UPDATE game_history 
            SET tips = ? 
            WHERE id = ?
            """, (tips, record_id))
            
            updated_count += 1
            
            # Show progress for large databases
            if updated_count % 100 == 0:
                print(f"   Updated {updated_count}/{len(records)} records...")
        
        conn.commit()
        conn.close()
        
        print(f"Updated tips for {updated_count} existing records.")
        return True
        
    except Exception as e:
        print(f"Error fixing tips values: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def main():
    """Main function."""
    print("TIPS VALUES FIX SCRIPT")
    print("=" * 50)
    
    success = fix_tips_values()
    
    if success:
        print("\nFix completed successfully!")
        print("The game history table now has correct tips values.")
    else:
        print("\nFix failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()