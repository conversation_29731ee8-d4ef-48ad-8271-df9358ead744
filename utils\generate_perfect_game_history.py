#!/usr/bin/env python3
"""
Script to generate perfect game history data that exactly matches the daily earnings.
Uses precise calculations to ensure exact commission amounts.
"""

import sqlite3
import os
import json
import random
from datetime import datetime

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

# Game configuration
GAME_CONFIG = {
    'commission_percentage': 20.0,
    'house_patterns': ['Full House', 'Line', 'Four Corners', 'X Pattern', 'T Pattern'],
    'game_statuses': ['Won', 'Completed', 'Finished']
}

# Realistic player names
PLAYER_NAMES = [
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>dr<PERSON>sefa', '<PERSON><PERSON>',
    '<PERSON>rek<PERSON>', 'Chaltu Negash', '<PERSON><PERSON><PERSON>', '<PERSON>eh<PERSON><PERSON>t Getachew', 'Getnet Tadele'
]

def create_perfect_game(date_time, target_commission, game_number=1):
    """Create a single game with exact commission amount."""
    
    # Calculate total bets needed for exact commission
    # Commission = Total_Bets * 0.20, so Total_Bets = Commission / 0.20
    total_bets = target_commission / 0.20
    prize_pool = total_bets - target_commission
    
    # Choose realistic player count and calculate exact stake
    players = random.choice([4, 5, 6, 7, 8])
    stake = total_bets / players  # This gives us the exact stake needed
    
    # Generate game details
    winner_name = random.choice(PLAYER_NAMES)
    winner_cartella = random.randint(1, 100)
    house_pattern = random.choice(GAME_CONFIG['house_patterns'])
    total_calls = random.randint(15, 44)
    game_duration = total_calls * 3 + random.randint(-30, 60)
    
    # Generate called numbers
    all_numbers = list(range(1, 76))
    called_numbers = sorted(random.sample(all_numbers, min(total_calls, 75)))
    
    # Create game details JSON
    details = json.dumps({
        'winner_cartella': winner_cartella,
        'claim_type': 'Full House',
        'game_duration': game_duration,
        'called_numbers': called_numbers,
        'game_type': 'Standard Bingo',
        'pattern_achieved': house_pattern,
        'game_number': game_number
    })
    
    return {
        'date_time': date_time.strftime('%Y-%m-%d %H:%M:%S'),
        'username': winner_name,
        'house': house_pattern,
        'stake': stake,
        'players': players,
        'total_calls': total_calls,
        'commission_percent': GAME_CONFIG['commission_percentage'],
        'fee': target_commission,  # Exact commission amount
        'total_prize': prize_pool,
        'details': details,
        'status': random.choice(GAME_CONFIG['game_statuses'])
    }

def generate_perfect_games_for_date(date_str, target_earnings):
    """Generate perfect game sessions for exact earnings match."""
    if target_earnings == 0:
        return []  # No games on days with zero earnings
    
    games = []
    base_date = datetime.strptime(date_str, '%Y-%m-%d')
    
    # Determine how to split earnings across games
    if target_earnings <= 250:
        # Single game
        game_commissions = [target_earnings]
    else:
        # Multiple games - split as evenly as possible
        if target_earnings == 500:
            game_commissions = [250, 250]
        elif target_earnings == 600:
            game_commissions = [200, 200, 200]
        elif target_earnings == 750:
            game_commissions = [250, 250, 250]
        elif target_earnings == 950:
            game_commissions = [300, 350, 300]
        else:
            # Default split for other amounts
            num_games = min(3, max(1, int(target_earnings / 200)))
            base_amount = target_earnings / num_games
            game_commissions = [base_amount] * (num_games - 1)
            game_commissions.append(target_earnings - sum(game_commissions))
    
    # Generate games with different times throughout the day
    for i, commission in enumerate(game_commissions):
        # Spread games throughout the day
        hour = 9 + (i * 4) + random.randint(0, 3)  # 9 AM, 1 PM, 5 PM, etc.
        if hour > 22:
            hour = 22
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        
        game_time = base_date.replace(hour=hour, minute=minute, second=second)
        game = create_perfect_game(game_time, commission, i + 1)
        games.append(game)
    
    total_commission = sum(game['fee'] for game in games)
    print(f"   Generated {len(games)} games for {date_str}: Target {target_earnings} ETB, Perfect {total_commission:.1f} ETB")
    return games

def clear_existing_history():
    """Clear existing game history for the date range."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
        DELETE FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        if deleted_count > 0:
            print(f"🗑️ Cleared {deleted_count} existing game history records")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing existing history: {e}")
        return False

def insert_perfect_game_history():
    """Insert perfect game history data into the database."""
    
    # Daily earnings data (matching the previously inserted data)
    daily_earnings = [
        ('2025-05-26', 500.0),  # Monday
        ('2025-05-27', 500.0),  # Tuesday  
        ('2025-05-28', 0.0),    # Wednesday (zero)
        ('2025-05-29', 180.0),  # Thursday
        ('2025-05-30', 600.0),  # Friday
        ('2025-05-31', 750.0),  # Saturday
        ('2025-06-01', 950.0),  # Sunday
    ]
    
    try:
        # Clear existing history first
        if not clear_existing_history():
            return False
        
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("🎮 Generating perfect game history data...")
        
        all_games = []
        
        # Generate games for each day
        for date_str, earnings in daily_earnings:
            day_games = generate_perfect_games_for_date(date_str, earnings)
            all_games.extend(day_games)
        
        # Insert all games into database
        print(f"\n📊 Inserting {len(all_games)} perfect game records...")
        
        for game in all_games:
            cursor.execute('''
            INSERT INTO game_history
            (date_time, username, house, stake, players, total_calls,
             commission_percent, fee, total_prize, details, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game['date_time'], game['username'], game['house'], 
                game['stake'], game['players'], game['total_calls'],
                game['commission_percent'], game['fee'], game['total_prize'],
                game['details'], game['status']
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ Perfect game history data inserted successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error inserting perfect game history: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def verify_perfect_game_history():
    """Verify the perfect game history data."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("\n📋 Verifying perfect game history data:")
        
        # Get detailed game information
        cursor.execute('''
        SELECT 
            DATE(date_time) as game_date,
            COUNT(*) as games_count,
            SUM(fee) as total_commission,
            SUM(total_prize) as total_prizes,
            AVG(players) as avg_players,
            MIN(stake) as min_stake,
            MAX(stake) as max_stake
        FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        GROUP BY DATE(date_time)
        ORDER BY game_date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   Date       | Games | Commission | Prizes   | Players | Stake Range")
            print("   -----------|-------|------------|----------|---------|------------")
            
            total_commission_check = 0
            for date, games, commission, prizes, avg_players, min_stake, max_stake in results:
                dt = datetime.strptime(date, '%Y-%m-%d')
                day_name = dt.strftime('%a')
                total_commission_check += commission
                print(f"   {date} ({day_name}) | {games:>5} | {commission:>10.1f} | {prizes:>8.1f} | {avg_players:>7.1f} | {min_stake:>5.1f}-{max_stake:<5.1f}")
            
            print("   -----------|-------|------------|----------|---------|------------")
            print(f"   TOTAL      |       | {total_commission_check:>10.1f} |          |         |")
            
            # Verify against expected total
            expected_total = 500 + 500 + 0 + 180 + 600 + 750 + 950
            print(f"\n✅ Expected total commission: {expected_total} ETB")
            print(f"✅ Actual total commission: {total_commission_check:.1f} ETB")
            print(f"✅ Perfect Match: {'YES' if abs(total_commission_check - expected_total) < 0.01 else 'NO'}")
            
            # Show sample game details
            print(f"\n📋 Sample Game Details:")
            cursor.execute('''
            SELECT date_time, username, house, stake, players, fee, total_prize
            FROM game_history 
            WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
            ORDER BY date_time
            LIMIT 5
            ''')
            
            sample_games = cursor.fetchall()
            print("   Date/Time           | Winner         | Pattern     | Stake | Players | Commission | Prize")
            print("   --------------------|----------------|-------------|-------|---------|------------|-------")
            
            for game in sample_games:
                date_time, username, house, stake, players, fee, prize = game
                dt = datetime.strptime(date_time, '%Y-%m-%d %H:%M:%S')
                formatted_time = dt.strftime('%m/%d %H:%M')
                print(f"   {formatted_time:<19} | {username:<14} | {house:<11} | {stake:>5.1f} | {players:>7} | {fee:>10.1f} | {prize:>6.1f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying perfect game history: {e}")

def main():
    """Main function to generate perfect game history."""
    print("🎯 Perfect Game History Data Generation")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists(STATS_DB_PATH):
        print(f"❌ Database not found: {STATS_DB_PATH}")
        print("Please run the stats data insertion script first.")
        return
    
    # Generate and insert perfect game history
    success = insert_perfect_game_history()
    
    if success:
        # Verify the data
        verify_perfect_game_history()
        print("\n🎉 Perfect game history generation completed successfully!")
        print("💡 The stats page will now show detailed game history with EXACT earnings match.")
    else:
        print("\n❌ Perfect game history generation failed!")

if __name__ == "__main__":
    main()
