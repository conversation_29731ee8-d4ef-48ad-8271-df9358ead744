"""
Stats Integration Module for the WOW Games application.

This module integrates the stats database with the game logic,
providing methods to record game events and retrieve statistics.
It uses the stats preloader for improved performance when available.
"""

import os
import json
import time
import pygame
from datetime import datetime
from stats_db import get_stats_db_manager

# Try to import stats preloader
try:
    from stats_preloader import get_stats_preloader
    PRELOADER_AVAILABLE = True
    print("Stats preloader available for integration")
except ImportError:
    PRELOADER_AVAILABLE = False
    print("Stats preloader not available, using direct database access")

def record_game_completed(game_data):
    """
    Record a completed game in the stats database.

    Args:
        game_data: Dictionary containing game data
            - winner_name: Name of the winner
            - winner_cartella: Cartella number of the winner
            - claim_type: Type of claim (e.g., 'Full House', 'First Line')
            - game_duration: Duration of the game in seconds
            - player_count: Number of players in the game
            - prize_amount: Prize amount for this game
            - commission_percentage: Commission percentage for this game
            - called_numbers: List of numbers called during the game
            - is_demo_mode: <PERSON><PERSON><PERSON> indicating if the game was in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if game_data.get("is_demo_mode", False):
        print("Game was in demo mode - statistics not updated")
        return False

    # Get stats database manager
    stats_db = get_stats_db_manager()

    # FIXED: Calculate fee based on total bets and commission percentage
    prize_amount = game_data.get("prize_amount", 0)
    commission_percentage = game_data.get("commission_percentage", 20)

    # Calculate total bets from stake and player count
    stake_amount = game_data.get("stake", 0)
    if stake_amount == 0:
        stake_amount = game_data.get("bet_amount", 25)
    player_count = game_data.get("player_count", 0)
    total_bets = stake_amount * player_count

    # CORRECT: Commission should be calculated on total bets, not prize amount
    fee = total_bets * (commission_percentage / 100)

    print(f"COMMISSION FIX: Stake: {stake_amount}, Players: {player_count}, Total bets: {total_bets}")
    print(f"COMMISSION FIX: Commission {commission_percentage}% = {fee} ETB (was incorrectly {prize_amount * (commission_percentage / 100)} ETB)")

    # Add game to history - FIXED to use correct stake amount
    stake_amount = game_data.get("stake", 0)
    if stake_amount == 0:
        stake_amount = game_data.get("bet_amount", 25)  # Updated default
    
    # Calculate tips according to requirements
    # The system will assume any missing numbers from the Prize pool amount,
    # i.e. or amounts less than ten in each game sessions prize pool as a tip
    player_count = game_data.get("player_count", 0)
    base_prize_pool = stake_amount * player_count
    
    # Round down to the nearest multiple of 10
    rounded_prize = (base_prize_pool // 10) * 10
    
    # The tip is the remainder (amount less than 10)
    tips = base_prize_pool - rounded_prize
    
    # Ensure tips is not negative
    tips = max(0, tips)
    
    # Adjust the total prize to reflect the rounded amount
    adjusted_prize = rounded_prize
    
    game_id = stats_db.add_game_to_history(
        username=game_data.get("winner_name", "Unknown"),
        house="Main House",
        stake=stake_amount,
        players=game_data.get("player_count", 0),
        total_calls=len(game_data.get("called_numbers", [])),
        commission_percent=commission_percentage,
        fee=fee,
        total_prize=adjusted_prize,
        details=json.dumps({
            "winner_cartella": game_data.get("winner_cartella", 0),
            "claim_type": game_data.get("claim_type", "Unknown"),
            "game_duration": game_data.get("game_duration", 0),
            "called_numbers": game_data.get("called_numbers", [])
        }),
        status="Won",
        tips=tips
    )
    print(f"DEBUG: stats_integration used stake amount: {stake_amount}")

    # Add enhanced logging for game winner recording
    print(f"Game winner recorded via stats_integration.py - ID: {game_id}, Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
    try:
        import logging
        logging.info(f"Game winner recorded via stats_integration.py - ID: {game_id}, Winner: {game_data.get('winner_name', 'Unknown')}, Status: Won")
    except ImportError:
        pass

    # Update wallet with fee
    if fee > 0:
        stats_db.add_wallet_transaction(
            amount=fee,
            transaction_type="fee",
            description=f"Commission from game #{game_id}"
        )

    # CRITICAL FIX: Clear all caches after recording a game to ensure fresh data
    try:
        print("CACHE FIX: Clearing all stats caches after game completion...")
        
        # Clear stats preloader cache
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()
            preloader.clear()
            print("CACHE FIX: Cleared stats preloader cache")
        except Exception as e:
            print(f"CACHE FIX: Could not clear preloader cache: {e}")
        
        # Clear instant loading cache
        try:
            from instant_loading.stats_loader import get_stats_loader
            loader = get_stats_loader()
            loader.clear_cache()
            print("CACHE FIX: Cleared instant loading cache")
        except Exception as e:
            print(f"CACHE FIX: Could not clear instant loading cache: {e}")
        
        # Clear centralized stats provider cache
        try:
            from stats_page import CentralizedStatsProvider
            # This will be handled by force_refresh_data below
            print("CACHE FIX: Will clear centralized stats provider cache")
        except Exception as e:
            print(f"CACHE FIX: Could not clear centralized stats cache: {e}")
        
        # Force refresh data to update UI
        force_refresh_data()
        print("CACHE FIX: Forced data refresh completed")
        
    except Exception as e:
        print(f"CACHE FIX: Error clearing caches: {e}")
        import traceback
        traceback.print_exc()

    return game_id > 0

def force_refresh_data():
    """
    PERFORMANCE FIX: Fast refresh of statistics data without heavy operations.

    This function performs a lightweight refresh that updates the UI quickly
    without blocking operations or extensive database synchronization.

    Returns:
        bool: True if refresh was successful, False otherwise
    """
    print("PERFORMANCE: Starting fast stats data refresh")

    try:
        # PERFORMANCE FIX: Only clear memory caches, not persistent files
        print("PERFORMANCE: Clearing memory caches only...")

        # Clear stats preloader memory cache only
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()
            if hasattr(preloader, 'cache') and hasattr(preloader.cache, 'cache'):
                # Clear only memory cache, not persistent cache
                preloader.cache.cache.clear()
                print("✓ Cleared preloader memory cache")
        except Exception as e:
            print(f"Info: Could not clear preloader cache: {e}")

        # PERFORMANCE FIX: Skip heavy database synchronization
        # Database sync is too slow and causes the 60-second delay
        print("PERFORMANCE: Skipping heavy database sync operations")

        # PERFORMANCE FIX: Get fresh data with timeout
        stats_db = get_stats_db_manager()

        # PERFORMANCE FIX: Quick data fetch with 1-second timeout
        import time
        start_time = time.time()
        timeout = 1.0  # 1 second maximum

        fresh_data = {}
        if time.time() - start_time < timeout:
            try:
                fresh_data['total_earnings'] = stats_db.get_total_earnings()
                fresh_data['daily_earnings'] = stats_db.get_daily_earnings()
                fresh_data['daily_games'] = stats_db.get_daily_games_played()
                fresh_data['wallet_balance'] = stats_db.get_wallet_balance()
                print("✓ Retrieved fresh summary data")
            except Exception as e:
                print(f"Info: Could not get fresh summary data: {e}")

        # PERFORMANCE FIX: Post lightweight refresh event
        try:
            import pygame

            # Check if pygame is initialized
            if pygame.get_init():
                # Create a lightweight refresh event
                refresh_event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'fast_refresh',
                    'source': 'force_refresh_data_fast',
                    'data': fresh_data,
                    'timestamp': time.time()
                })

                pygame.event.post(refresh_event)
                print("✓ Posted fast refresh event")
            else:
                print("Info: Pygame not initialized, skipping event posting")

            print("PERFORMANCE: Fast stats refresh completed successfully")
            return True

        except Exception as e:
            print(f"Warning: Could not post refresh event: {e}")
            return False

    except Exception as e:
        print(f"PERFORMANCE: Error during fast refresh: {e}")
        return False

def get_weekly_stats():
    """
    Get weekly statistics from the database.

    Returns:
        list: List of daily stats for the past week
    """
    stats_db = get_stats_db_manager()
    return stats_db.get_weekly_stats()

def record_game_started(player_count, bet_amount=50, is_demo_mode=False):
    """
    Record a game start event in the stats database.

    Args:
        player_count: Number of players in the game
        bet_amount: Bet amount per player
        is_demo_mode: Boolean indicating if the game is in demo mode

    Returns:
        bool: True if successful, False otherwise
    """
    # Skip if in demo mode
    if is_demo_mode:
        print("Game started in demo mode - statistics not updated")
        return False

    # Get stats database manager
    stats_db = get_stats_db_manager()

    # Update daily stats with player count
    today = datetime.now().strftime('%Y-%m-%d')
    return stats_db.update_daily_stats(
        date_str=today,
        total_players=player_count
    )

def get_stats_summary():
    """
    Get a summary of statistics for display on the stats page.
    Uses the preloader for improved performance when available.

    Returns:
        dict: Statistics summary
    """
    # Try to use preloader first for better performance
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()

            # Get cached data from preloader
            weekly_stats = preloader.get_cached_data('weekly_stats')
            total_earnings = preloader.get_cached_data('total_earnings')
            daily_earnings = preloader.get_cached_data('daily_earnings')
            daily_games = preloader.get_cached_data('daily_games')
            wallet_balance = preloader.get_cached_data('wallet_balance')

            # If we have all the data from the preloader, return it
            if weekly_stats and total_earnings is not None:
                return {
                    "weekly_stats": weekly_stats,
                    "total_earnings": total_earnings,
                    "daily_earnings": daily_earnings,
                    "daily_games": daily_games,
                    "wallet_balance": wallet_balance
                }

            # Otherwise, start preloading in the background for next time
            preloader.start_preloading()

        except Exception as e:
            print(f"Error using stats preloader: {e}")
            # Fall back to direct database access

    # Get stats database manager for direct access
    stats_db = get_stats_db_manager()

    # Get current date
    today = datetime.now().strftime('%Y-%m-%d')

    # Get weekly stats
    weekly_stats = stats_db.get_weekly_stats()

    # Calculate totals
    total_earnings = stats_db.get_total_earnings()
    daily_earnings = stats_db.get_daily_earnings()
    daily_games = stats_db.get_daily_games_played()
    wallet_balance = stats_db.get_wallet_balance()

    # Cache the results in the preloader if available
    if PRELOADER_AVAILABLE:
        try:
            preloader = get_stats_preloader()
            preloader.cache.set('weekly_stats', weekly_stats)
            preloader.cache.set('total_earnings', total_earnings)
            preloader.cache.set('daily_earnings', daily_earnings)
            preloader.cache.set('daily_games', daily_games)
            preloader.cache.set('wallet_balance', wallet_balance)
        except Exception as e:
            print(f"Error caching stats in preloader: {e}")

    return {
        "weekly_stats": weekly_stats,
        "total_earnings": total_earnings,
        "daily_earnings": daily_earnings,
        "daily_games": daily_games,
        "wallet_balance": wallet_balance
    }

def migrate_legacy_stats():
    """
    Migrate legacy stats from JSON to SQLite database.

    Returns:
        bool: True if successful, False otherwise
    """
    stats_db = get_stats_db_manager()
    return stats_db.migrate_from_json()

def add_wallet_transaction(amount, transaction_type, description):
    """
    Add a transaction to the wallet.

    Args:
        amount: Transaction amount
        transaction_type: Type of transaction (deposit, withdrawal, fee, etc.)
        description: Transaction description

    Returns:
        int: Transaction ID, or -1 if failed
    """
    stats_db = get_stats_db_manager()
    return stats_db.add_wallet_transaction(amount, transaction_type, description)

def get_game_history(page=0, page_size=10):
    """
    Get game history.
    Uses the preloader for improved performance when available.

    Args:
        page: Page number (0-based)
        page_size: Number of records per page

    Returns:
        tuple: (list of game history records, total pages)
    """
    try:
        # Try to use preloader first for better performance
        if PRELOADER_AVAILABLE:  # Preloader caches pages
            try:
                preloader = get_stats_preloader()

                # Only use cache for small page sizes
                if page_size <= 10:
                    history, total_pages = preloader.get_game_history_page(page, page_size)

                    # ENHANCED: Apply filtering to cached data as well
                    if history:
                        filtered_history = []
                        for game in history:
                            # CREDIT-STATS SYNC: Always include credit-based game records
                            if game.get('username', '') == 'Credit Deduction Game':
                                filtered_history.append(game)
                                continue

                            # Skip Game Reset entries with no meaningful activity
                            if (game.get('username', '') == 'Game Reset' and
                                game.get('total_calls', 0) == 0):
                                continue

                            # Skip demo games
                            if 'Demo' in game.get('username', ''):
                                continue

                            # Skip games with no activity (0 calls) unless they're credit-based
                            if (game.get('total_calls', 0) == 0 and
                                game.get('claim_type', '') != 'credit_based_recording'):
                                continue

                            # Include all other games (real games with activity)
                            filtered_history.append(game)

                        print(f"PRELOADER FILTER: Filtered {len(history)} cached records to {len(filtered_history)} meaningful games")
                        return filtered_history, total_pages
                    else:
                        return [], total_pages

                # Start preloading in the background for next time
                preloader.start_preloading()

            except Exception as e:
                print(f"Error using preloader for game history: {e}")
                # Fall back to direct database access

        # Use direct database access
        stats_db = get_stats_db_manager()
        result = stats_db.get_game_history(page, page_size)

        # Ensure we always return a valid tuple, even for empty databases
        if result and isinstance(result, (tuple, list)) and len(result) >= 2:
            history, total_pages = result[0], result[1]

            # ENHANCED: Filter out non-meaningful game entries for better user experience
            if history:
                filtered_history = []
                for game in history:
                    # CREDIT-STATS SYNC: Always include credit-based game records
                    if game.get('username', '') == 'Credit Deduction Game':
                        filtered_history.append(game)
                        continue

                    # Skip Game Reset entries with no meaningful activity
                    if (game.get('username', '') == 'Game Reset' and
                        game.get('total_calls', 0) == 0):
                        continue

                    # Skip demo games
                    if 'Demo' in game.get('username', ''):
                        continue

                    # Skip games with no activity (0 calls) unless they're credit-based
                    if (game.get('total_calls', 0) == 0 and
                        game.get('claim_type', '') != 'credit_based_recording'):
                        continue

                    # Include all other games (real games with activity)
                    filtered_history.append(game)

                print(f"STATS FILTER: Filtered {len(history)} raw records to {len(filtered_history)} meaningful games")
                return filtered_history, total_pages
            else:
                return [], max(1, total_pages)
        else:
            # Handle unexpected result format
            return [], 1

    except Exception as e:
        print(f"Error in get_game_history: {e}")
        # Return empty result for any errors (including post-cleanup scenarios)
        return [], 1
