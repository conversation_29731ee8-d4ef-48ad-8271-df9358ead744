import pygame
import math
import time

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GOLD = (255, 215, 0)
DARK_BLUE = (20, 30, 50)
LIGHT_BLUE = (40, 60, 100)
ORANGE = (255, 165, 0)
CYAN = (0, 255, 255)

def draw_wow_bingo_header(screen, scale_x, scale_y):
    """
    Draw the enhanced WOW Games header with professional animations in top-left corner

    Args:
        screen: The pygame screen surface to draw on
        scale_x: Horizontal scaling factor
        scale_y: Vertical scaling factor
    """
    screen_width, screen_height = screen.get_size()
    current_time = pygame.time.get_ticks()

    # Calculate adaptive values for responsive layout
    balanced_scale = min(scale_x, scale_y)

    # FIXED: Position logo in proper top-left corner
    logo_margin = int(15 * scale_x)  # Small margin from edge
    logo_x = logo_margin
    logo_y = int(15 * scale_y)  # Close to top edge

    # Compact logo dimensions for top-left corner
    logo_width = int(200 * scale_x)
    logo_height = int(60 * scale_y)

    # Create animated background for the logo
    _draw_animated_logo_background(screen, logo_x, logo_y, logo_width, logo_height, current_time, scale_x, scale_y)

    # Draw enhanced "WOW" text with animations
    _draw_animated_wow_text(screen, logo_x, logo_y, current_time, balanced_scale)

    # Draw enhanced "GAMES" text with animations
    _draw_animated_games_text(screen, logo_x, logo_y, current_time, balanced_scale)

    # Add professional glow effects
    _draw_logo_glow_effects(screen, logo_x, logo_y, logo_width, logo_height, current_time)

    # Calculate header height (compact for top positioning)
    header_height = logo_y + logo_height + int(10 * scale_y)

    # Draw subtle separator line below logo
    separator_y = header_height - int(5 * scale_y)
    _draw_animated_separator(screen, screen_width, separator_y, current_time, scale_x)

    return header_height

def _draw_animated_logo_background(screen, x, y, width, height, current_time, scale_x, scale_y):
    """Draw animated background for the logo."""
    # Create animated gradient background
    animation_phase = math.sin(current_time * 0.002) * 0.3 + 0.7

    # Professional gradient background
    background_rect = pygame.Rect(x - 5, y - 5, width + 10, height + 10)

    # Multi-layer gradient for depth
    gradient_steps = 20
    for i in range(gradient_steps):
        step_height = (height + 10) // gradient_steps
        step_y = y - 5 + (i * step_height)

        # Animated gradient colors
        ratio = i / gradient_steps
        base_intensity = animation_phase

        start_color = (int(25 * base_intensity), int(35 * base_intensity), int(60 * base_intensity))
        end_color = (int(45 * base_intensity), int(65 * base_intensity), int(100 * base_intensity))

        r = int(start_color[0] + (end_color[0] - start_color[0]) * ratio)
        g = int(start_color[1] + (end_color[1] - start_color[1]) * ratio)
        b = int(start_color[2] + (end_color[2] - start_color[2]) * ratio)

        step_rect = pygame.Rect(x - 5, step_y, width + 10, step_height + 1)
        pygame.draw.rect(screen, (r, g, b), step_rect)

    # Add animated border glow
    border_intensity = abs(math.sin(current_time * 0.004)) * 0.5 + 0.5
    border_color = (int(100 * border_intensity), int(150 * border_intensity), int(255 * border_intensity))
    pygame.draw.rect(screen, border_color, background_rect, 2, border_radius=8)

def _draw_animated_wow_text(screen, base_x, base_y, current_time, scale):
    """Draw animated 'WOW' text with professional effects."""
    # Position for WOW text
    wow_x = base_x + int(15 * scale)
    wow_y = base_y + int(8 * scale)

    # Animated font size
    size_animation = math.sin(current_time * 0.003) * 0.1 + 1.0
    font_size = int(32 * scale * size_animation)
    wow_font = pygame.font.SysFont("Arial", font_size, bold=True)

    # Individual letter colors with animation
    letter_colors = [
        (255, 100, 100),  # Red W
        (100, 255, 100),  # Green O
        (100, 100, 255)   # Blue W
    ]

    # Animate each letter individually
    letter_x = wow_x
    for i, letter in enumerate("WOW"):
        # Individual letter animation
        letter_phase = current_time * 0.005 + (i * 0.5)
        letter_intensity = abs(math.sin(letter_phase)) * 0.4 + 0.6

        # Animated color
        base_color = letter_colors[i]
        animated_color = (
            int(base_color[0] * letter_intensity),
            int(base_color[1] * letter_intensity),
            int(base_color[2] * letter_intensity)
        )

        # Draw shadow for depth
        shadow_offset = int(2 * scale)
        shadow_text = wow_font.render(letter, True, (20, 20, 20))
        screen.blit(shadow_text, (letter_x + shadow_offset, wow_y + shadow_offset))

        # Draw main letter
        letter_text = wow_font.render(letter, True, animated_color)
        screen.blit(letter_text, (letter_x, wow_y))

        # Add glow effect
        glow_surface = pygame.Surface(letter_text.get_size(), pygame.SRCALPHA)
        glow_text = wow_font.render(letter, True, (*animated_color, int(100 * letter_intensity)))
        glow_surface.blit(glow_text, (0, 0))
        screen.blit(glow_surface, (letter_x, wow_y))

        letter_x += letter_text.get_width() * 0.85

def _draw_animated_games_text(screen, base_x, base_y, current_time, scale):
    """Draw animated 'GAMES' text with professional effects."""
    # Position for GAMES text
    games_x = base_x + int(15 * scale)
    games_y = base_y + int(35 * scale)

    # Animated font size
    font_size = int(18 * scale)
    games_font = pygame.font.SysFont("Arial", font_size, bold=True)

    # Animated gold color
    gold_intensity = abs(math.sin(current_time * 0.004)) * 0.3 + 0.7
    animated_gold = (
        int(255 * gold_intensity),
        int(215 * gold_intensity),
        int(0 * gold_intensity + 100)
    )

    # Draw shadow
    shadow_offset = int(1 * scale)
    shadow_text = games_font.render("GAMES", True, (30, 30, 30))
    screen.blit(shadow_text, (games_x + shadow_offset, games_y + shadow_offset))

    # Draw main text
    games_text = games_font.render("GAMES", True, animated_gold)
    screen.blit(games_text, (games_x, games_y))

    # Add subtle highlight
    highlight_color = (255, 255, 200)
    highlight_text = games_font.render("GAMES", True, highlight_color)
    highlight_surface = pygame.Surface(highlight_text.get_size(), pygame.SRCALPHA)
    highlight_surface.blit(highlight_text, (0, 0))
    highlight_surface.set_alpha(int(80 * gold_intensity))
    screen.blit(highlight_surface, (games_x, games_y - 1))

def _draw_logo_glow_effects(screen, x, y, width, height, current_time):
    """Draw professional glow effects around the logo."""
    # Animated corner accents
    accent_size = 8
    accent_intensity = abs(math.sin(current_time * 0.006)) * 0.5 + 0.5
    accent_color = (int(255 * accent_intensity), int(200 * accent_intensity), int(100 * accent_intensity))

    # Top-left accent
    pygame.draw.circle(screen, accent_color, (x, y), accent_size)

    # Top-right accent
    pygame.draw.circle(screen, accent_color, (x + width, y), accent_size)

    # Subtle pulse effect around logo
    pulse_radius = int(5 + math.sin(current_time * 0.003) * 3)
    pulse_color = (*accent_color, 50)

    # Create pulse surface with alpha
    pulse_surface = pygame.Surface((width + pulse_radius * 2, height + pulse_radius * 2), pygame.SRCALPHA)
    pygame.draw.rect(pulse_surface, pulse_color,
                    (pulse_radius, pulse_radius, width, height),
                    border_radius=10)
    screen.blit(pulse_surface, (x - pulse_radius, y - pulse_radius))

def _draw_animated_separator(screen, screen_width, y, current_time, scale_x):
    """Draw animated separator line below the logo."""
    # Animated separator with gradient
    separator_length = int(300 * scale_x)  # Shorter separator for compact design
    separator_start_x = int(15 * scale_x)

    # Animated gradient line
    gradient_steps = 50
    for i in range(gradient_steps):
        step_width = separator_length // gradient_steps
        step_x = separator_start_x + (i * step_width)

        # Animated color intensity
        wave_phase = current_time * 0.005 + (i * 0.1)
        intensity = abs(math.sin(wave_phase)) * 0.6 + 0.4

        color = (
            int(100 * intensity),
            int(150 * intensity),
            int(255 * intensity)
        )

        pygame.draw.line(screen, color, (step_x, y), (step_x + step_width, y), 2)
