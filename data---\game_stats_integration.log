2025-07-16 13:48:32,573 - INFO - Started background data loading
2025-07-16 13:48:32,573 - INFO - OptimizedStats<PERSON>oader initialized
2025-07-16 13:48:32,573 - INFO - Using optimized stats loader for integration
2025-07-16 13:48:32,574 - ERROR - Error loading weekly stats: no such table: daily_stats
2025-07-16 13:48:32,574 - ERROR - Error loading summary data: no such table: daily_stats
2025-07-16 13:48:32,575 - ERROR - Error loading game history: no such table: game_history
2025-07-16 13:48:32,576 - INFO - Saved 0 items to cache
2025-07-16 13:48:32,576 - INFO - Background data loading completed
2025-07-16 13:48:33,396 - INFO - Database schema initialized successfully
2025-07-16 13:48:33,399 - INFO - Database schema initialized successfully
2025-07-16 13:48:33,400 - INFO - Stats database initialized successfully
2025-07-16 13:48:33,402 - INFO - Database schema initialized successfully
2025-07-16 13:48:33,403 - INFO - Stats database initialized successfully
2025-07-16 13:48:33,404 - INFO - Game stats integration module available
2025-07-16 13:48:33,405 - INFO - Started stats event worker thread
2025-07-16 13:48:33,405 - INFO - Stats event hooks initialized
2025-07-16 13:48:33,493 - INFO - New encryption key generated
2025-07-16 13:48:33,493 - INFO - Database security initialized successfully
2025-07-16 13:48:33,496 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:33", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:33,496 - INFO - Created secure database connection
2025-07-16 13:48:33,906 - INFO - Database schema initialized successfully
2025-07-16 13:48:37,989 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:48:37,989 - INFO - Sync thread started
2025-07-16 13:48:37,989 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-16 13:48:37,992 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-16 13:48:38,691 - INFO - Hooked into game's start_game method
2025-07-16 13:48:38,692 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-16 13:48:44,528 - INFO - Hooked into game's start_game method
2025-07-16 13:48:44,529 - INFO - Bingo Favor Mode initialized (DEVELOPER FEATURE)
2025-07-16 13:48:46,404 - INFO - Loaded 0 items from persistent cache
2025-07-16 13:48:46,404 - INFO - Stats cache initialized
2025-07-16 13:48:46,405 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:46", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:46,406 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:46", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:46,407 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-16 13:48:46,407 - INFO - Stats preloader initialized
2025-07-16 13:48:46,407 - INFO - Starting stats data preloading
2025-07-16 13:48:46,407 - INFO - Started stats data preloading
2025-07-16 13:48:46,409 - INFO - Loading data using optimized functions
2025-07-16 13:48:46,409 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:46,410 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:46", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:46,414 - INFO - Preloaded weekly stats for 7 days
2025-07-16 13:48:46,414 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:46,414 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:46", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:46,416 - INFO - Preloaded summary data
2025-07-16 13:48:46,416 - INFO - Preloaded game history (0 records)
2025-07-16 13:48:46,417 - INFO - Preloaded wallet data
2025-07-16 13:48:46,417 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-16 13:48:46,419 - INFO - Saved 8 items to persistent cache
2025-07-16 13:48:47,003 - INFO - Stats performance monitor initialized
2025-07-16 13:48:47,019 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-16 13:48:47,403 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,405 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,408 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,409 - INFO - Stats file data\stats.json not found, nothing to migrate
2025-07-16 13:48:47,409 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,412 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,412 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,414 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,415 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,419 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,420 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,425 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,425 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,427 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,429 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,430 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,431 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,436 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,437 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,443 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,443 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,445 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,446 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,448 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,448 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,454 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,455 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,459 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,459 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,461 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,462 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,464 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,464 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,466 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,467 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:47,469 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:47,469 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:47", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:48,462 - INFO - Admin button added to stats page
2025-07-16 13:48:48,479 - INFO - generate_background: 0.0078s
2025-07-16 13:48:48,557 - INFO - draw_background: 0.0930s
2025-07-16 13:48:48,570 - INFO - draw_stats_page: 0.1059s
2025-07-16 13:48:48,594 - INFO - draw_background: 0.0010s
2025-07-16 13:48:48,597 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:48,727 - INFO - draw_background: 0.0000s
2025-07-16 13:48:48,731 - INFO - draw_stats_page: 0.0041s
2025-07-16 13:48:48,863 - INFO - draw_background: 0.0009s
2025-07-16 13:48:48,866 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:48,996 - INFO - draw_background: 0.0010s
2025-07-16 13:48:48,997 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:49,000 - INFO - draw_stats_page: 0.0055s
2025-07-16 13:48:49,129 - INFO - draw_background: 0.0010s
2025-07-16 13:48:49,133 - INFO - draw_stats_page: 0.0051s
2025-07-16 13:48:49,262 - INFO - draw_background: 0.0009s
2025-07-16 13:48:49,265 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:49,395 - INFO - draw_background: 0.0011s
2025-07-16 13:48:49,399 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:49,526 - INFO - draw_background: 0.0009s
2025-07-16 13:48:49,530 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:49,658 - INFO - draw_background: 0.0010s
2025-07-16 13:48:49,659 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:49,662 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:49,792 - INFO - draw_background: 0.0010s
2025-07-16 13:48:49,796 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:49,924 - INFO - draw_background: 0.0010s
2025-07-16 13:48:49,927 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:48:50,057 - INFO - draw_background: 0.0000s
2025-07-16 13:48:50,063 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:50,192 - INFO - draw_background: 0.0010s
2025-07-16 13:48:50,196 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:50,323 - INFO - draw_background: 0.0010s
2025-07-16 13:48:50,324 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:50,328 - INFO - draw_stats_page: 0.0060s
2025-07-16 13:48:50,457 - INFO - draw_background: 0.0010s
2025-07-16 13:48:50,461 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:50,591 - INFO - draw_background: 0.0005s
2025-07-16 13:48:50,594 - INFO - draw_stats_page: 0.0041s
2025-07-16 13:48:50,723 - INFO - draw_background: 0.0010s
2025-07-16 13:48:50,727 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:48:50,856 - INFO - draw_background: 0.0010s
2025-07-16 13:48:50,860 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:50,990 - INFO - draw_background: 0.0015s
2025-07-16 13:48:50,990 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:50,997 - INFO - draw_stats_page: 0.0076s
2025-07-16 13:48:51,122 - INFO - draw_background: 0.0020s
2025-07-16 13:48:51,126 - INFO - draw_stats_page: 0.0060s
2025-07-16 13:48:51,255 - INFO - draw_background: 0.0010s
2025-07-16 13:48:51,260 - INFO - draw_stats_page: 0.0061s
2025-07-16 13:48:51,389 - INFO - draw_background: 0.0000s
2025-07-16 13:48:51,393 - INFO - draw_stats_page: 0.0061s
2025-07-16 13:48:51,523 - INFO - draw_background: 0.0010s
2025-07-16 13:48:51,527 - INFO - draw_stats_page: 0.0060s
2025-07-16 13:48:51,656 - INFO - draw_background: 0.0010s
2025-07-16 13:48:51,657 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:51,660 - INFO - draw_stats_page: 0.0055s
2025-07-16 13:48:51,723 - INFO - draw_background: 0.0010s
2025-07-16 13:48:51,727 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:51,791 - INFO - draw_background: 0.0013s
2025-07-16 13:48:51,794 - INFO - draw_stats_page: 0.0043s
2025-07-16 13:48:51,923 - INFO - draw_background: 0.0009s
2025-07-16 13:48:51,926 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:52,056 - INFO - draw_background: 0.0010s
2025-07-16 13:48:52,060 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:52,190 - INFO - draw_background: 0.0005s
2025-07-16 13:48:52,192 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:52,196 - INFO - draw_stats_page: 0.0061s
2025-07-16 13:48:52,323 - INFO - draw_background: 0.0010s
2025-07-16 13:48:52,326 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:48:52,456 - INFO - draw_background: 0.0009s
2025-07-16 13:48:52,460 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:52,589 - INFO - draw_background: 0.0015s
2025-07-16 13:48:52,594 - INFO - draw_stats_page: 0.0059s
2025-07-16 13:48:52,722 - INFO - draw_background: 0.0010s
2025-07-16 13:48:52,726 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:52,855 - INFO - draw_background: 0.0010s
2025-07-16 13:48:52,856 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:52,860 - INFO - draw_stats_page: 0.0055s
2025-07-16 13:48:52,922 - INFO - draw_background: 0.0010s
2025-07-16 13:48:52,926 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:53,056 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,059 - INFO - draw_stats_page: 0.0046s
2025-07-16 13:48:53,123 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,126 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:53,255 - INFO - draw_background: 0.0009s
2025-07-16 13:48:53,258 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:53,322 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,323 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:53,326 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:53,455 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,459 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:53,521 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,525 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:53,654 - INFO - draw_background: 0.0010s
2025-07-16 13:48:53,657 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:53,789 - INFO - draw_background: 0.0016s
2025-07-16 13:48:53,793 - INFO - draw_stats_page: 0.0051s
2025-07-16 13:48:53,922 - INFO - draw_background: 0.0009s
2025-07-16 13:48:53,922 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:53,926 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:48:54,055 - INFO - draw_background: 0.0009s
2025-07-16 13:48:54,058 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:48:54,189 - INFO - draw_background: 0.0000s
2025-07-16 13:48:54,193 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:54,323 - INFO - draw_background: 0.0010s
2025-07-16 13:48:54,326 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:48:54,391 - INFO - draw_background: 0.0005s
2025-07-16 13:48:54,395 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:54,523 - INFO - draw_background: 0.0010s
2025-07-16 13:48:54,524 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:54,527 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:48:54,656 - INFO - draw_background: 0.0009s
2025-07-16 13:48:54,660 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:48:54,790 - INFO - draw_background: 0.0011s
2025-07-16 13:48:54,793 - INFO - draw_stats_page: 0.0044s
2025-07-16 13:48:54,923 - INFO - draw_background: 0.0009s
2025-07-16 13:48:54,927 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:48:54,990 - INFO - draw_background: 0.0010s
2025-07-16 13:48:54,994 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:48:55,133 - INFO - load_statistics: 0.0118s
2025-07-16 13:48:55,134 - INFO - Starting stats data preloading
2025-07-16 13:48:55,135 - INFO - Started stats data preloading
2025-07-16 13:48:55,135 - INFO - Loading data using optimized functions
2025-07-16 13:48:55,136 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:48:55,136 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:55,137 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,138 - INFO - DB Operation: {"timestamp": "2025-07-16 13:48:55", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:48:55,143 - INFO - Preloaded weekly stats for 7 days
2025-07-16 13:48:55,151 - INFO - Preloaded summary data
2025-07-16 13:48:55,155 - INFO - Preloaded game history (0 records)
2025-07-16 13:48:55,157 - INFO - Preloaded wallet data
2025-07-16 13:48:55,157 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-16 13:48:55,159 - INFO - Saved 8 items to persistent cache
2025-07-16 13:48:55,174 - INFO - Background thread started
2025-07-16 13:48:55,175 - INFO - Hybrid database integration initialized
2025-07-16 13:48:55,213 - INFO - draw_stats_page: 0.0760s
2025-07-16 13:48:55,234 - INFO - draw_background: 0.0020s
2025-07-16 13:48:55,275 - INFO - draw_stats_page: 0.0429s
2025-07-16 13:48:55,367 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,407 - INFO - draw_stats_page: 0.0412s
2025-07-16 13:48:55,500 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,538 - INFO - draw_stats_page: 0.0385s
2025-07-16 13:48:55,634 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,673 - INFO - draw_stats_page: 0.0408s
2025-07-16 13:48:55,674 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:55,765 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,806 - INFO - draw_stats_page: 0.0413s
2025-07-16 13:48:55,899 - INFO - draw_background: 0.0010s
2025-07-16 13:48:55,937 - INFO - draw_stats_page: 0.0394s
2025-07-16 13:48:56,031 - INFO - draw_background: 0.0010s
2025-07-16 13:48:56,074 - INFO - draw_stats_page: 0.0438s
2025-07-16 13:48:56,164 - INFO - draw_background: 0.0009s
2025-07-16 13:48:56,205 - INFO - draw_stats_page: 0.0412s
2025-07-16 13:48:56,297 - INFO - draw_background: 0.0009s
2025-07-16 13:48:56,337 - INFO - draw_stats_page: 0.0414s
2025-07-16 13:48:56,338 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:56,431 - INFO - draw_background: 0.0015s
2025-07-16 13:48:56,472 - INFO - draw_stats_page: 0.0429s
2025-07-16 13:48:56,563 - INFO - draw_background: 0.0009s
2025-07-16 13:48:56,604 - INFO - draw_stats_page: 0.0416s
2025-07-16 13:48:56,695 - INFO - draw_background: 0.0013s
2025-07-16 13:48:56,736 - INFO - draw_stats_page: 0.0428s
2025-07-16 13:48:56,828 - INFO - draw_background: 0.0000s
2025-07-16 13:48:56,873 - INFO - draw_stats_page: 0.0458s
2025-07-16 13:48:56,962 - INFO - draw_background: 0.0010s
2025-07-16 13:48:57,006 - INFO - draw_stats_page: 0.0443s
2025-07-16 13:48:57,007 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:57,095 - INFO - draw_background: 0.0010s
2025-07-16 13:48:57,137 - INFO - draw_stats_page: 0.0425s
2025-07-16 13:48:57,238 - INFO - draw_background: 0.0010s
2025-07-16 13:48:57,276 - INFO - draw_stats_page: 0.0398s
2025-07-16 13:48:57,378 - INFO - draw_background: 0.0009s
2025-07-16 13:48:57,420 - INFO - draw_stats_page: 0.0430s
2025-07-16 13:48:57,485 - INFO - draw_background: 0.0009s
2025-07-16 13:48:57,527 - INFO - draw_stats_page: 0.0424s
2025-07-16 13:48:57,632 - INFO - draw_background: 0.0010s
2025-07-16 13:48:57,673 - INFO - draw_stats_page: 0.0418s
2025-07-16 13:48:57,674 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:57,777 - INFO - draw_background: 0.0000s
2025-07-16 13:48:57,819 - INFO - draw_stats_page: 0.0413s
2025-07-16 13:48:57,922 - INFO - draw_background: 0.0009s
2025-07-16 13:48:57,969 - INFO - draw_stats_page: 0.0474s
2025-07-16 13:48:58,072 - INFO - draw_background: 0.0010s
2025-07-16 13:48:58,115 - INFO - draw_stats_page: 0.0441s
2025-07-16 13:48:58,217 - INFO - draw_background: 0.0010s
2025-07-16 13:48:58,255 - INFO - draw_stats_page: 0.0395s
2025-07-16 13:48:58,357 - INFO - draw_background: 0.0009s
2025-07-16 13:48:58,402 - INFO - draw_stats_page: 0.0454s
2025-07-16 13:48:58,403 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:58,819 - INFO - draw_background: 0.0015s
2025-07-16 13:48:58,942 - INFO - draw_stats_page: 0.1243s
2025-07-16 13:48:58,999 - INFO - draw_background: 0.0015s
2025-07-16 13:48:59,060 - INFO - draw_stats_page: 0.0626s
2025-07-16 13:48:59,108 - INFO - draw_background: 0.0000s
2025-07-16 13:48:59,175 - INFO - draw_stats_page: 0.0684s
2025-07-16 13:48:59,217 - INFO - draw_background: 0.0010s
2025-07-16 13:48:59,272 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:48:59,272 - WARNING - Failed to connect to RethinkDB
2025-07-16 13:48:59,290 - INFO - draw_stats_page: 0.0741s
2025-07-16 13:48:59,332 - INFO - draw_background: 0.0022s
2025-07-16 13:48:59,436 - INFO - draw_stats_page: 0.1070s
2025-07-16 13:48:59,437 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:48:59,482 - INFO - draw_background: 0.0010s
2025-07-16 13:48:59,535 - INFO - draw_stats_page: 0.0538s
2025-07-16 13:48:59,640 - INFO - draw_background: 0.0000s
2025-07-16 13:48:59,689 - INFO - draw_stats_page: 0.0488s
2025-07-16 13:48:59,798 - INFO - draw_background: 0.0010s
2025-07-16 13:48:59,842 - INFO - draw_stats_page: 0.0452s
2025-07-16 13:48:59,947 - INFO - draw_background: 0.0000s
2025-07-16 13:49:00,020 - INFO - draw_stats_page: 0.0717s
2025-07-16 13:49:00,059 - INFO - draw_background: 0.0000s
2025-07-16 13:49:00,116 - INFO - draw_stats_page: 0.0575s
2025-07-16 13:49:00,117 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:00,165 - INFO - draw_background: 0.0010s
2025-07-16 13:49:00,228 - INFO - draw_stats_page: 0.0631s
2025-07-16 13:49:00,268 - INFO - draw_background: 0.0010s
2025-07-16 13:49:00,322 - INFO - draw_stats_page: 0.0548s
2025-07-16 13:49:00,427 - INFO - draw_background: 0.0005s
2025-07-16 13:49:00,473 - INFO - draw_stats_page: 0.0480s
2025-07-16 13:49:00,582 - INFO - draw_background: 0.0010s
2025-07-16 13:49:00,631 - INFO - draw_stats_page: 0.0496s
2025-07-16 13:49:00,736 - INFO - draw_background: 0.0000s
2025-07-16 13:49:00,783 - INFO - draw_stats_page: 0.0475s
2025-07-16 13:49:00,784 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:00,890 - INFO - draw_background: 0.0010s
2025-07-16 13:49:00,950 - INFO - draw_stats_page: 0.0612s
2025-07-16 13:49:00,995 - INFO - draw_background: 0.0030s
2025-07-16 13:49:01,037 - INFO - draw_stats_page: 0.0454s
2025-07-16 13:49:01,174 - INFO - draw_background: 0.0020s
2025-07-16 13:49:01,221 - INFO - draw_stats_page: 0.0487s
2025-07-16 13:49:01,257 - INFO - draw_background: 0.0010s
2025-07-16 13:49:01,300 - INFO - draw_stats_page: 0.0439s
2025-07-16 13:49:01,402 - INFO - draw_background: 0.0009s
2025-07-16 13:49:01,445 - INFO - draw_stats_page: 0.0430s
2025-07-16 13:49:01,445 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:01,548 - INFO - draw_background: 0.0009s
2025-07-16 13:49:01,593 - INFO - draw_stats_page: 0.0449s
2025-07-16 13:49:01,694 - INFO - draw_background: 0.0020s
2025-07-16 13:49:01,736 - INFO - draw_stats_page: 0.0432s
2025-07-16 13:49:01,839 - INFO - draw_background: 0.0010s
2025-07-16 13:49:01,881 - INFO - draw_stats_page: 0.0428s
2025-07-16 13:49:01,983 - INFO - draw_background: 0.0009s
2025-07-16 13:49:02,026 - INFO - draw_stats_page: 0.0441s
2025-07-16 13:49:02,128 - INFO - draw_background: 0.0000s
2025-07-16 13:49:02,171 - INFO - draw_stats_page: 0.0437s
2025-07-16 13:49:02,172 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:02,316 - INFO - draw_background: 0.0020s
2025-07-16 13:49:02,380 - INFO - draw_stats_page: 0.0660s
2025-07-16 13:49:02,430 - INFO - draw_background: 0.0010s
2025-07-16 13:49:02,514 - INFO - draw_stats_page: 0.0854s
2025-07-16 13:49:02,559 - INFO - draw_background: 0.0025s
2025-07-16 13:49:02,612 - INFO - draw_stats_page: 0.0549s
2025-07-16 13:49:02,660 - INFO - draw_background: 0.0010s
2025-07-16 13:49:02,713 - INFO - draw_stats_page: 0.0540s
2025-07-16 13:49:02,821 - INFO - draw_background: 0.0010s
2025-07-16 13:49:02,882 - INFO - draw_stats_page: 0.0616s
2025-07-16 13:49:02,954 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:02,998 - INFO - draw_background: 0.0010s
2025-07-16 13:49:03,053 - INFO - draw_stats_page: 0.0559s
2025-07-16 13:49:03,157 - INFO - draw_background: 0.0000s
2025-07-16 13:49:03,202 - INFO - draw_stats_page: 0.0446s
2025-07-16 13:49:03,308 - INFO - draw_background: 0.0010s
2025-07-16 13:49:03,386 - INFO - draw_stats_page: 0.0792s
2025-07-16 13:49:03,422 - INFO - draw_background: 0.0010s
2025-07-16 13:49:03,498 - INFO - draw_stats_page: 0.0704s
2025-07-16 13:49:03,578 - INFO - draw_background: 0.0040s
2025-07-16 13:49:03,631 - INFO - draw_stats_page: 0.0582s
2025-07-16 13:49:03,632 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:03,678 - INFO - draw_background: 0.0000s
2025-07-16 13:49:03,722 - INFO - draw_stats_page: 0.0450s
2025-07-16 13:49:03,827 - INFO - draw_background: 0.0010s
2025-07-16 13:49:03,876 - INFO - draw_stats_page: 0.0505s
2025-07-16 13:49:03,981 - INFO - draw_background: 0.0010s
2025-07-16 13:49:04,033 - INFO - draw_stats_page: 0.0524s
2025-07-16 13:49:04,137 - INFO - draw_background: 0.0000s
2025-07-16 13:49:04,186 - INFO - draw_stats_page: 0.0495s
2025-07-16 13:49:04,294 - INFO - draw_background: 0.0010s
2025-07-16 13:49:04,341 - INFO - draw_stats_page: 0.0474s
2025-07-16 13:49:04,343 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:04,446 - INFO - draw_background: 0.0010s
2025-07-16 13:49:04,494 - INFO - draw_stats_page: 0.0492s
2025-07-16 13:49:04,611 - INFO - draw_background: 0.0015s
2025-07-16 13:49:04,710 - INFO - draw_stats_page: 0.1007s
2025-07-16 13:49:04,811 - INFO - draw_background: 0.0021s
2025-07-16 13:49:04,879 - INFO - draw_stats_page: 0.0710s
2025-07-16 13:49:04,915 - INFO - draw_background: 0.0000s
2025-07-16 13:49:04,977 - INFO - draw_stats_page: 0.0622s
2025-07-16 13:49:05,026 - INFO - draw_background: 0.0020s
2025-07-16 13:49:05,083 - INFO - draw_stats_page: 0.0587s
2025-07-16 13:49:05,084 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:05,136 - INFO - draw_background: 0.0030s
2025-07-16 13:49:05,203 - INFO - draw_stats_page: 0.0706s
2025-07-16 13:49:05,246 - INFO - draw_background: 0.0010s
2025-07-16 13:49:05,301 - INFO - draw_stats_page: 0.0556s
2025-07-16 13:49:05,412 - INFO - draw_background: 0.0036s
2025-07-16 13:49:05,464 - INFO - draw_stats_page: 0.0538s
2025-07-16 13:49:05,574 - INFO - draw_background: 0.0021s
2025-07-16 13:49:05,652 - INFO - draw_stats_page: 0.0804s
2025-07-16 13:49:05,698 - INFO - draw_background: 0.0020s
2025-07-16 13:49:05,753 - INFO - draw_stats_page: 0.0572s
2025-07-16 13:49:05,756 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:05,797 - INFO - draw_background: 0.0010s
2025-07-16 13:49:05,849 - INFO - draw_stats_page: 0.0532s
2025-07-16 13:49:05,959 - INFO - draw_background: 0.0018s
2025-07-16 13:49:06,013 - INFO - draw_stats_page: 0.0558s
2025-07-16 13:49:06,074 - INFO - draw_background: 0.0020s
2025-07-16 13:49:06,122 - INFO - draw_stats_page: 0.0492s
2025-07-16 13:49:06,226 - INFO - draw_background: 0.0000s
2025-07-16 13:49:06,268 - INFO - draw_stats_page: 0.0430s
2025-07-16 13:49:06,375 - INFO - draw_background: 0.0020s
2025-07-16 13:49:06,419 - INFO - draw_stats_page: 0.0479s
2025-07-16 13:49:06,422 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:06,528 - INFO - draw_background: 0.0000s
2025-07-16 13:49:06,562 - INFO - draw_stats_page: 0.0345s
2025-07-16 13:49:06,664 - INFO - draw_background: 0.0010s
2025-07-16 13:49:06,697 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:06,801 - INFO - draw_background: 0.0010s
2025-07-16 13:49:06,835 - INFO - draw_stats_page: 0.0347s
2025-07-16 13:49:06,938 - INFO - draw_background: 0.0010s
2025-07-16 13:49:06,982 - INFO - draw_stats_page: 0.0448s
2025-07-16 13:49:07,085 - INFO - draw_background: 0.0009s
2025-07-16 13:49:07,125 - INFO - draw_stats_page: 0.0409s
2025-07-16 13:49:07,125 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:07,228 - INFO - draw_background: 0.0020s
2025-07-16 13:49:07,272 - INFO - draw_stats_page: 0.0468s
2025-07-16 13:49:07,378 - INFO - draw_background: 0.0009s
2025-07-16 13:49:07,416 - INFO - draw_stats_page: 0.0391s
2025-07-16 13:49:07,522 - INFO - draw_background: 0.0010s
2025-07-16 13:49:07,558 - INFO - draw_stats_page: 0.0365s
2025-07-16 13:49:07,657 - INFO - draw_background: 0.0000s
2025-07-16 13:49:07,695 - INFO - draw_stats_page: 0.0378s
2025-07-16 13:49:07,807 - INFO - draw_background: 0.0000s
2025-07-16 13:49:07,842 - INFO - draw_stats_page: 0.0347s
2025-07-16 13:49:07,843 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:07,949 - INFO - draw_background: 0.0000s
2025-07-16 13:49:07,989 - INFO - draw_stats_page: 0.0409s
2025-07-16 13:49:08,096 - INFO - draw_background: 0.0010s
2025-07-16 13:49:08,157 - INFO - draw_stats_page: 0.0614s
2025-07-16 13:49:08,201 - INFO - draw_background: 0.0005s
2025-07-16 13:49:08,248 - INFO - draw_stats_page: 0.0479s
2025-07-16 13:49:08,351 - INFO - draw_background: 0.0005s
2025-07-16 13:49:08,394 - INFO - draw_stats_page: 0.0437s
2025-07-16 13:49:08,503 - INFO - draw_background: 0.0010s
2025-07-16 13:49:08,542 - INFO - draw_stats_page: 0.0398s
2025-07-16 13:49:08,543 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:08,646 - INFO - draw_background: 0.0009s
2025-07-16 13:49:08,680 - INFO - draw_stats_page: 0.0345s
2025-07-16 13:49:08,782 - INFO - draw_background: 0.0000s
2025-07-16 13:49:08,824 - INFO - draw_stats_page: 0.0424s
2025-07-16 13:49:08,931 - INFO - draw_background: 0.0010s
2025-07-16 13:49:08,977 - INFO - draw_stats_page: 0.0459s
2025-07-16 13:49:09,086 - INFO - draw_background: 0.0010s
2025-07-16 13:49:09,120 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:09,224 - INFO - draw_background: 0.0010s
2025-07-16 13:49:09,259 - INFO - draw_stats_page: 0.0358s
2025-07-16 13:49:09,261 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:09,361 - INFO - draw_background: 0.0008s
2025-07-16 13:49:09,430 - INFO - draw_stats_page: 0.0694s
2025-07-16 13:49:09,481 - INFO - draw_background: 0.0005s
2025-07-16 13:49:09,542 - INFO - draw_stats_page: 0.0599s
2025-07-16 13:49:09,644 - INFO - draw_background: 0.0010s
2025-07-16 13:49:09,698 - INFO - draw_stats_page: 0.0551s
2025-07-16 13:49:09,806 - INFO - draw_background: 0.0000s
2025-07-16 13:49:09,856 - INFO - draw_stats_page: 0.0509s
2025-07-16 13:49:09,975 - INFO - draw_background: 0.0031s
2025-07-16 13:49:10,047 - INFO - draw_stats_page: 0.0759s
2025-07-16 13:49:10,050 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:10,099 - INFO - draw_background: 0.0011s
2025-07-16 13:49:10,167 - INFO - draw_stats_page: 0.0689s
2025-07-16 13:49:10,213 - INFO - draw_background: 0.0005s
2025-07-16 13:49:10,274 - INFO - draw_stats_page: 0.0617s
2025-07-16 13:49:10,325 - INFO - draw_background: 0.0020s
2025-07-16 13:49:10,394 - INFO - draw_stats_page: 0.0709s
2025-07-16 13:49:10,451 - INFO - draw_background: 0.0031s
2025-07-16 13:49:10,518 - INFO - draw_stats_page: 0.0698s
2025-07-16 13:49:10,574 - INFO - draw_background: 0.0031s
2025-07-16 13:49:10,639 - INFO - draw_stats_page: 0.0684s
2025-07-16 13:49:10,641 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:10,685 - INFO - draw_background: 0.0010s
2025-07-16 13:49:10,744 - INFO - draw_stats_page: 0.0607s
2025-07-16 13:49:10,787 - INFO - draw_background: 0.0008s
2025-07-16 13:49:10,843 - INFO - draw_stats_page: 0.0555s
2025-07-16 13:49:10,948 - INFO - draw_background: 0.0005s
2025-07-16 13:49:11,032 - INFO - draw_stats_page: 0.0847s
2025-07-16 13:49:11,072 - INFO - draw_background: 0.0011s
2025-07-16 13:49:11,132 - INFO - draw_stats_page: 0.0606s
2025-07-16 13:49:11,176 - INFO - draw_background: 0.0010s
2025-07-16 13:49:11,237 - INFO - draw_stats_page: 0.0615s
2025-07-16 13:49:11,238 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:11,278 - INFO - draw_background: 0.0006s
2025-07-16 13:49:11,337 - INFO - draw_stats_page: 0.0609s
2025-07-16 13:49:11,380 - INFO - draw_background: 0.0011s
2025-07-16 13:49:11,443 - INFO - draw_stats_page: 0.0631s
2025-07-16 13:49:11,487 - INFO - draw_background: 0.0018s
2025-07-16 13:49:11,542 - INFO - draw_stats_page: 0.0569s
2025-07-16 13:49:11,648 - INFO - draw_background: 0.0010s
2025-07-16 13:49:11,711 - INFO - draw_stats_page: 0.0645s
2025-07-16 13:49:11,753 - INFO - draw_background: 0.0023s
2025-07-16 13:49:11,820 - INFO - draw_stats_page: 0.0700s
2025-07-16 13:49:11,821 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:11,865 - INFO - draw_background: 0.0012s
2025-07-16 13:49:11,921 - INFO - draw_stats_page: 0.0572s
2025-07-16 13:49:11,970 - INFO - draw_background: 0.0027s
2025-07-16 13:49:12,032 - INFO - draw_stats_page: 0.0651s
2025-07-16 13:49:12,075 - INFO - draw_background: 0.0012s
2025-07-16 13:49:12,162 - INFO - draw_stats_page: 0.0882s
2025-07-16 13:49:12,203 - INFO - draw_background: 0.0005s
2025-07-16 13:49:12,274 - INFO - draw_stats_page: 0.0711s
2025-07-16 13:49:12,320 - INFO - draw_background: 0.0034s
2025-07-16 13:49:12,387 - INFO - draw_stats_page: 0.0705s
2025-07-16 13:49:12,387 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:12,435 - INFO - draw_background: 0.0047s
2025-07-16 13:49:12,493 - INFO - draw_stats_page: 0.0632s
2025-07-16 13:49:12,542 - INFO - draw_background: 0.0010s
2025-07-16 13:49:12,607 - INFO - draw_stats_page: 0.0657s
2025-07-16 13:49:12,650 - INFO - draw_background: 0.0011s
2025-07-16 13:49:12,704 - INFO - draw_stats_page: 0.0542s
2025-07-16 13:49:12,810 - INFO - draw_background: 0.0000s
2025-07-16 13:49:12,890 - INFO - draw_stats_page: 0.0810s
2025-07-16 13:49:12,926 - INFO - draw_background: 0.0000s
2025-07-16 13:49:12,978 - INFO - draw_stats_page: 0.0525s
2025-07-16 13:49:12,979 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:13,084 - INFO - draw_background: 0.0020s
2025-07-16 13:49:13,126 - INFO - draw_stats_page: 0.0440s
2025-07-16 13:49:13,231 - INFO - draw_background: 0.0010s
2025-07-16 13:49:13,275 - INFO - draw_stats_page: 0.0448s
2025-07-16 13:49:13,382 - INFO - draw_background: 0.0010s
2025-07-16 13:49:13,424 - INFO - draw_stats_page: 0.0430s
2025-07-16 13:49:13,528 - INFO - draw_background: 0.0000s
2025-07-16 13:49:13,575 - INFO - draw_stats_page: 0.0460s
2025-07-16 13:49:13,677 - INFO - draw_background: 0.0000s
2025-07-16 13:49:13,723 - INFO - draw_stats_page: 0.0455s
2025-07-16 13:49:13,723 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:13,831 - INFO - draw_background: 0.0015s
2025-07-16 13:49:13,873 - INFO - draw_stats_page: 0.0443s
2025-07-16 13:49:13,979 - INFO - draw_background: 0.0010s
2025-07-16 13:49:14,026 - INFO - draw_stats_page: 0.0472s
2025-07-16 13:49:14,131 - INFO - draw_background: 0.0010s
2025-07-16 13:49:14,175 - INFO - draw_stats_page: 0.0444s
2025-07-16 13:49:14,277 - INFO - draw_background: 0.0000s
2025-07-16 13:49:14,321 - INFO - draw_stats_page: 0.0431s
2025-07-16 13:49:14,423 - INFO - draw_background: 0.0009s
2025-07-16 13:49:14,470 - INFO - draw_stats_page: 0.0484s
2025-07-16 13:49:14,471 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:14,571 - INFO - draw_background: 0.0000s
2025-07-16 13:49:14,618 - INFO - draw_stats_page: 0.0465s
2025-07-16 13:49:14,721 - INFO - draw_background: 0.0010s
2025-07-16 13:49:14,768 - INFO - draw_stats_page: 0.0481s
2025-07-16 13:49:14,872 - INFO - draw_background: 0.0010s
2025-07-16 13:49:14,920 - INFO - draw_stats_page: 0.0485s
2025-07-16 13:49:15,025 - INFO - draw_background: 0.0010s
2025-07-16 13:49:15,071 - INFO - draw_stats_page: 0.0466s
2025-07-16 13:49:15,175 - INFO - draw_background: 0.0010s
2025-07-16 13:49:15,219 - INFO - draw_stats_page: 0.0456s
2025-07-16 13:49:15,221 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:15,323 - INFO - draw_background: 0.0009s
2025-07-16 13:49:15,370 - INFO - draw_stats_page: 0.0472s
2025-07-16 13:49:15,470 - INFO - draw_background: 0.0000s
2025-07-16 13:49:15,520 - INFO - draw_stats_page: 0.0509s
2025-07-16 13:49:15,624 - INFO - draw_background: 0.0009s
2025-07-16 13:49:15,666 - INFO - draw_stats_page: 0.0435s
2025-07-16 13:49:15,767 - INFO - draw_background: 0.0010s
2025-07-16 13:49:15,816 - INFO - draw_stats_page: 0.0503s
2025-07-16 13:49:15,918 - INFO - draw_background: 0.0000s
2025-07-16 13:49:15,959 - INFO - draw_stats_page: 0.0415s
2025-07-16 13:49:15,960 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:16,065 - INFO - draw_background: 0.0010s
2025-07-16 13:49:16,116 - INFO - draw_stats_page: 0.0512s
2025-07-16 13:49:16,217 - INFO - draw_background: 0.0000s
2025-07-16 13:49:16,257 - INFO - draw_stats_page: 0.0410s
2025-07-16 13:49:16,362 - INFO - draw_background: 0.0010s
2025-07-16 13:49:16,402 - INFO - draw_stats_page: 0.0409s
2025-07-16 13:49:16,502 - INFO - draw_background: 0.0010s
2025-07-16 13:49:16,550 - INFO - draw_stats_page: 0.0488s
2025-07-16 13:49:16,653 - INFO - draw_background: 0.0009s
2025-07-16 13:49:16,699 - INFO - draw_stats_page: 0.0468s
2025-07-16 13:49:16,699 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:16,801 - INFO - draw_background: 0.0010s
2025-07-16 13:49:16,843 - INFO - draw_stats_page: 0.0431s
2025-07-16 13:49:16,944 - INFO - draw_background: 0.0010s
2025-07-16 13:49:16,989 - INFO - draw_stats_page: 0.0458s
2025-07-16 13:49:17,091 - INFO - draw_background: 0.0010s
2025-07-16 13:49:17,142 - INFO - draw_stats_page: 0.0522s
2025-07-16 13:49:17,246 - INFO - draw_background: 0.0010s
2025-07-16 13:49:17,290 - INFO - draw_stats_page: 0.0449s
2025-07-16 13:49:17,395 - INFO - draw_background: 0.0010s
2025-07-16 13:49:17,439 - INFO - draw_stats_page: 0.0449s
2025-07-16 13:49:17,440 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:17,542 - INFO - draw_background: 0.0000s
2025-07-16 13:49:17,588 - INFO - draw_stats_page: 0.0467s
2025-07-16 13:49:17,688 - INFO - draw_background: 0.0000s
2025-07-16 13:49:17,729 - INFO - draw_stats_page: 0.0415s
2025-07-16 13:49:17,833 - INFO - draw_background: 0.0009s
2025-07-16 13:49:17,877 - INFO - draw_stats_page: 0.0457s
2025-07-16 13:49:17,977 - INFO - draw_background: 0.0000s
2025-07-16 13:49:18,022 - INFO - draw_stats_page: 0.0438s
2025-07-16 13:49:18,124 - INFO - draw_background: 0.0010s
2025-07-16 13:49:18,167 - INFO - draw_stats_page: 0.0438s
2025-07-16 13:49:18,168 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:18,270 - INFO - draw_background: 0.0011s
2025-07-16 13:49:18,322 - INFO - draw_stats_page: 0.0533s
2025-07-16 13:49:18,428 - INFO - draw_background: 0.0010s
2025-07-16 13:49:18,472 - INFO - draw_stats_page: 0.0452s
2025-07-16 13:49:18,573 - INFO - draw_background: 0.0009s
2025-07-16 13:49:18,620 - INFO - draw_stats_page: 0.0482s
2025-07-16 13:49:18,722 - INFO - draw_background: 0.0010s
2025-07-16 13:49:18,784 - INFO - draw_stats_page: 0.0622s
2025-07-16 13:49:18,824 - INFO - draw_background: 0.0020s
2025-07-16 13:49:18,886 - INFO - draw_stats_page: 0.0640s
2025-07-16 13:49:18,887 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:18,924 - INFO - draw_background: 0.0010s
2025-07-16 13:49:18,978 - INFO - draw_stats_page: 0.0549s
2025-07-16 13:49:19,084 - INFO - draw_background: 0.0000s
2025-07-16 13:49:19,133 - INFO - draw_stats_page: 0.0504s
2025-07-16 13:49:19,243 - INFO - draw_background: 0.0010s
2025-07-16 13:49:19,287 - INFO - draw_stats_page: 0.0447s
2025-07-16 13:49:19,389 - INFO - draw_background: 0.0005s
2025-07-16 13:49:19,435 - INFO - draw_stats_page: 0.0457s
2025-07-16 13:49:19,537 - INFO - draw_background: 0.0000s
2025-07-16 13:49:19,581 - INFO - draw_stats_page: 0.0437s
2025-07-16 13:49:19,582 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:19,687 - INFO - draw_background: 0.0009s
2025-07-16 13:49:19,727 - INFO - draw_stats_page: 0.0408s
2025-07-16 13:49:19,830 - INFO - draw_background: 0.0005s
2025-07-16 13:49:19,879 - INFO - draw_stats_page: 0.0488s
2025-07-16 13:49:19,985 - INFO - draw_background: 0.0010s
2025-07-16 13:49:20,036 - INFO - draw_stats_page: 0.0513s
2025-07-16 13:49:20,140 - INFO - draw_background: 0.0010s
2025-07-16 13:49:20,195 - INFO - draw_stats_page: 0.0561s
2025-07-16 13:49:20,300 - INFO - draw_background: 0.0010s
2025-07-16 13:49:20,382 - INFO - draw_stats_page: 0.0831s
2025-07-16 13:49:20,383 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:20,500 - INFO - draw_background: 0.0025s
2025-07-16 13:49:20,571 - INFO - draw_stats_page: 0.0743s
2025-07-16 13:49:20,620 - INFO - draw_background: 0.0010s
2025-07-16 13:49:20,745 - INFO - draw_stats_page: 0.1262s
2025-07-16 13:49:20,809 - INFO - draw_background: 0.0016s
2025-07-16 13:49:20,885 - INFO - draw_stats_page: 0.0778s
2025-07-16 13:49:20,937 - INFO - draw_background: 0.0010s
2025-07-16 13:49:21,011 - INFO - draw_stats_page: 0.0735s
2025-07-16 13:49:21,057 - INFO - draw_background: 0.0020s
2025-07-16 13:49:21,135 - INFO - draw_stats_page: 0.0802s
2025-07-16 13:49:21,137 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:21,182 - INFO - draw_background: 0.0031s
2025-07-16 13:49:21,257 - INFO - draw_stats_page: 0.0784s
2025-07-16 13:49:21,318 - INFO - draw_background: 0.0010s
2025-07-16 13:49:21,392 - INFO - draw_stats_page: 0.0752s
2025-07-16 13:49:21,438 - INFO - draw_background: 0.0010s
2025-07-16 13:49:21,504 - INFO - draw_stats_page: 0.0676s
2025-07-16 13:49:21,550 - INFO - draw_background: 0.0029s
2025-07-16 13:49:21,623 - INFO - draw_stats_page: 0.0755s
2025-07-16 13:49:21,667 - INFO - draw_background: 0.0020s
2025-07-16 13:49:21,734 - INFO - draw_stats_page: 0.0695s
2025-07-16 13:49:21,735 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:21,781 - INFO - draw_background: 0.0026s
2025-07-16 13:49:21,853 - INFO - draw_stats_page: 0.0741s
2025-07-16 13:49:21,900 - INFO - draw_background: 0.0030s
2025-07-16 13:49:21,974 - INFO - draw_stats_page: 0.0771s
2025-07-16 13:49:22,033 - INFO - draw_background: 0.0020s
2025-07-16 13:49:22,095 - INFO - draw_stats_page: 0.0640s
2025-07-16 13:49:22,135 - INFO - draw_background: 0.0020s
2025-07-16 13:49:22,190 - INFO - draw_stats_page: 0.0559s
2025-07-16 13:49:22,293 - INFO - draw_background: 0.0010s
2025-07-16 13:49:22,356 - INFO - draw_stats_page: 0.0635s
2025-07-16 13:49:22,357 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:22,396 - INFO - draw_background: 0.0020s
2025-07-16 13:49:22,449 - INFO - draw_stats_page: 0.0542s
2025-07-16 13:49:22,551 - INFO - draw_background: 0.0010s
2025-07-16 13:49:22,613 - INFO - draw_stats_page: 0.0627s
2025-07-16 13:49:22,652 - INFO - draw_background: 0.0010s
2025-07-16 13:49:22,747 - INFO - draw_stats_page: 0.0961s
2025-07-16 13:49:22,785 - INFO - draw_background: 0.0010s
2025-07-16 13:49:22,847 - INFO - draw_stats_page: 0.0625s
2025-07-16 13:49:22,890 - INFO - draw_background: 0.0016s
2025-07-16 13:49:22,946 - INFO - draw_stats_page: 0.0585s
2025-07-16 13:49:22,948 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:22,990 - INFO - draw_background: 0.0015s
2025-07-16 13:49:23,040 - INFO - draw_stats_page: 0.0521s
2025-07-16 13:49:23,144 - INFO - draw_background: 0.0000s
2025-07-16 13:49:23,195 - INFO - draw_stats_page: 0.0512s
2025-07-16 13:49:23,298 - INFO - draw_background: 0.0010s
2025-07-16 13:49:23,361 - INFO - draw_stats_page: 0.0645s
2025-07-16 13:49:23,400 - INFO - draw_background: 0.0009s
2025-07-16 13:49:23,455 - INFO - draw_stats_page: 0.0563s
2025-07-16 13:49:23,560 - INFO - draw_background: 0.0011s
2025-07-16 13:49:23,617 - INFO - draw_stats_page: 0.0578s
2025-07-16 13:49:23,618 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:23,725 - INFO - draw_background: 0.0010s
2025-07-16 13:49:23,777 - INFO - draw_stats_page: 0.0534s
2025-07-16 13:49:23,882 - INFO - draw_background: 0.0010s
2025-07-16 13:49:23,933 - INFO - draw_stats_page: 0.0525s
2025-07-16 13:49:24,041 - INFO - draw_background: 0.0010s
2025-07-16 13:49:24,086 - INFO - draw_stats_page: 0.0464s
2025-07-16 13:49:24,191 - INFO - draw_background: 0.0005s
2025-07-16 13:49:24,233 - INFO - draw_stats_page: 0.0431s
2025-07-16 13:49:24,333 - INFO - draw_background: 0.0010s
2025-07-16 13:49:24,371 - INFO - draw_stats_page: 0.0388s
2025-07-16 13:49:24,372 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:24,472 - INFO - draw_background: 0.0009s
2025-07-16 13:49:24,512 - INFO - draw_stats_page: 0.0410s
2025-07-16 13:49:24,612 - INFO - draw_background: 0.0010s
2025-07-16 13:49:24,655 - INFO - draw_stats_page: 0.0432s
2025-07-16 13:49:24,754 - INFO - draw_background: 0.0009s
2025-07-16 13:49:24,797 - INFO - draw_stats_page: 0.0438s
2025-07-16 13:49:24,902 - INFO - draw_background: 0.0010s
2025-07-16 13:49:24,943 - INFO - draw_stats_page: 0.0419s
2025-07-16 13:49:25,047 - INFO - draw_background: 0.0000s
2025-07-16 13:49:25,090 - INFO - draw_stats_page: 0.0426s
2025-07-16 13:49:25,090 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:25,190 - INFO - draw_background: 0.0010s
2025-07-16 13:49:25,230 - INFO - draw_stats_page: 0.0408s
2025-07-16 13:49:25,332 - INFO - draw_background: 0.0009s
2025-07-16 13:49:25,377 - INFO - draw_stats_page: 0.0460s
2025-07-16 13:49:25,478 - INFO - draw_background: 0.0009s
2025-07-16 13:49:25,519 - INFO - draw_stats_page: 0.0418s
2025-07-16 13:49:25,617 - INFO - draw_background: 0.0000s
2025-07-16 13:49:25,660 - INFO - draw_stats_page: 0.0413s
2025-07-16 13:49:25,760 - INFO - draw_background: 0.0000s
2025-07-16 13:49:25,807 - INFO - draw_stats_page: 0.0483s
2025-07-16 13:49:25,807 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:25,907 - INFO - draw_background: 0.0010s
2025-07-16 13:49:25,947 - INFO - draw_stats_page: 0.0412s
2025-07-16 13:49:26,052 - INFO - draw_background: 0.0010s
2025-07-16 13:49:26,092 - INFO - draw_stats_page: 0.0403s
2025-07-16 13:49:26,192 - INFO - draw_background: 0.0009s
2025-07-16 13:49:26,232 - INFO - draw_stats_page: 0.0404s
2025-07-16 13:49:26,334 - INFO - draw_background: 0.0009s
2025-07-16 13:49:26,377 - INFO - draw_stats_page: 0.0435s
2025-07-16 13:49:26,476 - INFO - draw_background: 0.0009s
2025-07-16 13:49:26,515 - INFO - draw_stats_page: 0.0396s
2025-07-16 13:49:26,516 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:26,617 - INFO - draw_background: 0.0009s
2025-07-16 13:49:26,660 - INFO - draw_stats_page: 0.0437s
2025-07-16 13:49:26,760 - INFO - draw_background: 0.0000s
2025-07-16 13:49:26,800 - INFO - draw_stats_page: 0.0406s
2025-07-16 13:49:26,903 - INFO - draw_background: 0.0009s
2025-07-16 13:49:26,942 - INFO - draw_stats_page: 0.0404s
2025-07-16 13:49:27,047 - INFO - draw_background: 0.0009s
2025-07-16 13:49:27,088 - INFO - draw_stats_page: 0.0413s
2025-07-16 13:49:27,186 - INFO - draw_background: 0.0010s
2025-07-16 13:49:27,227 - INFO - draw_stats_page: 0.0416s
2025-07-16 13:49:27,227 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:27,327 - INFO - draw_background: 0.0005s
2025-07-16 13:49:27,381 - INFO - draw_stats_page: 0.0546s
2025-07-16 13:49:27,483 - INFO - draw_background: 0.0010s
2025-07-16 13:49:27,531 - INFO - draw_stats_page: 0.0489s
2025-07-16 13:49:27,633 - INFO - draw_background: 0.0010s
2025-07-16 13:49:27,680 - INFO - draw_stats_page: 0.0475s
2025-07-16 13:49:27,782 - INFO - draw_background: 0.0009s
2025-07-16 13:49:27,832 - INFO - draw_stats_page: 0.0502s
2025-07-16 13:49:27,933 - INFO - draw_background: 0.0010s
2025-07-16 13:49:27,977 - INFO - draw_stats_page: 0.0449s
2025-07-16 13:49:27,979 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:28,083 - INFO - draw_background: 0.0010s
2025-07-16 13:49:28,134 - INFO - draw_stats_page: 0.0520s
2025-07-16 13:49:28,241 - INFO - draw_background: 0.0005s
2025-07-16 13:49:28,281 - INFO - draw_stats_page: 0.0411s
2025-07-16 13:49:28,383 - INFO - draw_background: 0.0010s
2025-07-16 13:49:28,425 - INFO - draw_stats_page: 0.0430s
2025-07-16 13:49:28,526 - INFO - draw_background: 0.0000s
2025-07-16 13:49:28,576 - INFO - draw_stats_page: 0.0506s
2025-07-16 13:49:28,677 - INFO - draw_background: 0.0010s
2025-07-16 13:49:28,717 - INFO - draw_stats_page: 0.0410s
2025-07-16 13:49:28,719 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:28,824 - INFO - draw_background: 0.0010s
2025-07-16 13:49:28,865 - INFO - draw_stats_page: 0.0416s
2025-07-16 13:49:28,970 - INFO - draw_background: 0.0005s
2025-07-16 13:49:29,007 - INFO - draw_stats_page: 0.0363s
2025-07-16 13:49:29,110 - INFO - draw_background: 0.0005s
2025-07-16 13:49:29,145 - INFO - draw_stats_page: 0.0364s
2025-07-16 13:49:29,253 - INFO - draw_background: 0.0010s
2025-07-16 13:49:29,291 - INFO - draw_stats_page: 0.0385s
2025-07-16 13:49:29,396 - INFO - draw_background: 0.0000s
2025-07-16 13:49:29,433 - INFO - draw_stats_page: 0.0383s
2025-07-16 13:49:29,434 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:29,535 - INFO - draw_background: 0.0000s
2025-07-16 13:49:29,568 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:29,675 - INFO - draw_background: 0.0009s
2025-07-16 13:49:29,710 - INFO - draw_stats_page: 0.0358s
2025-07-16 13:49:29,812 - INFO - draw_background: 0.0010s
2025-07-16 13:49:29,844 - INFO - draw_stats_page: 0.0330s
2025-07-16 13:49:29,945 - INFO - draw_background: 0.0010s
2025-07-16 13:49:29,979 - INFO - draw_stats_page: 0.0330s
2025-07-16 13:49:30,082 - INFO - draw_background: 0.0010s
2025-07-16 13:49:30,117 - INFO - draw_stats_page: 0.0348s
2025-07-16 13:49:30,118 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:30,217 - INFO - draw_background: 0.0010s
2025-07-16 13:49:30,250 - INFO - draw_stats_page: 0.0340s
2025-07-16 13:49:30,352 - INFO - draw_background: 0.0000s
2025-07-16 13:49:30,390 - INFO - draw_stats_page: 0.0388s
2025-07-16 13:49:30,495 - INFO - draw_background: 0.0000s
2025-07-16 13:49:30,528 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:49:30,630 - INFO - draw_background: 0.0011s
2025-07-16 13:49:30,661 - INFO - draw_stats_page: 0.0325s
2025-07-16 13:49:30,761 - INFO - draw_background: 0.0010s
2025-07-16 13:49:30,793 - INFO - draw_stats_page: 0.0329s
2025-07-16 13:49:30,794 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:30,894 - INFO - draw_background: 0.0009s
2025-07-16 13:49:30,927 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:49:31,028 - INFO - draw_background: 0.0009s
2025-07-16 13:49:31,062 - INFO - draw_stats_page: 0.0346s
2025-07-16 13:49:31,163 - INFO - draw_background: 0.0009s
2025-07-16 13:49:31,200 - INFO - draw_stats_page: 0.0380s
2025-07-16 13:49:31,302 - INFO - draw_background: 0.0009s
2025-07-16 13:49:31,339 - INFO - draw_stats_page: 0.0380s
2025-07-16 13:49:31,440 - INFO - draw_background: 0.0010s
2025-07-16 13:49:31,477 - INFO - draw_stats_page: 0.0382s
2025-07-16 13:49:31,477 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:31,583 - INFO - draw_background: 0.0009s
2025-07-16 13:49:31,617 - INFO - draw_stats_page: 0.0349s
2025-07-16 13:49:31,721 - INFO - draw_background: 0.0005s
2025-07-16 13:49:31,767 - INFO - draw_stats_page: 0.0468s
2025-07-16 13:49:31,867 - INFO - draw_background: 0.0000s
2025-07-16 13:49:31,901 - INFO - draw_stats_page: 0.0349s
2025-07-16 13:49:32,003 - INFO - draw_background: 0.0010s
2025-07-16 13:49:32,041 - INFO - draw_stats_page: 0.0383s
2025-07-16 13:49:32,143 - INFO - draw_background: 0.0010s
2025-07-16 13:49:32,181 - INFO - draw_stats_page: 0.0379s
2025-07-16 13:49:32,182 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:32,283 - INFO - draw_background: 0.0000s
2025-07-16 13:49:32,354 - INFO - draw_stats_page: 0.0722s
2025-07-16 13:49:32,416 - INFO - draw_background: 0.0030s
2025-07-16 13:49:32,456 - INFO - draw_stats_page: 0.0429s
2025-07-16 13:49:32,556 - INFO - draw_background: 0.0009s
2025-07-16 13:49:32,589 - INFO - draw_stats_page: 0.0335s
2025-07-16 13:49:32,691 - INFO - draw_background: 0.0005s
2025-07-16 13:49:32,724 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:49:32,825 - INFO - draw_background: 0.0005s
2025-07-16 13:49:32,866 - INFO - draw_stats_page: 0.0404s
2025-07-16 13:49:32,866 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:32,970 - INFO - draw_background: 0.0021s
2025-07-16 13:49:33,006 - INFO - draw_stats_page: 0.0382s
2025-07-16 13:49:33,106 - INFO - draw_background: 0.0010s
2025-07-16 13:49:33,139 - INFO - draw_stats_page: 0.0334s
2025-07-16 13:49:33,240 - INFO - draw_background: 0.0005s
2025-07-16 13:49:33,271 - INFO - draw_stats_page: 0.0326s
2025-07-16 13:49:33,371 - INFO - draw_background: 0.0005s
2025-07-16 13:49:33,403 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:49:33,505 - INFO - draw_background: 0.0009s
2025-07-16 13:49:33,538 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:49:33,539 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:33,640 - INFO - draw_background: 0.0010s
2025-07-16 13:49:33,671 - INFO - draw_stats_page: 0.0319s
2025-07-16 13:49:33,775 - INFO - draw_background: 0.0009s
2025-07-16 13:49:33,807 - INFO - draw_stats_page: 0.0333s
2025-07-16 13:49:33,910 - INFO - draw_background: 0.0000s
2025-07-16 13:49:33,945 - INFO - draw_stats_page: 0.0361s
2025-07-16 13:49:34,048 - INFO - draw_background: 0.0000s
2025-07-16 13:49:34,083 - INFO - draw_stats_page: 0.0350s
2025-07-16 13:49:34,185 - INFO - draw_background: 0.0010s
2025-07-16 13:49:34,223 - INFO - draw_stats_page: 0.0386s
2025-07-16 13:49:34,224 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:34,324 - INFO - draw_background: 0.0000s
2025-07-16 13:49:34,356 - INFO - draw_stats_page: 0.0334s
2025-07-16 13:49:34,460 - INFO - draw_background: 0.0010s
2025-07-16 13:49:34,495 - INFO - draw_stats_page: 0.0358s
2025-07-16 13:49:34,597 - INFO - draw_background: 0.0010s
2025-07-16 13:49:34,632 - INFO - draw_stats_page: 0.0359s
2025-07-16 13:49:34,731 - INFO - draw_background: 0.0000s
2025-07-16 13:49:34,767 - INFO - draw_stats_page: 0.0356s
2025-07-16 13:49:34,868 - INFO - draw_background: 0.0010s
2025-07-16 13:49:34,902 - INFO - draw_stats_page: 0.0354s
2025-07-16 13:49:34,903 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:35,003 - INFO - draw_background: 0.0009s
2025-07-16 13:49:35,038 - INFO - draw_stats_page: 0.0350s
2025-07-16 13:49:35,145 - INFO - draw_background: 0.0000s
2025-07-16 13:49:35,180 - INFO - draw_stats_page: 0.0357s
2025-07-16 13:49:35,286 - INFO - draw_background: 0.0010s
2025-07-16 13:49:35,338 - INFO - draw_stats_page: 0.0533s
2025-07-16 13:49:35,438 - INFO - draw_background: 0.0009s
2025-07-16 13:49:35,470 - INFO - draw_stats_page: 0.0335s
2025-07-16 13:49:35,571 - INFO - draw_background: 0.0010s
2025-07-16 13:49:35,604 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:35,710 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:35,743 - INFO - draw_background: 0.0010s
2025-07-16 13:49:35,783 - INFO - draw_stats_page: 0.0410s
2025-07-16 13:49:35,884 - INFO - draw_background: 0.0010s
2025-07-16 13:49:35,917 - INFO - draw_stats_page: 0.0348s
2025-07-16 13:49:36,021 - INFO - draw_background: 0.0005s
2025-07-16 13:49:36,062 - INFO - draw_stats_page: 0.0418s
2025-07-16 13:49:36,165 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,203 - INFO - draw_stats_page: 0.0389s
2025-07-16 13:49:36,304 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,345 - INFO - draw_stats_page: 0.0419s
2025-07-16 13:49:36,346 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:36,380 - INFO - draw_background: 0.0012s
2025-07-16 13:49:36,419 - INFO - draw_stats_page: 0.0401s
2025-07-16 13:49:36,454 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,494 - INFO - draw_stats_page: 0.0407s
2025-07-16 13:49:36,595 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,632 - INFO - draw_stats_page: 0.0379s
2025-07-16 13:49:36,734 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,768 - INFO - draw_stats_page: 0.0355s
2025-07-16 13:49:36,869 - INFO - draw_background: 0.0015s
2025-07-16 13:49:36,901 - INFO - draw_stats_page: 0.0340s
2025-07-16 13:49:36,902 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:36,938 - INFO - draw_background: 0.0010s
2025-07-16 13:49:36,980 - INFO - draw_stats_page: 0.0429s
2025-07-16 13:49:37,080 - INFO - draw_background: 0.0011s
2025-07-16 13:49:37,113 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:37,214 - INFO - draw_background: 0.0009s
2025-07-16 13:49:37,246 - INFO - draw_stats_page: 0.0331s
2025-07-16 13:49:37,347 - INFO - draw_background: 0.0009s
2025-07-16 13:49:37,379 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:49:37,479 - INFO - draw_background: 0.0009s
2025-07-16 13:49:37,510 - INFO - draw_stats_page: 0.0319s
2025-07-16 13:49:37,511 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:37,611 - INFO - draw_background: 0.0005s
2025-07-16 13:49:37,643 - INFO - draw_stats_page: 0.0326s
2025-07-16 13:49:37,743 - INFO - draw_background: 0.0009s
2025-07-16 13:49:37,775 - INFO - draw_stats_page: 0.0331s
2025-07-16 13:49:38,119 - INFO - draw_background: 0.0005s
2025-07-16 13:49:38,150 - INFO - draw_stats_page: 0.0314s
2025-07-16 13:49:38,237 - INFO - draw_background: 0.0005s
2025-07-16 13:49:38,273 - INFO - draw_stats_page: 0.0371s
2025-07-16 13:49:38,387 - INFO - draw_background: 0.0009s
2025-07-16 13:49:38,418 - INFO - draw_stats_page: 0.0321s
2025-07-16 13:49:38,418 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:38,533 - INFO - draw_background: 0.0009s
2025-07-16 13:49:38,564 - INFO - draw_stats_page: 0.0324s
2025-07-16 13:49:38,678 - INFO - draw_background: 0.0000s
2025-07-16 13:49:38,713 - INFO - draw_stats_page: 0.0352s
2025-07-16 13:49:38,827 - INFO - draw_background: 0.0009s
2025-07-16 13:49:38,860 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:38,976 - INFO - draw_background: 0.0010s
2025-07-16 13:49:39,008 - INFO - draw_stats_page: 0.0328s
2025-07-16 13:49:39,122 - INFO - draw_background: 0.0010s
2025-07-16 13:49:39,160 - INFO - draw_stats_page: 0.0391s
2025-07-16 13:49:39,161 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:39,282 - INFO - draw_background: 0.0000s
2025-07-16 13:49:39,315 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:39,439 - INFO - draw_background: 0.0021s
2025-07-16 13:49:39,475 - INFO - draw_stats_page: 0.0379s
2025-07-16 13:49:39,590 - INFO - draw_background: 0.0006s
2025-07-16 13:49:39,623 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:49:39,742 - INFO - draw_background: 0.0010s
2025-07-16 13:49:39,779 - INFO - draw_stats_page: 0.0378s
2025-07-16 13:49:39,896 - INFO - draw_background: 0.0010s
2025-07-16 13:49:39,934 - INFO - draw_stats_page: 0.0383s
2025-07-16 13:49:39,935 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:40,061 - INFO - draw_background: 0.0010s
2025-07-16 13:49:40,097 - INFO - draw_stats_page: 0.0369s
2025-07-16 13:49:40,215 - INFO - draw_background: 0.0009s
2025-07-16 13:49:40,249 - INFO - draw_stats_page: 0.0346s
2025-07-16 13:49:40,364 - INFO - draw_background: 0.0010s
2025-07-16 13:49:40,396 - INFO - draw_stats_page: 0.0322s
2025-07-16 13:49:40,512 - INFO - draw_background: 0.0000s
2025-07-16 13:49:40,544 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:49:40,595 - INFO - draw_background: 0.0010s
2025-07-16 13:49:40,630 - INFO - draw_stats_page: 0.0365s
2025-07-16 13:49:40,631 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:40,727 - INFO - draw_background: 0.0009s
2025-07-16 13:49:40,762 - INFO - draw_stats_page: 0.0359s
2025-07-16 13:49:40,861 - INFO - draw_background: 0.0010s
2025-07-16 13:49:40,895 - INFO - draw_stats_page: 0.0348s
2025-07-16 13:49:40,994 - INFO - draw_background: 0.0009s
2025-07-16 13:49:41,027 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:41,127 - INFO - draw_background: 0.0009s
2025-07-16 13:49:41,160 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:49:41,262 - INFO - draw_background: 0.0009s
2025-07-16 13:49:41,294 - INFO - draw_stats_page: 0.0328s
2025-07-16 13:49:41,295 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:41,395 - INFO - draw_background: 0.0010s
2025-07-16 13:49:41,428 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:41,528 - INFO - draw_background: 0.0009s
2025-07-16 13:49:41,561 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:49:41,660 - INFO - draw_background: 0.0000s
2025-07-16 13:49:41,693 - INFO - draw_stats_page: 0.0340s
2025-07-16 13:49:41,793 - INFO - draw_background: 0.0010s
2025-07-16 13:49:41,829 - INFO - draw_stats_page: 0.0369s
2025-07-16 13:49:41,927 - INFO - draw_background: 0.0010s
2025-07-16 13:49:41,960 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:49:41,961 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:42,061 - INFO - draw_background: 0.0010s
2025-07-16 13:49:42,071 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:49:42,093 - INFO - draw_stats_page: 0.0328s
2025-07-16 13:49:42,194 - INFO - draw_background: 0.0010s
2025-07-16 13:49:42,227 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:49:42,327 - INFO - draw_background: 0.0009s
2025-07-16 13:49:42,362 - INFO - draw_stats_page: 0.0360s
2025-07-16 13:49:42,459 - INFO - draw_background: 0.0000s
2025-07-16 13:49:42,490 - INFO - draw_stats_page: 0.0321s
2025-07-16 13:49:42,593 - INFO - draw_background: 0.0010s
2025-07-16 13:49:42,627 - INFO - draw_stats_page: 0.0349s
2025-07-16 13:49:42,628 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:42,725 - INFO - draw_background: 0.0009s
2025-07-16 13:49:42,759 - INFO - draw_stats_page: 0.0329s
2025-07-16 13:49:42,859 - INFO - draw_background: 0.0009s
2025-07-16 13:49:42,894 - INFO - draw_stats_page: 0.0359s
2025-07-16 13:49:42,992 - INFO - draw_background: 0.0010s
2025-07-16 13:49:43,026 - INFO - draw_stats_page: 0.0352s
2025-07-16 13:49:43,124 - INFO - draw_background: 0.0009s
2025-07-16 13:49:43,158 - INFO - draw_stats_page: 0.0346s
2025-07-16 13:49:43,256 - INFO - draw_background: 0.0000s
2025-07-16 13:49:43,290 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:43,290 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:43,390 - INFO - draw_background: 0.0000s
2025-07-16 13:49:43,424 - INFO - draw_stats_page: 0.0349s
2025-07-16 13:49:43,522 - INFO - draw_background: 0.0010s
2025-07-16 13:49:43,555 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:43,656 - INFO - draw_background: 0.0009s
2025-07-16 13:49:43,690 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:49:43,791 - INFO - draw_background: 0.0010s
2025-07-16 13:49:43,824 - INFO - draw_stats_page: 0.0341s
2025-07-16 13:49:43,858 - INFO - draw_background: 0.0010s
2025-07-16 13:49:43,905 - INFO - draw_stats_page: 0.0488s
2025-07-16 13:49:43,906 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:43,992 - INFO - draw_background: 0.0009s
2025-07-16 13:49:44,027 - INFO - draw_stats_page: 0.0351s
2025-07-16 13:49:44,124 - INFO - draw_background: 0.0010s
2025-07-16 13:49:44,162 - INFO - draw_stats_page: 0.0384s
2025-07-16 13:49:44,257 - INFO - draw_background: 0.0009s
2025-07-16 13:49:44,293 - INFO - draw_stats_page: 0.0369s
2025-07-16 13:49:44,391 - INFO - draw_background: 0.0010s
2025-07-16 13:49:44,428 - INFO - draw_stats_page: 0.0379s
2025-07-16 13:49:44,523 - INFO - draw_background: 0.0010s
2025-07-16 13:49:44,558 - INFO - draw_stats_page: 0.0354s
2025-07-16 13:49:44,559 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:44,656 - INFO - draw_background: 0.0009s
2025-07-16 13:49:44,693 - INFO - draw_stats_page: 0.0373s
2025-07-16 13:49:44,788 - INFO - draw_background: 0.0000s
2025-07-16 13:49:44,826 - INFO - draw_stats_page: 0.0376s
2025-07-16 13:49:44,922 - INFO - draw_background: 0.0009s
2025-07-16 13:49:44,957 - INFO - draw_stats_page: 0.0361s
2025-07-16 13:49:45,032 - INFO - draw_background: 0.0010s
2025-07-16 13:49:45,076 - INFO - draw_stats_page: 0.0445s
2025-07-16 13:49:45,139 - INFO - draw_background: 0.0000s
2025-07-16 13:49:45,184 - INFO - draw_stats_page: 0.0467s
2025-07-16 13:49:45,185 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:49:45,245 - INFO - draw_background: 0.0005s
2025-07-16 13:49:45,287 - INFO - draw_stats_page: 0.0424s
2025-07-16 13:49:45,349 - INFO - draw_background: 0.0015s
2025-07-16 13:49:45,395 - INFO - draw_stats_page: 0.0478s
2025-07-16 13:50:03,331 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:50:03,331 - WARNING - Failed to connect to RethinkDB
2025-07-16 13:50:06,853 - INFO - Game started in demo mode - statistics not updated
2025-07-16 13:50:06,860 - INFO - Posted refresh_stats event to trigger UI update
2025-07-16 13:50:06,865 - INFO - Started background data loading
2025-07-16 13:50:06,865 - INFO - Forced data refresh
2025-07-16 13:50:06,865 - INFO - Forced refresh of optimized loader data
2025-07-16 13:50:06,868 - INFO - Loaded weekly stats for 7 days
2025-07-16 13:50:06,876 - INFO - Loaded summary data
2025-07-16 13:50:06,879 - INFO - Loaded game history page 0 (0 records)
2025-07-16 13:50:06,879 - INFO - Loaded game history metadata (total pages: 1)
2025-07-16 13:50:06,880 - INFO - Saved 7 items to cache
2025-07-16 13:50:06,880 - INFO - Background data loading completed
2025-07-16 13:50:06,881 - INFO - Forced refresh of thread_safe_db data
2025-07-16 13:50:06,881 - INFO - Saved 0 items to persistent cache
2025-07-16 13:50:06,881 - INFO - Cache cleared
2025-07-16 13:50:06,881 - INFO - Cleared all preloader cache data
2025-07-16 13:50:06,882 - INFO - Starting stats data preloading
2025-07-16 13:50:06,882 - INFO - Loading data using optimized functions
2025-07-16 13:50:06,882 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:50:06,882 - INFO - Started stats data preloading
2025-07-16 13:50:06,883 - INFO - Cleared stats preloader cache
2025-07-16 13:50:06,883 - INFO - DB Operation: {"timestamp": "2025-07-16 13:50:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:50:06,884 - INFO - Preloaded weekly stats for 7 days
2025-07-16 13:50:06,884 - INFO - Preloaded summary data
2025-07-16 13:50:06,885 - INFO - Preloaded game history (0 records)
2025-07-16 13:50:06,885 - INFO - Preloaded wallet data
2025-07-16 13:50:06,885 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-07-16 13:50:06,886 - INFO - Saved 8 items to persistent cache
2025-07-16 13:50:06,984 - INFO - Got summary stats
2025-07-16 13:50:07,019 - INFO - Posted refresh_stats event
2025-07-16 13:50:07,020 - INFO - Processed game_started event: False
2025-07-16 13:50:07,020 - INFO - Loaded game history page 0 from GameStatsIntegration
2025-07-16 13:50:07,021 - INFO - Loaded weekly stats from GameStatsIntegration
2025-07-16 13:50:25,517 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-16 13:50:25,518 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-16 13:50:46,191 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:50:57,566 - INFO - Game start event recorded in database (players: 3)
2025-07-16 13:50:57,566 - INFO - Posted refresh_stats event to trigger UI update
2025-07-16 13:50:57,567 - INFO - Started background data loading
2025-07-16 13:50:57,567 - INFO - Forced data refresh
2025-07-16 13:50:57,567 - INFO - Game start recorded with optimized integration: True
2025-07-16 13:50:57,569 - INFO - Loaded weekly stats for 7 days
2025-07-16 13:50:57,570 - INFO - Loaded summary data
2025-07-16 13:50:57,571 - INFO - Loaded game history page 0 (0 records)
2025-07-16 13:50:57,571 - INFO - Loaded game history metadata (total pages: 1)
2025-07-16 13:50:57,572 - INFO - Saved 7 items to cache
2025-07-16 13:50:57,572 - INFO - Background data loading completed
2025-07-16 13:50:57,572 - INFO - Posted refresh_stats event to trigger UI update
2025-07-16 13:50:57,573 - INFO - Started background data loading
2025-07-16 13:50:57,573 - INFO - Forced data refresh
2025-07-16 13:50:57,573 - INFO - Forced refresh of optimized loader data
2025-07-16 13:50:57,574 - INFO - Loaded weekly stats for 7 days
2025-07-16 13:50:57,574 - INFO - Loaded summary data
2025-07-16 13:50:57,575 - INFO - Loaded game history page 0 (0 records)
2025-07-16 13:50:57,575 - INFO - Loaded game history metadata (total pages: 1)
2025-07-16 13:50:57,576 - INFO - Saved 7 items to cache
2025-07-16 13:50:57,576 - INFO - Background data loading completed
2025-07-16 13:50:57,576 - INFO - Forced refresh of thread_safe_db data
2025-07-16 13:50:57,577 - INFO - Saved 0 items to persistent cache
2025-07-16 13:50:57,577 - INFO - Cache cleared
2025-07-16 13:50:57,577 - INFO - Cleared all preloader cache data
2025-07-16 13:50:57,578 - INFO - Starting stats data preloading
2025-07-16 13:50:57,578 - INFO - Started stats data preloading
2025-07-16 13:50:57,578 - INFO - Loading data using optimized functions
2025-07-16 13:50:57,578 - INFO - Cleared stats preloader cache
2025-07-16 13:50:57,578 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:50:57,579 - INFO - DB Operation: {"timestamp": "2025-07-16 13:50:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:50:57,579 - INFO - Posted refresh_stats event
2025-07-16 13:50:57,580 - INFO - Preloaded weekly stats for 7 days
2025-07-16 13:50:57,580 - INFO - Processed game_started event: True
2025-07-16 13:50:57,581 - INFO - Preloaded summary data
2025-07-16 13:50:57,581 - INFO - Preloaded game history (0 records)
2025-07-16 13:50:57,581 - INFO - Preloaded wallet data
2025-07-16 13:50:57,581 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-07-16 13:50:57,582 - INFO - Saved 8 items to persistent cache
2025-07-16 13:51:02,363 - INFO - DEVELOPER MODE: Resetting Bingo Favor Mode due to game reset.
2025-07-16 13:51:02,363 - INFO - DEVELOPER MODE: Bingo Favor Mode reset complete.
2025-07-16 13:51:07,443 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:51:07,446 - WARNING - Failed to connect to RethinkDB
2025-07-16 13:51:45,850 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:45,851 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:45", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:45,851 - INFO - Stats file data\stats.json not found, nothing to migrate
2025-07-16 13:51:45,853 - INFO - Admin button added to stats page
2025-07-16 13:51:45,893 - INFO - draw_background: 0.0400s
2025-07-16 13:51:45,907 - INFO - draw_stats_page: 0.0541s
2025-07-16 13:51:45,999 - INFO - draw_background: 0.0016s
2025-07-16 13:51:46,002 - INFO - draw_stats_page: 0.0046s
2025-07-16 13:51:46,130 - INFO - draw_background: 0.0000s
2025-07-16 13:51:46,133 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:46,134 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:46,263 - INFO - draw_background: 0.0010s
2025-07-16 13:51:46,267 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:46,396 - INFO - draw_background: 0.0010s
2025-07-16 13:51:46,399 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:46,529 - INFO - draw_background: 0.0006s
2025-07-16 13:51:46,533 - INFO - draw_stats_page: 0.0047s
2025-07-16 13:51:46,662 - INFO - draw_background: 0.0010s
2025-07-16 13:51:46,666 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:46,793 - INFO - draw_background: 0.0009s
2025-07-16 13:51:46,797 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:51:46,797 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:46,927 - INFO - draw_background: 0.0010s
2025-07-16 13:51:46,931 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:47,060 - INFO - draw_background: 0.0005s
2025-07-16 13:51:47,065 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:47,194 - INFO - draw_background: 0.0010s
2025-07-16 13:51:47,199 - INFO - draw_stats_page: 0.0060s
2025-07-16 13:51:47,326 - INFO - draw_background: 0.0010s
2025-07-16 13:51:47,330 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:47,461 - INFO - draw_background: 0.0010s
2025-07-16 13:51:47,465 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:47,466 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:47,594 - INFO - draw_background: 0.0010s
2025-07-16 13:51:47,597 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:51:47,661 - INFO - draw_background: 0.0018s
2025-07-16 13:51:47,665 - INFO - draw_stats_page: 0.0048s
2025-07-16 13:51:47,793 - INFO - draw_background: 0.0010s
2025-07-16 13:51:47,797 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:47,927 - INFO - draw_background: 0.0009s
2025-07-16 13:51:47,930 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:51:48,061 - INFO - draw_background: 0.0005s
2025-07-16 13:51:48,065 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:48,066 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:48,194 - INFO - draw_background: 0.0010s
2025-07-16 13:51:48,197 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:51:48,261 - INFO - draw_background: 0.0013s
2025-07-16 13:51:48,264 - INFO - draw_stats_page: 0.0043s
2025-07-16 13:51:48,393 - INFO - draw_background: 0.0010s
2025-07-16 13:51:48,397 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:48,526 - INFO - draw_background: 0.0009s
2025-07-16 13:51:48,530 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:48,593 - INFO - draw_background: 0.0009s
2025-07-16 13:51:48,597 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:51:48,597 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:48,725 - INFO - draw_background: 0.0009s
2025-07-16 13:51:48,730 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:48,858 - INFO - draw_background: 0.0009s
2025-07-16 13:51:48,862 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:48,992 - INFO - draw_background: 0.0009s
2025-07-16 13:51:48,995 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:49,059 - INFO - draw_background: 0.0009s
2025-07-16 13:51:49,063 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:49,192 - INFO - draw_background: 0.0010s
2025-07-16 13:51:49,195 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:51:49,196 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:49,258 - INFO - draw_background: 0.0009s
2025-07-16 13:51:49,262 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:49,390 - INFO - draw_background: 0.0005s
2025-07-16 13:51:49,394 - INFO - draw_stats_page: 0.0046s
2025-07-16 13:51:49,524 - INFO - draw_background: 0.0010s
2025-07-16 13:51:49,528 - INFO - draw_stats_page: 0.0049s
2025-07-16 13:51:49,656 - INFO - draw_background: 0.0009s
2025-07-16 13:51:49,661 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:49,722 - INFO - draw_background: 0.0009s
2025-07-16 13:51:49,725 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:49,726 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:49,855 - INFO - draw_background: 0.0010s
2025-07-16 13:51:49,859 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:49,989 - INFO - draw_background: 0.0010s
2025-07-16 13:51:49,992 - INFO - draw_stats_page: 0.0040s
2025-07-16 13:51:50,121 - INFO - draw_background: 0.0005s
2025-07-16 13:51:50,125 - INFO - draw_stats_page: 0.0045s
2025-07-16 13:51:50,254 - INFO - draw_background: 0.0010s
2025-07-16 13:51:50,257 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:50,291 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-16 13:51:50,389 - INFO - draw_background: 0.0026s
2025-07-16 13:51:50,393 - INFO - draw_stats_page: 0.0071s
2025-07-16 13:51:50,394 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:50,520 - INFO - draw_background: 0.0005s
2025-07-16 13:51:50,524 - INFO - draw_stats_page: 0.0042s
2025-07-16 13:51:50,588 - INFO - draw_background: 0.0009s
2025-07-16 13:51:50,592 - INFO - draw_stats_page: 0.0050s
2025-07-16 13:51:50,719 - INFO - draw_background: 0.0000s
2025-07-16 13:51:50,722 - INFO - draw_stats_page: 0.0041s
2025-07-16 13:51:50,853 - INFO - draw_background: 0.0010s
2025-07-16 13:51:50,853 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,856 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:50,857 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,860 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,864 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,866 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,867 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,867 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,869 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,869 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,870 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,870 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,871 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,871 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,872 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,872 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,873 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,873 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,874 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,874 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,876 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,876 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,877 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,879 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,880 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,881 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,882 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,883 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,885 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,886 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,887 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,889 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,890 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:50,890 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:50", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:50,920 - INFO - draw_background: 0.0006s
2025-07-16 13:51:50,924 - INFO - draw_stats_page: 0.0046s
2025-07-16 13:51:50,925 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:51,051 - INFO - draw_background: 0.0005s
2025-07-16 13:51:51,055 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:51,184 - INFO - draw_background: 0.0009s
2025-07-16 13:51:51,187 - INFO - draw_stats_page: 0.0039s
2025-07-16 13:51:51,324 - INFO - load_statistics: 0.0085s
2025-07-16 13:51:51,326 - INFO - draw_background: 0.0020s
2025-07-16 13:51:51,327 - INFO - Starting stats data preloading
2025-07-16 13:51:51,327 - INFO - Started stats data preloading
2025-07-16 13:51:51,327 - INFO - Loading data using optimized functions
2025-07-16 13:51:51,330 - WARNING - Connection validation failed, creating new connection
2025-07-16 13:51:51,332 - INFO - DB Operation: {"timestamp": "2025-07-16 13:51:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-16 13:51:51,339 - INFO - Preloaded weekly stats for 7 days
2025-07-16 13:51:51,344 - INFO - Preloaded summary data
2025-07-16 13:51:51,346 - INFO - Preloaded game history (0 records)
2025-07-16 13:51:51,348 - INFO - Preloaded wallet data
2025-07-16 13:51:51,348 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-16 13:51:51,349 - INFO - Saved 8 items to persistent cache
2025-07-16 13:51:51,435 - INFO - draw_stats_page: 0.1106s
2025-07-16 13:51:51,460 - INFO - draw_background: 0.0026s
2025-07-16 13:51:51,492 - INFO - draw_stats_page: 0.0345s
2025-07-16 13:51:51,591 - INFO - draw_background: 0.0010s
2025-07-16 13:51:51,592 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:51,622 - INFO - draw_stats_page: 0.0316s
2025-07-16 13:51:51,723 - INFO - draw_background: 0.0000s
2025-07-16 13:51:51,757 - INFO - draw_stats_page: 0.0349s
2025-07-16 13:51:51,854 - INFO - draw_background: 0.0009s
2025-07-16 13:51:51,885 - INFO - draw_stats_page: 0.0313s
2025-07-16 13:51:51,990 - INFO - draw_background: 0.0010s
2025-07-16 13:51:52,020 - INFO - draw_stats_page: 0.0307s
2025-07-16 13:51:52,122 - INFO - draw_background: 0.0005s
2025-07-16 13:51:52,156 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:51:52,256 - INFO - draw_background: 0.0009s
2025-07-16 13:51:52,257 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:52,292 - INFO - draw_stats_page: 0.0369s
2025-07-16 13:51:52,389 - INFO - draw_background: 0.0009s
2025-07-16 13:51:52,421 - INFO - draw_stats_page: 0.0331s
2025-07-16 13:51:52,522 - INFO - draw_background: 0.0009s
2025-07-16 13:51:52,554 - INFO - draw_stats_page: 0.0331s
2025-07-16 13:51:52,654 - INFO - draw_background: 0.0010s
2025-07-16 13:51:52,686 - INFO - draw_stats_page: 0.0333s
2025-07-16 13:51:52,787 - INFO - draw_background: 0.0010s
2025-07-16 13:51:52,817 - INFO - draw_stats_page: 0.0312s
2025-07-16 13:51:52,920 - INFO - draw_background: 0.0000s
2025-07-16 13:51:52,920 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:52,953 - INFO - draw_stats_page: 0.0339s
2025-07-16 13:51:53,052 - INFO - draw_background: 0.0010s
2025-07-16 13:51:53,085 - INFO - draw_stats_page: 0.0333s
2025-07-16 13:51:53,185 - INFO - draw_background: 0.0010s
2025-07-16 13:51:53,216 - INFO - draw_stats_page: 0.0320s
2025-07-16 13:51:53,317 - INFO - draw_background: 0.0009s
2025-07-16 13:51:53,349 - INFO - draw_stats_page: 0.0328s
2025-07-16 13:51:53,449 - INFO - draw_background: 0.0009s
2025-07-16 13:51:53,481 - INFO - draw_stats_page: 0.0330s
2025-07-16 13:51:53,582 - INFO - draw_background: 0.0010s
2025-07-16 13:51:53,583 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:53,615 - INFO - draw_stats_page: 0.0338s
2025-07-16 13:51:53,715 - INFO - draw_background: 0.0010s
2025-07-16 13:51:53,751 - INFO - draw_stats_page: 0.0370s
2025-07-16 13:51:53,848 - INFO - draw_background: 0.0009s
2025-07-16 13:51:53,879 - INFO - draw_stats_page: 0.0326s
2025-07-16 13:51:53,980 - INFO - draw_background: 0.0010s
2025-07-16 13:51:54,011 - INFO - draw_stats_page: 0.0321s
2025-07-16 13:51:54,113 - INFO - draw_background: 0.0009s
2025-07-16 13:51:54,145 - INFO - draw_stats_page: 0.0326s
2025-07-16 13:51:54,246 - INFO - draw_background: 0.0009s
2025-07-16 13:51:54,247 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:54,279 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:51:54,380 - INFO - draw_background: 0.0000s
2025-07-16 13:51:54,410 - INFO - draw_stats_page: 0.0309s
2025-07-16 13:51:54,512 - INFO - draw_background: 0.0010s
2025-07-16 13:51:54,544 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:51:54,645 - INFO - draw_background: 0.0009s
2025-07-16 13:51:54,677 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:51:54,778 - INFO - draw_background: 0.0009s
2025-07-16 13:51:54,809 - INFO - draw_stats_page: 0.0323s
2025-07-16 13:51:54,910 - INFO - draw_background: 0.0000s
2025-07-16 13:51:54,910 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:54,943 - INFO - draw_stats_page: 0.0337s
2025-07-16 13:51:55,043 - INFO - draw_background: 0.0010s
2025-07-16 13:51:55,075 - INFO - draw_stats_page: 0.0319s
2025-07-16 13:51:55,177 - INFO - draw_background: 0.0009s
2025-07-16 13:51:55,208 - INFO - draw_stats_page: 0.0317s
2025-07-16 13:51:55,310 - INFO - draw_background: 0.0006s
2025-07-16 13:51:55,341 - INFO - draw_stats_page: 0.0317s
2025-07-16 13:51:55,442 - INFO - draw_background: 0.0009s
2025-07-16 13:51:55,473 - INFO - draw_stats_page: 0.0317s
2025-07-16 13:51:55,574 - INFO - draw_background: 0.0009s
2025-07-16 13:51:55,575 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:55,607 - INFO - draw_stats_page: 0.0336s
2025-07-16 13:51:55,706 - INFO - draw_background: 0.0009s
2025-07-16 13:51:55,740 - INFO - draw_stats_page: 0.0347s
2025-07-16 13:51:55,842 - INFO - draw_background: 0.0010s
2025-07-16 13:51:55,875 - INFO - draw_stats_page: 0.0346s
2025-07-16 13:51:55,910 - INFO - draw_background: 0.0015s
2025-07-16 13:51:55,941 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:51:56,040 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,072 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:51:56,174 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,175 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:56,206 - INFO - draw_stats_page: 0.0327s
2025-07-16 13:51:56,306 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,338 - INFO - draw_stats_page: 0.0323s
2025-07-16 13:51:56,439 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,469 - INFO - draw_stats_page: 0.0310s
2025-07-16 13:51:56,570 - INFO - draw_background: 0.0006s
2025-07-16 13:51:56,600 - INFO - draw_stats_page: 0.0308s
2025-07-16 13:51:56,702 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,733 - INFO - draw_stats_page: 0.0317s
2025-07-16 13:51:56,836 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,837 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:56,871 - INFO - draw_stats_page: 0.0358s
2025-07-16 13:51:56,902 - INFO - draw_background: 0.0010s
2025-07-16 13:51:56,937 - INFO - draw_stats_page: 0.0367s
2025-07-16 13:51:57,035 - INFO - draw_background: 0.0000s
2025-07-16 13:51:57,067 - INFO - draw_stats_page: 0.0329s
2025-07-16 13:51:57,168 - INFO - draw_background: 0.0000s
2025-07-16 13:51:57,201 - INFO - draw_stats_page: 0.0342s
2025-07-16 13:51:57,301 - INFO - draw_background: 0.0010s
2025-07-16 13:51:57,331 - INFO - draw_stats_page: 0.0309s
2025-07-16 13:51:57,434 - INFO - draw_background: 0.0010s
2025-07-16 13:51:57,435 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-16 13:51:57,465 - INFO - draw_stats_page: 0.0315s
