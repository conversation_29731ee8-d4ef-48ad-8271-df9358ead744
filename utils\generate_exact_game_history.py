#!/usr/bin/env python3
"""
Script to generate exact game history data that precisely matches the daily earnings.
This creates detailed game session records with exact financial calculations.
"""

import sqlite3
import os
import json
import random
from datetime import datetime, timedelta

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

# Game configuration
GAME_CONFIG = {
    'commission_percentage': 20.0,
    'default_bet_amount': 50,
    'house_patterns': ['Full House', 'Line', 'Four Corners', 'X Pattern', 'T Pattern'],
    'game_statuses': ['Won', 'Completed', 'Finished']
}

# Realistic player names
PLAYER_NAMES = [
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON> Girma', '<PERSON>', 'Tewodros Assefa', 'Yodit <PERSON>',
    'Bereket Desta', '<PERSON><PERSON><PERSON>ash', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> Getachew', '<PERSON><PERSON> Tadele',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
]

def calculate_exact_game_params(target_commission, commission_percent=20.0):
    """Calculate exact game parameters to achieve target commission."""
    # Commission = Total_Bets * (commission_percent / 100)
    # Therefore: Total_Bets = Commission / (commission_percent / 100)
    total_bets_needed = target_commission / (commission_percent / 100.0)
    
    # Find reasonable player count and bet amount combination
    # Try different player counts (3-8 players typically)
    for players in range(3, 9):
        bet_amount = total_bets_needed / players
        # Round to nearest 5 ETB for realistic betting
        bet_amount = round(bet_amount / 5) * 5
        
        # Check if this is a reasonable bet amount (25-200 ETB)
        if 25 <= bet_amount <= 200:
            actual_total_bets = players * bet_amount
            actual_commission = actual_total_bets * (commission_percent / 100.0)
            prize_pool = actual_total_bets - actual_commission
            
            return {
                'players': players,
                'bet_amount': bet_amount,
                'total_bets': actual_total_bets,
                'commission': actual_commission,
                'prize_pool': prize_pool
            }
    
    # Fallback: use default values and accept close approximation
    players = 5
    bet_amount = 50
    actual_total_bets = players * bet_amount
    actual_commission = actual_total_bets * (commission_percent / 100.0)
    prize_pool = actual_total_bets - actual_commission
    
    return {
        'players': players,
        'bet_amount': bet_amount,
        'total_bets': actual_total_bets,
        'commission': actual_commission,
        'prize_pool': prize_pool
    }

def generate_called_numbers(count):
    """Generate a realistic sequence of called bingo numbers."""
    all_numbers = list(range(1, 76))
    return sorted(random.sample(all_numbers, min(count, 75)))

def create_game_details(winner_cartella, claim_type, game_duration, called_numbers, pattern):
    """Create game details JSON."""
    return json.dumps({
        'winner_cartella': winner_cartella,
        'claim_type': claim_type,
        'game_duration': game_duration,
        'called_numbers': called_numbers,
        'game_type': 'Standard Bingo',
        'pattern_achieved': pattern
    })

def generate_exact_games_for_date(date_str, target_earnings):
    """Generate exact game sessions for a specific date to match target earnings."""
    if target_earnings == 0:
        return []  # No games on days with zero earnings
    
    games = []
    base_date = datetime.strptime(date_str, '%Y-%m-%d')
    
    # Determine how to split the earnings across multiple games
    if target_earnings <= 200:
        # Single game
        game_earnings = [target_earnings]
    elif target_earnings <= 400:
        # Two games
        split = target_earnings / 2
        game_earnings = [split, target_earnings - split]
    elif target_earnings <= 600:
        # Two or three games
        if target_earnings == 500:
            game_earnings = [250, 250]
        elif target_earnings == 600:
            game_earnings = [200, 200, 200]
        else:
            split = target_earnings / 2
            game_earnings = [split, target_earnings - split]
    else:
        # Three or more games for high earnings
        if target_earnings == 750:
            game_earnings = [250, 250, 250]
        elif target_earnings == 950:
            game_earnings = [300, 350, 300]
        else:
            # Split into 3 games
            base = target_earnings / 3
            game_earnings = [base, base, target_earnings - (2 * base)]
    
    # Generate games
    for i, earnings in enumerate(game_earnings):
        # Generate game time (spread throughout the day)
        hour = random.randint(9, 22)
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        game_time = base_date.replace(hour=hour, minute=minute, second=second)
        
        # Calculate exact game parameters
        params = calculate_exact_game_params(earnings, GAME_CONFIG['commission_percentage'])
        
        # Generate game details
        winner_name = random.choice(PLAYER_NAMES)
        winner_cartella = random.randint(1, 100)
        house_pattern = random.choice(GAME_CONFIG['house_patterns'])
        total_calls = random.randint(15, 44)
        called_numbers = generate_called_numbers(total_calls)
        game_duration = total_calls * 3 + random.randint(-30, 60)
        
        # Create game record
        game = {
            'date_time': game_time.strftime('%Y-%m-%d %H:%M:%S'),
            'username': winner_name,
            'house': house_pattern,
            'stake': params['bet_amount'],
            'players': params['players'],
            'total_calls': total_calls,
            'commission_percent': GAME_CONFIG['commission_percentage'],
            'fee': params['commission'],
            'total_prize': params['prize_pool'],
            'details': create_game_details(winner_cartella, 'Full House', game_duration, called_numbers, house_pattern),
            'status': random.choice(GAME_CONFIG['game_statuses'])
        }
        
        games.append(game)
    
    total_commission = sum(game['fee'] for game in games)
    print(f"   Generated {len(games)} games for {date_str}: Target {target_earnings} ETB, Exact {total_commission:.1f} ETB")
    return games

def clear_existing_history():
    """Clear existing game history for the date range."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
        DELETE FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        if deleted_count > 0:
            print(f"🗑️ Cleared {deleted_count} existing game history records")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing existing history: {e}")
        return False

def insert_exact_game_history():
    """Insert exact game history data into the database."""
    
    # Daily earnings data (matching the previously inserted data)
    daily_earnings = [
        ('2025-05-26', 500.0),  # Monday
        ('2025-05-27', 500.0),  # Tuesday  
        ('2025-05-28', 0.0),    # Wednesday (zero)
        ('2025-05-29', 180.0),  # Thursday
        ('2025-05-30', 600.0),  # Friday
        ('2025-05-31', 750.0),  # Saturday
        ('2025-06-01', 950.0),  # Sunday
    ]
    
    try:
        # Clear existing history first
        if not clear_existing_history():
            return False
        
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("🎮 Generating exact game history data...")
        
        all_games = []
        
        # Generate games for each day
        for date_str, earnings in daily_earnings:
            day_games = generate_exact_games_for_date(date_str, earnings)
            all_games.extend(day_games)
        
        # Insert all games into database
        print(f"\n📊 Inserting {len(all_games)} exact game records...")
        
        for game in all_games:
            cursor.execute('''
            INSERT INTO game_history
            (date_time, username, house, stake, players, total_calls,
             commission_percent, fee, total_prize, details, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game['date_time'], game['username'], game['house'], 
                game['stake'], game['players'], game['total_calls'],
                game['commission_percent'], game['fee'], game['total_prize'],
                game['details'], game['status']
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ Exact game history data inserted successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error inserting exact game history: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def verify_exact_game_history():
    """Verify the exact game history data."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("\n📋 Verifying exact game history data:")
        
        # Get summary by date
        cursor.execute('''
        SELECT 
            DATE(date_time) as game_date,
            COUNT(*) as games_count,
            SUM(fee) as total_commission,
            SUM(total_prize) as total_prizes,
            AVG(players) as avg_players,
            AVG(stake) as avg_stake
        FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        GROUP BY DATE(date_time)
        ORDER BY game_date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   Date       | Games | Commission | Prizes   | Avg Players | Avg Stake")
            print("   -----------|-------|------------|----------|-------------|----------")
            
            total_commission_check = 0
            for date, games, commission, prizes, avg_players, avg_stake in results:
                dt = datetime.strptime(date, '%Y-%m-%d')
                day_name = dt.strftime('%a')
                total_commission_check += commission
                print(f"   {date} ({day_name}) | {games:>5} | {commission:>10.1f} | {prizes:>8.1f} | {avg_players:>11.1f} | {avg_stake:>9.1f}")
            
            print("   -----------|-------|------------|----------|-------------|----------")
            print(f"   TOTAL      |       | {total_commission_check:>10.1f} |          |             |")
            
            # Verify against expected total (3480.0 ETB)
            expected_total = 500 + 500 + 0 + 180 + 600 + 750 + 950
            print(f"\n✅ Expected total commission: {expected_total} ETB")
            print(f"✅ Actual total commission: {total_commission_check:.1f} ETB")
            print(f"✅ Match: {'YES' if abs(total_commission_check - expected_total) < 0.1 else 'NO'}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying exact game history: {e}")

def main():
    """Main function to generate exact game history."""
    print("🎯 Exact Game History Data Generation")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists(STATS_DB_PATH):
        print(f"❌ Database not found: {STATS_DB_PATH}")
        print("Please run the stats data insertion script first.")
        return
    
    # Generate and insert exact game history
    success = insert_exact_game_history()
    
    if success:
        # Verify the data
        verify_exact_game_history()
        print("\n🎉 Exact game history generation completed successfully!")
        print("💡 The stats page will now show detailed game history with exact earnings match.")
    else:
        print("\n❌ Exact game history generation failed!")

if __name__ == "__main__":
    main()
