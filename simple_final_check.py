import os
import sqlite3

STATS_DB_PATH = os.path.join('data', 'stats.db')

print("=== FINAL STATUS CHECK ===")

conn = sqlite3.connect(STATS_DB_PATH)
cursor = conn.cursor()

# Check total earnings
cursor.execute('SELECT SUM(earnings) FROM daily_stats')
total = cursor.fetchone()[0] or 0
print(f"Total earnings: {total} ETB (target: 39782.6667)")

# Check yesterday (05/26/2025)
cursor.execute('SELECT earnings FROM daily_stats WHERE date = "2025-05-26"')
result = cursor.fetchone()
yesterday = result[0] if result else 0
print(f"Yesterday (05/26/2025): {yesterday} ETB (target: 4086.667)")

# Check wallet balance
cursor.execute('SELECT balance_after FROM wallet_transactions ORDER BY id DESC LIMIT 1')
result = cursor.fetchone()
wallet = result[0] if result else 0
print(f"Wallet balance: {wallet} ETB (target: 4159.0)")

# Status check
total_ok = abs(total - 39782.6667) < 0.001
yesterday_ok = abs(yesterday - 4086.667) < 0.001
wallet_ok = abs(wallet - 4159.0) < 0.001

print(f"\nStatus:")
print(f"- Total earnings: {'OK' if total_ok else 'NEEDS FIX'}")
print(f"- Yesterday earnings: {'OK' if yesterday_ok else 'NEEDS FIX'}")
print(f"- Wallet balance: {'OK' if wallet_ok else 'NEEDS FIX'}")

if total_ok and yesterday_ok and wallet_ok:
    print(f"\n*** ALL VALUES CORRECT! ***")
    print(f"Your stats page should now show the requested values.")
else:
    print(f"\n*** SOME VALUES NEED ATTENTION ***")

conn.close()