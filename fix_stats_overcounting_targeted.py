#!/usr/bin/env python3
"""
Targeted Fix for Stats Overcounting Issue

This script implements a targeted fix for the issue where the stats page overcounts
balances and game counts after playing more than 10 games continuously.

The fix addresses the specific root causes:
1. Multiple recording paths causing duplicate entries
2. Race conditions in database updates
3. Lack of transaction isolation
4. No duplicate detection mechanism

Author: AI Assistant
Date: 2023-11-10
"""

import os
import sqlite3
import shutil
import time
from datetime import datetime

# Constants
STATS_DB_PATH = os.path.join('data', 'stats.db')
BACKUP_DIR = os.path.join('data', 'backups')

def create_backup():
    """Create a backup of the stats database before making changes."""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(BACKUP_DIR, f'stats_db_backup_{timestamp}.db')
    
    try:
        shutil.copy2(STATS_DB_PATH, backup_path)
        print(f"✅ Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

def add_processed_games_table():
    """
    Add a processed_games table to track which games have been recorded.
    This is the key to preventing duplicate recordings.
    """
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Create processed_games table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS processed_games (
            game_signature TEXT PRIMARY KEY,
            date_time TEXT,
            username TEXT,
            stake REAL,
            players INTEGER,
            total_calls INTEGER,
            processed_time TEXT,
            recording_path TEXT
        )
        ''')
        
        # Create index for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_processed_games_signature ON processed_games(game_signature)')
        
        conn.commit()
        conn.close()
        
        print("✅ Added processed_games tracking table")
        return True
    except Exception as e:
        print(f"❌ Error adding processed_games table: {e}")
        return False

def patch_thread_safe_db():
    """
    Patch the thread_safe_db.py file to add duplicate detection.
    This is the most common path for recording game completions.
    """
    file_path = 'thread_safe_db.py'
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Create backup
    backup_path = os.path.join(BACKUP_DIR, f'thread_safe_db.py.bak.{int(time.time())}')
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✅ Created backup at {backup_path}")
    except Exception as e:
        print(f"⚠️ Failed to create backup: {e}")
        response = input("Continue without backup? (y/n): ").strip().lower()
        if not response.startswith('y'):
            return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the record_game_completed function
        if 'def record_game_completed(game_data):' not in content:
            print("❌ Could not find record_game_completed function")
            return False
        
        # Add import for hashlib if not present
        if 'import hashlib' not in content:
            import_section_end = content.find('import')
            while content.find('import', import_section_end + 1) > 0:
                import_section_end = content.find('import', import_section_end + 1)
            import_section_end = content.find('\n', import_section_end)
            
            content = content[:import_section_end + 1] + 'import hashlib\n' + content[import_section_end + 1:]
        
        # Find the beginning of the record_game_completed function
        func_start = content.find('def record_game_completed(game_data):')
        func_body_start = content.find(':', func_start) + 1
        
        # Add duplicate detection code at the beginning of the function
        duplicate_check_code = '''
    # Skip if in demo mode
    if game_data.get("is_demo_mode", False):
        print("Game was in demo mode - statistics not updated")
        return False
        
    # Create a unique signature for this game to detect duplicates
    try:
        username = str(game_data.get("winner_name", "Unknown"))
        stake = str(game_data.get("stake", 0))
        players = str(game_data.get("player_count", 0))
        total_calls = str(len(game_data.get("called_numbers", [])))
        timestamp = str(game_data.get("timestamp", time.time()))
        
        # Create a unique signature
        signature_str = f"{username}:{stake}:{players}:{total_calls}:{timestamp}"
        game_signature = hashlib.md5(signature_str.encode()).hexdigest()
        
        # Check if this game has already been processed
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT COUNT(*) FROM processed_games 
        WHERE game_signature = ?
        """, (game_signature,))
        
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"⚠️ Duplicate game detected with signature {game_signature} - skipping")
            cursor.execute("""
            SELECT recording_path, processed_time FROM processed_games 
            WHERE game_signature = ?
            """, (game_signature,))
            
            result = cursor.fetchone()
            if result:
                print(f"⚠️ Previously recorded via {result[0]} at {result[1]}")
            
            conn.close()
            return False
            
        # Continue with normal processing if not a duplicate
    except Exception as e:
        print(f"Error in duplicate detection: {e}")
        # Continue with normal processing if duplicate detection fails
'''
        
        # Insert the duplicate check code at the beginning of the function body
        modified_content = content[:func_body_start] + duplicate_check_code + content[func_body_start:]
        
        # Find where the game is added to history
        add_to_history = modified_content.find('cursor.execute(\'\'\'')
        if add_to_history == -1:
            print("❌ Could not find where game is added to history")
            return False
        
        # Find the end of the transaction (after the commit)
        commit_pos = modified_content.find('conn.commit()', add_to_history)
        if commit_pos == -1:
            print("❌ Could not find commit statement")
            return False
        
        end_pos = modified_content.find('\n', commit_pos)
        
        # Add code to record this game in processed_games table
        record_processed_code = '''
        # Record this game in processed_games to prevent duplicates
        try:
            cursor.execute("""
            INSERT INTO processed_games 
            (game_signature, date_time, username, stake, players, total_calls, processed_time, recording_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                game_signature,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                username,
                game_data.get("stake", 0),
                game_data.get("player_count", 0),
                len(game_data.get("called_numbers", [])),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "thread_safe_db.record_game_completed"
            ))
            conn.commit()
            print(f"✅ Recorded game with signature {game_signature} to prevent duplicates")
        except Exception as e:
            print(f"⚠️ Error recording processed game: {e}")
            # Continue even if recording fails
'''
        
        # Insert the record processed code after the commit
        modified_content = modified_content[:end_pos + 1] + record_processed_code + modified_content[end_pos + 1:]
        
        # Write the modified content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("✅ Successfully patched thread_safe_db.py")
        return True
    except Exception as e:
        print(f"❌ Error patching thread_safe_db.py: {e}")
        return False

def patch_stats_event_hooks():
    """
    Patch the stats_event_hooks.py file to prevent duplicate processing.
    This is another path for recording game completions.
    """
    file_path = 'stats_event_hooks.py'
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Create backup
    backup_path = os.path.join(BACKUP_DIR, f'stats_event_hooks.py.bak.{int(time.time())}')
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✅ Created backup at {backup_path}")
    except Exception as e:
        print(f"⚠️ Failed to create backup: {e}")
        response = input("Continue without backup? (y/n): ").strip().lower()
        if not response.startswith('y'):
            return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the _process_game_completed function
        if 'def _process_game_completed(self, event_data):' not in content:
            print("❌ Could not find _process_game_completed function")
            return False
        
        # Add import for hashlib if not present
        if 'import hashlib' not in content:
            import_section_end = content.find('import')
            while content.find('import', import_section_end + 1) > 0:
                import_section_end = content.find('import', import_section_end + 1)
            import_section_end = content.find('\n', import_section_end)
            
            content = content[:import_section_end + 1] + 'import hashlib\n' + content[import_section_end + 1:]
        
        # Find the beginning of the _process_game_completed function
        func_start = content.find('def _process_game_completed(self, event_data):')
        func_body_start = content.find(':', func_start) + 1
        
        # Add duplicate detection code at the beginning of the function
        duplicate_check_code = '''
        # Skip if in demo mode
        if event_data.get("is_demo_mode", False):
            print("Game was in demo mode - statistics not updated")
            return False
            
        # Create a unique signature for this game to detect duplicates
        try:
            username = str(event_data.get("winner_name", "Unknown"))
            stake = str(event_data.get("stake", 0))
            players = str(event_data.get("player_count", 0))
            total_calls = str(len(event_data.get("called_numbers", [])))
            timestamp = str(event_data.get("timestamp", time.time()))
            
            # Create a unique signature
            signature_str = f"{username}:{stake}:{players}:{total_calls}:{timestamp}"
            game_signature = hashlib.md5(signature_str.encode()).hexdigest()
            
            # Check if this game has already been processed
            import sqlite3
            conn = sqlite3.connect('data/stats.db')
            cursor = conn.cursor()
            
            # Ensure the processed_games table exists
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS processed_games (
                game_signature TEXT PRIMARY KEY,
                date_time TEXT,
                username TEXT,
                stake REAL,
                players INTEGER,
                total_calls INTEGER,
                processed_time TEXT,
                recording_path TEXT
            )
            """)
            
            cursor.execute("""
            SELECT COUNT(*) FROM processed_games 
            WHERE game_signature = ?
            """, (game_signature,))
            
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"⚠️ Duplicate game detected in event hooks with signature {game_signature} - skipping")
                cursor.execute("""
                SELECT recording_path, processed_time FROM processed_games 
                WHERE game_signature = ?
                """, (game_signature,))
                
                result = cursor.fetchone()
                if result:
                    print(f"⚠️ Previously recorded via {result[0]} at {result[1]}")
                
                conn.close()
                return False
                
            # Continue with normal processing if not a duplicate
            conn.close()
        except Exception as e:
            print(f"Error in event hooks duplicate detection: {e}")
            # Continue with normal processing if duplicate detection fails
'''
        
        # Insert the duplicate check code at the beginning of the function body
        modified_content = content[:func_body_start] + duplicate_check_code + content[func_body_start:]
        
        # Find where the game is recorded via thread_safe_db
        db_record = modified_content.find('db_result = thread_safe_db.record_game_completed(event_data)')
        if db_record == -1:
            print("❌ Could not find where game is recorded via thread_safe_db")
            return False
        
        # Find the line after the recording
        next_line = modified_content.find('\n', db_record)
        
        # Add code to record this game in processed_games table
        record_processed_code = '''
            # Record this game in processed_games to prevent duplicates
            if db_result:
                try:
                    import sqlite3
                    conn = sqlite3.connect('data/stats.db')
                    cursor = conn.cursor()
                    
                    cursor.execute("""
                    INSERT INTO processed_games 
                    (game_signature, date_time, username, stake, players, total_calls, processed_time, recording_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        game_signature,
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        username,
                        event_data.get("stake", 0),
                        event_data.get("player_count", 0),
                        len(event_data.get("called_numbers", [])),
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        "stats_event_hooks._process_game_completed"
                    ))
                    conn.commit()
                    conn.close()
                    print(f"✅ Recorded game with signature {game_signature} in event hooks to prevent duplicates")
                except Exception as e:
                    print(f"⚠️ Error recording processed game in event hooks: {e}")
                    # Continue even if recording fails
'''
        
        # Insert the record processed code after the recording line
        modified_content = modified_content[:next_line + 1] + record_processed_code + modified_content[next_line + 1:]
        
        # Write the modified content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("✅ Successfully patched stats_event_hooks.py")
        return True
    except Exception as e:
        print(f"❌ Error patching stats_event_hooks.py: {e}")
        return False

def fix_existing_data():
    """
    Fix existing data in the database by recounting games and recalculating earnings.
    """
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get all dates in daily_stats
        cursor.execute('SELECT date FROM daily_stats')
        dates = [row[0] for row in cursor.fetchall()]
        
        fixed_count = 0
        for date in dates:
            # Count actual games for this date (excluding resets and games with 0 calls)
            cursor.execute('''
                SELECT COUNT(*) FROM game_history 
                WHERE date(date_time) = ? 
                AND username NOT LIKE "Game Reset" 
                AND total_calls > 0
            ''', (date,))
            actual_count = cursor.fetchone()[0]
            
            # Sum actual fees for this date
            cursor.execute('''
                SELECT SUM(fee) FROM game_history 
                WHERE date(date_time) = ? 
                AND username NOT LIKE "Game Reset" 
                AND total_calls > 0
            ''', (date,))
            actual_earnings = cursor.fetchone()[0] or 0
            
            # Get current values
            cursor.execute('SELECT games_played, earnings FROM daily_stats WHERE date = ?', (date,))
            result = cursor.fetchone()
            current_count = result[0]
            current_earnings = result[1]
            
            if current_count != actual_count or abs(current_earnings - actual_earnings) > 0.01:
                # Update with correct values
                cursor.execute('''
                    UPDATE daily_stats 
                    SET games_played = ?, earnings = ?
                    WHERE date = ?
                ''', (actual_count, actual_earnings, date))
                
                print(f"📊 Fixed stats for {date}:")
                print(f"  - Games: {current_count} → {actual_count}")
                print(f"  - Earnings: {current_earnings} → {actual_earnings}")
                fixed_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ Fixed data for {fixed_count} dates")
        return fixed_count
    except Exception as e:
        print(f"❌ Error fixing existing data: {e}")
        return 0

def main():
    print("=" * 80)
    print("TARGETED FIX FOR STATS OVERCOUNTING ISSUE")
    print("This utility fixes the issue where the stats page overcounts balances and game counts")
    print("after playing more than 10 games continuously.")
    print("=" * 80)
    
    # Create backup first
    if not create_backup():
        response = input("Failed to create backup. Continue anyway? (y/n): ").strip().lower()
        if not response.startswith('y'):
            print("Operation cancelled.")
            return
    
    # Add processed_games table
    add_processed_games_table()
    
    # Patch thread_safe_db.py
    patch_thread_safe_db()
    
    # Patch stats_event_hooks.py
    patch_stats_event_hooks()
    
    # Fix existing data
    fix_existing_data()
    
    print("=" * 80)
    print("FIX COMPLETED")
    print("The stats page should now correctly track game counts and balances.")
    print("=" * 80)
    
    # Pause at the end to let user read the output if run by double-clicking
    if os.name == 'nt':  # Windows
        os.system('pause')

if __name__ == "__main__":
    main()