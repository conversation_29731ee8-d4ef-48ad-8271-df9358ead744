#!/usr/bin/env python3
"""
Demo script to show the time warning feature in the actual board selection page
"""

import pygame
import sys
import os
from datetime import datetime

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_time_warning():
    """Demo the time warning in the actual board selection"""
    
    print("Time Warning Demo")
    print("=" * 50)
    print("This demo will show the board selection page with a time warning.")
    print("The warning simulates the scenario where:")
    print("  Database time: 06/28/2025")
    print("  Local PC time: 01/21/2020")
    print()
    print("Press SPACE to toggle the simulated time warning on/off")
    print("Press ESC to exit")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    # Set up the display
    screen = pygame.display.set_mode((1280, 720), pygame.RESIZABLE)
    pygame.display.set_caption("Board Selection - Time Warning Demo")
    
    try:
        # Import the board selection window
        from Board_selection_fixed import BoardSelectionWindow
        
        # Create the board selection window
        board_window = BoardSelectionWindow(screen)
        
        # Initially disable the time warning
        simulation_active = False
        
        # Run the demo
        clock = pygame.time.Clock()
        running = True
        
        while running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        # Toggle simulation
                        if simulation_active:
                            # Disable simulation
                            board_window.time_checker.clear_simulated_local_time()
                            board_window.time_checker.warning_active = False
                            simulation_active = False
                            print("Time warning simulation DISABLED")
                        else:
                            # Enable simulation
                            simulated_local_time = datetime(2020, 1, 21, 10, 30, 0).timestamp()
                            board_window.time_checker.set_simulated_local_time(simulated_local_time)
                            board_window.time_checker.last_check_time = 0
                            board_window.time_checker.check_time_difference()
                            simulation_active = True
                            print("Time warning simulation ENABLED")
                else:
                    # Let the board selection handle other events
                    # For this demo, we'll just handle basic events
                    pass
            
            # Update the time checker
            if hasattr(board_window, 'time_checker'):
                board_window.time_checker.update()
            
            # Draw the board selection
            try:
                board_window.draw()
            except Exception as e:
                print(f"Error drawing board selection: {e}")
                # Draw a simple fallback
                screen.fill((30, 30, 60))
                font = pygame.font.Font(None, 36)
                error_text = font.render("Error drawing board selection", True, (255, 100, 100))
                screen.blit(error_text, (50, 50))
            
            # Draw demo instructions overlay
            overlay_font = pygame.font.Font(None, 24)
            instructions = [
                "DEMO CONTROLS:",
                "SPACE - Toggle time warning simulation",
                "ESC - Exit demo",
                "",
                f"Simulation: {'ON' if simulation_active else 'OFF'}"
            ]
            
            # Semi-transparent background for instructions
            overlay_surface = pygame.Surface((300, len(instructions) * 25 + 20), pygame.SRCALPHA)
            overlay_surface.fill((0, 0, 0, 128))
            screen.blit(overlay_surface, (10, 10))
            
            # Draw instruction text
            for i, instruction in enumerate(instructions):
                color = (255, 255, 0) if "Simulation:" in instruction else (255, 255, 255)
                text_surface = overlay_font.render(instruction, True, color)
                screen.blit(text_surface, (20, 20 + i * 25))
            
            # Update display
            pygame.display.flip()
            clock.tick(60)
        
        # Clean up
        if hasattr(board_window, 'time_checker'):
            board_window.time_checker.clear_simulated_local_time()
        
    except ImportError as e:
        print(f"Error importing BoardSelectionWindow: {e}")
        print("Make sure you're running this from the correct directory.")
    except Exception as e:
        print(f"Error during demo: {e}")
    finally:
        pygame.quit()

if __name__ == "__main__":
    demo_time_warning()