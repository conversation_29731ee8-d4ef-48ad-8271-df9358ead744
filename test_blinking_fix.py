#!/usr/bin/env python3
"""
Test script to verify that the blinking issue in the stats page has been fixed.

This script will:
1. Import the stats page module
2. Check that the frame skipping logic has been properly modified
3. Verify that essential UI elements are always drawn
4. Test the performance optimizations
"""

import sys
import os
import inspect

def test_blinking_fix():
    """Test that the blinking fix has been properly implemented."""
    print("🔍 Testing Stats Page Blinking Fix...")
    print("=" * 50)
    
    try:
        # Import the stats page module
        sys.path.append('.')
        from stats_page import StatsPage
        
        print("✅ Successfully imported StatsPage")
        
        # Read the source file directly for more reliable testing
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            source_content = f.read()
        source_lines = source_content.split('\n')
        
        # Test 1: Check that old frame skipping variables are removed
        old_variables = ['skip_expensive_frame', 'skip_expensive']
        found_old_variables = []
        
        for line in source_lines:
            for var in old_variables:
                if var in line and '=' in line:
                    found_old_variables.append(var)
        
        if found_old_variables:
            print(f"❌ Found old frame skipping variables: {found_old_variables}")
            return False
        else:
            print("✅ Old problematic frame skipping variables removed")
        
        # Test 2: Check that game history is always drawn
        game_history_lines = [line for line in source_lines if 'draw_game_history' in line and 'self.' in line]

        if not game_history_lines:
            print("❌ draw_game_history call not found")
            # Debug: print all lines that contain draw_game_history
            debug_lines = [line.strip() for line in source_lines if 'draw_game_history' in line]
            print(f"Debug - Found lines with draw_game_history: {debug_lines}")
            return False

        # Check that game history is not conditionally skipped
        conditional_game_history = [line for line in game_history_lines if 'if not' in line and 'skip' in line]

        if conditional_game_history:
            print(f"❌ Game history still conditionally skipped: {conditional_game_history}")
            return False
        else:
            print("✅ Game history is always drawn (no conditional skipping)")
        
        # Test 3: Check that notifications are always drawn
        notification_lines = [line for line in source_lines if 'draw_notifications' in line]
        
        if not notification_lines:
            print("❌ draw_notifications call not found")
            return False
        
        conditional_notifications = [line for line in notification_lines if 'if not' in line and 'skip' in line]
        
        if conditional_notifications:
            print(f"❌ Notifications still conditionally skipped: {conditional_notifications}")
            return False
        else:
            print("✅ Notifications are always drawn (no conditional skipping)")
        
        # Test 4: Check that credit history is always drawn
        credit_history_lines = [line for line in source_lines if 'draw_credit_history_section' in line]
        
        if not credit_history_lines:
            print("❌ draw_credit_history_section call not found")
            return False
        
        conditional_credit = [line for line in credit_history_lines if 'if not' in line and 'skip' in line]
        
        if conditional_credit:
            print(f"❌ Credit history still conditionally skipped: {conditional_credit}")
            return False
        else:
            print("✅ Credit history is always drawn (no conditional skipping)")
        
        # Test 5: Check that intelligent performance optimizations are in place
        performance_lines = [line for line in source_lines if 'skip_heavy_calculations' in line or 'skip_decorative_animations' in line]
        
        if not performance_lines:
            print("❌ Performance optimizations not found")
            return False
        else:
            print("✅ Intelligent performance optimizations implemented")
        
        # Test 6: Check for BLINKING FIX comments
        fix_comments = [line for line in source_lines if 'BLINKING FIX' in line]
        
        if not fix_comments:
            print("❌ BLINKING FIX comments not found")
            return False
        else:
            print(f"✅ Found {len(fix_comments)} BLINKING FIX comments documenting the changes")
        
        print("\n🎉 All tests passed! The blinking fix has been successfully implemented.")
        print("\nSummary of fixes:")
        print("- Removed problematic frame skipping that caused blinking")
        print("- Essential UI elements (game history, notifications, credit history) are always drawn")
        print("- Added intelligent performance optimizations for non-essential elements")
        print("- Maintained performance while eliminating visual flickering")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import StatsPage: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")
        return False

def test_performance_impact():
    """Test that the performance optimizations don't negatively impact the application."""
    print("\n🚀 Testing Performance Impact...")
    print("=" * 50)
    
    try:
        # Check that the new frame skipping logic is more intelligent
        from stats_page import StatsPage
        
        # Create a mock screen for testing
        import pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        
        # Create a stats page instance
        stats_page = StatsPage(screen)
        
        # Test that the frame counter is properly initialized
        if hasattr(stats_page, '_frame_count'):
            print("✅ Frame counter properly initialized")
        
        # Test that anti-blinking mechanisms are in place
        if hasattr(stats_page, '_stable_ui_state'):
            print("✅ Stable UI state management in place")
        
        if hasattr(stats_page, '_section_update_cooldown'):
            print("✅ Section update cooldown mechanism active")
        
        print("✅ Performance optimizations verified")
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

if __name__ == "__main__":
    print("Stats Page Blinking Fix Test Suite")
    print("==================================\n")
    
    # Run the tests
    fix_test_passed = test_blinking_fix()
    performance_test_passed = test_performance_impact()
    
    print("\n" + "=" * 50)
    if fix_test_passed and performance_test_passed:
        print("🎉 ALL TESTS PASSED! The blinking issue has been successfully resolved.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please review the implementation.")
        sys.exit(1)
