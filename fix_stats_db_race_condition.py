#!/usr/bin/env python3
"""
Fix Stats Database Race Condition

This script patches the stats_db.py file to prevent race conditions that cause
overcounting in the stats page after playing more than 10 games continuously.
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """Create a backup of the file before modifying it."""
    backup_dir = os.path.join('data', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = os.path.basename(file_path)
    backup_path = os.path.join(backup_dir, f'{filename}_backup_{timestamp}')
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✅ Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

def patch_stats_db():
    """Patch the stats_db.py file to fix race conditions."""
    file_path = os.path.join('stats_db.py')
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    # Create backup
    if not backup_file(file_path):
        print("⚠ Failed to create backup, proceeding with caution")
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Patch 1: Add transaction tracking to add_game_to_history method
        add_game_pattern = r'def add_game_to_history\(self, username, house, stake, players, total_calls,'
        add_game_replacement = '''def add_game_to_history(self, username, house, stake, players, total_calls,'''
        
        # Patch 2: Add transaction check before inserting game history
        insert_pattern = r'cursor\.execute\(\'\'\'\s+INSERT INTO game_history'
        insert_replacement = '''# Check if this is a duplicate transaction
            cursor.execute("""
            SELECT COUNT(*) FROM processed_transactions 
            WHERE game_id IN (
                SELECT id FROM game_history 
                WHERE date_time = ? AND username = ? AND stake = ? AND players = ?
            )
            """, (date_time, username, stake, players))
            
            if cursor.fetchone()[0] > 0:
                logging.warning(f"Duplicate game transaction detected: {username}, {date_time}")
                conn.close()
                return -1
                
            cursor.execute(\'\'\'
            INSERT INTO game_history'''
        
        # Patch 3: Add transaction tracking after successful insert
        commit_pattern = r'conn\.commit\(\)\s+conn\.close\(\)'
        commit_replacement = '''conn.commit()
            
            # Record this transaction to prevent duplicates
            try:
                cursor.execute("""
                INSERT INTO processed_transactions (game_id, processed_time)
                VALUES (?, ?)
                """, (game_id, independent_strftime('%Y-%m-%d %H:%M:%S')))
                conn.commit()
            except Exception as e:
                logging.error(f"Error recording transaction: {e}")
                
            conn.close()'''
        
        # Apply patches
        content = re.sub(add_game_pattern, add_game_replacement, content)
        content = re.sub(insert_pattern, insert_replacement, content)
        content = re.sub(commit_pattern, commit_replacement, content)
        
        # Write modified content back to file
        with open(file_path, 'w') as f:
            f.write(content)
        
        print("✅ Successfully patched stats_db.py")
        return True
    except Exception as e:
        print(f"❌ Error patching file: {e}")
        return False

def ensure_transaction_table():
    """Ensure the processed_transactions table exists in the database."""
    try:
        import sqlite3
        
        db_path = os.path.join('data', 'stats.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create processed_transactions table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS processed_transactions (
            game_id INTEGER PRIMARY KEY,
            processed_time TEXT,
            FOREIGN KEY (game_id) REFERENCES game_history(id)
        )
        ''')
        
        # Create index for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_processed_transactions_game_id ON processed_transactions(game_id)')
        
        conn.commit()
        conn.close()
        
        print("✅ Ensured transaction tracking table exists")
        return True
    except Exception as e:
        print(f"❌ Error creating transaction table: {e}")
        return False

def main():
    print("=" * 80)
    print("STATS DATABASE RACE CONDITION FIX")
    print("This utility patches the stats_db.py file to prevent race conditions")
    print("that cause overcounting in the stats page after playing many games.")
    print("=" * 80)
    
    # Patch the stats_db.py file
    if patch_stats_db():
        # Ensure transaction table exists
        ensure_transaction_table()
        
        print("=" * 80)
        print("FIX COMPLETED")
        print("The stats page should now correctly track game counts and balances.")
        print("=" * 80)
    else:
        print("=" * 80)
        print("FIX FAILED")
        print("Please check the error messages above.")
        print("=" * 80)
    
    # Pause at the end to let user read the output if run by double-clicking
    if os.name == 'nt':  # Windows
        os.system('pause')

if __name__ == "__main__":
    main()