# Time Warning Feature Documentation

## Overview

The Time Warning Feature automatically detects differences between database timestamps and the current local system time, displaying a prominent warning when the database time is significantly ahead of the local PC time.

## Problem Scenario

This feature addresses situations where:
- **Database time**: 6/28/2025 12:00:00
- **Local PC time**: 1/21/2020 10:30:00

When the database contains timestamps from the future compared to the local system time, it indicates that the Windows system clock needs to be manually adjusted.

## Features

### Automatic Detection
- ✅ Monitors database files for timestamps
- ✅ Compares with local system time every 5 seconds
- ✅ Triggers warning when database time is >1 minute ahead
- ✅ Checks multiple database files: `players.json`, `ui_state.json`, `game_settings.json`, etc.

### Visual Warning Display
- ✅ Prominent red warning overlay
- ✅ Clear time difference calculation
- ✅ Step-by-step instructions for fixing Windows time
- ✅ Progress bar showing remaining warning duration
- ✅ Blinking close instruction
- ✅ Automatic dismissal after 15 seconds

### Warning Message Content
```
⚠️ TIME SYNC WARNING! ⚠️
Database time: 06/28/2025 12:00:00
Local PC time: 01/21/2020 10:30:00
Time difference: 1985 days 3 hours 8 minutes

Please adjust your Windows system time manually:
1. Right-click on the clock in the taskbar
2. Select 'Adjust date/time'
3. Turn off 'Set time automatically'
4. Click 'Change' and set the correct date/time

📞 NEED HELP? CALL NOW: +251913250168
```

### Enhanced Phone Number Display
- ✅ **Bold and larger font** for maximum visibility
- ✅ **Animated pulsing effect** to draw attention
- ✅ **Color cycling animation** through bright colors
- ✅ **Glow effect** around the phone number
- ✅ **Highlighted background** with animated transparency
- ✅ **Double border** with animation
- ✅ **Rotating icons** around the phone number
- ✅ **Blinking "URGENT" indicator** above the number
- ✅ **Phone emoji** and "CALL NOW" text for clarity

## Implementation Details

### Files Modified
- `Board_selection_fixed.py` - Added `TimeChecker` class and integration
- Added time checker initialization in `BoardSelectionWindow.__init__()`
- Added time checker update in main game loop
- Added warning display in draw method
- Added redraw condition for active warnings

### TimeChecker Class Methods
- `get_database_timestamp()` - Scans database files for most recent timestamp
- `check_time_difference()` - Compares database vs local time
- `update()` - Handles warning duration and periodic checks
- `draw_warning()` - Renders the warning overlay
- `set_simulated_local_time()` - For testing purposes
- `clear_simulated_local_time()` - Restore normal time checking

## Testing

### Test Scripts Provided

1. **`test_time_checker.py`** - Creates test data with future timestamps
   ```bash
   python test_time_checker.py          # Create future timestamp data
   python test_time_checker.py scenario # Create 6/28/2025 scenario data
   python test_time_checker.py restore  # Restore normal timestamps
   ```

2. **`test_time_warning.py`** - Standalone test of warning display
   ```bash
   python test_time_warning.py
   ```

3. **`demo_time_warning.py`** - Interactive demo in board selection
   ```bash
   python demo_time_warning.py
   # Press SPACE to toggle warning simulation
   # Press ESC to exit
   ```

### Manual Testing in Board Selection
- Press **Ctrl+T** in the board selection page to toggle time warning test
- This simulates the scenario: DB time 6/28/2025, Local time 1/21/2020
- Press **Ctrl+T** again to disable the test

## Configuration

### Adjustable Parameters
```python
# In TimeChecker.__init__()
self.warning_duration = 15000      # Warning display time (15 seconds)
self.check_interval = 5000         # Check frequency (5 seconds)

# In check_time_difference()
if time_diff > 60:                 # Threshold: 60 seconds
```

### Database Files Monitored
- `data/players.json`
- `data/ui_state.json`
- `data/game_settings.json`
- `data/stats.json`
- `data/current_session.json`

## Usage Instructions for End Users

### When the Warning Appears
1. **Don't panic** - This is a helpful notification
2. **Read the displayed times** - Compare database vs local PC time
3. **Follow the step-by-step instructions** shown in the warning
4. **Manually adjust Windows system time**:
   - Right-click on taskbar clock
   - Select "Adjust date/time"
   - Turn off "Set time automatically"
   - Click "Change" and set correct date/time
5. **Restart the application** to verify the fix
6. **Need help?** - Call +251913250168 for technical support

### Prevention
- Ensure Windows time synchronization is working properly
- Check that your system timezone is correct
- Verify internet connection for automatic time updates

## Technical Notes

### Performance Considerations
- Time checks occur every 5 seconds (not every frame)
- Warning display only redraws when active
- Minimal impact on game performance

### Error Handling
- Graceful handling of missing database files
- Safe fallback if timestamp parsing fails
- Continues normal operation if time check fails

### Compatibility
- Works with existing save/load system
- Compatible with all game modes
- No changes required to existing data files

## Future Enhancements

Potential improvements:
- [ ] Option to automatically sync time (if permissions allow)
- [ ] Different warning thresholds for different scenarios
- [ ] Integration with system time change notifications
- [ ] Logging of time sync issues for debugging

## Troubleshooting

### Warning Doesn't Appear
- Check if database files exist in `data/` directory
- Verify timestamps in JSON files are valid
- Ensure time difference is >60 seconds

### Warning Appears Incorrectly
- Check system time is correct
- Verify timezone settings
- Use `Ctrl+T` to test with known scenario

### Performance Issues
- Increase `check_interval` if needed
- Reduce `warning_duration` for faster dismissal