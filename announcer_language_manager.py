import os

class AnnouncerLanguageManager:
    """
    Manages the announcer language options for the bingo game.
    """
    
    @staticmethod
    def get_available_languages():
        """
        Get a list of available announcer languages from the assets/Audios and assets/audio-effects directories.
        
        Returns:
            list: List of available language options, starting with "Default (English)"
        """
        # Start with the default announcer
        languages = ["Default (English)"]
        found_languages = set()
        
        # Check for available language folders in assets/Audios
        audio_dirs = ["assets/Audios", "assets/audio-effects"]
        for audio_dir in audio_dirs:
            if os.path.exists(audio_dir):
                for folder in os.listdir(audio_dir):
                    folder_path = os.path.join(audio_dir, folder)
                    if os.path.isdir(folder_path) and folder != "English" and folder not in found_languages:  # Skip English as it's included in Default
                        languages.append(folder)
                        found_languages.add(folder)
        
        # If no languages were found, add some hardcoded options
        if len(languages) <= 1:
            languages = ["Default (English)", "Amharic", "Oromo", "Agewgna", "Somali", "Tigrigna"]
            
        return languages
    
    @staticmethod
    def get_audio_path(number, letter, language):
        """
        Get the audio file path for a number announcement in the specified language.
        
        Args:
            number: The bingo number to announce
            letter: The BINGO letter for the number
            language: The language to use for the announcement
            
        Returns:
            str: The path to the audio file
        """
        # If no language is specified, try to get it from settings
        if language is None:
            try:
                from settings_manager import SettingsManager
                settings_manager = SettingsManager()
                language = settings_manager.get_setting('audio', 'announcer_language', 'Default (English)')
                print(f"Using language from settings: {language}")
            except Exception as e:
                print(f"Error getting language from settings: {e}")
                language = "English"
        
        # Extract language name from format like "Default (English)"
        if '(' in language and ')' in language:
            language_name = language.split('(')[1].split(')')[0]
        else:
            language_name = language
                
        if language == "Default" or language == "Default (English)" or language is None or language_name == "English":
            # Use the default English announcements
            audio_path = f"assets/Audios/English/{letter}{number}.mp3"
        else:
            # Try different possible file naming conventions
            possible_paths = [
                # Standard format
                f"assets/Audios/{language_name}/{letter}{number}.mp3",
                # Format with language name
                f"assets/Audios/{language_name}/{letter}{number} {language_name}.mp3",
                # Format with language name and suffix
                f"assets/Audios/{language_name}/{letter}{number} {language_name} 2.mp3",
                # Format with just letter and number
                f"assets/Audios/{language_name}/{letter}{number}.mp3"
            ]
            
            # Try each path until we find one that exists
            audio_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    audio_path = path
                    break
            
            # If no path exists, fall back to English
            if audio_path is None:
                print(f"Could not find {language_name} announcement for {letter}{number}, falling back to English")
                audio_path = f"assets/Audios/English/{letter}{number}.mp3"
        
        return audio_path
        
    @staticmethod
    def get_effect_audio_path(audio_name, language=None, fallback_language="English"):
        """
        Get the path to a language-specific audio effect file
        
        Args:
            audio_name: Base name of the audio file (e.g., "game_started", "Game_Paused_")
            language: Language to use for the audio effect, or None to use the default
            fallback_language: Language to use if the selected language is not available
            
        Returns:
            Path to the audio file, or None if not found
        """
        # If no language is specified, try to get it from settings
        if language is None:
            try:
                from settings_manager import SettingsManager
                settings_manager = SettingsManager()
                language = settings_manager.get_setting('audio', 'announcer_language', 'Default (English)')
                print(f"Using language from settings: {language}")
            except Exception as e:
                print(f"Error getting language from settings: {e}")
                language = "English"
                
        # Extract language name from format like "Default (English)"
        if '(' in language and ')' in language:
            language_name = language.split('(')[1].split(')')[0]
        else:
            language_name = language
            
        # If Default or English, use English
        if language == "Default (English)" or language_name == "English":
            language_name = "English"
            
        audio_path = None
        
        # Try language-specific directory first with various naming patterns
        possible_paths = [
            # Standard format with .mp3 extension
            f"assets/audio-effects/{language_name}/{audio_name}.mp3",
            # Without .mp3 extension (in case the filename already includes it)
            f"assets/audio-effects/{language_name}/{audio_name}",
            # Try with lowercase filename
            f"assets/audio-effects/{language_name}/{audio_name.lower()}.mp3",
            # Try with uppercase first letter
            f"assets/audio-effects/{language_name}/{audio_name[0].upper() + audio_name[1:]}.mp3"
        ]
        
        # Try each path for the selected language
        for path in possible_paths:
            if os.path.exists(path):
                audio_path = path
                print(f"Using language-specific audio for {language_name}: {audio_path}")
                return audio_path
        
        # If we couldn't find the file in the selected language, log this clearly
        print(f"WARNING: Could not find audio file '{audio_name}' for language '{language_name}'")
        print(f"Checked paths: {possible_paths}")
        
        # Only now try the fallback language
        fallback_paths = [
            f"assets/audio-effects/{fallback_language}/{audio_name}.mp3",
            f"assets/audio-effects/{fallback_language}/{audio_name}",
            f"assets/audio-effects/{fallback_language}/{audio_name.lower()}.mp3",
            f"assets/audio-effects/{fallback_language}/{audio_name[0].upper() + audio_name[1:]}.mp3"
        ]
        
        for path in fallback_paths:
            if os.path.exists(path):
                audio_path = path
                print(f"Using fallback language audio ({fallback_language}): {audio_path}")
                return audio_path
            
        # As a last resort, try the root directory
        root_paths = [
            f"assets/audio-effects/{audio_name}.mp3",
            f"assets/audio-effects/{audio_name}"
        ]
        
        for path in root_paths:
            if os.path.exists(path):
                audio_path = path
                print(f"Using root directory audio: {audio_path}")
                return audio_path
            
        print(f"ERROR: No audio file found for '{audio_name}' in any language directory")
        return None
        
    @staticmethod
    def get_cartella_announcement_path(cartella_number, language=None):
        """
        Get the path to a language-specific cartella announcement audio file
        
        Args:
            cartella_number: The cartella number to announce
            language: Language to use for the announcement, or None to use the default from settings
            
        Returns:
            Path to the audio file, or None if not found
        """
        # If no language is specified, try to get it from settings
        if language is None:
            try:
                from settings_manager import SettingsManager
                settings_manager = SettingsManager()
                language = settings_manager.get_setting('audio', 'announcer_language', 'Default (English)')
                print(f"Using language from settings for cartella announcement: {language}")
            except Exception as e:
                print(f"Error getting language from settings for cartella announcement: {e}")
                language = "English"
                
        # Extract language name from format like "Default (English)"
        if '(' in language and ')' in language:
            language_name = language.split('(')[1].split(')')[0]
        else:
            language_name = language
            
        # If Default or English, use English
        if language == "Default (English)" or language_name == "English":
            language_name = "English"
            
        # Try language-specific directory first with various naming patterns
        language_specific_paths = [
            # Language-specific directory with standard naming
            f"assets/cartella-announcer/{language_name}/{cartella_number} Registration {language_name}.mp3",
            # Language-specific directory with just the number
            f"assets/cartella-announcer/{language_name}/{cartella_number}.mp3",
            # Language-specific directory with alternative naming
            f"assets/cartella-announcer/{language_name}/{cartella_number} Registration.mp3",
            # Root directory with language-specific naming
            f"assets/cartella-announcer/{cartella_number} Registration {language_name}.mp3"
        ]
        
        # Try each path for the selected language
        for path in language_specific_paths:
            if os.path.exists(path):
                print(f"Found cartella announcement for {language_name}: {path}")
                return path
        
        # If we couldn't find the file in the selected language, log this clearly
        print(f"WARNING: Could not find cartella announcement for number {cartella_number} in language {language_name}")
        print(f"Checked paths: {language_specific_paths}")
        
        # Only now try the fallback to English
        english_fallback_paths = [
            f"assets/cartella-announcer/English/{cartella_number} Registration English.mp3",
            f"assets/cartella-announcer/English/{cartella_number}.mp3",
            f"assets/cartella-announcer/English/{cartella_number} Registration.mp3"
        ]
        
        # Try each English fallback path
        for path in english_fallback_paths:
            if os.path.exists(path):
                print(f"Using English fallback for cartella announcement: {path}")
                return path
        
        # Last resort - try hardcoded Amharic (for backward compatibility)
        legacy_path = f"assets/cartella-announcer/{cartella_number} Registration Amharic.mp3"
        if os.path.exists(legacy_path):
            print(f"Using legacy Amharic fallback for cartella announcement: {legacy_path}")
            return legacy_path
                
        print(f"ERROR: No cartella announcement found for number {cartella_number} in any language")
        return None
