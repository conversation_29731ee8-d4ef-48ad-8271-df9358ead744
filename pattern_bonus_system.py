"""
Pattern Bonus System for Bingo Game

This module defines bonuses for specific pattern claims in the bingo game.
Players receive bonus points for claiming specific winning patterns, especially when claimed early.
"""

class PatternBonusSystem:
    """
    Manages bonuses for pattern claims in the bingo game.
    
    The bonus system rewards players who claim specific winning patterns,
    with special bonuses for early claims (within 5 calls).
    """
    
    def __init__(self):
        """Initialize the pattern bonus system with default bonus configurations."""
        # Import settings manager
        from settings_manager import SettingsManager
        self.settings = SettingsManager()
        
        # Default early claim threshold (will be overridden by pattern-specific settings)
        self.early_claim_threshold = 5
        
        # Define pattern difficulty tiers for description purposes
        self.pattern_tiers = {
            "easy": 100,    # Rows, columns
            "medium": 200,  # Diagonals, four corners
            "hard": 300,    # Special shapes (Inner Square, Diamond, Cross)
            "very_hard": 500 # Blackout/Coverall
        }
        
        # Map patterns to their difficulty tiers for description purposes
        self.pattern_difficulty = {
            # Easy patterns
            "Row 1": "easy",
            "Row 2": "easy",
            "Row 3": "easy",
            "Row 4": "easy",
            "Row 5": "easy",
            "Column 1": "easy",
            "Column 2": "easy",
            "Column 3": "easy",
            "Column 4": "easy",
            "Column 5": "easy",
            
            # Medium patterns
            "Diagonal (top-left to bottom-right)": "medium",
            "Diagonal (top-right to bottom-left)": "medium",
            "Four Corners": "medium",
            
            # Hard patterns
            "Inner Square": "hard",
            "3x3 Square": "hard",
            "Diamond": "hard",
            "Cross": "hard",
            
            # Very hard patterns
            "Blackout/Coverall": "very_hard",
            "Coverall": "very_hard",
            "Blackout": "very_hard"
        }
        
    def get_pattern_tier(self, pattern_name):
        """
        Get the difficulty tier for a pattern.
        
        Args:
            pattern_name: The name of the pattern
            
        Returns:
            str: The difficulty tier ("easy", "medium", "hard", "very_hard")
        """
        # Normalize pattern name to handle variations
        normalized_name = self._normalize_pattern_name(pattern_name)
        
        # Return the tier or default to "easy" if not found
        return self.pattern_difficulty.get(normalized_name, "easy")
    
    def _normalize_pattern_name(self, pattern_name):
        """
        Normalize pattern names to handle variations in naming.
        
        Args:
            pattern_name: The pattern name to normalize
            
        Returns:
            str: The normalized pattern name
        """
        # Handle common variations in pattern names
        if pattern_name is None:
            return "Unknown"
            
        pattern_name = pattern_name.strip()
        pattern_lower = pattern_name.lower()
        
        # Handle diagonal pattern variations with more comprehensive checks
        if "diagonal" in pattern_lower or "diag" in pattern_lower:
            if any(term in pattern_lower for term in ["top-left", "bottom-right", "topleft", "bottomright", "tl", "br", "main", "primary"]):
                return "Diagonal (top-left to bottom-right)"
            elif any(term in pattern_lower for term in ["top-right", "bottom-left", "topright", "bottomleft", "tr", "bl", "secondary", "anti"]):
                return "Diagonal (top-right to bottom-left)"
            # If it's just "diagonal" without specifics, default to top-left to bottom-right
            return "Diagonal (top-left to bottom-right)"
        
        # Handle other pattern variations with more comprehensive checks
        if any(term in pattern_lower for term in ["four corner", "4 corner", "4corner", "corners", "4-corner"]):
            return "Four Corners"
            
        if any(term in pattern_lower for term in ["blackout", "coverall", "cover all", "full house", "full card", "full board"]):
            return "Blackout/Coverall"
            
        if any(term in pattern_lower for term in ["inner square", "3x3", "3 x 3", "middle square", "center square", "inside square"]):
            return "Inner Square"
            
        if any(term in pattern_lower for term in ["diamond", "rhombus", "kite"]):
            return "Diamond"
            
        if any(term in pattern_lower for term in ["cross", "plus", "+", "x", "middle cross", "center cross"]):
            return "Cross"
            
        # Handle row patterns with more comprehensive checks
        if any(term in pattern_lower for term in ["row", "line", "horizontal"]):
            for i in range(1, 6):
                # Check for various formats: "Row 1", "Row#1", "Row-1", "1st Row", "First Row", etc.
                if any(format in pattern_lower for format in [
                    f"row {i}", f"row{i}", f"row-{i}", f"row#{i}", f"row {i}th", f"row {i}st", f"row {i}nd", f"row {i}rd",
                    f"{i}th row", f"{i}st row", f"{i}nd row", f"{i}rd row", f"{i} row"
                ]):
                    return f"Row {i}"
                    
        # Handle column patterns with more comprehensive checks
        if any(term in pattern_lower for term in ["column", "col", "vertical"]):
            for i in range(1, 6):
                # Check for various formats: "Column 1", "Col#1", "Col-1", "1st Column", "First Column", etc.
                if any(format in pattern_lower for format in [
                    f"column {i}", f"column{i}", f"column-{i}", f"column#{i}", 
                    f"col {i}", f"col{i}", f"col-{i}", f"col#{i}",
                    f"column {i}th", f"column {i}st", f"column {i}nd", f"column {i}rd",
                    f"col {i}th", f"col {i}st", f"col {i}nd", f"col {i}rd",
                    f"{i}th column", f"{i}st column", f"{i}nd column", f"{i}rd column", f"{i} column",
                    f"{i}th col", f"{i}st col", f"{i}nd col", f"{i}rd col", f"{i} col"
                ]):
                    return f"Column {i}"
                    
        # Handle BINGO letter columns (B=1, I=2, N=3, G=4, O=5)
        bingo_letters = {'b': 1, 'i': 2, 'n': 3, 'g': 4, 'o': 5}
        for letter, col_num in bingo_letters.items():
            if any(format in pattern_lower for format in [
                f"{letter} column", f"{letter}column", f"{letter}-column", f"{letter} col", f"{letter}col", f"{letter}-col",
                f"column {letter}", f"column{letter}", f"column-{letter}", f"col {letter}", f"col{letter}", f"col-{letter}"
            ]):
                return f"Column {col_num}"
            
        return pattern_name
    
    def calculate_bonus(self, pattern_name, call_count):
        """
        Calculate the bonus for a pattern claim based on pattern type and call count.
        
        Args:
            pattern_name: The name of the pattern claimed
            call_count: The number of calls made when the pattern was claimed
            
        Returns:
            tuple: (bonus_amount, multiplier, description)
        """
        # Get the pattern tier for description purposes
        tier = self.get_pattern_tier(pattern_name)
        
        # Get settings manager
        from settings_manager import SettingsManager
        settings = SettingsManager()
        
        # Determine the setting key for this pattern type
        if "Inner Square" in pattern_name or "3x3 Square" in pattern_name:
            bonus_key = "bonus_amount_inner_square"
            default_bonus = 300
        elif "Diamond" in pattern_name:
            bonus_key = "bonus_amount_diamond"
            default_bonus = 300
        elif "Cross" in pattern_name:
            bonus_key = "bonus_amount_cross"
            default_bonus = 300
        elif "Four Corners" in pattern_name:
            bonus_key = "bonus_amount_four_corners"
            default_bonus = 200
        elif "Row" in pattern_name:
            bonus_key = "bonus_amount_rows"
            default_bonus = 100
        elif "Column" in pattern_name:
            bonus_key = "bonus_amount_columns"
            default_bonus = 100
        elif "Diagonal" in pattern_name:
            bonus_key = "bonus_amount_diagonals"
            default_bonus = 200
        else:
            # Default bonus if pattern not recognized
            bonus_key = None
            default_bonus = 100
        
        # Get the bonus amount from settings
        base_bonus = settings.get_setting("pattern_bonuses", bonus_key, default_bonus) if bonus_key else default_bonus
        
        # No early claim multipliers - use the assigned tier amounts directly
        multiplier = 1.0
        
        # Get the appropriate call threshold for this pattern type
        if "Inner Square" in pattern_name or "3x3 Square" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_inner_square", 0)
            pattern_key = "inner_square"
        elif "Diamond" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_diamond", 0)
            pattern_key = "diamond"
        elif "Cross" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_cross", 0)
            pattern_key = "cross"
        elif "Four Corners" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_four_corners", 5)
            pattern_key = "four_corners"
        elif "Row" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_rows", 5)
            pattern_key = "rows"
        elif "Column" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_columns", 5)
            pattern_key = "columns"
        elif "Diagonal" in pattern_name:
            call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_diagonals", 5)
            pattern_key = "diagonals"
        else:
            call_threshold = 0
            pattern_key = None
            
        # Create threshold description based on the call threshold
        if call_threshold == 0:
            threshold_description = "no call limit"
        elif call_count <= call_threshold:
            threshold_description = f"claimed within {call_threshold} calls"
        else:
            threshold_description = f"claimed after {call_threshold} calls (invalid)"
        
        # Use the base bonus amount directly without multiplication
        bonus_amount = base_bonus
        
        # Create a description of the bonus with call threshold information
        if call_threshold == 0:
            # No call limit
            description = f"{pattern_name} ({tier} pattern): {bonus_amount} bonus ETB"
        else:
            # Include call threshold information
            description = f"{pattern_name} ({tier} pattern, {threshold_description}): {bonus_amount} bonus ETB"
        
        return (bonus_amount, multiplier, description)
    
    def is_valid_pattern_claim(self, pattern_name, call_count):
        """
        Check if a pattern can be claimed as a winning pattern based on the rules.
        
        Args:
            pattern_name: The name of the pattern
            call_count: The number of calls made when the pattern was claimed
            
        Returns:
            bool: True if the pattern can be claimed, False otherwise
        """
        try:
            print(f"PATTERN VALIDATION DEBUG: Checking if pattern '{pattern_name}' is valid with call_count {call_count}")
            
            # Normalize the pattern name
            try:
                normalized_name = self._normalize_pattern_name(pattern_name)
                print(f"PATTERN VALIDATION DEBUG: Normalized pattern name: '{normalized_name}'")
            except Exception as norm_e:
                print(f"ERROR normalizing pattern name: {norm_e}")
                normalized_name = pattern_name  # Use original if normalization fails
            
            # Get settings manager
            try:
                from settings_manager import SettingsManager
                settings = SettingsManager()
                print(f"PATTERN VALIDATION DEBUG: Settings manager loaded successfully")
            except Exception as settings_e:
                print(f"ERROR loading settings manager: {settings_e}")
                # Default to allowing all patterns if settings can't be loaded
                return True
            
            # Check pattern type and verify if the pattern is enabled and within call threshold
            try:
                if "Inner Square" in normalized_name or "3x3 Square" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Inner Square pattern")
                    # Check if Inner Square pattern is enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_inner_square", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Inner Square pattern is disabled")
                        return False
                        
                    # Get call threshold for Inner Square (0 = no limit)
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_inner_square", 0)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Inner Square - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Diamond" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Diamond pattern")
                    # Check if Diamond pattern is enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_diamond", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Diamond pattern is disabled")
                        return False
                        
                    # Get call threshold for Diamond (0 = no limit)
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_diamond", 0)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Diamond - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Cross" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Cross pattern")
                    # Check if Cross pattern is enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_cross", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Cross pattern is disabled")
                        return False
                        
                    # Get call threshold for Cross (0 = no limit)
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_cross", 0)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Cross - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Four Corners" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Four Corners pattern")
                    # Check if Four Corners pattern is enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_four_corners", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Four Corners pattern is disabled")
                        return False
                        
                    # Get call threshold for Four Corners
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_four_corners", 5)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Four Corners - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Row" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Row pattern")
                    # Check if Row patterns are enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_rows", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Row patterns are disabled")
                        return False
                        
                    # Get call threshold for Rows
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_rows", 5)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Row - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Column" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Column pattern")
                    # Check if Column patterns are enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_columns", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Column patterns are disabled")
                        return False
                        
                    # Get call threshold for Columns
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_columns", 5)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Column - threshold: {call_threshold}, result: {result}")
                    return result
                    
                elif "Diagonal" in normalized_name:
                    print(f"PATTERN VALIDATION DEBUG: Checking Diagonal pattern")
                    # Check if Diagonal patterns are enabled
                    pattern_enabled = settings.get_setting("pattern_bonuses", "enable_diagonals", True)
                    if not pattern_enabled:
                        print(f"PATTERN VALIDATION DEBUG: Diagonal patterns are disabled")
                        return False
                        
                    # Get call threshold for Diagonals
                    call_threshold = settings.get_setting("pattern_bonuses", "call_threshold_diagonals", 5)
                    # If threshold is 0, there's no limit; otherwise check if within threshold
                    result = call_threshold == 0 or call_count <= call_threshold
                    print(f"PATTERN VALIDATION DEBUG: Diagonal - threshold: {call_threshold}, result: {result}")
                    return result
                    
                # If pattern type is not recognized, it's not valid
                print(f"PATTERN VALIDATION DEBUG: Pattern type not recognized: '{normalized_name}'")
                return False
                
            except Exception as check_e:
                print(f"ERROR checking pattern validation: {check_e}")
                import traceback
                traceback.print_exc()
                # Default to valid to avoid blocking legitimate claims
                return True
                
        except Exception as e:
            print(f"CRITICAL ERROR in is_valid_pattern_claim: {e}")
            import traceback
            traceback.print_exc()
            # Default to valid to avoid blocking legitimate claims
            return True
    
    def get_bonus_info(self, pattern_name, call_count):
        """
        Get formatted information about the bonus for a pattern claim.
        
        Args:
            pattern_name: The name of the pattern claimed
            call_count: The number of calls made when the pattern was claimed
            
        Returns:
            dict: Information about the bonus
        """
        try:
            print(f"PATTERN BONUS DEBUG: Getting bonus info for pattern '{pattern_name}', call_count {call_count}")
            
            # Calculate bonus with error handling
            try:
                bonus_amount, multiplier, description = self.calculate_bonus(pattern_name, call_count)
                print(f"PATTERN BONUS DEBUG: Calculated bonus - amount: {bonus_amount}, multiplier: {multiplier}")
            except Exception as calc_e:
                print(f"ERROR in calculate_bonus: {calc_e}")
                # Provide fallback values
                bonus_amount, multiplier, description = 100, 1.0, f"Fallback bonus for {pattern_name}"
            
            # Get pattern tier with error handling
            try:
                tier = self.get_pattern_tier(pattern_name)
                print(f"PATTERN BONUS DEBUG: Pattern tier: {tier}")
            except Exception as tier_e:
                print(f"ERROR getting pattern tier: {tier_e}")
                tier = "Standard"
            
            # Check if claim is valid with error handling
            try:
                is_valid = self.is_valid_pattern_claim(pattern_name, call_count)
                print(f"PATTERN BONUS DEBUG: Is valid claim: {is_valid}")
            except Exception as valid_e:
                print(f"ERROR checking if claim is valid: {valid_e}")
                is_valid = True  # Default to valid to avoid blocking legitimate claims
            
            bonus_info = {
                "pattern": pattern_name,
                "call_count": call_count,
                "bonus_amount": bonus_amount,
                "multiplier": multiplier,
                "description": description,
                "tier": tier,
                "is_valid_claim": is_valid
            }
            
            print(f"PATTERN BONUS DEBUG: Bonus info created successfully: {bonus_info}")
            return bonus_info
            
        except Exception as e:
            print(f"CRITICAL ERROR in get_bonus_info: {e}")
            import traceback
            traceback.print_exc()
            
            # Return safe fallback bonus info
            return {
                "pattern": pattern_name,
                "call_count": call_count,
                "bonus_amount": 100,
                "multiplier": 1.0,
                "description": f"Emergency fallback bonus for {pattern_name}",
                "tier": "Standard",
                "is_valid_claim": True
            }