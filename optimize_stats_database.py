"""
Database Optimization Script for Stats Performance
Creates indexes and optimizes database structure for ultra-fast queries.
"""

import os
import sqlite3
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def optimize_stats_database():
    """
    Optimize the stats database for maximum performance.
    Creates indexes, analyzes tables, and optimizes settings.
    """
    db_path = os.path.join('data', 'stats.db')
    
    if not os.path.exists(db_path):
        logger.warning(f"Database not found at {db_path}")
        return False
    
    try:
        logger.info("Starting database optimization...")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Enable WAL mode for better concurrency
        cursor.execute('PRAGMA journal_mode=WAL')
        
        # Optimize performance settings
        cursor.execute('PRAGMA synchronous=NORMAL')  # Faster than FULL, safer than OFF
        cursor.execute('PRAGMA cache_size=10000')    # 10MB cache
        cursor.execute('PRAGMA temp_store=MEMORY')   # Use memory for temp operations
        cursor.execute('PRAGMA mmap_size=268435456') # 256MB memory map
        
        logger.info("Applied performance settings")
        
        # Create indexes for game_history table (most critical for performance)
        indexes_to_create = [
            # Date-based queries (most common)
            ("idx_game_history_date", "CREATE INDEX IF NOT EXISTS idx_game_history_date ON game_history(date(date_time))"),
            
            # Date and username combined (for filtering)
            ("idx_game_history_date_username", "CREATE INDEX IF NOT EXISTS idx_game_history_date_username ON game_history(date(date_time), username)"),
            
            # Status queries
            ("idx_game_history_status", "CREATE INDEX IF NOT EXISTS idx_game_history_status ON game_history(status)"),
            
            # Fee calculations
            ("idx_game_history_fee", "CREATE INDEX IF NOT EXISTS idx_game_history_fee ON game_history(fee)"),
            
            # Total calls filtering
            ("idx_game_history_calls", "CREATE INDEX IF NOT EXISTS idx_game_history_calls ON game_history(total_calls)"),
            
            # Composite index for common WHERE clause
            ("idx_game_history_composite", """
                CREATE INDEX IF NOT EXISTS idx_game_history_composite 
                ON game_history(date(date_time), username, total_calls, status)
            """),
            
            # Date-time ordering (for pagination)
            ("idx_game_history_datetime", "CREATE INDEX IF NOT EXISTS idx_game_history_datetime ON game_history(date_time DESC)"),
        ]
        
        for index_name, index_sql in indexes_to_create:
            try:
                start_time = time.time()
                cursor.execute(index_sql)
                end_time = time.time()
                logger.info(f"Created index {index_name} in {end_time - start_time:.2f}s")
            except Exception as e:
                logger.error(f"Error creating index {index_name}: {e}")
        
        # Create indexes for daily_stats table
        daily_stats_indexes = [
            ("idx_daily_stats_date", "CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date)"),
            ("idx_daily_stats_earnings", "CREATE INDEX IF NOT EXISTS idx_daily_stats_earnings ON daily_stats(earnings)"),
        ]
        
        for index_name, index_sql in daily_stats_indexes:
            try:
                cursor.execute(index_sql)
                logger.info(f"Created index {index_name}")
            except Exception as e:
                logger.error(f"Error creating index {index_name}: {e}")
        
        # Create indexes for wallet_transactions table
        wallet_indexes = [
            ("idx_wallet_id", "CREATE INDEX IF NOT EXISTS idx_wallet_id ON wallet_transactions(id DESC)"),
            ("idx_wallet_datetime", "CREATE INDEX IF NOT EXISTS idx_wallet_datetime ON wallet_transactions(date_time)"),
        ]
        
        for index_name, index_sql in wallet_indexes:
            try:
                cursor.execute(index_sql)
                logger.info(f"Created index {index_name}")
            except Exception as e:
                logger.error(f"Error creating index {index_name}: {e}")
        
        # Analyze tables to update statistics for query optimizer
        tables_to_analyze = ['game_history', 'daily_stats', 'wallet_transactions']
        
        for table in tables_to_analyze:
            try:
                start_time = time.time()
                cursor.execute(f'ANALYZE {table}')
                end_time = time.time()
                logger.info(f"Analyzed table {table} in {end_time - start_time:.2f}s")
            except Exception as e:
                logger.error(f"Error analyzing table {table}: {e}")
        
        # Vacuum to optimize database file
        logger.info("Starting VACUUM operation...")
        start_time = time.time()
        cursor.execute('VACUUM')
        end_time = time.time()
        logger.info(f"VACUUM completed in {end_time - start_time:.2f}s")
        
        conn.commit()
        conn.close()
        
        logger.info("Database optimization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error optimizing database: {e}")
        return False

def check_database_performance():
    """
    Check database performance by running sample queries.
    """
    db_path = os.path.join('data', 'stats.db')
    
    if not os.path.exists(db_path):
        logger.warning(f"Database not found at {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test queries with timing
        test_queries = [
            ("Count all games", "SELECT COUNT(*) FROM game_history"),
            ("Daily earnings today", "SELECT SUM(fee) FROM game_history WHERE date(date_time) = date('now')"),
            ("Weekly stats", """
                SELECT date(date_time), COUNT(*), SUM(fee) 
                FROM game_history 
                WHERE date(date_time) >= date('now', '-7 days')
                GROUP BY date(date_time)
            """),
            ("Wallet balance", "SELECT balance_after FROM wallet_transactions ORDER BY id DESC LIMIT 1"),
        ]
        
        logger.info("Performance test results:")
        
        for query_name, query_sql in test_queries:
            start_time = time.time()
            cursor.execute(query_sql)
            result = cursor.fetchall()
            end_time = time.time()
            
            logger.info(f"{query_name}: {end_time - start_time:.4f}s ({len(result)} rows)")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking database performance: {e}")

if __name__ == "__main__":
    print("Optimizing stats database for ultra-fast performance...")
    
    # Run optimization
    success = optimize_stats_database()
    
    if success:
        print("✓ Database optimization completed successfully!")
        
        # Run performance check
        print("\nRunning performance tests...")
        check_database_performance()
        
        print("\n✓ Database is now optimized for ultra-fast stats loading!")
    else:
        print("✗ Database optimization failed. Check logs for details.")