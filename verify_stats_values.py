#!/usr/bin/env python3
"""
Verification script to check if all stats values are correctly set.
"""

import os
import sqlite3
from datetime import datetime
import sys

# Add current directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

STATS_DB_PATH = os.path.join('data', 'stats.db')

def verify_stats_values():
    """Verify all stats values are correctly set."""
    
    TARGET_TOTAL_EARNINGS = 39782.6667
    TARGET_WEDNESDAY_EARNINGS = 4086.667
    TARGET_WALLET_BALANCE = 4159.0
    WEDNESDAY_DATE = "2024-06-25"
    
    print("=" * 60)
    print("STATS VALUES VERIFICATION")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Check 1: Total earnings
        print("\n1. TOTAL EARNINGS:")
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        actual_total = result[0] if result and result[0] is not None else 0
        
        print(f"   Target: {TARGET_TOTAL_EARNINGS}")
        print(f"   Actual: {actual_total}")
        print(f"   Match:  {'✅ YES' if abs(actual_total - TARGET_TOTAL_EARNINGS) < 0.001 else '❌ NO'}")
        
        # Check 2: Wednesday earnings
        print("\n2. WEDNESDAY (06/25) EARNINGS:")
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (WEDNESDAY_DATE,))
        result = cursor.fetchone()
        actual_wednesday = result[0] if result else 0
        
        print(f"   Target: {TARGET_WEDNESDAY_EARNINGS}")
        print(f"   Actual: {actual_wednesday}")
        print(f"   Match:  {'✅ YES' if abs(actual_wednesday - TARGET_WEDNESDAY_EARNINGS) < 0.001 else '❌ NO'}")
        
        # Check 3: Wallet balance
        print("\n3. WALLET BALANCE:")
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        actual_wallet = result[0] if result and result[0] is not None else 0
        
        print(f"   Target: {TARGET_WALLET_BALANCE}")
        print(f"   Actual: {actual_wallet}")
        print(f"   Match:  {'✅ YES' if abs(actual_wallet - TARGET_WALLET_BALANCE) < 0.001 else '❌ NO'}")
        
        # Additional info: Show recent daily stats
        print("\n4. RECENT DAILY STATS:")
        cursor.execute('''
        SELECT date, earnings, games_played 
        FROM daily_stats 
        WHERE earnings > 0 
        ORDER BY date DESC 
        LIMIT 10
        ''')
        
        print("   Date       | Earnings  | Games")
        print("   -----------|-----------|------")
        for row in cursor.fetchall():
            date, earnings, games = row
            print(f"   {date} | {earnings:8.2f} | {games:4d}")
        
        # Additional info: Show recent wallet transactions
        print("\n5. RECENT WALLET TRANSACTIONS:")
        cursor.execute('''
        SELECT date_time, amount, transaction_type, balance_after 
        FROM wallet_transactions 
        ORDER BY id DESC 
        LIMIT 5
        ''')
        
        print("   Date/Time           | Amount    | Type       | Balance")
        print("   --------------------|-----------|------------|--------")
        for row in cursor.fetchall():
            date_time, amount, trans_type, balance = row
            print(f"   {date_time} | {amount:8.2f} | {trans_type:10s} | {balance:7.2f}")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("VERIFICATION COMPLETE")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    verify_stats_values()