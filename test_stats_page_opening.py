#!/usr/bin/env python3
"""
Test script to verify that the stats page can be opened without errors.
"""

import sys
import os
from unittest.mock import Mock, patch

def test_stats_page_import():
    """Test that the stats page can be imported without errors."""
    
    print("🧪 Testing Stats Page Import and Opening")
    print("=" * 50)
    
    try:
        print("📦 Attempting to import stats page module...")
        
        # Mock pygame to avoid initialization issues
        with patch('pygame.init'), \
             patch('pygame.display.set_mode'), \
             patch('pygame.font.Font'), \
             patch('pygame.time.get_ticks', return_value=1000):
            
            # Import the stats page module
            import stats_page
            print("✅ Stats page module imported successfully")
            
            # Test that the threading import is available
            if hasattr(stats_page, 'threading'):
                print("✅ Threading module is available in stats_page")
            else:
                print("❌ Threading module not found in stats_page")
                return False
            
            # Test that we can access the StatsPage class
            if hasattr(stats_page, 'StatsPage'):
                print("✅ StatsPage class is available")
            else:
                print("❌ StatsPage class not found")
                return False
            
            return True
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_stats_page_creation():
    """Test that a StatsPage instance can be created."""
    
    print("\n🏗️  Testing Stats Page Creation")
    print("-" * 30)
    
    try:
        # Mock pygame components
        with patch('pygame.init'), \
             patch('pygame.display.set_mode'), \
             patch('pygame.font.Font'), \
             patch('pygame.time.get_ticks', return_value=1000), \
             patch('pygame.Surface'), \
             patch('pygame.Rect'):
            
            from stats_page import StatsPage
            
            # Create a mock screen
            mock_screen = Mock()
            mock_screen.get_size.return_value = (1024, 768)
            mock_screen.get_width.return_value = 1024
            mock_screen.get_height.return_value = 768
            
            print("📱 Creating mock screen...")
            
            # Try to create StatsPage instance
            print("🏗️  Creating StatsPage instance...")
            
            # Mock the problematic methods that might cause issues
            with patch.object(StatsPage, '_load_data_background_optimized', return_value=None), \
                 patch.object(StatsPage, 'get_font', return_value=Mock()), \
                 patch.object(StatsPage, '_load_essential_data', return_value=None):
                
                stats_page = StatsPage(mock_screen)
                print("✅ StatsPage instance created successfully")
                
                # Test that threading attributes are present
                if hasattr(stats_page, '_loading_state_lock'):
                    print("✅ Loading state lock is present")
                else:
                    print("❌ Loading state lock is missing")
                    return False
                
                if hasattr(stats_page, '_stable_loading_complete'):
                    print("✅ Stable loading complete flag is present")
                else:
                    print("❌ Stable loading complete flag is missing")
                    return False
                
                return True
                
    except Exception as e:
        print(f"❌ Error creating StatsPage: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def test_threading_availability():
    """Test that threading is properly available in the stats_page module."""
    
    print("\n🧵 Testing Threading Availability")
    print("-" * 30)
    
    try:
        import stats_page
        
        # Check if threading is imported at module level
        if 'threading' in dir(stats_page):
            print("✅ Threading is available at module level")
        else:
            print("❌ Threading not available at module level")
            return False
        
        # Test that we can access threading.Lock
        try:
            lock = stats_page.threading.Lock()
            print("✅ Threading.Lock can be created")
        except Exception as e:
            print(f"❌ Error creating threading.Lock: {e}")
            return False
        
        # Test that we can access threading.Thread
        try:
            def dummy_function():
                pass
            thread = stats_page.threading.Thread(target=dummy_function, daemon=True)
            print("✅ Threading.Thread can be created")
        except Exception as e:
            print(f"❌ Error creating threading.Thread: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing threading availability: {e}")
        return False

def main():
    """Run all tests."""
    
    print("🔧 Stats Page Opening Test Suite")
    print("=" * 60)
    
    # Test 1: Import
    import_success = test_stats_page_import()
    
    # Test 2: Threading availability
    threading_success = test_threading_availability()
    
    # Test 3: Creation (only if import succeeded)
    creation_success = False
    if import_success:
        creation_success = test_stats_page_creation()
    else:
        print("\n⏭️  Skipping creation test due to import failure")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    if import_success:
        print("✅ Import test: PASSED")
    else:
        print("❌ Import test: FAILED")
    
    if threading_success:
        print("✅ Threading test: PASSED")
    else:
        print("❌ Threading test: FAILED")
    
    if creation_success:
        print("✅ Creation test: PASSED")
    else:
        print("❌ Creation test: FAILED")
    
    overall_success = import_success and threading_success and creation_success
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("   The stats page should now open without the threading error.")
        print("   You can try opening the stats page in your application.")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   There may still be issues with the stats page.")
        print("   Check the error messages above for details.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)