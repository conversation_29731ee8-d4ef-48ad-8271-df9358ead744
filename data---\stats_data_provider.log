2025-07-16 13:50:06.979 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:06.979 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:06.985 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:06.985 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-16 13:50:06.986 - Initializing StatsDataProvider
2025-07-16 13:50:07.004 - Loaded 8 items from cache
2025-07-16 13:50:07.009 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-16 13:50:07.009 - Starting background data loading thread
2025-07-16 13:50:07.014 - Background data loading started
2025-07-16 13:50:07.014 - StatsDataProvider initialization completed
2025-07-16 13:50:07.015 - Loading summary statistics
2025-07-16 13:50:07.015 - Created singleton instance of StatsDataProvider on module import
2025-07-16 13:50:07.016 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 13:50:07.017 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-16 13:50:07.017 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:07.018 - get_stats_provider called, returning provider with initialized=True
2025-07-16 13:50:07.018 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 13:50:07.019 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 13:50:07.021 - Successfully loaded weekly stats: 7 days
2025-07-16 13:50:07.023 - Saved 10 items to cache
2025-07-16 13:50:07.024 - Background data loading completed
2025-07-16 13:50:57.578 - get_stats_provider called, returning provider with initialized=True
