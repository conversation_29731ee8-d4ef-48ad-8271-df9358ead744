"""
TEST SPECIFIC FIXES: Verify the targeted fixes for:
1. History section blinking continuously
2. Authentication state flickering (login page appearing/disappearing)
"""

import pygame
import time
import threading
import sys

def test_history_section_stability():
    """Test that the history section no longer blinks continuously."""
    print("🔍 TESTING HISTORY SECTION STABILITY")
    print("=" * 60)
    
    try:
        # Initialize pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        
        # Import and create stats page
        from stats_page import StatsPage
        stats_page = StatsPage(screen, None, "test")
        
        print("✓ StatsPage created")
        
        # Check that loading indicators are disabled
        loading_states = {
            'stats_loading_in_progress': getattr(stats_page, 'stats_loading_in_progress', None),
            'initial_loading_complete': getattr(stats_page, 'initial_loading_complete', None),
            'game_history_loading_complete': getattr(stats_page, 'game_history_loading_complete', None),
            'credit_history_loading_complete': getattr(stats_page, 'credit_history_loading_complete', None)
        }
        
        print("📊 Loading states:")
        for state_name, state_value in loading_states.items():
            print(f"   {state_name}: {state_value}")
        
        # Check if all loading states are properly set
        expected_states = {
            'stats_loading_in_progress': False,
            'initial_loading_complete': True,
            'game_history_loading_complete': True,
            'credit_history_loading_complete': True
        }
        
        all_correct = True
        for state_name, expected_value in expected_states.items():
            actual_value = loading_states.get(state_name)
            if actual_value != expected_value:
                print(f"❌ {state_name}: Expected {expected_value}, got {actual_value}")
                all_correct = False
        
        if all_correct:
            print("✅ All loading states correctly initialized")
        
        # Test that _show_loading_indicator is disabled
        try:
            stats_page._show_loading_indicator("Test message")
            print("✅ Loading indicator method disabled (no exception)")
        except Exception as e:
            print(f"❌ Loading indicator method error: {e}")
            return False
        
        # Test continuous drawing without blinking
        print("\n🎨 Testing continuous drawing for 10 seconds...")
        
        start_time = time.time()
        frame_count = 0
        stable_frames = 0
        blinking_frames = 0
        
        while time.time() - start_time < 10.0:  # Test for 10 seconds
            try:
                # Update and draw
                stats_page.update()
                stats_page.draw()
                
                # Check if any loading states changed (indicating blinking)
                current_states = {
                    'stats_loading_in_progress': getattr(stats_page, 'stats_loading_in_progress', None),
                    'game_history_loading_complete': getattr(stats_page, 'game_history_loading_complete', None),
                    'credit_history_loading_complete': getattr(stats_page, 'credit_history_loading_complete', None)
                }
                
                # Check for stability
                if (current_states['stats_loading_in_progress'] is False and
                    current_states['game_history_loading_complete'] is True and
                    current_states['credit_history_loading_complete'] is True):
                    stable_frames += 1
                else:
                    blinking_frames += 1
                
                frame_count += 1
                time.sleep(0.05)  # ~20 FPS
                
            except Exception as e:
                print(f"❌ Error during frame {frame_count}: {e}")
                return False
        
        stability_percentage = (stable_frames / frame_count) * 100 if frame_count > 0 else 0
        
        print(f"📊 Stability test results:")
        print(f"   Total frames: {frame_count}")
        print(f"   Stable frames: {stable_frames}")
        print(f"   Blinking frames: {blinking_frames}")
        print(f"   Stability: {stability_percentage:.1f}%")
        
        pygame.quit()
        
        if stability_percentage >= 99.0:
            print("✅ HISTORY SECTION BLINKING FIXED!")
            return True
        else:
            print("❌ History section still has blinking issues")
            return False
        
    except Exception as e:
        print(f"❌ Error testing history section: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentication_stability():
    """Test that authentication state doesn't flicker."""
    print("\n🔍 TESTING AUTHENTICATION STABILITY")
    print("=" * 60)
    
    try:
        # Initialize pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        
        # Import and create stats page
        from stats_page import StatsPage
        stats_page = StatsPage(screen, None, "test")
        
        print("✓ StatsPage created")
        
        # Check authentication stability features
        auth_features = {
            '_auth_state_stable': getattr(stats_page, '_auth_state_stable', None),
            '_auth_check_cooldown': getattr(stats_page, '_auth_check_cooldown', None),
            '_last_auth_check': getattr(stats_page, '_last_auth_check', None)
        }
        
        print("📊 Authentication stability features:")
        for feature_name, feature_value in auth_features.items():
            print(f"   {feature_name}: {feature_value}")
        
        # Test stable authentication checking method
        if hasattr(stats_page, '_check_authentication_stable'):
            print("✅ Stable authentication method available")
            
            # Test multiple calls to ensure stability
            auth_results = []
            for i in range(10):
                result = stats_page._check_authentication_stable()
                auth_results.append(result)
                time.sleep(0.1)
            
            # Check if results are consistent
            unique_results = set(auth_results)
            if len(unique_results) <= 1:
                print(f"✅ Authentication state stable: {auth_results[0]}")
                auth_stable = True
            else:
                print(f"❌ Authentication state flickering: {auth_results}")
                auth_stable = False
        else:
            print("❌ Stable authentication method not found")
            auth_stable = False
        
        # Test that authentication doesn't change rapidly during drawing
        print("\n🎨 Testing authentication during continuous drawing...")
        
        auth_states = []
        start_time = time.time()
        
        while time.time() - start_time < 5.0:  # Test for 5 seconds
            try:
                stats_page.update()
                stats_page.draw()
                
                # Check authentication state
                if hasattr(stats_page, '_check_authentication_stable'):
                    auth_state = stats_page._check_authentication_stable()
                else:
                    auth_state = getattr(stats_page, 'authenticated', None)
                
                auth_states.append(auth_state)
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Error during authentication test: {e}")
                return False
        
        # Analyze authentication stability
        unique_auth_states = set(auth_states)
        changes = sum(1 for i in range(1, len(auth_states)) if auth_states[i] != auth_states[i-1])
        
        print(f"📊 Authentication stability results:")
        print(f"   Total checks: {len(auth_states)}")
        print(f"   Unique states: {len(unique_auth_states)}")
        print(f"   State changes: {changes}")
        
        if changes <= 1:  # Allow one initial change
            print("✅ AUTHENTICATION FLICKERING FIXED!")
            auth_drawing_stable = True
        else:
            print("❌ Authentication still flickering during drawing")
            auth_drawing_stable = False
        
        pygame.quit()
        
        return auth_stable and auth_drawing_stable
        
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_overall_user_experience():
    """Test the overall user experience to ensure both issues are resolved."""
    print("\n🎯 TESTING OVERALL USER EXPERIENCE")
    print("=" * 60)
    
    try:
        # Initialize pygame
        pygame.init()
        screen = pygame.display.set_mode((1024, 768))
        
        print("👆 Simulating user opening stats page...")
        
        # Measure loading time
        start_time = time.time()
        
        # Import and create stats page (as user would experience)
        from stats_page import show_stats_page
        
        # Create test callback
        test_complete = threading.Event()
        def close_callback():
            test_complete.set()
        
        # Run stats page
        def run_stats():
            try:
                show_stats_page(screen, on_close_callback=close_callback, previous_page="test")
            except Exception as e:
                print(f"Error: {e}")
                test_complete.set()
        
        stats_thread = threading.Thread(target=run_stats, daemon=True)
        stats_thread.start()
        
        # Wait for initialization
        time.sleep(3)
        
        end_time = time.time()
        loading_time = end_time - start_time
        
        print(f"⏱️  Loading time: {loading_time:.2f} seconds")
        
        # Close test
        test_complete.set()
        
        pygame.quit()
        
        # Evaluate user experience
        if loading_time < 5.0:
            print("✅ Fast loading time")
            return True
        else:
            print("❌ Still slow loading")
            return False
        
    except Exception as e:
        print(f"❌ Error testing user experience: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 TESTING SPECIFIC STATS PAGE FIXES")
    print("=" * 80)
    print()
    print("Testing the targeted fixes for:")
    print("1. History section blinking continuously")
    print("2. Authentication state flickering")
    print()
    
    # Run tests
    results = {}
    
    # Test 1: History section stability
    results['history_stability'] = test_history_section_stability()
    
    # Test 2: Authentication stability
    results['auth_stability'] = test_authentication_stability()
    
    # Test 3: Overall user experience
    results['user_experience'] = test_overall_user_experience()
    
    # Generate report
    print("\n" + "=" * 80)
    print("🏁 SPECIFIC FIXES TEST REPORT")
    print("=" * 80)
    
    all_passed = True
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name.upper().replace('_', ' ')} TEST: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "-" * 80)
    
    if all_passed:
        print("🎉 ALL SPECIFIC FIXES WORKING!")
        print()
        print("ISSUES RESOLVED:")
        print("✅ History section no longer blinks continuously")
        print("✅ Authentication state is stable (no flickering)")
        print("✅ Login page doesn't appear/disappear randomly")
        print("✅ Data displays consistently without blinking")
        print()
        print("🚀 The stats page should now work perfectly!")
        print("   • No more blinking in history section")
        print("   • Stable login state")
        print("   • Consistent data display")
    else:
        print("⚠️  SOME SPECIFIC ISSUES STILL EXIST")
        print()
        print("TROUBLESHOOTING:")
        print("1. Check if the fixes were applied correctly")
        print("2. Restart the application completely")
        print("3. Test in the actual game environment")
        print("4. Check console output for error messages")
    
    print("=" * 80)
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error during testing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)