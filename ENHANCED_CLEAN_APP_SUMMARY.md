# Enhanced Clean App Script - Builder Ready State

## Overview
The enhanced `clean_app_keep_settings.bat` and `clean_app.py` scripts have been significantly improved to prepare the application for builders and clean installations. The script now performs comprehensive cleaning while maintaining essential settings and preparing the application for distribution.

## What Gets Cleaned

### Core Application Data
- ✅ Player data and cartellas
- ✅ Game history and statistics
- ✅ Total earnings (reset to 0)
- ✅ All databases and backups
- ✅ Payment/voucher data and credits
- ✅ Session data and usage logs

### Cache and Temporary Files
- ✅ Cache files and directories (`data/cache`, `data/sync_cache`, etc.)
- ✅ Python cache files (`__pycache__` directories, `.pyc`, `.pyo`, `.pyd` files)
- ✅ Stats cache and preloader cache
- ✅ Instant loading cache
- ✅ Performance results cache

### Build and Development Artifacts
- ✅ Build directories (`build_directory`, `build`, `dist`, `output`)
- ✅ PyInstaller spec files (`*.spec`)
- ✅ Distribution files (`*.whl`, `*.tar.gz`, `*.zip`)
- ✅ Python egg info directories (`*.egg-info`)

### IDE and Development Files
- ✅ IDE cache directories (`.vscode`, `.cursor`, `.qodo`, `.idea`, `.vs`)
- ✅ Testing cache (`.pytest_cache`, `.mypy_cache`, `.tox`)
- ✅ Coverage files (`.coverage`)
- ✅ Test files (`test_*.py`, `*_test.py`, `test_*.log`, etc.)
- ✅ Development artifacts (`*.stackdump`, `core.*`, `*.pid`, `*.lock`)
- ✅ System files (`.DS_Store`, `Thumbs.db`, `*.swp`, `*~`)

### Log Files
- ✅ Application log files in `data/` directory
- ✅ Root directory log files (`*.log`)
- ✅ Integration and performance logs
- ✅ Database and stats logs

### Backup and Archive Files
- ✅ Backup directories (`backup`, `backup_*`, `data copy`)
- ✅ Backup files (`*.bak`, `*.backup`, `*_backup.py`)
- ✅ Database backup files
- ✅ Configuration backup files

### Database Files
- ✅ Statistics databases (`stats.db`, `stats_new.db`)
- ✅ External PCs database (`external_pcs.db`)
- ✅ Vouchers database (reset to zero balance)
- ✅ RethinkDB data directory

### Generated and Temporary Files
- ✅ Generated voucher files (`compact_vouchers_*.json`, `vouchers_*.json`)
- ✅ Voucher test reports and diagnostics
- ✅ Export files and directories
- ✅ Temporary files (`*.tmp`, `*.temp`, `temp_log.txt`)
- ✅ Configuration files (except settings when `--keep-settings` is used)

### Environment and Configuration
- ✅ Environment files (`.env`, `.env.local`, `config.ini`)
- ✅ Local configuration files
- ✅ Admin sessions data

## What Gets Preserved

### Essential Settings (when using `--keep-settings`)
- ✅ Game settings (`data/settings.json`)
- ✅ Display preferences
- ✅ Audio settings
- ✅ Language preferences
- ✅ IDE configuration files (when `--keep-settings` is used)

### Core Application Files
- ✅ Bingo boards and presets
- ✅ Application source code
- ✅ Assets and templates
- ✅ Essential configuration files

## Builder-Ready Features

### Fresh State Initialization
- 🔧 Creates fresh start marker file
- 📁 Ensures critical directories exist
- 💰 Sets account balance to 0
- 📊 Resets all statistics to initial state
- 🎯 Prepares for clean installation

### Zero Account Balance
- All earnings reset to 0
- Wallet balance cleared
- Payment history cleared
- Credits reset to 0
- Usage logs cleared

### Clean Statistics
- Games played: 0
- Total winners: 0
- Total prize pool: 0
- Player count: 0
- Number frequencies cleared
- Recent activity cleared

## Usage

### Command Line
```bash
# Clean everything including settings
python clean_app.py

# Clean everything but keep settings (recommended for builders)
python clean_app.py --keep-settings

# Silent mode (no output)
python clean_app.py --keep-settings --silent
```

### Batch File
```batch
# Interactive cleaning with settings preservation
clean_app_keep_settings.bat
```

## Benefits for Builders

1. **Clean Distribution**: No development artifacts or personal data
2. **Zero Balance**: Fresh financial state for new installations
3. **Reset Statistics**: Clean slate for tracking new usage
4. **Preserved Settings**: Maintains optimal configuration
5. **Fresh Markers**: Clear indication of clean state
6. **Reduced Size**: Removes unnecessary cache and temporary files
7. **Professional**: Ready for distribution without personal traces

## Safety Features

- Preserves essential application files
- Maintains bingo boards for functionality
- Keeps settings when requested
- Creates fresh start markers
- Comprehensive error handling
- Detailed logging of operations

## Perfect For

- 🏗️ Application builders and packagers
- 📦 Distribution preparation
- 🔄 Clean installations
- 🧹 Development environment cleanup
- 🚀 Production deployment
- 🎯 Quality assurance testing

The enhanced cleaning script ensures your application is in the perfect state for builders while maintaining all essential functionality and user preferences.