#!/usr/bin/env python
"""
Enhanced Clean App Script

This script comprehensively resets the Bingo Game application to a completely fresh state by:
1. Clearing ALL player data (players, cartellas, UI state)
2. Resetting settings to defaults (optional with --keep-settings)
3. Clearing ALL statistics and game history
4. Resetting total earnings to 0
5. Clearing ALL databases (stats.db, external_pcs.db, etc.)
6. Resetting payment/voucher data and credits
7. Clearing session data and usage logs
8. Cleaning cache files and directories
9. Cleaning log files
10. Cleaning export files
11. Cleaning temporary files
12. Cleaning configuration files
13. Removing database backups
14. Cleaning Python cache files (__pycache__, .pyc files)
15. Cleaning build artifacts and directories
16. Cleaning IDE-specific cache files
17. Cleaning RethinkDB data directory
18. Cleaning output directory contents
19. Cleaning backup directories and files
20. Cleaning root directory log files
21. Preparing application for builder-ready state

Usage:
    python clean_app.py [--keep-settings] [--silent]

Options:
    --keep-settings    Keep current settings instead of resetting them
    --silent          Run in silent mode (no output)

Note: Bingo boards are preserved to maintain game functionality.
"""

import os
import json
import shutil
import sqlite3
import argparse
import sys
import glob

def ensure_directory(directory):
    """Ensure a directory exists."""
    os.makedirs(directory, exist_ok=True)

def reset_file_with_json(filepath, default_content, silent=False):
    """Reset a JSON file with default content."""
    directory = os.path.dirname(filepath)
    ensure_directory(directory)

    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(default_content, f, indent=4, ensure_ascii=False)

    if not silent:
        print(f"Reset: {filepath}")

def delete_file_if_exists(filepath, silent=False):
    """Delete a file if it exists."""
    if os.path.exists(filepath):
        try:
            os.remove(filepath)
            if not silent:
                print(f"Deleted: {filepath}")
        except PermissionError:
            if not silent:
                print(f"Warning: Could not delete {filepath} (file in use)")
        except Exception as e:
            if not silent:
                print(f"Warning: Could not delete {filepath}: {e}")
    elif not silent:
        print(f"Not found (skipped): {filepath}")

def reset_settings(silent=False):
    """Reset settings to defaults."""
    default_settings = {
        "game": {
            "number_call_delay": 3.0,
            "strict_claim_timing": True,
            "shuffle_duration": 3.0,
            "commission_percentage": 20.0,
            "show_total_selected": True
        },
        "boards": {
            "presets": [],
            "current_preset": ""
        },
        "display": {
            "fullscreen": False,
            "resolution": "1280x720",
            "animations_enabled": True,
            "show_recent_calls": True,
            "recent_calls_count": 5,
            "ui_theme": "dark",
            "ui_accent_color": "blue"
        },
        "animations": {
            "transition_animation_enabled": True,
            "transition_animation_duration": 0.3
        },
        "audio": {
            "sound_effects_enabled": True,
            "sound_effects_volume": 0.7,
            "music_enabled": True,
            "music_volume": 0.5,
            "voice_enabled": True,
            "voice_volume": 0.8,
            "cartella_announcements_enabled": True,
            "announcer_language": "Default"
        },
        "language": {
            "current_language": "English",
            "available_languages": [
                "English", "Oromo", "Amharic", "Tigrinya", "Somali", "Agew"
            ],
            "custom_language_path": ""
        },
        "import_export": {
            "last_import_path": "",
            "last_export_path": "",
            "auto_backup": True,
            "backup_interval": 20
        },
        "advertising": {
            "enabled": True,
            "text": "ዋው ጌምስ - ዋው ቢንጎ",
            "scroll_speed": 2,
            "text_color": "#FFFF00",
            "background_color": "#FF0000"
        }
    }

    reset_file_with_json(os.path.join('data', 'settings.json'), default_settings, silent)

def reset_stats(silent=False):
    """Reset statistics data."""
    default_stats = {
        "games_played": 0,
        "total_winners": 0,
        "total_prize_pool": 0,
        "player_count": 0,
        "average_game_duration": 0,
        "top_players": [],
        "number_frequencies": {},
        "session_start_time": 0,
        "recent_activity": []
    }

    reset_file_with_json(os.path.join('data', 'stats.json'), default_stats, silent)

    # Reset stats database (SQLite)
    stats_db_files = [
        os.path.join('data', 'stats.db'),
        os.path.join('data', 'stats_new.db'),
        os.path.join('data', 'external_pcs.db')
    ]

    for db_file in stats_db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Get all table names
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()

                # Clear all tables (except sqlite_sequence which is auto-managed)
                for table in tables:
                    table_name = table[0]
                    if table_name != 'sqlite_sequence':
                        cursor.execute(f'DELETE FROM {table_name}')
                        if not silent:
                            print(f"Cleared table: {table_name} in {db_file}")

                # Reset overall_summary table with default values (if it exists)
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO overall_summary
                        (id, total_earnings, current_daily_games, current_daily_earnings, wallet_balance)
                        VALUES (1, 0.0, 0, 0.0, 0.0)
                    ''')
                except sqlite3.OperationalError:
                    # Table doesn't exist, skip this step
                    pass

                conn.commit()
                conn.close()
                if not silent:
                    print(f"Reset database: {db_file}")
            except sqlite3.Error as e:
                if not silent:
                    print(f"Error resetting database {db_file}: {e}")
                # If there's an error, delete the file
                delete_file_if_exists(db_file, silent)
        elif not silent:
            print(f"Not found (skipped): {db_file}")

    # Delete stats database backups
    stats_backup_files = [
        os.path.join('data', 'stats.db.backup.1747090774'),
        os.path.join('data', 'stats.db.backup.1747090783'),
        os.path.join('data', 'stats.db.backup.1747090892'),
        os.path.join('data', 'stats.db.test'),
        os.path.join('data', 'stats.db_backup_20250502_135200'),
        os.path.join('data', 'stats.db_backup_20250502_135329')
    ]

    for backup_file in stats_backup_files:
        delete_file_if_exists(backup_file, silent)

def reset_payment_data(silent=False):
    """Reset payment system data."""
    # Reset vouchers database
    vouchers_db_path = os.path.join('data', 'vouchers.db')
    if os.path.exists(vouchers_db_path):
        try:
            # Connect to the database
            conn = sqlite3.connect(vouchers_db_path)
            cursor = conn.cursor()

            # Clear vouchers table
            cursor.execute('DELETE FROM vouchers')

            # Reset credits to 0
            cursor.execute('UPDATE credits SET balance = 0, last_updated = ? WHERE id = 1', (0,))
            cursor.execute('INSERT OR IGNORE INTO credits (id, balance, last_updated) VALUES (1, 0, 0)')

            conn.commit()
            conn.close()
            if not silent:
                print(f"Reset: {vouchers_db_path}")
        except sqlite3.Error as e:
            if not silent:
                print(f"Error resetting vouchers database: {e}")
            # If there's an error, just delete the file
            delete_file_if_exists(vouchers_db_path, silent)
    elif not silent:
        print(f"Not found (skipped): {vouchers_db_path}")

    # Reset usage log
    usage_log_path = os.path.join('data', 'usage_log.json')
    default_usage_log = {
        "usage": [],
        "total_usage": 0,
        "last_updated": 0
    }
    reset_file_with_json(usage_log_path, default_usage_log, silent)

def reset_player_data(silent=False):
    """Reset player data."""
    # Delete players.json and its backup
    delete_file_if_exists(os.path.join('data', 'players.json'), silent)
    delete_file_if_exists(os.path.join('data', 'players.json.bak'), silent)

    # Delete current_session.json
    delete_file_if_exists(os.path.join('data', 'current_session.json'), silent)

    # Delete players.robadb if it exists
    delete_file_if_exists('players.robadb', silent)

    # Delete remembered cartellas
    delete_file_if_exists(os.path.join('data', 'remembered_cartellas.json'), silent)
    delete_file_if_exists(os.path.join('data', 'remembered_cartellas.json.bak'), silent)

    # Delete UI state
    delete_file_if_exists(os.path.join('data', 'ui_state.json'), silent)
    delete_file_if_exists(os.path.join('data', 'ui_state.json.bak'), silent)

    # Delete game settings (but preserve if keep_settings is used)
    delete_file_if_exists(os.path.join('data', 'game_settings.json'), silent)
    delete_file_if_exists(os.path.join('data', 'game_settings.json.bak'), silent)

def clean_cache_files(silent=False):
    """Clean cache files and directories."""
    if not silent:
        print("Cleaning cache files...")

    # Cache directories to clean
    cache_dirs = [
        os.path.join('data', 'cache'),
        os.path.join('data', 'cache_backup_20250502_135200'),
        os.path.join('data', 'cache_backup_20250502_135329'),
        os.path.join('data', 'sync_cache'),
        os.path.join('data', 'metrics')
    ]

    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                if not silent:
                    print(f"Deleted cache directory: {cache_dir}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting cache directory {cache_dir}: {e}")
        elif not silent:
            print(f"Not found (skipped): {cache_dir}")

    # Individual cache files
    cache_files = [
        os.path.join('data', 'performance_results.json'),
        os.path.join('data', 'fresh_start.marker')
    ]

    for cache_file in cache_files:
        delete_file_if_exists(cache_file, silent)

def clean_log_files(silent=False):
    """Clean log files."""
    if not silent:
        print("Cleaning log files...")

    log_files = [
        os.path.join('data', 'db_security.log'),
        os.path.join('data', 'direct_optimizer.log'),
        os.path.join('data', 'game_stats_integration.log'),
        os.path.join('data', 'initialize_stats.log'),
        os.path.join('data', 'integrate_rethinkdb.log'),
        os.path.join('data', 'integration.log'),
        os.path.join('data', 'login_page.log'),
        os.path.join('data', 'optimized_stats.log'),
        os.path.join('data', 'rethink_status.log'),
        os.path.join('data', 'rethinkdb_setup.log'),
        os.path.join('data', 'stats_data_provider.log'),
        os.path.join('data', 'stats_event_hooks.log'),
        os.path.join('data', 'stats_page.log'),
        os.path.join('data', 'stats_performance.log'),
        os.path.join('data', 'stats_preloader.log'),
        os.path.join('data', 'successful_game_records.log'),
        os.path.join('data', 'supabase.log'),
        os.path.join('data', 'supabase_provider.log'),
        os.path.join('data', 'thread_safe_db.log'),
        os.path.join('data', 'unified_provider.log')
    ]

    for log_file in log_files:
        delete_file_if_exists(log_file, silent)

def clean_config_files(silent=False):
    """Clean configuration files (except settings if keep_settings is used)."""
    if not silent:
        print("Cleaning configuration files...")

    config_files = [
        os.path.join('data', 'rethink_config.json'),
        os.path.join('data', 'supabase_config.json'),
        os.path.join('data', 'admin_sessions.json')
    ]

    for config_file in config_files:
        delete_file_if_exists(config_file, silent)

def clean_export_files(silent=False):
    """Clean export files."""
    if not silent:
        print("Cleaning export files...")

    exports_dir = os.path.join('data', 'exports')
    if os.path.exists(exports_dir):
        try:
            shutil.rmtree(exports_dir)
            if not silent:
                print(f"Deleted exports directory: {exports_dir}")
        except Exception as e:
            if not silent:
                print(f"Error deleting exports directory: {e}")
    elif not silent:
        print(f"Not found (skipped): {exports_dir}")

def clean_temp_files(silent=False):
    """Clean temporary files."""
    if not silent:
        print("Cleaning temporary files...")

    temp_files = [
        os.path.join('data', 'temp_bingo_boards.json'),
        os.path.join('data', 'convert_pretty.py')
    ]

    for temp_file in temp_files:
        delete_file_if_exists(temp_file, silent)

def clear_stats_cache(silent=False):
    """Clear ALL stats cache to ensure fresh data loading after cleanup."""
    try:
        if not silent:
            print("Clearing all stats cache systems...")

        # 1. Clear stats preloader cache
        try:
            from stats_preloader import get_stats_preloader
            preloader = get_stats_preloader()
            preloader.clear()
            if not silent:
                print("✓ Cleared stats preloader cache")
        except ImportError:
            if not silent:
                print("Stats preloader not available - skipping cache clear")
        except Exception as e:
            if not silent:
                print(f"Warning: Could not clear stats preloader cache: {e}")

        # 2. Clear instant loading cache if available
        try:
            from instant_loading.stats_loader import get_stats_loader
            loader = get_stats_loader()
            loader.clear_cache()
            if not silent:
                print("✓ Cleared instant loading cache")
        except ImportError:
            pass  # Module not available
        except Exception as e:
            if not silent:
                print(f"Warning: Could not clear instant loading cache: {e}")

        # 3. Clear centralized stats provider cache
        try:
            from stats_page import CentralizedStatsProvider
            provider = CentralizedStatsProvider()
            provider.clear_cache()
            if not silent:
                print("✓ Cleared centralized stats provider cache")
        except Exception as e:
            if not silent:
                print(f"Warning: Could not clear centralized stats provider cache: {e}")

        # 4. Clear persistent cache files from disk
        cache_dirs = [
            os.path.join('data', 'cache'),
            os.path.join('data', 'sync_cache'),
            os.path.join('instant_loading', 'cache'),
            'cache'
        ]
        
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                try:
                    import shutil
                    shutil.rmtree(cache_dir)
                    if not silent:
                        print(f"✓ Removed cache directory: {cache_dir}")
                except Exception as e:
                    if not silent:
                        print(f"Warning: Could not remove cache directory {cache_dir}: {e}")

        # 5. Clear specific cache files
        cache_files = [
            os.path.join('data', 'cache', 'stats_cache.json'),
            os.path.join('data', 'stats_cache.json'),
            os.path.join('data', 'preloader_cache.json'),
            'stats_cache.json',
            'preloader_cache.json'
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                try:
                    os.remove(cache_file)
                    if not silent:
                        print(f"✓ Removed cache file: {cache_file}")
                except Exception as e:
                    if not silent:
                        print(f"Warning: Could not remove cache file {cache_file}: {e}")

        # 6. Force clear any in-memory caches by triggering garbage collection
        try:
            import gc
            gc.collect()
            if not silent:
                print("✓ Forced garbage collection to clear in-memory caches")
        except Exception as e:
            if not silent:
                print(f"Warning: Could not force garbage collection: {e}")

        if not silent:
            print("✓ All stats cache systems cleared successfully")

    except Exception as e:
        if not silent:
            print(f"Error clearing stats cache: {e}")

def clean_python_cache(silent=False):
    """Clean Python cache files and directories."""
    if not silent:
        print("Cleaning Python cache files...")

    # Clean __pycache__ directories
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                if not silent:
                    print(f"Deleted __pycache__ directory: {pycache_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting __pycache__ directory {pycache_path}: {e}")

    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith(('.pyc', '.pyo', '.pyd')):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    if not silent:
                        print(f"Deleted Python cache file: {file_path}")
                except Exception as e:
                    if not silent:
                        print(f"Error deleting Python cache file {file_path}: {e}")

def clean_build_artifacts(silent=False):
    """Clean build artifacts and directories."""
    if not silent:
        print("Cleaning build artifacts...")

    build_dirs = [
        'build_directory',
        'build',
        'dist',
        'output',
        '*.egg-info'
    ]

    for build_dir in build_dirs:
        if build_dir.endswith('*'):
            # Handle glob patterns
            for path in glob.glob(build_dir):
                if os.path.exists(path):
                    try:
                        if os.path.isdir(path):
                            shutil.rmtree(path)
                        else:
                            os.remove(path)
                        if not silent:
                            print(f"Deleted build artifact: {path}")
                    except Exception as e:
                        if not silent:
                            print(f"Error deleting build artifact {path}: {e}")
        else:
            if os.path.exists(build_dir):
                try:
                    shutil.rmtree(build_dir)
                    if not silent:
                        print(f"Deleted build directory: {build_dir}")
                except Exception as e:
                    if not silent:
                        print(f"Error deleting build directory {build_dir}: {e}")
            elif not silent:
                print(f"Not found (skipped): {build_dir}")

def clean_ide_cache(silent=False):
    """Clean IDE-specific cache files and directories."""
    if not silent:
        print("Cleaning IDE cache files...")

    ide_dirs = [
        '.vscode',
        '.cursor',
        '.qodo',
        '.idea',
        '.vs',
        '.pytest_cache',
        '.mypy_cache',
        '.tox',
        '.coverage'
    ]

    for ide_dir in ide_dirs:
        if os.path.exists(ide_dir):
            try:
                shutil.rmtree(ide_dir)
                if not silent:
                    print(f"Deleted IDE cache directory: {ide_dir}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting IDE cache directory {ide_dir}: {e}")
        elif not silent:
            print(f"Not found (skipped): {ide_dir}")

def clean_rethinkdb_data(silent=False):
    """Clean RethinkDB data directory."""
    if not silent:
        print("Cleaning RethinkDB data...")

    rethinkdb_dir = 'rethinkdb_data'
    if os.path.exists(rethinkdb_dir):
        try:
            shutil.rmtree(rethinkdb_dir)
            if not silent:
                print(f"Deleted RethinkDB data directory: {rethinkdb_dir}")
        except Exception as e:
            if not silent:
                print(f"Error deleting RethinkDB data directory: {e}")
    elif not silent:
        print(f"Not found (skipped): {rethinkdb_dir}")

def clean_backup_directories(silent=False):
    """Clean backup directories and files."""
    if not silent:
        print("Cleaning backup directories...")

    backup_dirs = [
        'backup',
        'backup_before_time_migration_20250628_112710',
        'backup_voucher_fix_1748212422',
        'data copy'
    ]

    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            try:
                shutil.rmtree(backup_dir)
                if not silent:
                    print(f"Deleted backup directory: {backup_dir}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting backup directory {backup_dir}: {e}")
        elif not silent:
            print(f"Not found (skipped): {backup_dir}")

def clean_root_log_files(silent=False):
    """Clean log files in the root directory."""
    if not silent:
        print("Cleaning root directory log files...")

    # Get all .log files in root directory
    for file in os.listdir('.'):
        if file.endswith('.log'):
            try:
                os.remove(file)
                if not silent:
                    print(f"Deleted root log file: {file}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting root log file {file}: {e}")

def clean_additional_temp_files(silent=False):
    """Clean additional temporary files and directories."""
    if not silent:
        print("Cleaning additional temporary files...")

    temp_items = [
        'temp',
        'tmp',
        '*.tmp',
        '*.temp',
        'bash.exe.stackdump',
        'temp_log.txt'
    ]

    for temp_item in temp_items:
        if temp_item.startswith('*.'):
            # Handle glob patterns
            for path in glob.glob(temp_item):
                try:
                    if os.path.isdir(path):
                        shutil.rmtree(path)
                    else:
                        os.remove(path)
                    if not silent:
                        print(f"Deleted temporary item: {path}")
                except Exception as e:
                    if not silent:
                        print(f"Error deleting temporary item {path}: {e}")
        else:
            if os.path.exists(temp_item):
                try:
                    if os.path.isdir(temp_item):
                        shutil.rmtree(temp_item)
                    else:
                        os.remove(temp_item)
                    if not silent:
                        print(f"Deleted temporary item: {temp_item}")
                except Exception as e:
                    if not silent:
                        print(f"Error deleting temporary item {temp_item}: {e}")
            elif not silent:
                print(f"Not found (skipped): {temp_item}")

def clean_voucher_files(silent=False):
    """Clean generated voucher files."""
    if not silent:
        print("Cleaning generated voucher files...")

    # Clean compact voucher files
    voucher_patterns = [
        'compact_vouchers_*.json',
        'compact_vouchers_*.txt',
        'compact_external_vouchers_*.json',
        'vouchers_*.json',
        'voucher_test_report_*.json',
        'voucher_diagnostic_report.json'
    ]

    for pattern in voucher_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                if not silent:
                    print(f"Deleted voucher file: {file_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting voucher file {file_path}: {e}")

def clean_backup_files(silent=False):
    """Clean backup files with various extensions."""
    if not silent:
        print("Cleaning backup files...")

    # Clean files with backup extensions
    backup_patterns = [
        '*.bak',
        '*.backup',
        '*.bak.*',
        '*.backup.*',
        '*_backup.py',
        '*_backup_*.py'
    ]

    for pattern in backup_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                if not silent:
                    print(f"Deleted backup file: {file_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting backup file {file_path}: {e}")

def initialize_fresh_data_structures(silent=False):
    """Initialize fresh data structures for builder-ready state."""
    if not silent:
        print("Initializing fresh data structures...")

    # Create fresh marker file to indicate clean state
    fresh_marker_path = os.path.join('data', 'fresh_start.marker')
    with open(fresh_marker_path, 'w') as f:
        import time
        f.write(f"Fresh start initialized at: {time.time()}\n")
        f.write("Account balance: 0\n")
        f.write("Statistics: Reset\n")
        f.write("Builder-ready state: True\n")
    
    if not silent:
        print(f"Created fresh start marker: {fresh_marker_path}")

    # Ensure critical directories exist
    critical_dirs = [
        os.path.join('data'),
        os.path.join('assets'),
        os.path.join('templates'),
        os.path.join('vouchers')
    ]
    
    for dir_path in critical_dirs:
        ensure_directory(dir_path)
        if not silent:
            print(f"Ensured directory exists: {dir_path}")

def clean_spec_files(silent=False):
    """Clean PyInstaller spec files."""
    if not silent:
        print("Cleaning PyInstaller spec files...")

    spec_patterns = [
        '*.spec',
        'build/*.spec'
    ]

    for pattern in spec_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                if not silent:
                    print(f"Deleted spec file: {file_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting spec file {file_path}: {e}")

def clean_distribution_files(silent=False):
    """Clean distribution and packaging files."""
    if not silent:
        print("Cleaning distribution files...")

    dist_patterns = [
        '*.whl',
        '*.tar.gz',
        '*.zip',
        'MANIFEST.in',
        'setup.cfg'
    ]

    for pattern in dist_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                if not silent:
                    print(f"Deleted distribution file: {file_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting distribution file {file_path}: {e}")

def clean_environment_files(silent=False):
    """Clean environment and configuration files that shouldn't be in builds."""
    if not silent:
        print("Cleaning environment files...")

    env_files = [
        '.env',
        '.env.local',
        '.env.development',
        '.env.production',
        'config.ini',
        'local_config.json'
    ]

    for env_file in env_files:
        if os.path.exists(env_file):
            try:
                os.remove(env_file)
                if not silent:
                    print(f"Deleted environment file: {env_file}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting environment file {env_file}: {e}")
        elif not silent:
            print(f"Not found (skipped): {env_file}")

def clean_test_and_dev_files(silent=False):
    """Clean test files and development artifacts."""
    if not silent:
        print("Cleaning test and development files...")

    # Test file patterns
    test_patterns = [
        'test_*.py',
        '*_test.py',
        'tests/*.py',
        '*.test',
        'test_*.log',
        'test_*.json',
        'test_*.txt'
    ]

    for pattern in test_patterns:
        for file_path in glob.glob(pattern):
            # Skip if it's a directory or important test file
            if os.path.isfile(file_path) and not file_path.endswith('__init__.py'):
                try:
                    os.remove(file_path)
                    if not silent:
                        print(f"Deleted test file: {file_path}")
                except Exception as e:
                    if not silent:
                        print(f"Error deleting test file {file_path}: {e}")

    # Development artifact patterns
    dev_patterns = [
        '*.stackdump',
        'core.*',
        '*.pid',
        '*.lock',
        '.DS_Store',
        'Thumbs.db',
        '*.swp',
        '*.swo',
        '*~'
    ]

    for pattern in dev_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                if not silent:
                    print(f"Deleted development artifact: {file_path}")
            except Exception as e:
                if not silent:
                    print(f"Error deleting development artifact {file_path}: {e}")

def clean_app(keep_settings=False, silent=False):
    """Clean the app to start from scratch."""
    if not silent:
        print("=== Enhanced Cleaning Bingo Game App ===")
        print("This will clean ALL data including game history, total earnings, cache, logs, and more...")
        print("Preparing application for builder-ready state...")

    # Ensure data directory exists
    ensure_directory('data')

    # Reset player data (includes remembered cartellas, UI state, etc.)
    reset_player_data(silent)

    # Reset settings (unless --keep-settings is specified)
    if not keep_settings:
        reset_settings(silent)
    elif not silent:
        print("Keeping current settings (--keep-settings flag used)")

    # Reset statistics (includes game history, total earnings, all databases)
    reset_stats(silent)

    # Reset payment data (includes vouchers, credits, usage logs)
    reset_payment_data(silent)

    # Clean cache files and directories
    clean_cache_files(silent)

    # Clean log files
    clean_log_files(silent)

    # Clean configuration files (except settings if keep_settings is used)
    clean_config_files(silent)

    # Clean export files
    clean_export_files(silent)

    # Clean temporary files
    clean_temp_files(silent)

    # Clear stats cache to ensure fresh data loading
    clear_stats_cache(silent)

    # === NEW ENHANCED CLEANING FUNCTIONS ===
    
    # Clean Python cache files (__pycache__, .pyc files)
    clean_python_cache(silent)

    # Clean build artifacts and directories
    clean_build_artifacts(silent)

    # Clean IDE-specific cache files (but preserve settings if keep_settings is used)
    if not keep_settings:
        clean_ide_cache(silent)
    elif not silent:
        print("Skipping IDE cache cleaning (--keep-settings flag used)")

    # Clean RethinkDB data directory
    clean_rethinkdb_data(silent)

    # Clean backup directories and files
    clean_backup_directories(silent)

    # Clean root directory log files
    clean_root_log_files(silent)

    # Clean additional temporary files and directories
    clean_additional_temp_files(silent)

    # Clean generated voucher files
    clean_voucher_files(silent)

    # Clean backup files with various extensions
    clean_backup_files(silent)

    # Clean PyInstaller spec files
    clean_spec_files(silent)

    # Clean distribution and packaging files
    clean_distribution_files(silent)

    # Clean environment files that shouldn't be in builds
    clean_environment_files(silent)

    # Clean test files and development artifacts
    clean_test_and_dev_files(silent)

    # Initialize fresh data structures for builder-ready state
    initialize_fresh_data_structures(silent)

    if not silent:
        print("\n" + "="*70)
        print("🎉 COMPREHENSIVE APP CLEANING COMPLETED! 🎉")
        print("="*70)
        print("✅ Player data cleared")
        print("✅ Game history cleared")
        print("✅ Total earnings reset to 0")
        print("✅ Statistics databases reset")
        print("✅ Payment/voucher data cleared")
        print("✅ Cache files cleaned")
        print("✅ Log files cleaned (including root directory)")
        print("✅ Export files cleaned")
        print("✅ Temporary files and directories cleaned")
        print("✅ Configuration files cleaned")
        print("✅ Stats cache cleared")
        print("✅ Python cache files removed (__pycache__, .pyc)")
        print("✅ Build artifacts and directories removed")
        print("✅ RethinkDB data directory cleaned")
        print("✅ Backup directories and files removed")
        print("✅ Generated voucher files cleaned")
        print("✅ Additional temporary files cleaned")
        print("✅ PyInstaller spec files cleaned")
        print("✅ Distribution and packaging files cleaned")
        print("✅ Environment configuration files cleaned")
        print("✅ Test files and development artifacts cleaned")
        print("✅ Fresh data structures initialized")
        if keep_settings:
            print("✅ Settings preserved (--keep-settings used)")
            print("✅ IDE cache preserved (--keep-settings used)")
        else:
            print("✅ Settings reset to defaults")
            print("✅ IDE cache files cleaned")
        print("\n📝 Note: Bingo boards have been preserved.")
        print("🏗️  Application is now in BUILDER-READY state!")
        print("💰 Account balance: 0 (zero)")
        print("📊 Statistics: Reset to initial state")
        print("🔧 Fresh start marker created")
        print("📁 Critical directories ensured")
        print("🚀 Ready for fresh deployment and distribution!")
        print("🎯 Perfect for builders and clean installations!")
        print("="*70)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Clean the Bingo Game app to start from scratch.')
    parser.add_argument('--keep-settings', action='store_true', help='Keep current settings instead of resetting them')
    parser.add_argument('--silent', action='store_true', help='Run in silent mode (no output)')

    args = parser.parse_args()

    clean_app(keep_settings=args.keep_settings, silent=args.silent)

if __name__ == '__main__':
    main()
