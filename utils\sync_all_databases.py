#!/usr/bin/env python3
"""
Script to sync game history data to all database files to ensure consistency.
"""

import sqlite3
import os

def sync_game_history_to_stats_new():
    """Copy game history from stats.db to stats_new.db."""
    
    stats_db_path = os.path.join('data', 'stats.db')
    stats_new_db_path = os.path.join('data', 'stats_new.db')
    
    if not os.path.exists(stats_db_path):
        print(f"❌ Source database not found: {stats_db_path}")
        return False
    
    if not os.path.exists(stats_new_db_path):
        print(f"❌ Target database not found: {stats_new_db_path}")
        return False
    
    try:
        # Connect to both databases
        source_conn = sqlite3.connect(stats_db_path)
        target_conn = sqlite3.connect(stats_new_db_path)
        
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # Clear existing game history in target database
        target_cursor.execute('''
        DELETE FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        
        # Get game history from source database (excluding details column for stats_new.db)
        source_cursor.execute('''
        SELECT date_time, username, house, stake, players, total_calls,
               commission_percent, fee, total_prize, status
        FROM game_history
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ORDER BY date_time
        ''')

        games = source_cursor.fetchall()

        # Insert into target database (stats_new.db schema doesn't have details column)
        print(f"📊 Syncing {len(games)} game records to stats_new.db...")

        for game in games:
            target_cursor.execute('''
            INSERT INTO game_history
            (date_time, username, house, stake, players, total_calls,
             commission_percent, fee, total_prize, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', game)
        
        # Commit changes
        target_conn.commit()
        
        # Close connections
        source_conn.close()
        target_conn.close()
        
        print("✅ Game history synced successfully to stats_new.db")
        return True
        
    except Exception as e:
        print(f"❌ Error syncing game history: {e}")
        return False

def verify_sync():
    """Verify that both databases have the same game history data."""
    
    databases = [
        ('stats.db', os.path.join('data', 'stats.db')),
        ('stats_new.db', os.path.join('data', 'stats_new.db'))
    ]
    
    print("\n📋 Verifying database synchronization:")
    
    for db_name, db_path in databases:
        if not os.path.exists(db_path):
            print(f"❌ {db_name} not found")
            continue
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get summary statistics
            cursor.execute('''
            SELECT 
                COUNT(*) as total_games,
                SUM(fee) as total_commission,
                MIN(DATE(date_time)) as earliest_date,
                MAX(DATE(date_time)) as latest_date
            FROM game_history 
            WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
            ''')
            
            result = cursor.fetchone()
            total_games, total_commission, earliest_date, latest_date = result
            
            print(f"   {db_name:<12} | Games: {total_games:>2} | Commission: {total_commission:>7.1f} ETB | Range: {earliest_date} to {latest_date}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Error checking {db_name}: {e}")

def main():
    """Main function to sync all databases."""
    print("🔄 Database Synchronization")
    print("=" * 40)
    
    # Sync game history to stats_new.db
    success = sync_game_history_to_stats_new()
    
    if success:
        # Verify synchronization
        verify_sync()
        print("\n✅ All databases synchronized successfully!")
    else:
        print("\n❌ Database synchronization failed!")

if __name__ == "__main__":
    main()
