{"games_played": 12, "total_winners": 12, "total_prize_pool": 936, "player_count": 5, "average_game_duration": 300.85293902738437, "top_players": [], "number_frequencies": {}, "session_start_time": 1753126350.28289, "recent_activity": [{"time": "23:15", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:15", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:15", "event": "Game started with 5 players"}, {"time": "23:15", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:15", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:03", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:02", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:02", "event": "Game started with 4 players"}, {"time": "23:02", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:02", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}]}