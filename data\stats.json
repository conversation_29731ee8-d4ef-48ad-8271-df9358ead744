{"games_played": 8, "total_winners": 8, "total_prize_pool": 576, "player_count": 4, "average_game_duration": 300.94413516180856, "top_players": [], "number_frequencies": {}, "session_start_time": 1753126350.28289, "recent_activity": [{"time": "23:03", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:02", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:02", "event": "Game started with 4 players"}, {"time": "23:02", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:02", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:01", "event": "Game started with 4 players"}, {"time": "23:00", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:00", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "22:59", "event": "Game started with 4 players"}, {"time": "22:59", "event": "Player 'Credit-Based Game' won with credit_based"}]}