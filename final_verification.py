#!/usr/bin/env python3
"""
Final verification of all corrected values
"""

import os
import sqlite3
from datetime import datetime

STATS_DB_PATH = os.path.join('data', 'stats.db')

def final_verification():
    """Final verification of all values."""
    
    print("=" * 60)
    print("FINAL VERIFICATION - ALL CORRECTED VALUES")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # 1. Check total earnings
        print("\n1. TOTAL EARNINGS:")
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        total_earnings = result[0] if result and result[0] is not None else 0
        print(f"   Current: {total_earnings} ETB")
        print(f"   Target:  39782.6667 ETB")
        print(f"   Status:  {'✓ CORRECT' if abs(total_earnings - 39782.6667) < 0.001 else '✗ INCORRECT'}")
        
        # 2. Check yesterday (05/26/2025) earnings
        print("\n2. YESTERDAY (05/26/2025) EARNINGS:")
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', ("2025-05-26",))
        result = cursor.fetchone()
        yesterday_earnings = result[0] if result else 0
        print(f"   Current: {yesterday_earnings} ETB")
        print(f"   Target:  4086.667 ETB")
        print(f"   Status:  {'✓ CORRECT' if abs(yesterday_earnings - 4086.667) < 0.001 else '✗ INCORRECT'}")
        
        # 3. Check wallet balance
        print("\n3. WALLET BALANCE:")
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0
        print(f"   Current: {wallet_balance} ETB")
        print(f"   Target:  4159.0 ETB")
        print(f"   Status:  {'✓ CORRECT' if abs(wallet_balance - 4159.0) < 0.001 else '✗ INCORRECT'}")
        
        # 4. Show recent daily stats for context
        print("\n4. RECENT DAILY STATS (Last 7 days):")
        cursor.execute('''
        SELECT date, earnings, games_played 
        FROM daily_stats 
        WHERE earnings > 0 
        ORDER BY date DESC 
        LIMIT 7
        ''')
        
        print("   Date       | Earnings  | Games")
        print("   -----------|-----------|------")
        for row in cursor.fetchall():
            date, earnings, games = row
            marker = " ← YESTERDAY" if date == "2025-05-26" else ""
            print(f"   {date} | {earnings:8.2f} | {games:4d}{marker}")
        
        # 5. Show recent wallet transactions
        print("\n5. RECENT WALLET TRANSACTIONS:")
        cursor.execute('''
        SELECT date_time, amount, transaction_type, balance_after 
        FROM wallet_transactions 
        ORDER BY id DESC 
        LIMIT 3
        ''')
        
        print("   Date/Time           | Amount    | Type            | Balance")
        print("   --------------------|-----------|-----------------|--------")
        for row in cursor.fetchall():
            date_time, amount, trans_type, balance = row
            print(f"   {date_time} | {amount:8.2f} | {trans_type:15s} | {balance:7.2f}")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("VERIFICATION COMPLETE")
        print("=" * 60)
        
        # Summary
        all_correct = (
            abs(total_earnings - 39782.6667) < 0.001 and
            abs(yesterday_earnings - 4086.667) < 0.001 and
            abs(wallet_balance - 4159.0) < 0.001
        )
        
        if all_correct:
            print("\n🎉 ALL VALUES ARE CORRECTLY SET!")
            print("\nYour stats page should now display:")
            print("   • Total Earnings: 39782.7 ETB")
            print("   • Yesterday (05/26/2025): 4086.7 ETB")
            print("   • Wallet Balance: 4159.0 ETB")
        else:
            print("\n⚠️  SOME VALUES NEED ATTENTION")
        
        return all_correct
        
    except Exception as e:
        print(f"Error during verification: {e}")
        return False

if __name__ == "__main__":
    final_verification()