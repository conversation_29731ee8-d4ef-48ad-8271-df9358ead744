2025-07-20 09:32:25.257 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-20 09:32:25.263 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-20 09:32:25.288 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-20 09:32:25.294 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-20 09:32:25.296 - Initializing StatsDataProvider
2025-07-20 09:32:25.317 - Loaded 8 items from cache
2025-07-20 09:32:25.319 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-20 09:32:25.320 - Starting background data loading thread
2025-07-20 09:32:25.322 - Background data loading started
2025-07-20 09:32:25.322 - StatsDataProvider initialization completed
2025-07-20 09:32:25.322 - Loading summary statistics
2025-07-20 09:32:25.327 - Created singleton instance of StatsDataProvider on module import
2025-07-20 09:32:25.328 - Attempting to load summary stats from GameStatsIntegration
2025-07-20 09:32:25.330 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-20 09:32:25.331 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-20 09:32:25.332 - Successfully loaded summary stats from GameStatsIntegration
2025-07-20 09:32:25.332 - get_stats_provider called, returning provider with initialized=True
2025-07-20 09:32:25.335 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-20 09:32:25.342 - Successfully loaded weekly stats: 7 days
2025-07-20 09:32:25.352 - Saved 10 items to cache
2025-07-20 09:32:25.356 - Background data loading completed
