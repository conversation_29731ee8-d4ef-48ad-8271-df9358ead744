"""
Demonstration: Independent Time System Immunity to PC Clock Changes

This script demonstrates how the independent time system continues
to work correctly even when the PC clock is changed.
"""

import time
from datetime import datetime
from time_manager import (
    independent_now, 
    independent_strftime, 
    get_time_stats, 
    check_clock_changes
)

def show_time_comparison():
    """Show comparison between system time and independent time."""
    print("=" * 70)
    print("TIME COMPARISON")
    print("=" * 70)
    
    system_time = datetime.now()
    independent_time = independent_now()
    
    # Convert to same timezone for comparison
    system_time_aware = system_time.replace(tzinfo=independent_time.tzinfo)
    
    print(f"System Time:      {system_time}")
    print(f"Independent Time: {independent_time}")
    
    try:
        difference = abs((independent_time - system_time_aware).total_seconds())
        print(f"Difference:       {difference:.2f} seconds")
    except:
        print("Difference:       Cannot calculate (timezone mismatch)")
    
    print()

def show_time_stats():
    """Show detailed time system statistics."""
    print("=" * 70)
    print("TIME SYSTEM STATISTICS")
    print("=" * 70)
    
    stats = get_time_stats()
    
    print(f"Reference Time:        {stats['reference_time']}")
    print(f"Current Independent:   {stats['current_independent_time']}")
    print(f"Current System:        {stats['current_system_time']}")
    print(f"Application Startups:  {stats['startup_count']}")
    print(f"Days Since First Run:  {stats['elapsed_since_first_startup']['days']}")
    print(f"Hours Since First Run: {stats['elapsed_since_first_startup']['hours']:.2f}")
    print()

def demonstrate_clock_change_detection():
    """Demonstrate clock change detection."""
    print("=" * 70)
    print("CLOCK CHANGE DETECTION")
    print("=" * 70)
    
    # Check for clock changes
    clock_check = check_clock_changes()
    
    print(f"Clock Changed:     {clock_check['clock_changed']}")
    print(f"Difference:        {clock_check.get('difference_seconds', 0):.2f} seconds")
    print(f"Threshold:         {clock_check.get('threshold', 5)} seconds")
    
    if clock_check['clock_changed']:
        print("WARNING: System clock change detected!")
        print("Independent time system continues unaffected.")
    else:
        print("No significant clock changes detected.")
    print()

def simulate_database_operations():
    """Simulate database operations using independent time."""
    print("=" * 70)
    print("DATABASE OPERATIONS WITH INDEPENDENT TIME")
    print("=" * 70)
    
    try:
        from db_time_integration import DatabaseTimeIntegration
        
        db_time = DatabaseTimeIntegration("data/stats.db")
        
        # Add a sample game record
        timestamp_before = independent_strftime('%Y-%m-%d %H:%M:%S')
        
        record_id = db_time.add_game_record(
            username="DemoPlayer",
            house="DemoHouse",
            stake=50,
            players=3,
            total_calls=20,
            commission_percent=5,
            fee=5,
            total_prize=140,
            details='{"demo": true, "pattern": "Line"}',
            status="completed"
        )
        
        timestamp_after = independent_strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"Timestamp Before: {timestamp_before}")
        print(f"Timestamp After:  {timestamp_after}")
        print(f"Record ID:        {record_id}")
        print("Database record created with independent timestamp!")
        
    except Exception as e:
        print(f"Error in database operation: {e}")
    
    print()

def show_immunity_explanation():
    """Explain how the system is immune to clock changes."""
    print("=" * 70)
    print("HOW IMMUNITY TO CLOCK CHANGES WORKS")
    print("=" * 70)
    print()
    print("1. REFERENCE TIME STORAGE:")
    print("   - First startup: Current UTC time stored as reference")
    print("   - Reference never changes after first startup")
    print()
    print("2. MONOTONIC CLOCK USAGE:")
    print("   - Uses time.monotonic() for elapsed time calculation")
    print("   - Monotonic clock is immune to system clock adjustments")
    print("   - Always moves forward at steady rate")
    print()
    print("3. TIME CALCULATION:")
    print("   - Current Time = Reference Time + Monotonic Elapsed")
    print("   - Independent of system clock changes")
    print("   - Consistent across application restarts")
    print()
    print("4. BENEFITS:")
    print("   - Database timestamps remain consistent")
    print("   - Statistics calculations are accurate")
    print("   - Game history maintains proper chronology")
    print("   - Immune to manual clock changes")
    print("   - Immune to automatic time synchronization")
    print()

def main():
    """Main demonstration function."""
    print("INDEPENDENT TIME SYSTEM DEMONSTRATION")
    print("Immunity to PC Clock Changes")
    print()
    
    # Show current time comparison
    show_time_comparison()
    
    # Show detailed statistics
    show_time_stats()
    
    # Demonstrate clock change detection
    demonstrate_clock_change_detection()
    
    # Simulate database operations
    simulate_database_operations()
    
    # Explain how immunity works
    show_immunity_explanation()
    
    print("=" * 70)
    print("DEMONSTRATION COMPLETED")
    print("=" * 70)
    print()
    print("To test immunity to clock changes:")
    print("1. Note the current independent time above")
    print("2. Change your PC's clock (forward or backward)")
    print("3. Run this script again")
    print("4. Observe that independent time continues normally")
    print("5. System will detect and report the clock change")
    print()
    print("Your database records will always use consistent timestamps!")

if __name__ == "__main__":
    main()