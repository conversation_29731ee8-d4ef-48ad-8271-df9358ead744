import os
import sqlite3
from datetime import datetime, timedelta

class SimpleStatsProvider:
    def __init__(self):
        self.db_path = os.path.join('data', 'stats.db')
    
    def get_daily_earnings(self, date_str):
        try:
            if not os.path.exists(self.db_path):
                return 0.0
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT earnings FROM daily_stats WHERE date = ?", (date_str,))
            result = cursor.fetchone()
            conn.close()
            
            return float(result[0]) if result else 0.0
        except:
            return 0.0
    
    def get_weekly_stats(self, end_date=None):
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        try:
            stats = []
            start_date = end_date - timedelta(days=6)
            current = start_date
            
            while current <= end_date:
                date_str = current.strftime('%Y-%m-%d')
                earnings = self.get_daily_earnings(date_str)
                games = self.get_daily_games(date_str)
                
                stats.append({
                    'date': date_str,
                    'games_played': games,
                    'earnings': earnings,
                    'winners': max(1, games // 2) if games > 0 else 0,
                    'total_players': games * 8 if games > 0 else 0
                })
                
                current += timedelta(days=1)
            
            return stats
        except:
            return []
    
    def get_daily_games(self, date_str):
        try:
            if not os.path.exists(self.db_path):
                return 0
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT games_played FROM daily_stats WHERE date = ?", (date_str,))
            result = cursor.fetchone()
            conn.close()
            
            return int(result[0]) if result else 0
        except:
            return 0

def get_simple_stats_provider():
    return SimpleStatsProvider()
