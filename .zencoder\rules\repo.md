---
description: Repository Information Overview
alwaysApply: true
---

# WOW Bingo Game Information

## Summary
WOW Bingo Game is a modern Python-based bingo application with advanced features. It's designed as a desktop application for playing bingo games with multiple players, featuring automatic number calling, winner detection, and various winning patterns. The game includes a voucher system, statistics tracking, and support for multiple languages including Amharic.

## Structure
- **Root Directory**: Contains main Python files for game logic, UI, and utilities
- **assets/**: Game assets including audio effects, images, and splash screens
- **data/**: Game data storage for settings, player information, and statistics
- **payment/**: Payment and voucher system integration
- **Voucher_Generator-GUI/**: Separate GUI application for voucher generation
- **templates/**: HTML templates for web-based dashboard views
- **utils/**: Utility scripts for database operations and verification
- **scripts/**: Build and deployment scripts

## Language & Runtime
**Language**: Python
**Version**: 3.7+ (recommended 3.9+)
**Build System**: Multiple options (PyInstaller, Nuitka)
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- flet (≥0.24.0): Modern UI framework
- pygame (≥2.5.0): Game engine
- pygame-ce (≥2.4.0): Community edition of pygame
- rethinkdb (≥2.4.10): Database for real-time data
- pydantic (≥2.0.0): Data validation and settings management
- cryptography (≥41.0.0): For voucher system security
- pydub (≥0.25.1): Audio processing
- pillow (≥10.0.0): Image processing
- requests (≥2.31.0): HTTP client

**Development Dependencies**:
- pytest (≥7.4.0): Testing framework
- black (≥23.7.0): Code formatting
- mypy (≥1.5.0): Type checking
- nuitka (≥2.0.0): Compilation to executable
- pyinstaller (≥6.0.0): Alternative executable builder

## Build & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Build executable with PyInstaller
python build_executable.py --tool pyinstaller

# Alternative build with Nuitka (more optimized but slower build)
python build_executable.py --tool nuitka --optimize
```

## Main Components
The bingo game logic is split into three main components:

1. **BingoCard** (`bingo_card.py`): Represents a player's 5x5 bingo card with methods to mark numbers and check for winning patterns.
2. **BingoCaller** (`bingo_caller.py`): Manages the calling of bingo numbers with features like timed calling, call history, and announcements.
3. **BingoLogic** (`bingo_logic.py`): Core game logic that manages the relationship between players, cards, and winning conditions.
4. **Game Integration** (`game_integration.py`): Shows how to integrate the new components with the existing code.

## Testing
**Framework**: pytest
**Test Location**: Limited test files in payment/ and Voucher_Generator-GUI/
**Configuration**: pytest configuration in pyproject.toml
**Run Command**:
```bash
pytest
```

## Subprojects

### Voucher Generator GUI
**Configuration File**: Voucher_Generator-GUI/buildozer.spec, Voucher_Generator-GUI/requirements.txt

#### Language & Runtime
**Language**: Python
**Framework**: Kivy
**Build System**: Buildozer (for mobile deployment)

#### Dependencies
**Main Dependencies**:
- kivy: Cross-platform UI framework
- cryptography: For secure voucher generation

#### Build & Installation
```bash
# Run the voucher generator
cd Voucher_Generator-GUI
python run_voucher_generator.bat
```

### Payment System
**Configuration File**: payment/__init__.py

#### Language & Runtime
**Language**: Python
**Integration**: Modular design to integrate with main game

#### Key Components
- voucher_generator.py: Generates payment vouchers
- voucher_processor.py: Processes and validates vouchers
- recharge_ui.py: User interface for payment/recharge operations
- game_integration.py: Integration with the main game

## Data Storage
The application uses multiple data storage mechanisms:
- Local JSON files for settings and player data
- RethinkDB for real-time data synchronization
- SQLite for statistics and performance metrics

## Additional Features
- Multi-language support (English, Amharic, Oromo)
- External display support for second screens
- Power management to keep screens active during gameplay
- Modern advertising system with GPU acceleration
- Comprehensive statistics tracking and reporting