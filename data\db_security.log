2025-07-18 01:14:29,406 - INFO - Loaded 0 items from persistent cache
2025-07-18 01:14:29,406 - INFO - Stats cache initialized
2025-07-18 01:14:29,407 - INFO - Database security initialized successfully
2025-07-18 01:14:29,408 - INFO - DB Operation: {"timestamp": "2025-07-18 01:14:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 01:14:29,408 - INFO - Created secure database connection
2025-07-18 01:14:29,411 - INFO - Database schema initialized successfully
2025-07-18 01:14:29,412 - INFO - DB Operation: {"timestamp": "2025-07-18 01:14:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 01:14:29,412 - INFO - DB Operation: {"timestamp": "2025-07-18 01:14:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 01:14:29,415 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 01:14:29,416 - INFO - Stats preloader initialized
2025-07-18 01:14:29,421 - INFO - Starting stats data preloading
2025-07-18 01:14:29,421 - INFO - Started stats data preloading
2025-07-18 01:14:29,422 - INFO - Loading data using optimized functions
2025-07-18 01:14:29,422 - WARNING - Connection validation failed, creating new connection
2025-07-18 01:14:29,423 - INFO - DB Operation: {"timestamp": "2025-07-18 01:14:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 01:14:29,450 - INFO - Loaded 0 items from cache
2025-07-18 01:14:29,454 - INFO - Started background data loading
2025-07-18 01:14:29,455 - INFO - OptimizedStatsLoader initialized
2025-07-18 01:14:29,455 - INFO - Using optimized stats loader for integration
2025-07-18 01:14:29,465 - INFO - Loaded weekly stats for 7 days
2025-07-18 01:14:29,467 - INFO - Loaded summary data
2025-07-18 01:14:29,473 - INFO - Loaded game history page 0 (10 records)
2025-07-18 01:14:29,473 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 01:14:29,478 - INFO - Saved 7 items to cache
2025-07-18 01:14:29,479 - INFO - Background data loading completed
2025-07-18 01:14:29,498 - INFO - Preloaded weekly stats for 7 days
2025-07-18 01:14:29,498 - WARNING - Connection validation failed, creating new connection
2025-07-18 01:14:29,499 - INFO - DB Operation: {"timestamp": "2025-07-18 01:14:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 01:14:29,500 - INFO - Preloaded summary data
2025-07-18 01:14:29,503 - INFO - Preloaded game history (10 records)
2025-07-18 01:14:29,504 - INFO - Preloaded wallet data
2025-07-18 01:14:29,504 - INFO - Stats data preloaded successfully in 0.09 seconds
2025-07-18 01:14:29,508 - INFO - Saved 8 items to persistent cache
2025-07-18 01:14:29,550 - INFO - Database schema initialized successfully
2025-07-18 01:14:29,643 - INFO - Database schema initialized successfully
2025-07-18 01:14:29,676 - INFO - Stats database initialized successfully
2025-07-18 01:14:29,676 - INFO - Game stats integration module available
2025-07-18 01:14:29,677 - INFO - Started stats event worker thread
2025-07-18 01:14:29,677 - INFO - Stats event hooks initialized
2025-07-18 01:14:35,241 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 01:14:35,241 - INFO - Stats performance monitor initialized
2025-07-18 01:14:35,383 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-18 02:10:11,679 - INFO - Loaded 0 items from persistent cache
2025-07-18 02:10:11,680 - INFO - Stats cache initialized
2025-07-18 02:10:11,680 - INFO - Database security initialized successfully
2025-07-18 02:10:11,681 - INFO - DB Operation: {"timestamp": "2025-07-18 02:10:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:10:11,681 - INFO - Created secure database connection
2025-07-18 02:10:11,684 - INFO - Database schema initialized successfully
2025-07-18 02:10:11,688 - INFO - DB Operation: {"timestamp": "2025-07-18 02:10:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:10:11,689 - INFO - DB Operation: {"timestamp": "2025-07-18 02:10:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:10:11,689 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 02:10:11,690 - INFO - Stats preloader initialized
2025-07-18 02:10:11,691 - INFO - Starting stats data preloading
2025-07-18 02:10:11,691 - INFO - Started stats data preloading
2025-07-18 02:10:11,691 - INFO - Loading data using optimized functions
2025-07-18 02:10:11,692 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:10:11,692 - INFO - DB Operation: {"timestamp": "2025-07-18 02:10:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:10:11,700 - INFO - Preloaded weekly stats for 7 days
2025-07-18 02:10:11,700 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:10:11,701 - INFO - DB Operation: {"timestamp": "2025-07-18 02:10:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:10:11,704 - INFO - Loaded 0 items from cache
2025-07-18 02:10:11,707 - INFO - Started background data loading
2025-07-18 02:10:11,707 - INFO - OptimizedStatsLoader initialized
2025-07-18 02:10:11,707 - INFO - Using optimized stats loader for integration
2025-07-18 02:10:11,708 - INFO - Preloaded summary data
2025-07-18 02:10:11,709 - INFO - Loaded weekly stats for 7 days
2025-07-18 02:10:11,710 - INFO - Preloaded game history (10 records)
2025-07-18 02:10:11,711 - INFO - Loaded summary data
2025-07-18 02:10:11,711 - INFO - Preloaded wallet data
2025-07-18 02:10:11,711 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-18 02:10:11,712 - INFO - Loaded game history page 0 (10 records)
2025-07-18 02:10:11,712 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 02:10:11,716 - INFO - Saved 7 items to cache
2025-07-18 02:10:11,716 - INFO - Background data loading completed
2025-07-18 02:10:11,716 - INFO - Saved 8 items to persistent cache
2025-07-18 02:10:11,724 - INFO - Database schema initialized successfully
2025-07-18 02:10:11,728 - INFO - Database schema initialized successfully
2025-07-18 02:10:11,729 - INFO - Stats database initialized successfully
2025-07-18 02:10:11,730 - INFO - Game stats integration module available
2025-07-18 02:10:11,730 - INFO - Started stats event worker thread
2025-07-18 02:10:11,731 - INFO - Stats event hooks initialized
2025-07-18 02:10:13,198 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 02:10:13,199 - INFO - Stats performance monitor initialized
2025-07-18 02:10:13,274 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-18 02:16:56,014 - INFO - Loaded 0 items from persistent cache
2025-07-18 02:16:56,015 - INFO - Stats cache initialized
2025-07-18 02:16:56,016 - INFO - Database security initialized successfully
2025-07-18 02:16:56,016 - INFO - DB Operation: {"timestamp": "2025-07-18 02:16:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:16:56,016 - INFO - Created secure database connection
2025-07-18 02:16:56,032 - INFO - Database schema initialized successfully
2025-07-18 02:16:56,032 - INFO - DB Operation: {"timestamp": "2025-07-18 02:16:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:16:56,033 - INFO - DB Operation: {"timestamp": "2025-07-18 02:16:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:16:56,033 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 02:16:56,033 - INFO - Stats preloader initialized
2025-07-18 02:16:56,033 - INFO - Starting stats data preloading
2025-07-18 02:16:56,033 - INFO - Started stats data preloading
2025-07-18 02:16:56,033 - INFO - Loading data using optimized functions
2025-07-18 02:16:56,034 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:16:56,034 - INFO - DB Operation: {"timestamp": "2025-07-18 02:16:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:16:56,044 - INFO - Preloaded weekly stats for 7 days
2025-07-18 02:16:56,044 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:16:56,044 - INFO - DB Operation: {"timestamp": "2025-07-18 02:16:56", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:16:56,045 - INFO - Loaded 0 items from cache
2025-07-18 02:16:56,046 - INFO - Started background data loading
2025-07-18 02:16:56,046 - INFO - OptimizedStatsLoader initialized
2025-07-18 02:16:56,046 - INFO - Using optimized stats loader for integration
2025-07-18 02:16:56,046 - INFO - Preloaded summary data
2025-07-18 02:16:56,047 - INFO - Loaded weekly stats for 7 days
2025-07-18 02:16:56,048 - INFO - Preloaded game history (10 records)
2025-07-18 02:16:56,048 - INFO - Preloaded wallet data
2025-07-18 02:16:56,048 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-18 02:16:56,050 - INFO - Loaded summary data
2025-07-18 02:16:56,051 - INFO - Saved 8 items to persistent cache
2025-07-18 02:16:56,051 - INFO - Loaded game history page 0 (10 records)
2025-07-18 02:16:56,051 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 02:16:56,054 - INFO - Saved 7 items to cache
2025-07-18 02:16:56,054 - INFO - Background data loading completed
2025-07-18 02:16:56,059 - INFO - Database schema initialized successfully
2025-07-18 02:16:56,062 - INFO - Database schema initialized successfully
2025-07-18 02:16:56,063 - INFO - Stats database initialized successfully
2025-07-18 02:16:56,063 - INFO - Game stats integration module available
2025-07-18 02:16:56,065 - INFO - Started stats event worker thread
2025-07-18 02:16:56,065 - INFO - Stats event hooks initialized
2025-07-18 02:16:56,717 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 02:16:56,717 - INFO - Stats performance monitor initialized
2025-07-18 02:16:56,779 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-18 02:21:20,558 - INFO - Loaded 7 items from persistent cache
2025-07-18 02:21:20,558 - INFO - Stats cache initialized
2025-07-18 02:21:20,559 - INFO - Database security initialized successfully
2025-07-18 02:21:20,559 - INFO - DB Operation: {"timestamp": "2025-07-18 02:21:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:21:20,560 - INFO - Created secure database connection
2025-07-18 02:21:20,561 - INFO - Database schema initialized successfully
2025-07-18 02:21:20,561 - INFO - DB Operation: {"timestamp": "2025-07-18 02:21:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:21:20,561 - INFO - DB Operation: {"timestamp": "2025-07-18 02:21:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:21:20,562 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 02:21:20,562 - INFO - Stats preloader initialized
2025-07-18 02:21:20,566 - INFO - Starting stats data preloading
2025-07-18 02:21:20,566 - INFO - Started stats data preloading
2025-07-18 02:21:20,566 - INFO - Loading data using optimized functions
2025-07-18 02:21:20,567 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:21:20,567 - INFO - DB Operation: {"timestamp": "2025-07-18 02:21:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:21:20,573 - INFO - Preloaded weekly stats for 7 days
2025-07-18 02:21:20,573 - WARNING - Connection validation failed, creating new connection
2025-07-18 02:21:20,575 - INFO - DB Operation: {"timestamp": "2025-07-18 02:21:20", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 02:21:20,576 - INFO - Loaded 7 items from cache
2025-07-18 02:21:20,577 - INFO - Started background data loading
2025-07-18 02:21:20,577 - INFO - OptimizedStatsLoader initialized
2025-07-18 02:21:20,577 - INFO - Using optimized stats loader for integration
2025-07-18 02:21:20,577 - INFO - Preloaded summary data
2025-07-18 02:21:20,580 - INFO - Preloaded game history (10 records)
2025-07-18 02:21:20,581 - INFO - Loaded weekly stats for 7 days
2025-07-18 02:21:20,583 - INFO - Preloaded wallet data
2025-07-18 02:21:20,583 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-18 02:21:20,584 - INFO - Loaded summary data
2025-07-18 02:21:20,586 - INFO - Loaded game history page 0 (10 records)
2025-07-18 02:21:20,586 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 02:21:20,587 - INFO - Saved 9 items to persistent cache
2025-07-18 02:21:20,588 - INFO - Saved 7 items to cache
2025-07-18 02:21:20,588 - INFO - Background data loading completed
2025-07-18 02:21:20,592 - INFO - Database schema initialized successfully
2025-07-18 02:21:20,595 - INFO - Database schema initialized successfully
2025-07-18 02:21:20,600 - INFO - Stats database initialized successfully
2025-07-18 02:21:20,600 - INFO - Game stats integration module available
2025-07-18 02:21:20,601 - INFO - Started stats event worker thread
2025-07-18 02:21:20,601 - INFO - Stats event hooks initialized
2025-07-18 02:21:21,318 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 02:21:21,318 - INFO - Stats performance monitor initialized
2025-07-18 02:21:21,375 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-18 03:52:34,640 - INFO - Loaded 0 items from persistent cache
2025-07-18 03:52:34,640 - INFO - Stats cache initialized
2025-07-18 03:52:34,641 - INFO - Database security initialized successfully
2025-07-18 03:52:34,644 - INFO - DB Operation: {"timestamp": "2025-07-18 03:52:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 03:52:34,645 - INFO - Created secure database connection
2025-07-18 03:52:34,648 - INFO - Database schema initialized successfully
2025-07-18 03:52:34,648 - INFO - DB Operation: {"timestamp": "2025-07-18 03:52:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 03:52:34,649 - INFO - DB Operation: {"timestamp": "2025-07-18 03:52:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 03:52:34,649 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 03:52:34,649 - INFO - Stats preloader initialized
2025-07-18 03:52:34,652 - INFO - Starting stats data preloading
2025-07-18 03:52:34,653 - INFO - Started stats data preloading
2025-07-18 03:52:34,653 - INFO - Loading data using optimized functions
2025-07-18 03:52:34,654 - WARNING - Connection validation failed, creating new connection
2025-07-18 03:52:34,655 - INFO - DB Operation: {"timestamp": "2025-07-18 03:52:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 03:52:34,668 - INFO - Preloaded weekly stats for 7 days
2025-07-18 03:52:34,668 - WARNING - Connection validation failed, creating new connection
2025-07-18 03:52:34,669 - INFO - DB Operation: {"timestamp": "2025-07-18 03:52:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 03:52:34,670 - INFO - Preloaded summary data
2025-07-18 03:52:34,673 - INFO - Preloaded game history (10 records)
2025-07-18 03:52:34,674 - INFO - Preloaded wallet data
2025-07-18 03:52:34,674 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-18 03:52:34,678 - INFO - Saved 8 items to persistent cache
2025-07-18 03:52:34,731 - INFO - Loaded 8 items from cache
2025-07-18 03:52:34,732 - INFO - Started background data loading
2025-07-18 03:52:34,732 - INFO - OptimizedStatsLoader initialized
2025-07-18 03:52:34,732 - INFO - Using optimized stats loader for integration
2025-07-18 03:52:34,733 - INFO - Loaded weekly stats for 7 days
2025-07-18 03:52:34,734 - INFO - Loaded summary data
2025-07-18 03:52:34,735 - INFO - Loaded game history page 0 (10 records)
2025-07-18 03:52:34,735 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 03:52:34,737 - INFO - Saved 9 items to cache
2025-07-18 03:52:34,737 - INFO - Background data loading completed
2025-07-18 03:52:34,741 - INFO - Database schema initialized successfully
2025-07-18 03:52:34,754 - INFO - Database schema initialized successfully
2025-07-18 03:52:34,756 - INFO - Stats database initialized successfully
2025-07-18 03:52:34,756 - INFO - Game stats integration module available
2025-07-18 03:52:34,757 - INFO - Started stats event worker thread
2025-07-18 03:52:34,758 - INFO - Stats event hooks initialized
2025-07-18 03:52:35,605 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 03:52:35,606 - INFO - Stats performance monitor initialized
2025-07-18 03:52:35,677 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-18 04:01:09,397 - INFO - Loaded 0 items from persistent cache
2025-07-18 04:01:09,401 - INFO - Stats cache initialized
2025-07-18 04:01:09,406 - INFO - Database security initialized successfully
2025-07-18 04:01:09,409 - INFO - DB Operation: {"timestamp": "2025-07-18 04:01:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 04:01:09,410 - INFO - Created secure database connection
2025-07-18 04:01:09,494 - INFO - Database schema initialized successfully
2025-07-18 04:01:09,497 - INFO - DB Operation: {"timestamp": "2025-07-18 04:01:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 04:01:09,499 - INFO - DB Operation: {"timestamp": "2025-07-18 04:01:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 04:01:09,500 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-18 04:01:09,500 - INFO - Stats preloader initialized
2025-07-18 04:01:09,503 - INFO - Starting stats data preloading
2025-07-18 04:01:09,504 - INFO - Loading data using optimized functions
2025-07-18 04:01:09,505 - WARNING - Connection validation failed, creating new connection
2025-07-18 04:01:09,505 - INFO - Started stats data preloading
2025-07-18 04:01:09,507 - INFO - DB Operation: {"timestamp": "2025-07-18 04:01:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 04:01:09,513 - INFO - Preloaded weekly stats for 7 days
2025-07-18 04:01:09,514 - WARNING - Connection validation failed, creating new connection
2025-07-18 04:01:09,514 - INFO - DB Operation: {"timestamp": "2025-07-18 04:01:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-18 04:01:09,515 - INFO - Preloaded summary data
2025-07-18 04:01:09,517 - INFO - Preloaded game history (10 records)
2025-07-18 04:01:09,517 - INFO - Preloaded wallet data
2025-07-18 04:01:09,517 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-18 04:01:09,522 - INFO - Saved 8 items to persistent cache
2025-07-18 04:01:09,564 - INFO - Loaded 8 items from cache
2025-07-18 04:01:09,595 - INFO - Started background data loading
2025-07-18 04:01:09,596 - INFO - OptimizedStatsLoader initialized
2025-07-18 04:01:09,596 - INFO - Using optimized stats loader for integration
2025-07-18 04:01:09,598 - INFO - Loaded weekly stats for 7 days
2025-07-18 04:01:09,598 - INFO - Loaded summary data
2025-07-18 04:01:09,599 - INFO - Loaded game history page 0 (10 records)
2025-07-18 04:01:09,599 - INFO - Loaded game history metadata (total pages: 7)
2025-07-18 04:01:09,602 - INFO - Saved 9 items to cache
2025-07-18 04:01:09,602 - INFO - Background data loading completed
2025-07-18 04:01:09,615 - INFO - Database schema initialized successfully
2025-07-18 04:01:09,635 - INFO - Database schema initialized successfully
2025-07-18 04:01:09,685 - INFO - Stats database initialized successfully
2025-07-18 04:01:09,695 - INFO - Game stats integration module available
2025-07-18 04:01:09,701 - INFO - Started stats event worker thread
2025-07-18 04:01:09,701 - INFO - Stats event hooks initialized
2025-07-18 04:01:11,695 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-18 04:01:11,695 - INFO - Stats performance monitor initialized
2025-07-18 04:01:11,784 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:30:25,581 - INFO - Loaded 0 items from persistent cache
2025-07-19 23:30:25,590 - INFO - Stats cache initialized
2025-07-19 23:30:25,591 - INFO - Database security initialized successfully
2025-07-19 23:30:25,592 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:25,592 - INFO - Created secure database connection
2025-07-19 23:30:25,664 - INFO - Database schema initialized successfully
2025-07-19 23:30:25,665 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:25,666 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:25,666 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:30:25,666 - INFO - Stats preloader initialized
2025-07-19 23:30:25,669 - INFO - Starting stats data preloading
2025-07-19 23:30:25,669 - INFO - Started stats data preloading
2025-07-19 23:30:25,669 - INFO - Loading data using optimized functions
2025-07-19 23:30:25,670 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:30:25,671 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:25,676 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:30:25,676 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:30:25,677 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:25,678 - INFO - Preloaded summary data
2025-07-19 23:30:25,678 - INFO - Loaded 0 items from cache
2025-07-19 23:30:25,680 - INFO - Preloaded game history (10 records)
2025-07-19 23:30:25,680 - INFO - Started background data loading
2025-07-19 23:30:25,680 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:30:25,680 - INFO - Using optimized stats loader for integration
2025-07-19 23:30:25,681 - INFO - Preloaded wallet data
2025-07-19 23:30:25,681 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:30:25,681 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:30:25,683 - INFO - Saved 8 items to persistent cache
2025-07-19 23:30:25,684 - INFO - Loaded summary data
2025-07-19 23:30:25,687 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:30:25,687 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:30:25,689 - INFO - Saved 7 items to cache
2025-07-19 23:30:25,689 - INFO - Background data loading completed
2025-07-19 23:30:25,690 - INFO - Database schema initialized successfully
2025-07-19 23:30:25,692 - INFO - Database schema initialized successfully
2025-07-19 23:30:25,693 - INFO - Stats database initialized successfully
2025-07-19 23:30:25,693 - INFO - Game stats integration module available
2025-07-19 23:30:25,694 - INFO - Started stats event worker thread
2025-07-19 23:30:25,694 - INFO - Stats event hooks initialized
2025-07-19 23:30:26,303 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:30:26,303 - INFO - Stats performance monitor initialized
2025-07-19 23:30:26,362 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:30:40,977 - INFO - Loaded 7 items from persistent cache
2025-07-19 23:30:40,977 - INFO - Stats cache initialized
2025-07-19 23:30:40,977 - INFO - Database security initialized successfully
2025-07-19 23:30:40,978 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:40", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:41,057 - INFO - Created secure database connection
2025-07-19 23:30:41,062 - INFO - Database schema initialized successfully
2025-07-19 23:30:41,062 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:41", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:41,063 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:41", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:41,063 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:30:41,063 - INFO - Stats preloader initialized
2025-07-19 23:30:41,064 - INFO - Starting stats data preloading
2025-07-19 23:30:41,064 - INFO - Started stats data preloading
2025-07-19 23:30:41,064 - INFO - Loading data using optimized functions
2025-07-19 23:30:41,065 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:30:41,065 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:41", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:41,071 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:30:41,072 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:30:41,072 - INFO - DB Operation: {"timestamp": "2025-07-19 23:30:41", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:30:41,074 - INFO - Preloaded summary data
2025-07-19 23:30:41,076 - INFO - Loaded 7 items from cache
2025-07-19 23:30:41,076 - INFO - Preloaded game history (10 records)
2025-07-19 23:30:41,077 - INFO - Started background data loading
2025-07-19 23:30:41,077 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:30:41,077 - INFO - Using optimized stats loader for integration
2025-07-19 23:30:41,077 - INFO - Preloaded wallet data
2025-07-19 23:30:41,077 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:30:41,079 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:30:41,080 - INFO - Saved 9 items to persistent cache
2025-07-19 23:30:41,080 - INFO - Loaded summary data
2025-07-19 23:30:41,081 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:30:41,081 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:30:41,083 - INFO - Saved 7 items to cache
2025-07-19 23:30:41,083 - INFO - Background data loading completed
2025-07-19 23:30:41,086 - INFO - Database schema initialized successfully
2025-07-19 23:30:41,090 - INFO - Database schema initialized successfully
2025-07-19 23:30:41,091 - INFO - Stats database initialized successfully
2025-07-19 23:30:41,091 - INFO - Game stats integration module available
2025-07-19 23:30:41,092 - INFO - Started stats event worker thread
2025-07-19 23:30:41,092 - INFO - Stats event hooks initialized
2025-07-19 23:30:41,689 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:30:41,689 - INFO - Stats performance monitor initialized
2025-07-19 23:30:41,746 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:32:09,207 - INFO - Loaded 7 items from persistent cache
2025-07-19 23:32:09,207 - INFO - Stats cache initialized
2025-07-19 23:32:09,208 - INFO - Database security initialized successfully
2025-07-19 23:32:09,209 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:09,209 - INFO - Created secure database connection
2025-07-19 23:32:09,213 - INFO - Database schema initialized successfully
2025-07-19 23:32:09,213 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:09,214 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:09,214 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:32:09,214 - INFO - Stats preloader initialized
2025-07-19 23:32:09,217 - INFO - Starting stats data preloading
2025-07-19 23:32:09,217 - INFO - Started stats data preloading
2025-07-19 23:32:09,217 - INFO - Loading data using optimized functions
2025-07-19 23:32:09,218 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:09,218 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:09,226 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:32:09,227 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:09,227 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:09,228 - INFO - Preloaded summary data
2025-07-19 23:32:09,229 - INFO - Preloaded game history (10 records)
2025-07-19 23:32:09,229 - INFO - Preloaded wallet data
2025-07-19 23:32:09,229 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:32:09,231 - INFO - Saved 9 items to persistent cache
2025-07-19 23:32:09,295 - INFO - Loaded 9 items from cache
2025-07-19 23:32:09,295 - INFO - Started background data loading
2025-07-19 23:32:09,295 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:32:09,295 - INFO - Using optimized stats loader for integration
2025-07-19 23:32:09,297 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:32:09,297 - INFO - Loaded summary data
2025-07-19 23:32:09,298 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:32:09,298 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:32:09,300 - INFO - Saved 9 items to cache
2025-07-19 23:32:09,301 - INFO - Background data loading completed
2025-07-19 23:32:09,303 - INFO - Database schema initialized successfully
2025-07-19 23:32:09,306 - INFO - Database schema initialized successfully
2025-07-19 23:32:09,307 - INFO - Stats database initialized successfully
2025-07-19 23:32:09,307 - INFO - Game stats integration module available
2025-07-19 23:32:09,308 - INFO - Started stats event worker thread
2025-07-19 23:32:09,308 - INFO - Stats event hooks initialized
2025-07-19 23:32:09,920 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:32:09,920 - INFO - Stats performance monitor initialized
2025-07-19 23:32:09,979 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:32:27,678 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:32:27,678 - INFO - Stats cache initialized
2025-07-19 23:32:27,679 - INFO - Database security initialized successfully
2025-07-19 23:32:27,679 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:27,679 - INFO - Created secure database connection
2025-07-19 23:32:27,682 - INFO - Database schema initialized successfully
2025-07-19 23:32:27,683 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:27,683 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:27,683 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:32:27,684 - INFO - Stats preloader initialized
2025-07-19 23:32:27,685 - INFO - Starting stats data preloading
2025-07-19 23:32:27,685 - INFO - Started stats data preloading
2025-07-19 23:32:27,686 - INFO - Loading data using optimized functions
2025-07-19 23:32:27,686 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:27,687 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:27,695 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:32:27,695 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:27,695 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:27", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:27,699 - INFO - Preloaded summary data
2025-07-19 23:32:27,700 - INFO - Preloaded game history (10 records)
2025-07-19 23:32:27,700 - INFO - Preloaded wallet data
2025-07-19 23:32:27,700 - INFO - Loaded 9 items from cache
2025-07-19 23:32:27,702 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-19 23:32:27,705 - INFO - Started background data loading
2025-07-19 23:32:27,705 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:32:27,705 - INFO - Saved 9 items to persistent cache
2025-07-19 23:32:27,706 - INFO - Using optimized stats loader for integration
2025-07-19 23:32:27,707 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:32:27,709 - INFO - Loaded summary data
2025-07-19 23:32:27,709 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:32:27,709 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:32:27,711 - INFO - Saved 9 items to cache
2025-07-19 23:32:27,712 - INFO - Background data loading completed
2025-07-19 23:32:27,714 - INFO - Database schema initialized successfully
2025-07-19 23:32:27,719 - INFO - Database schema initialized successfully
2025-07-19 23:32:27,719 - INFO - Stats database initialized successfully
2025-07-19 23:32:27,719 - INFO - Game stats integration module available
2025-07-19 23:32:27,721 - INFO - Started stats event worker thread
2025-07-19 23:32:27,721 - INFO - Stats event hooks initialized
2025-07-19 23:32:28,298 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:32:28,298 - INFO - Stats performance monitor initialized
2025-07-19 23:32:28,355 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:32:58,266 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:32:58,267 - INFO - Stats cache initialized
2025-07-19 23:32:58,267 - INFO - Database security initialized successfully
2025-07-19 23:32:58,268 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:58,268 - INFO - Created secure database connection
2025-07-19 23:32:58,288 - INFO - Database schema initialized successfully
2025-07-19 23:32:58,289 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:58,291 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:58,291 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:32:58,291 - INFO - Stats preloader initialized
2025-07-19 23:32:58,292 - INFO - Starting stats data preloading
2025-07-19 23:32:58,292 - INFO - Started stats data preloading
2025-07-19 23:32:58,292 - INFO - Loading data using optimized functions
2025-07-19 23:32:58,292 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:58,293 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:58,298 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:32:58,298 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:58,299 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:58,300 - INFO - Loaded 9 items from cache
2025-07-19 23:32:58,305 - INFO - Preloaded summary data
2025-07-19 23:32:58,306 - INFO - Started background data loading
2025-07-19 23:32:58,306 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:32:58,306 - INFO - Using optimized stats loader for integration
2025-07-19 23:32:58,307 - INFO - Preloaded game history (10 records)
2025-07-19 23:32:58,308 - INFO - Preloaded wallet data
2025-07-19 23:32:58,308 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:32:58,308 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-19 23:32:58,310 - INFO - Loaded summary data
2025-07-19 23:32:58,310 - INFO - Saved 9 items to persistent cache
2025-07-19 23:32:58,311 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:32:58,311 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:32:58,313 - INFO - Saved 9 items to cache
2025-07-19 23:32:58,313 - INFO - Background data loading completed
2025-07-19 23:32:58,314 - INFO - Database schema initialized successfully
2025-07-19 23:32:58,316 - INFO - Database schema initialized successfully
2025-07-19 23:32:58,317 - INFO - Stats database initialized successfully
2025-07-19 23:32:58,317 - INFO - Game stats integration module available
2025-07-19 23:32:58,320 - INFO - Started stats event worker thread
2025-07-19 23:32:58,321 - INFO - Stats event hooks initialized
2025-07-19 23:32:58,993 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:32:58,993 - INFO - Stats performance monitor initialized
2025-07-19 23:32:59,052 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:32:59,586 - INFO - generate_background: 0.0030s
2025-07-19 23:32:59,655 - INFO - draw_background: 0.0748s
2025-07-19 23:32:59,683 - INFO - draw_stats_page: 0.1029s
2025-07-19 23:32:59,698 - INFO - Starting stats data preloading
2025-07-19 23:32:59,699 - INFO - Started stats data preloading
2025-07-19 23:32:59,699 - INFO - Loading data using optimized functions
2025-07-19 23:32:59,699 - INFO - load_statistics: 0.0122s
2025-07-19 23:32:59,700 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:59,700 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:59,703 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:32:59,704 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:32:59,704 - INFO - DB Operation: {"timestamp": "2025-07-19 23:32:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:32:59,707 - INFO - Preloaded summary data
2025-07-19 23:32:59,708 - INFO - Preloaded game history (10 records)
2025-07-19 23:32:59,709 - INFO - Preloaded wallet data
2025-07-19 23:32:59,709 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:32:59,711 - INFO - Saved 9 items to persistent cache
2025-07-19 23:33:25,536 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:33:25,537 - INFO - Stats cache initialized
2025-07-19 23:33:25,537 - INFO - Database security initialized successfully
2025-07-19 23:33:25,538 - INFO - DB Operation: {"timestamp": "2025-07-19 23:33:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:33:25,538 - INFO - Created secure database connection
2025-07-19 23:33:25,555 - INFO - Database schema initialized successfully
2025-07-19 23:33:25,556 - INFO - DB Operation: {"timestamp": "2025-07-19 23:33:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:33:25,556 - INFO - DB Operation: {"timestamp": "2025-07-19 23:33:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:33:25,557 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:33:25,557 - INFO - Stats preloader initialized
2025-07-19 23:33:25,562 - INFO - Starting stats data preloading
2025-07-19 23:33:25,562 - INFO - Started stats data preloading
2025-07-19 23:33:25,562 - INFO - Loading data using optimized functions
2025-07-19 23:33:25,563 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:33:25,563 - INFO - DB Operation: {"timestamp": "2025-07-19 23:33:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:33:25,568 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:33:25,568 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:33:25,569 - INFO - DB Operation: {"timestamp": "2025-07-19 23:33:25", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:33:25,570 - INFO - Preloaded summary data
2025-07-19 23:33:25,570 - INFO - Preloaded game history (10 records)
2025-07-19 23:33:25,571 - INFO - Preloaded wallet data
2025-07-19 23:33:25,571 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:33:25,576 - INFO - Saved 9 items to persistent cache
2025-07-19 23:33:25,589 - INFO - Loaded 9 items from cache
2025-07-19 23:33:25,589 - INFO - Started background data loading
2025-07-19 23:33:25,590 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:33:25,590 - INFO - Using optimized stats loader for integration
2025-07-19 23:33:25,595 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:33:25,595 - INFO - Loaded summary data
2025-07-19 23:33:25,596 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:33:25,596 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:33:25,600 - INFO - Saved 9 items to cache
2025-07-19 23:33:25,600 - INFO - Background data loading completed
2025-07-19 23:33:25,606 - INFO - Database schema initialized successfully
2025-07-19 23:33:25,613 - INFO - Database schema initialized successfully
2025-07-19 23:33:25,614 - INFO - Stats database initialized successfully
2025-07-19 23:33:25,614 - INFO - Game stats integration module available
2025-07-19 23:33:25,615 - INFO - Started stats event worker thread
2025-07-19 23:33:25,615 - INFO - Stats event hooks initialized
2025-07-19 23:33:26,245 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:33:26,245 - INFO - Stats performance monitor initialized
2025-07-19 23:33:26,302 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:33:26,930 - INFO - draw_background: 0.0821s
2025-07-19 23:33:26,958 - INFO - draw_stats_page: 0.1113s
2025-07-19 23:33:26,962 - INFO - draw_background: 0.0016s
2025-07-19 23:33:26,966 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:26,977 - INFO - draw_background: 0.0000s
2025-07-19 23:33:26,982 - INFO - draw_stats_page: 0.0057s
2025-07-19 23:33:26,994 - INFO - draw_background: 0.0000s
2025-07-19 23:33:26,999 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:27,010 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,015 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:27,016 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,027 - INFO - draw_background: 0.0011s
2025-07-19 23:33:27,032 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,044 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,048 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,062 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,065 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:27,078 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,082 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:27,095 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,100 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,101 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,111 - INFO - draw_background: 0.0005s
2025-07-19 23:33:27,114 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:27,128 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,131 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:27,145 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,149 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:27,161 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,164 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:27,177 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,181 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:27,182 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,193 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,197 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,209 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,214 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:27,227 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,231 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,244 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,247 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:27,261 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,264 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:27,265 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,278 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,281 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:27,296 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,299 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:27,311 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,315 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:27,328 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,332 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:27,344 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,348 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:27,349 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,362 - INFO - draw_background: 0.0016s
2025-07-19 23:33:27,366 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,378 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,383 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:27,395 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,399 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,410 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,415 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:27,427 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,431 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:27,432 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,444 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,448 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:27,460 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,465 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,476 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,481 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,493 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,498 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,510 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,514 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:27,515 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,525 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,531 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:27,542 - INFO - draw_background: 0.0011s
2025-07-19 23:33:27,547 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:27,558 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,564 - INFO - draw_stats_page: 0.0052s
2025-07-19 23:33:27,576 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,580 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,592 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,596 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,597 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,606 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,613 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:27,623 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,629 - INFO - draw_stats_page: 0.0070s
2025-07-19 23:33:27,640 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,645 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:27,656 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,663 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:27,673 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,678 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:27,679 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,688 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,695 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:27,706 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,712 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:27,722 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,728 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:27,740 - INFO - draw_background: 0.0010s
2025-07-19 23:33:27,744 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,755 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,761 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:27,762 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,771 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,778 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:27,787 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,793 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:27,804 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,811 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:27,821 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,827 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:27,837 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,843 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:27,844 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,853 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,857 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,869 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,875 - INFO - draw_stats_page: 0.0047s
2025-07-19 23:33:27,886 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,891 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:27,902 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,907 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:27,919 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,923 - INFO - draw_stats_page: 0.0044s
2025-07-19 23:33:27,925 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:27,934 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,938 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:27,951 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,956 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:27,968 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,975 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:27,986 - INFO - draw_background: 0.0000s
2025-07-19 23:33:27,993 - INFO - draw_stats_page: 0.0072s
2025-07-19 23:33:28,002 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,008 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:28,011 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,019 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,024 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:28,035 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,039 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:28,051 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,054 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:28,067 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,070 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,083 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,086 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,087 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,100 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,103 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:28,115 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,118 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:28,133 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,135 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,148 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,153 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:28,165 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,170 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:28,171 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,181 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,184 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:28,198 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,201 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:28,213 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,216 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:28,231 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,234 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:28,247 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,250 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,251 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,262 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,265 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:28,279 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,283 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:28,296 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,299 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,313 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,316 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:28,328 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,332 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:28,332 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,345 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,348 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,362 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,365 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,380 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,383 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:28,395 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,398 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:28,412 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,415 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,416 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,429 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,433 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:33:28,446 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,449 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:28,463 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,467 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:28,480 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,483 - INFO - draw_stats_page: 0.0048s
2025-07-19 23:33:28,496 - INFO - draw_background: 0.0011s
2025-07-19 23:33:28,500 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:28,500 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,511 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,515 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:28,529 - INFO - draw_background: 0.0011s
2025-07-19 23:33:28,532 - INFO - draw_stats_page: 0.0047s
2025-07-19 23:33:28,546 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,550 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:28,561 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,566 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:28,577 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,581 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:28,583 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,592 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,597 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:28,610 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,615 - INFO - draw_stats_page: 0.0047s
2025-07-19 23:33:28,626 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,631 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:28,643 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,648 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:28,660 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,665 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:28,666 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,676 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,681 - INFO - draw_stats_page: 0.0057s
2025-07-19 23:33:28,691 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,697 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:28,708 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,716 - INFO - draw_stats_page: 0.0082s
2025-07-19 23:33:28,726 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,731 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:28,741 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,746 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:28,748 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,759 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,762 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:28,774 - INFO - draw_background: 0.0009s
2025-07-19 23:33:28,781 - INFO - draw_stats_page: 0.0085s
2025-07-19 23:33:28,791 - INFO - draw_background: 0.0016s
2025-07-19 23:33:28,799 - INFO - draw_stats_page: 0.0093s
2025-07-19 23:33:28,808 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,811 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:28,824 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,831 - INFO - draw_stats_page: 0.0075s
2025-07-19 23:33:28,832 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,841 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,848 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:28,859 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,864 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:28,875 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,881 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:28,895 - INFO - draw_background: 0.0020s
2025-07-19 23:33:28,899 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:28,908 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,913 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:28,916 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:28,926 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,931 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:28,942 - INFO - draw_background: 0.0000s
2025-07-19 23:33:28,948 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:28,961 - INFO - draw_background: 0.0017s
2025-07-19 23:33:28,966 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:28,978 - INFO - draw_background: 0.0010s
2025-07-19 23:33:28,981 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:28,993 - INFO - draw_background: 0.0009s
2025-07-19 23:33:28,997 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:28,998 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,011 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,014 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:29,027 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,031 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:29,044 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,047 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:29,060 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,063 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:29,078 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,081 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:29,081 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,093 - INFO - draw_background: 0.0009s
2025-07-19 23:33:29,098 - INFO - draw_stats_page: 0.0049s
2025-07-19 23:33:29,111 - INFO - draw_background: 0.0015s
2025-07-19 23:33:29,115 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:29,128 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,131 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:29,144 - INFO - draw_background: 0.0009s
2025-07-19 23:33:29,149 - INFO - draw_stats_page: 0.0059s
2025-07-19 23:33:29,159 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,163 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:29,164 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,175 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,181 - INFO - draw_stats_page: 0.0070s
2025-07-19 23:33:29,192 - INFO - draw_background: 0.0017s
2025-07-19 23:33:29,197 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,208 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,213 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:29,225 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,230 - INFO - draw_stats_page: 0.0070s
2025-07-19 23:33:29,241 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,245 - INFO - draw_stats_page: 0.0064s
2025-07-19 23:33:29,246 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,257 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,262 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,274 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,279 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:29,291 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,294 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:29,305 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,311 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,321 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,328 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,329 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,338 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,344 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:29,354 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,360 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:29,371 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,377 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:29,391 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,395 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:29,407 - INFO - draw_background: 0.0020s
2025-07-19 23:33:29,413 - INFO - draw_stats_page: 0.0081s
2025-07-19 23:33:29,414 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,424 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,430 - INFO - draw_stats_page: 0.0070s
2025-07-19 23:33:29,439 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,445 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:29,457 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,462 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:29,473 - INFO - draw_background: 0.0018s
2025-07-19 23:33:29,479 - INFO - draw_stats_page: 0.0077s
2025-07-19 23:33:29,487 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,494 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:29,495 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,507 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,511 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:29,521 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,528 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:29,537 - INFO - draw_background: 0.0009s
2025-07-19 23:33:29,544 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:29,554 - INFO - draw_background: 0.0010s
2025-07-19 23:33:29,561 - INFO - draw_stats_page: 0.0076s
2025-07-19 23:33:29,571 - INFO - draw_background: 0.0005s
2025-07-19 23:33:29,576 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:29,577 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,588 - INFO - draw_background: 0.0009s
2025-07-19 23:33:29,593 - INFO - draw_stats_page: 0.0067s
2025-07-19 23:33:29,605 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,610 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:29,621 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,627 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:29,637 - INFO - draw_background: 0.0009s
2025-07-19 23:33:29,644 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:29,652 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,659 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:29,659 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,669 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,676 - INFO - draw_stats_page: 0.0067s
2025-07-19 23:33:29,686 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,692 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,703 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,710 - INFO - draw_stats_page: 0.0070s
2025-07-19 23:33:29,718 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,727 - INFO - draw_stats_page: 0.0081s
2025-07-19 23:33:29,735 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,741 - INFO - draw_stats_page: 0.0065s
2025-07-19 23:33:29,742 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,751 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,757 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:29,768 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,775 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,784 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,791 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:29,801 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,807 - INFO - draw_stats_page: 0.0067s
2025-07-19 23:33:29,817 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,821 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:29,824 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,833 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,838 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:29,850 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,854 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:29,866 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,869 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:29,883 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,887 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:29,899 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,903 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:29,904 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,916 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,920 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:29,933 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,937 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:29,949 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,954 - INFO - draw_stats_page: 0.0044s
2025-07-19 23:33:29,964 - INFO - draw_background: 0.0000s
2025-07-19 23:33:29,968 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:29,982 - INFO - draw_background: 0.0005s
2025-07-19 23:33:29,985 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:29,986 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:29,997 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,001 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:30,014 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,017 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,032 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,036 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,049 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,055 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:30,066 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,069 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:30,070 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,081 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,084 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:30,098 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,101 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,114 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,118 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,129 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,133 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:30,146 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,149 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,150 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,162 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,165 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,178 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,182 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:30,196 - INFO - draw_background: 0.0010s
2025-07-19 23:33:30,199 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:30,212 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,216 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,229 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,232 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:30,233 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,244 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,247 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,262 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,266 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:30,278 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,281 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:30,296 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,300 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:30,313 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,316 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,317 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,328 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,331 - INFO - draw_stats_page: 0.0024s
2025-07-19 23:33:30,346 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,350 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,362 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,366 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,380 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,383 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,396 - INFO - draw_background: 0.0009s
2025-07-19 23:33:30,399 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:30,401 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,412 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,415 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,429 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,432 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:30,446 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,449 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,463 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,466 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,478 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,482 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,483 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,495 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,498 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,512 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,515 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,530 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,533 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:30,545 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,549 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,563 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,566 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,567 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,579 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,583 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:30,597 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,600 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,613 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,616 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,630 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,633 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:30,645 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,648 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,649 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,663 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,667 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:30,679 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,683 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:30,696 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,699 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,713 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,717 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:30,728 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,731 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:30,732 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,746 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,748 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,762 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,765 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,780 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,784 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:30,795 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,799 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,812 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,815 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:30,816 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,829 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,833 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:30,845 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,849 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:30,862 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,865 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,880 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,883 - INFO - draw_stats_page: 0.0037s
2025-07-19 23:33:30,896 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,899 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,900 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,912 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,915 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,929 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,932 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,945 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,948 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:30,961 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,965 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:30,978 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,981 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:30,982 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:30,993 - INFO - draw_background: 0.0000s
2025-07-19 23:33:30,996 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,011 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,014 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:31,027 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,030 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:31,042 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,046 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,059 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,063 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:31,064 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,077 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,080 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:31,093 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,096 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,109 - INFO - draw_background: 0.0011s
2025-07-19 23:33:31,113 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:31,126 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,129 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,143 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,147 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,147 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,160 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,162 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:31,177 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,180 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,193 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,197 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,210 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,213 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:31,226 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,229 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,229 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,243 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,246 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,259 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,263 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:31,276 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,280 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:31,293 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,296 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,310 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,313 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:31,313 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,326 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,329 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,343 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,346 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,359 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,362 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:31,376 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,379 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,391 - INFO - draw_background: 0.0005s
2025-07-19 23:33:31,395 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,395 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,406 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,409 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:31,423 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,427 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,440 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,444 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,455 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,459 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,472 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,477 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,478 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,489 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,493 - INFO - draw_stats_page: 0.0047s
2025-07-19 23:33:31,505 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,509 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,521 - INFO - draw_background: 0.0005s
2025-07-19 23:33:31,526 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:31,538 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,542 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:31,554 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,559 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:31,561 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,571 - INFO - draw_background: 0.0015s
2025-07-19 23:33:31,576 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:31,588 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,592 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:31,604 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,608 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:31,619 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,625 - INFO - draw_stats_page: 0.0052s
2025-07-19 23:33:31,636 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,640 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,641 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,653 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,659 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:31,671 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,676 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:31,685 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,689 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,703 - INFO - draw_background: 0.0005s
2025-07-19 23:33:31,708 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:31,719 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,724 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:31,725 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,736 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,741 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:31,752 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,757 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:31,769 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,773 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,786 - INFO - draw_background: 0.0010s
2025-07-19 23:33:31,791 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:31,801 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,805 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:31,807 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,817 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,823 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,832 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,838 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:31,855 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,859 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,865 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,871 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,882 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,888 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:31,889 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,898 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,904 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:31,914 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,918 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:31,931 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,935 - INFO - draw_stats_page: 0.0043s
2025-07-19 23:33:31,947 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,951 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:31,963 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,966 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:31,967 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:31,980 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,984 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:31,996 - INFO - draw_background: 0.0000s
2025-07-19 23:33:31,999 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,014 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,016 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,030 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,033 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,046 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,049 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,051 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,062 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,066 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,079 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,082 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,095 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,097 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,112 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,116 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,129 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,132 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:32,133 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,147 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,150 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,162 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,165 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,179 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,182 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,194 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,197 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,211 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,215 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,216 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,228 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,231 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:32,246 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,249 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,263 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,266 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,279 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,282 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,296 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,300 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,300 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,312 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,316 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,329 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,332 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,345 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,347 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,362 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,365 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,379 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,382 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:32,383 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,396 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,399 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,413 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,417 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,430 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,433 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,445 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,448 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,462 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,467 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,468 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,479 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,482 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:32,496 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,499 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:32,512 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,515 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,529 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,532 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,545 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,548 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,549 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,561 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,565 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,578 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,581 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:32,596 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,600 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,612 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,615 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,629 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,632 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,633 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,644 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,647 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,661 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,664 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,676 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,680 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,692 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,695 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,710 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,713 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,714 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,725 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,729 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,742 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,745 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:32,760 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,762 - INFO - draw_stats_page: 0.0026s
2025-07-19 23:33:32,775 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,779 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,792 - INFO - draw_background: 0.0011s
2025-07-19 23:33:32,795 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:32,796 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,809 - INFO - draw_background: 0.0010s
2025-07-19 23:33:32,811 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,824 - INFO - draw_background: 0.0010s
2025-07-19 23:33:32,829 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:32,841 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,845 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,858 - INFO - draw_background: 0.0010s
2025-07-19 23:33:32,861 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,876 - INFO - draw_background: 0.0010s
2025-07-19 23:33:32,878 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:32,879 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,891 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,896 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:32,909 - INFO - draw_background: 0.0010s
2025-07-19 23:33:32,913 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:32,926 - INFO - draw_background: 0.0009s
2025-07-19 23:33:32,929 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:32,943 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,946 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:32,959 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,962 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,963 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:32,975 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,979 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:32,992 - INFO - draw_background: 0.0000s
2025-07-19 23:33:32,995 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,009 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,013 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:33,026 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,029 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:33,042 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,045 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,046 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,059 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,062 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:33,076 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,079 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,093 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,096 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,108 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,112 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:33,125 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,129 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:33,129 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,142 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,145 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:33,159 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,161 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,176 - INFO - draw_background: 0.0010s
2025-07-19 23:33:33,179 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:33,191 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,196 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:33,208 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,211 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:33,212 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,226 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,229 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,243 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,246 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,259 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,262 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:33,276 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,280 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:33,294 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,297 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:33,298 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,309 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,312 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:33,327 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,330 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,343 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,346 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,360 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,363 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:33,376 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,380 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,381 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,393 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,396 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,408 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,413 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:33,425 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,428 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:33,441 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,445 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,457 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,461 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,461 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,475 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,478 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,491 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,495 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,508 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,511 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,524 - INFO - draw_background: 0.0010s
2025-07-19 23:33:33,527 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:33,541 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,544 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:33,545 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,558 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,561 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:33,576 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,579 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,591 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,595 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:33,608 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,611 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:33,626 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,628 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,629 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,643 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,646 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,660 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,664 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:33,677 - INFO - draw_background: 0.0009s
2025-07-19 23:33:33,680 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,693 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,697 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:33,709 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,713 - INFO - draw_stats_page: 0.0024s
2025-07-19 23:33:33,714 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,724 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,727 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:33,741 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,745 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:33,758 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,762 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:33,775 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,778 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,791 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,795 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,796 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,808 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,811 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:33,826 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,829 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:33,843 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,845 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,860 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,863 - INFO - draw_stats_page: 0.0037s
2025-07-19 23:33:33,875 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,879 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:33,880 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,893 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,896 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,909 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,912 - INFO - draw_stats_page: 0.0037s
2025-07-19 23:33:33,924 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,927 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:33,941 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,945 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:33,958 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,961 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:33,962 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:33,974 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,977 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:33,992 - INFO - draw_background: 0.0000s
2025-07-19 23:33:33,995 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,009 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,013 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,024 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,027 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,041 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,045 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,046 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,058 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,061 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:34,076 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,079 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,091 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,094 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:34,108 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,111 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:34,126 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,129 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,130 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,142 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,145 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,159 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,162 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,175 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,179 - INFO - draw_stats_page: 0.0033s
2025-07-19 23:33:34,192 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,195 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,210 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,213 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,214 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,226 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,229 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,244 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,247 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,261 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,265 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,278 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,282 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,296 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,299 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:34,301 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,311 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,315 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,328 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,331 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,344 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,347 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,361 - INFO - draw_background: 0.0005s
2025-07-19 23:33:34,364 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,378 - INFO - draw_background: 0.0005s
2025-07-19 23:33:34,381 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,383 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,394 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,397 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,411 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,415 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,429 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,433 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:34,445 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,449 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:34,461 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,465 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,465 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,478 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,481 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,493 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,496 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,510 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,513 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,525 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,528 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,542 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,545 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,546 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,559 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,562 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:34,574 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,578 - INFO - draw_stats_page: 0.0034s
2025-07-19 23:33:34,591 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,594 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:34,606 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,609 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:34,622 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,625 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,626 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,640 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,642 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,655 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,659 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,672 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,675 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,689 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,692 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:34,707 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,710 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,711 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,722 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,726 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,738 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,743 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,755 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,759 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,773 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,776 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:34,789 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,792 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:34,793 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,806 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,809 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,821 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,826 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:34,839 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,841 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,855 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,858 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,871 - INFO - draw_background: 0.0006s
2025-07-19 23:33:34,875 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:34,875 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,888 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,891 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,905 - INFO - draw_background: 0.0010s
2025-07-19 23:33:34,908 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:34,921 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,926 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:34,937 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,942 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:34,954 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,959 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:34,959 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:34,971 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,975 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:34,987 - INFO - draw_background: 0.0000s
2025-07-19 23:33:34,993 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:35,004 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,009 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,019 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,025 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:35,037 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,041 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,043 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,053 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,058 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,069 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,075 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:35,086 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,093 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:35,103 - INFO - draw_background: 0.0012s
2025-07-19 23:33:35,106 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:35,120 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,124 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:35,124 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,136 - INFO - draw_background: 0.0011s
2025-07-19 23:33:35,142 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:35,153 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,157 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,170 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,173 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:35,186 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,191 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,203 - INFO - draw_background: 0.0016s
2025-07-19 23:33:35,207 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:35,208 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,219 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,224 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:35,234 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,239 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:35,251 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,256 - INFO - draw_stats_page: 0.0062s
2025-07-19 23:33:35,267 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,273 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:35,283 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,287 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,289 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,301 - INFO - draw_background: 0.0011s
2025-07-19 23:33:35,305 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,316 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,321 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,334 - INFO - draw_background: 0.0021s
2025-07-19 23:33:35,338 - INFO - draw_stats_page: 0.0061s
2025-07-19 23:33:35,351 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,356 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,368 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,372 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,399 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,401 - INFO - draw_background: 0.0006s
2025-07-19 23:33:35,405 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,419 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,423 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:35,435 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,439 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,451 - INFO - draw_background: 0.0005s
2025-07-19 23:33:35,455 - INFO - draw_stats_page: 0.0046s
2025-07-19 23:33:35,468 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,472 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:35,472 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,485 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,489 - INFO - draw_stats_page: 0.0050s
2025-07-19 23:33:35,501 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,505 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,517 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,521 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,534 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,538 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,551 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,555 - INFO - draw_stats_page: 0.0067s
2025-07-19 23:33:35,556 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,567 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,571 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,585 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,589 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,600 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,604 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,618 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,622 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,635 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,640 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,640 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,651 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,656 - INFO - draw_stats_page: 0.0072s
2025-07-19 23:33:35,665 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,671 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:35,683 - INFO - draw_background: 0.0011s
2025-07-19 23:33:35,688 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,701 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,706 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:35,717 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,721 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,722 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,734 - INFO - draw_background: 0.0020s
2025-07-19 23:33:35,738 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,751 - INFO - draw_background: 0.0020s
2025-07-19 23:33:35,755 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:35,765 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,771 - INFO - draw_stats_page: 0.0066s
2025-07-19 23:33:35,781 - INFO - draw_background: 0.0005s
2025-07-19 23:33:35,788 - INFO - draw_stats_page: 0.0071s
2025-07-19 23:33:35,797 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,803 - INFO - draw_stats_page: 0.0067s
2025-07-19 23:33:35,804 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,812 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,818 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,829 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,834 - INFO - draw_stats_page: 0.0055s
2025-07-19 23:33:35,845 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,851 - INFO - draw_stats_page: 0.0060s
2025-07-19 23:33:35,861 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,865 - INFO - draw_stats_page: 0.0045s
2025-07-19 23:33:35,877 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,882 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:35,883 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,894 - INFO - draw_background: 0.0010s
2025-07-19 23:33:35,897 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:35,909 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,912 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:35,925 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,928 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:35,942 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,945 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:35,959 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,962 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:35,963 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:35,975 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,977 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:35,991 - INFO - draw_background: 0.0000s
2025-07-19 23:33:35,994 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:36,006 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,009 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,023 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,027 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:36,039 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,045 - INFO - draw_stats_page: 0.0051s
2025-07-19 23:33:36,046 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,056 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,059 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,073 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,077 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,090 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,093 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,106 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,109 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,123 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,126 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,127 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,139 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,142 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,155 - INFO - draw_background: 0.0009s
2025-07-19 23:33:36,158 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:36,171 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,175 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:36,188 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,193 - INFO - draw_stats_page: 0.0056s
2025-07-19 23:33:36,203 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,207 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:36,208 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,220 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,224 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:36,236 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,239 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,252 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,257 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:36,269 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,273 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:36,285 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,289 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,289 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,302 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,305 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,320 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,323 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,336 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,339 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,353 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,356 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,371 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,374 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:36,375 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,388 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,391 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:36,405 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,409 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:33:36,423 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,426 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,440 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,443 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:36,456 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,460 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:36,461 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,472 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,475 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,489 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,492 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:33:36,504 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,507 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,521 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,525 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,537 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,541 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,541 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,554 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,557 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,572 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,575 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,588 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,591 - INFO - draw_stats_page: 0.0025s
2025-07-19 23:33:36,605 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,609 - INFO - draw_stats_page: 0.0039s
2025-07-19 23:33:36,623 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,626 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,627 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,639 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,644 - INFO - draw_stats_page: 0.0041s
2025-07-19 23:33:36,656 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,659 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,673 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,676 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:33:36,690 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,694 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:33:36,707 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,710 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,711 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,722 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,725 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,739 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,742 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:36,756 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,759 - INFO - draw_stats_page: 0.0029s
2025-07-19 23:33:36,774 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,777 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,789 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,793 - INFO - draw_stats_page: 0.0031s
2025-07-19 23:33:36,794 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:33:36,805 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,808 - INFO - draw_stats_page: 0.0030s
2025-07-19 23:33:36,823 - INFO - draw_background: 0.0000s
2025-07-19 23:33:36,826 - INFO - draw_stats_page: 0.0035s
2025-07-19 23:34:10,871 - INFO - Database security initialized successfully
2025-07-19 23:34:10,872 - INFO - DB Operation: {"timestamp": "2025-07-19 23:34:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:34:10,872 - INFO - Created secure database connection
2025-07-19 23:34:10,874 - INFO - Database schema initialized successfully
2025-07-19 23:34:10,875 - INFO - DB Operation: {"timestamp": "2025-07-19 23:34:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:34:10,876 - INFO - DB Operation: {"timestamp": "2025-07-19 23:34:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:34:10,876 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:34:10,942 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:34:10,942 - INFO - Stats cache initialized
2025-07-19 23:34:10,942 - INFO - Stats preloader initialized
2025-07-19 23:34:10,944 - INFO - Starting stats data preloading
2025-07-19 23:34:10,944 - INFO - Loading data using optimized functions
2025-07-19 23:34:10,944 - INFO - Started stats data preloading
2025-07-19 23:34:12,109 - INFO - Loaded 9 items from cache
2025-07-19 23:34:12,110 - INFO - Started background data loading
2025-07-19 23:34:12,110 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:34:12,110 - INFO - Using optimized stats loader for integration
2025-07-19 23:34:12,111 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:34:12,113 - INFO - Loaded summary data
2025-07-19 23:34:12,113 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:34:12,114 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:34:12,117 - INFO - Saved 9 items to cache
2025-07-19 23:34:12,117 - INFO - Background data loading completed
2025-07-19 23:34:12,120 - INFO - Database schema initialized successfully
2025-07-19 23:34:12,126 - INFO - Database schema initialized successfully
2025-07-19 23:34:12,127 - INFO - Stats database initialized successfully
2025-07-19 23:34:12,128 - INFO - Game stats integration module available
2025-07-19 23:34:12,129 - INFO - Started stats event worker thread
2025-07-19 23:34:12,129 - INFO - Stats event hooks initialized
2025-07-19 23:34:15,953 - INFO - DB Operation: {"timestamp": "2025-07-19 23:34:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:34:15,956 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:34:15,957 - INFO - Preloaded summary data
2025-07-19 23:34:15,957 - INFO - Preloaded game history (10 records)
2025-07-19 23:34:15,957 - INFO - Preloaded wallet data
2025-07-19 23:34:15,958 - INFO - Stats data preloaded successfully in 5.01 seconds
2025-07-19 23:34:15,959 - INFO - Saved 9 items to persistent cache
2025-07-19 23:34:16,946 - ERROR - Failed to connect to RethinkDB: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-07-19 23:34:16,947 - INFO - Sync thread started
2025-07-19 23:34:16,947 - INFO - Sync manager initialized (RethinkDB available: True)
2025-07-19 23:34:16,947 - INFO - Hybrid DB initialized in offline mode (RethinkDB not available)
2025-07-19 23:34:16,948 - INFO - Background thread started
2025-07-19 23:34:16,948 - INFO - Hybrid database integration initialized
2025-07-19 23:34:16,948 - WARNING - Failed to connect to RethinkDB
2025-07-19 23:34:16,948 - ERROR - Error getting stats summary: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 9508 and this is thread id 6712.
2025-07-19 23:47:34,831 - INFO - Loaded 0 items from persistent cache
2025-07-19 23:47:34,832 - INFO - Stats cache initialized
2025-07-19 23:47:34,832 - INFO - Database security initialized successfully
2025-07-19 23:47:34,833 - INFO - DB Operation: {"timestamp": "2025-07-19 23:47:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:47:34,833 - INFO - Created secure database connection
2025-07-19 23:47:34,898 - INFO - Database schema initialized successfully
2025-07-19 23:47:34,900 - INFO - DB Operation: {"timestamp": "2025-07-19 23:47:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:47:34,900 - INFO - DB Operation: {"timestamp": "2025-07-19 23:47:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:47:34,900 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:47:34,900 - INFO - Stats preloader initialized
2025-07-19 23:47:34,901 - INFO - Starting stats data preloading
2025-07-19 23:47:34,901 - INFO - Started stats data preloading
2025-07-19 23:47:34,902 - INFO - Loading data using optimized functions
2025-07-19 23:47:34,903 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:47:34,903 - INFO - DB Operation: {"timestamp": "2025-07-19 23:47:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:47:34,911 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:47:34,911 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:47:34,911 - INFO - DB Operation: {"timestamp": "2025-07-19 23:47:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:47:34,912 - INFO - Preloaded summary data
2025-07-19 23:47:34,913 - INFO - Preloaded game history (10 records)
2025-07-19 23:47:34,914 - INFO - Preloaded wallet data
2025-07-19 23:47:34,914 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:47:34,915 - INFO - Loaded 0 items from cache
2025-07-19 23:47:34,916 - INFO - Started background data loading
2025-07-19 23:47:34,916 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:47:34,916 - INFO - Using optimized stats loader for integration
2025-07-19 23:47:34,917 - INFO - Saved 8 items to persistent cache
2025-07-19 23:47:34,917 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:47:34,918 - INFO - Loaded summary data
2025-07-19 23:47:34,919 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:47:34,919 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:47:34,920 - INFO - Saved 7 items to cache
2025-07-19 23:47:34,921 - INFO - Background data loading completed
2025-07-19 23:47:34,925 - INFO - Database schema initialized successfully
2025-07-19 23:47:34,927 - INFO - Database schema initialized successfully
2025-07-19 23:47:34,928 - INFO - Stats database initialized successfully
2025-07-19 23:47:34,928 - INFO - Game stats integration module available
2025-07-19 23:47:34,928 - INFO - Started stats event worker thread
2025-07-19 23:47:34,928 - INFO - Stats event hooks initialized
2025-07-19 23:47:35,521 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:47:35,522 - INFO - Stats performance monitor initialized
2025-07-19 23:47:35,580 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:47:36,245 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:48:37,463 - INFO - Loaded 7 items from persistent cache
2025-07-19 23:48:37,463 - INFO - Stats cache initialized
2025-07-19 23:48:37,464 - INFO - Database security initialized successfully
2025-07-19 23:48:37,464 - INFO - DB Operation: {"timestamp": "2025-07-19 23:48:37", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:48:37,465 - INFO - Created secure database connection
2025-07-19 23:48:37,478 - INFO - Database schema initialized successfully
2025-07-19 23:48:37,479 - INFO - DB Operation: {"timestamp": "2025-07-19 23:48:37", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:48:37,479 - INFO - DB Operation: {"timestamp": "2025-07-19 23:48:37", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:48:37,480 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:48:37,480 - INFO - Stats preloader initialized
2025-07-19 23:48:37,481 - INFO - Starting stats data preloading
2025-07-19 23:48:37,481 - INFO - Started stats data preloading
2025-07-19 23:48:37,481 - INFO - Loading data using optimized functions
2025-07-19 23:48:37,482 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:48:37,482 - INFO - DB Operation: {"timestamp": "2025-07-19 23:48:37", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:48:37,491 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:48:37,491 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:48:37,491 - INFO - DB Operation: {"timestamp": "2025-07-19 23:48:37", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:48:37,497 - INFO - Loaded 7 items from cache
2025-07-19 23:48:37,497 - INFO - Preloaded summary data
2025-07-19 23:48:37,497 - INFO - Started background data loading
2025-07-19 23:48:37,498 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:48:37,498 - INFO - Using optimized stats loader for integration
2025-07-19 23:48:37,499 - INFO - Preloaded game history (10 records)
2025-07-19 23:48:37,499 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:48:37,500 - INFO - Preloaded wallet data
2025-07-19 23:48:37,500 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-19 23:48:37,500 - INFO - Loaded summary data
2025-07-19 23:48:37,502 - INFO - Saved 9 items to persistent cache
2025-07-19 23:48:37,504 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:48:37,504 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:48:37,506 - INFO - Saved 7 items to cache
2025-07-19 23:48:37,507 - INFO - Background data loading completed
2025-07-19 23:48:37,508 - INFO - Database schema initialized successfully
2025-07-19 23:48:37,511 - INFO - Database schema initialized successfully
2025-07-19 23:48:37,512 - INFO - Stats database initialized successfully
2025-07-19 23:48:37,512 - INFO - Game stats integration module available
2025-07-19 23:48:37,513 - INFO - Started stats event worker thread
2025-07-19 23:48:37,513 - INFO - Stats event hooks initialized
2025-07-19 23:48:38,115 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:48:38,115 - INFO - Stats performance monitor initialized
2025-07-19 23:48:38,172 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:48:38,723 - INFO - Starting migration from JSON: data\stats.json
2025-07-19 23:48:38,723 - INFO - Loaded time reference (startup #10)
2025-07-19 23:48:38,725 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-19 23:48:38,726 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 8492 and this is thread id 9968.
2025-07-19 23:48:38,726 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 8492 and this is thread id 9968.
2025-07-19 23:48:38,727 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 8492 and this is thread id 9968.
2025-07-19 23:48:38,727 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 8492 and this is thread id 9968.
2025-07-19 23:48:38,799 - INFO - draw_stats_page: 0.0627s
2025-07-19 23:49:36,850 - INFO - Loaded 7 items from persistent cache
2025-07-19 23:49:36,850 - INFO - Stats cache initialized
2025-07-19 23:49:36,851 - INFO - Database security initialized successfully
2025-07-19 23:49:36,854 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:36,855 - INFO - Created secure database connection
2025-07-19 23:49:36,881 - INFO - Database schema initialized successfully
2025-07-19 23:49:36,882 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:36,883 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:36,884 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:49:36,884 - INFO - Stats preloader initialized
2025-07-19 23:49:36,888 - INFO - Starting stats data preloading
2025-07-19 23:49:36,889 - INFO - Started stats data preloading
2025-07-19 23:49:36,889 - INFO - Loading data using optimized functions
2025-07-19 23:49:36,890 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:49:36,894 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:36,898 - INFO - Loaded 7 items from cache
2025-07-19 23:49:36,947 - INFO - Started background data loading
2025-07-19 23:49:36,948 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:49:36,948 - INFO - Using optimized stats loader for integration
2025-07-19 23:49:36,949 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:49:36,950 - INFO - Loaded summary data
2025-07-19 23:49:36,954 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:49:36,956 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:49:36,956 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:36", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:36,958 - INFO - Preloaded summary data
2025-07-19 23:49:36,959 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:49:36,959 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:49:36,961 - INFO - Saved 7 items to cache
2025-07-19 23:49:36,962 - INFO - Background data loading completed
2025-07-19 23:49:36,962 - INFO - Preloaded game history (10 records)
2025-07-19 23:49:36,963 - INFO - Preloaded wallet data
2025-07-19 23:49:36,963 - INFO - Stats data preloaded successfully in 0.08 seconds
2025-07-19 23:49:36,966 - INFO - Saved 9 items to persistent cache
2025-07-19 23:49:36,967 - INFO - Database schema initialized successfully
2025-07-19 23:49:36,976 - INFO - Database schema initialized successfully
2025-07-19 23:49:36,977 - INFO - Stats database initialized successfully
2025-07-19 23:49:36,977 - INFO - Game stats integration module available
2025-07-19 23:49:36,978 - INFO - Started stats event worker thread
2025-07-19 23:49:36,978 - INFO - Stats event hooks initialized
2025-07-19 23:49:38,219 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:49:38,219 - INFO - Stats performance monitor initialized
2025-07-19 23:49:38,337 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:49:39,558 - INFO - Starting migration from JSON: data\stats.json
2025-07-19 23:49:39,575 - INFO - Loaded time reference (startup #11)
2025-07-19 23:49:39,575 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-19 23:49:39,576 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 7744 and this is thread id 4144.
2025-07-19 23:49:39,576 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 7744 and this is thread id 4144.
2025-07-19 23:49:39,576 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 7744 and this is thread id 4144.
2025-07-19 23:49:39,576 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 7744 and this is thread id 4144.
2025-07-19 23:49:39,594 - INFO - draw_stats_page: 0.0254s
2025-07-19 23:49:39,710 - INFO - draw_stats_page: 0.0036s
2025-07-19 23:49:39,848 - INFO - draw_stats_page: 0.0075s
2025-07-19 23:49:40,011 - INFO - draw_stats_page: 0.0389s
2025-07-19 23:49:40,108 - INFO - draw_stats_page: 0.0015s
2025-07-19 23:49:40,240 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:40,374 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:40,508 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:40,640 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:40,773 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:40,774 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:40,908 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:41,041 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,172 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,306 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,438 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,570 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,703 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,836 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:41,968 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:42,102 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:42,103 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:42,236 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:42,367 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:42,500 - INFO - draw_stats_page: 0.0017s
2025-07-19 23:49:42,632 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:42,767 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:42,900 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:43,035 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:49:43,166 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:43,298 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:43,433 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:43,434 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:43,566 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:43,700 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:43,833 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:43,965 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:44,098 - INFO - draw_stats_page: 0.0015s
2025-07-19 23:49:44,231 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:44,364 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:44,498 - INFO - draw_stats_page: 0.0017s
2025-07-19 23:49:44,576 - ERROR - Error getting connection from pool: 
2025-07-19 23:49:44,576 - WARNING - Falling back to direct connection
2025-07-19 23:49:44,578 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:44", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:44,580 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:44", "operation": "UPDATE", "details": {"table": "daily_stats", "record_id": null, "details": {"date": "2025-07-15", "games_played": 0, "earnings": 0, "winners": 0, "total_players": 9}}}
2025-07-19 23:49:44,635 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:44,763 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:44,764 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:44,896 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:45,030 - INFO - draw_stats_page: 0.0016s
2025-07-19 23:49:45,164 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:45,298 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:45,431 - INFO - draw_stats_page: 0.0009s
2025-07-19 23:49:45,564 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:45,696 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:45,829 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:45,963 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:46,097 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:46,099 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:46,229 - INFO - draw_stats_page: 0.0011s
2025-07-19 23:49:46,364 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:46,498 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:46,631 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:46,763 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:46,896 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:47,031 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:47,163 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:47,294 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:47,428 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:47,428 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:47,560 - INFO - draw_stats_page: 0.0005s
2025-07-19 23:49:47,706 - INFO - draw_stats_page: 0.0156s
2025-07-19 23:49:47,824 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:47,956 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:48,091 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:48,225 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:49:48,358 - INFO - draw_stats_page: 0.0009s
2025-07-19 23:49:48,491 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:49:48,624 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:48,755 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:48,756 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:48,918 - INFO - draw_stats_page: 0.0277s
2025-07-19 23:49:49,023 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:49,155 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:49,288 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:49,426 - INFO - draw_stats_page: 0.0040s
2025-07-19 23:49:49,557 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:49,582 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:49,676 - ERROR - Error getting connection from pool: 
2025-07-19 23:49:49,676 - WARNING - Falling back to direct connection
2025-07-19 23:49:49,676 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:49,690 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:49", "operation": "UPDATE", "details": {"table": "daily_stats", "record_id": null, "details": {"date": "2025-07-15", "games_played": 0, "earnings": 0, "winners": 0, "total_players": 9}}}
2025-07-19 23:49:49,693 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:49:49,693 - INFO - draw_stats_page: 0.0020s
2025-07-19 23:49:49,693 - INFO - DB Operation: {"timestamp": "2025-07-19 23:49:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:49:49,816 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5380 and this is thread id 4144.
2025-07-19 23:49:49,816 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5380 and this is thread id 4144.
2025-07-19 23:49:49,823 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:49,958 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:50,092 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:50,094 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:50,226 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:50,357 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:50,490 - INFO - draw_stats_page: 0.0011s
2025-07-19 23:49:50,623 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:50,757 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:50,890 - INFO - draw_stats_page: 0.0005s
2025-07-19 23:49:51,023 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:51,156 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:51,288 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:51,421 - INFO - draw_stats_page: 0.0009s
2025-07-19 23:49:51,423 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:49:51,555 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:51,688 - INFO - draw_stats_page: 0.0009s
2025-07-19 23:49:51,822 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:51,955 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:49:52,089 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:52,223 - INFO - draw_stats_page: 0.0011s
2025-07-19 23:49:52,355 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:52,487 - INFO - draw_stats_page: 0.0000s
2025-07-19 23:49:52,621 - INFO - draw_stats_page: 0.0010s
2025-07-19 23:50:59,967 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:50:59,967 - INFO - Stats cache initialized
2025-07-19 23:50:59,967 - INFO - Database security initialized successfully
2025-07-19 23:50:59,968 - INFO - DB Operation: {"timestamp": "2025-07-19 23:50:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:50:59,968 - INFO - Created secure database connection
2025-07-19 23:51:00,298 - INFO - Database schema initialized successfully
2025-07-19 23:51:00,300 - INFO - DB Operation: {"timestamp": "2025-07-19 23:51:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:51:00,301 - INFO - DB Operation: {"timestamp": "2025-07-19 23:51:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:51:00,301 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:51:00,301 - INFO - Stats preloader initialized
2025-07-19 23:51:00,302 - INFO - Starting stats data preloading
2025-07-19 23:51:00,302 - INFO - Started stats data preloading
2025-07-19 23:51:00,302 - INFO - Loading data using optimized functions
2025-07-19 23:51:00,303 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:51:00,305 - INFO - DB Operation: {"timestamp": "2025-07-19 23:51:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:51:00,310 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:51:00,310 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:51:00,311 - INFO - DB Operation: {"timestamp": "2025-07-19 23:51:00", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:51:00,313 - INFO - Preloaded summary data
2025-07-19 23:51:00,314 - INFO - Loaded 9 items from cache
2025-07-19 23:51:00,314 - INFO - Preloaded game history (10 records)
2025-07-19 23:51:00,314 - INFO - Started background data loading
2025-07-19 23:51:00,315 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:51:00,315 - INFO - Using optimized stats loader for integration
2025-07-19 23:51:00,315 - INFO - Preloaded wallet data
2025-07-19 23:51:00,315 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:51:00,316 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:51:00,318 - INFO - Saved 9 items to persistent cache
2025-07-19 23:51:00,318 - INFO - Loaded summary data
2025-07-19 23:51:00,319 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:51:00,319 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:51:00,323 - INFO - Saved 9 items to cache
2025-07-19 23:51:00,324 - INFO - Background data loading completed
2025-07-19 23:51:00,326 - INFO - Database schema initialized successfully
2025-07-19 23:51:00,328 - INFO - Database schema initialized successfully
2025-07-19 23:51:00,328 - INFO - Stats database initialized successfully
2025-07-19 23:51:00,328 - INFO - Game stats integration module available
2025-07-19 23:51:00,329 - INFO - Started stats event worker thread
2025-07-19 23:51:00,329 - INFO - Stats event hooks initialized
2025-07-19 23:51:00,948 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:51:00,948 - INFO - Stats performance monitor initialized
2025-07-19 23:51:01,004 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:51:01,574 - INFO - Starting migration from JSON: data\stats.json
2025-07-19 23:51:01,584 - INFO - Loaded time reference (startup #12)
2025-07-19 23:51:01,585 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-19 23:51:01,585 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 14084 and this is thread id 6796.
2025-07-19 23:51:01,585 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 14084 and this is thread id 6796.
2025-07-19 23:51:01,585 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 14084 and this is thread id 6796.
2025-07-19 23:51:01,585 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 14084 and this is thread id 6796.
2025-07-19 23:51:01,616 - INFO - draw_stats_page: 0.0350s
2025-07-19 23:51:01,730 - INFO - draw_stats_page: 0.0100s
2025-07-19 23:51:01,865 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:01,998 - INFO - draw_stats_page: 0.0106s
2025-07-19 23:51:02,132 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:51:02,265 - INFO - draw_stats_page: 0.0126s
2025-07-19 23:51:02,395 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:02,529 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:02,661 - INFO - draw_stats_page: 0.0090s
2025-07-19 23:51:02,796 - INFO - draw_stats_page: 0.0106s
2025-07-19 23:51:02,797 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:51:02,928 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:03,062 - INFO - draw_stats_page: 0.0091s
2025-07-19 23:51:03,196 - INFO - draw_stats_page: 0.0107s
2025-07-19 23:51:03,329 - INFO - draw_stats_page: 0.0100s
2025-07-19 23:51:03,461 - INFO - draw_stats_page: 0.0091s
2025-07-19 23:51:03,594 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:03,729 - INFO - draw_stats_page: 0.0105s
2025-07-19 23:51:03,861 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:03,994 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:04,126 - INFO - draw_stats_page: 0.0110s
2025-07-19 23:51:04,129 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:51:04,262 - INFO - draw_stats_page: 0.0131s
2025-07-19 23:51:04,393 - INFO - draw_stats_page: 0.0114s
2025-07-19 23:51:04,530 - INFO - draw_stats_page: 0.0136s
2025-07-19 23:51:04,661 - INFO - draw_stats_page: 0.0111s
2025-07-19 23:51:04,793 - INFO - draw_stats_page: 0.0101s
2025-07-19 23:51:04,919 - INFO - draw_stats_page: 0.0011s
2025-07-19 23:51:05,146 - INFO - draw_stats_page: 0.0005s
2025-07-19 23:55:29,210 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:55:29,210 - INFO - Stats cache initialized
2025-07-19 23:55:29,210 - INFO - Database security initialized successfully
2025-07-19 23:55:29,211 - INFO - DB Operation: {"timestamp": "2025-07-19 23:55:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:55:29,211 - INFO - Created secure database connection
2025-07-19 23:55:29,349 - INFO - Database schema initialized successfully
2025-07-19 23:55:29,351 - INFO - DB Operation: {"timestamp": "2025-07-19 23:55:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:55:29,351 - INFO - DB Operation: {"timestamp": "2025-07-19 23:55:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:55:29,352 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:55:29,352 - INFO - Stats preloader initialized
2025-07-19 23:55:29,359 - INFO - Starting stats data preloading
2025-07-19 23:55:29,359 - INFO - Started stats data preloading
2025-07-19 23:55:29,360 - INFO - Loading data using optimized functions
2025-07-19 23:55:29,360 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:55:29,360 - INFO - DB Operation: {"timestamp": "2025-07-19 23:55:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:55:29,366 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:55:29,368 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:55:29,368 - INFO - DB Operation: {"timestamp": "2025-07-19 23:55:29", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:55:29,370 - INFO - Preloaded summary data
2025-07-19 23:55:29,371 - INFO - Preloaded game history (10 records)
2025-07-19 23:55:29,371 - INFO - Preloaded wallet data
2025-07-19 23:55:29,371 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:55:29,374 - INFO - Saved 9 items to persistent cache
2025-07-19 23:55:29,392 - INFO - Loaded 9 items from cache
2025-07-19 23:55:29,427 - INFO - Started background data loading
2025-07-19 23:55:29,427 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:55:29,427 - INFO - Using optimized stats loader for integration
2025-07-19 23:55:29,429 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:55:29,430 - INFO - Loaded summary data
2025-07-19 23:55:29,430 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:55:29,430 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:55:29,433 - INFO - Saved 9 items to cache
2025-07-19 23:55:29,433 - INFO - Background data loading completed
2025-07-19 23:55:29,533 - INFO - Database schema initialized successfully
2025-07-19 23:55:29,569 - INFO - Database schema initialized successfully
2025-07-19 23:55:29,571 - INFO - Stats database initialized successfully
2025-07-19 23:55:29,571 - INFO - Game stats integration module available
2025-07-19 23:55:29,572 - INFO - Started stats event worker thread
2025-07-19 23:55:29,572 - INFO - Stats event hooks initialized
2025-07-19 23:55:30,470 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:55:30,470 - INFO - Stats performance monitor initialized
2025-07-19 23:55:30,541 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:55:31,128 - INFO - Starting migration from JSON: data\stats.json
2025-07-19 23:55:31,129 - INFO - Loaded time reference (startup #14)
2025-07-19 23:55:31,131 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-19 23:55:31,131 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11336 and this is thread id 10420.
2025-07-19 23:55:31,131 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11336 and this is thread id 10420.
2025-07-19 23:55:31,132 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11336 and this is thread id 10420.
2025-07-19 23:55:31,132 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11336 and this is thread id 10420.
2025-07-19 23:55:31,162 - INFO - draw_stats_page: 0.0349s
2025-07-19 23:55:31,281 - INFO - draw_stats_page: 0.0131s
2025-07-19 23:55:31,413 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:31,546 - INFO - draw_stats_page: 0.0116s
2025-07-19 23:55:31,681 - INFO - draw_stats_page: 0.0128s
2025-07-19 23:55:31,813 - INFO - draw_stats_page: 0.0129s
2025-07-19 23:55:31,946 - INFO - draw_stats_page: 0.0116s
2025-07-19 23:55:32,081 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:32,216 - INFO - draw_stats_page: 0.0141s
2025-07-19 23:55:32,346 - INFO - draw_stats_page: 0.0120s
2025-07-19 23:55:32,348 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:55:32,479 - INFO - draw_stats_page: 0.0126s
2025-07-19 23:55:32,611 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:32,745 - INFO - draw_stats_page: 0.0136s
2025-07-19 23:55:32,877 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:33,011 - INFO - draw_stats_page: 0.0123s
2025-07-19 23:55:33,144 - INFO - draw_stats_page: 0.0131s
2025-07-19 23:55:33,277 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:33,410 - INFO - draw_stats_page: 0.0121s
2025-07-19 23:55:33,542 - INFO - draw_stats_page: 0.0119s
2025-07-19 23:55:33,673 - INFO - draw_stats_page: 0.0081s
2025-07-19 23:55:33,674 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-19 23:56:58,325 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:56:58,325 - INFO - Stats cache initialized
2025-07-19 23:56:58,326 - INFO - Database security initialized successfully
2025-07-19 23:56:58,326 - INFO - DB Operation: {"timestamp": "2025-07-19 23:56:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:56:58,326 - INFO - Created secure database connection
2025-07-19 23:56:58,337 - INFO - Database schema initialized successfully
2025-07-19 23:56:58,337 - INFO - DB Operation: {"timestamp": "2025-07-19 23:56:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:56:58,338 - INFO - DB Operation: {"timestamp": "2025-07-19 23:56:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:56:58,338 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:56:58,338 - INFO - Stats preloader initialized
2025-07-19 23:56:58,339 - INFO - Starting stats data preloading
2025-07-19 23:56:58,340 - INFO - Started stats data preloading
2025-07-19 23:56:58,340 - INFO - Loading data using optimized functions
2025-07-19 23:56:58,340 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:56:58,341 - INFO - DB Operation: {"timestamp": "2025-07-19 23:56:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:56:58,351 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:56:58,352 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:56:58,352 - INFO - DB Operation: {"timestamp": "2025-07-19 23:56:58", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:56:58,353 - INFO - Loaded 9 items from cache
2025-07-19 23:56:58,354 - INFO - Preloaded summary data
2025-07-19 23:56:58,355 - INFO - Preloaded game history (10 records)
2025-07-19 23:56:58,356 - INFO - Started background data loading
2025-07-19 23:56:58,356 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:56:58,357 - INFO - Using optimized stats loader for integration
2025-07-19 23:56:58,357 - INFO - Preloaded wallet data
2025-07-19 23:56:58,357 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-19 23:56:58,361 - INFO - Saved 9 items to persistent cache
2025-07-19 23:56:58,361 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:56:58,362 - INFO - Loaded summary data
2025-07-19 23:56:58,363 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:56:58,363 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:56:58,365 - INFO - Saved 9 items to cache
2025-07-19 23:56:58,365 - INFO - Background data loading completed
2025-07-19 23:56:58,368 - INFO - Database schema initialized successfully
2025-07-19 23:56:58,371 - INFO - Database schema initialized successfully
2025-07-19 23:56:58,373 - INFO - Stats database initialized successfully
2025-07-19 23:56:58,374 - INFO - Game stats integration module available
2025-07-19 23:56:58,375 - INFO - Started stats event worker thread
2025-07-19 23:56:58,375 - INFO - Stats event hooks initialized
2025-07-19 23:56:58,945 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:56:58,945 - INFO - Stats performance monitor initialized
2025-07-19 23:56:58,999 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:56:59,680 - INFO - draw_stats_page: 0.0172s
2025-07-19 23:58:11,292 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:58:11,292 - INFO - Stats cache initialized
2025-07-19 23:58:11,293 - INFO - Database security initialized successfully
2025-07-19 23:58:11,294 - INFO - DB Operation: {"timestamp": "2025-07-19 23:58:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:58:11,294 - INFO - Created secure database connection
2025-07-19 23:58:11,306 - INFO - Database schema initialized successfully
2025-07-19 23:58:11,306 - INFO - DB Operation: {"timestamp": "2025-07-19 23:58:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:58:11,307 - INFO - DB Operation: {"timestamp": "2025-07-19 23:58:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:58:11,307 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:58:11,307 - INFO - Stats preloader initialized
2025-07-19 23:58:11,308 - INFO - Starting stats data preloading
2025-07-19 23:58:11,311 - INFO - Started stats data preloading
2025-07-19 23:58:11,312 - INFO - Loading data using optimized functions
2025-07-19 23:58:11,312 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:58:11,313 - INFO - DB Operation: {"timestamp": "2025-07-19 23:58:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:58:11,320 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:58:11,320 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:58:11,321 - INFO - DB Operation: {"timestamp": "2025-07-19 23:58:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:58:11,323 - INFO - Preloaded summary data
2025-07-19 23:58:11,323 - INFO - Loaded 9 items from cache
2025-07-19 23:58:11,324 - INFO - Preloaded game history (10 records)
2025-07-19 23:58:11,324 - INFO - Started background data loading
2025-07-19 23:58:11,324 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:58:11,325 - INFO - Using optimized stats loader for integration
2025-07-19 23:58:11,325 - INFO - Preloaded wallet data
2025-07-19 23:58:11,325 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-19 23:58:11,327 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:58:11,327 - INFO - Saved 9 items to persistent cache
2025-07-19 23:58:11,328 - INFO - Loaded summary data
2025-07-19 23:58:11,329 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:58:11,329 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:58:11,331 - INFO - Saved 9 items to cache
2025-07-19 23:58:11,332 - INFO - Background data loading completed
2025-07-19 23:58:11,335 - INFO - Database schema initialized successfully
2025-07-19 23:58:11,338 - INFO - Database schema initialized successfully
2025-07-19 23:58:11,338 - INFO - Stats database initialized successfully
2025-07-19 23:58:11,339 - INFO - Game stats integration module available
2025-07-19 23:58:11,339 - INFO - Started stats event worker thread
2025-07-19 23:58:11,339 - INFO - Stats event hooks initialized
2025-07-19 23:58:11,895 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:58:11,895 - INFO - Stats performance monitor initialized
2025-07-19 23:58:11,949 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:58:12,524 - INFO - draw_stats_page: 0.0333s
2025-07-19 23:59:34,859 - INFO - Loaded 9 items from persistent cache
2025-07-19 23:59:34,860 - INFO - Stats cache initialized
2025-07-19 23:59:34,860 - INFO - Database security initialized successfully
2025-07-19 23:59:34,861 - INFO - DB Operation: {"timestamp": "2025-07-19 23:59:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:59:34,861 - INFO - Created secure database connection
2025-07-19 23:59:34,875 - INFO - Database schema initialized successfully
2025-07-19 23:59:34,875 - INFO - DB Operation: {"timestamp": "2025-07-19 23:59:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:59:34,875 - INFO - DB Operation: {"timestamp": "2025-07-19 23:59:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:59:34,876 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-19 23:59:34,876 - INFO - Stats preloader initialized
2025-07-19 23:59:34,881 - INFO - Starting stats data preloading
2025-07-19 23:59:34,881 - INFO - Started stats data preloading
2025-07-19 23:59:34,881 - INFO - Loading data using optimized functions
2025-07-19 23:59:34,882 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:59:34,882 - INFO - DB Operation: {"timestamp": "2025-07-19 23:59:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:59:34,887 - INFO - Preloaded weekly stats for 7 days
2025-07-19 23:59:34,887 - WARNING - Connection validation failed, creating new connection
2025-07-19 23:59:34,889 - INFO - DB Operation: {"timestamp": "2025-07-19 23:59:34", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-19 23:59:34,891 - INFO - Preloaded summary data
2025-07-19 23:59:34,892 - INFO - Loaded 9 items from cache
2025-07-19 23:59:34,892 - INFO - Preloaded game history (10 records)
2025-07-19 23:59:34,893 - INFO - Preloaded wallet data
2025-07-19 23:59:34,893 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-19 23:59:34,894 - INFO - Loaded weekly stats for 7 days
2025-07-19 23:59:34,895 - INFO - Started background data loading
2025-07-19 23:59:34,897 - INFO - OptimizedStatsLoader initialized
2025-07-19 23:59:34,897 - INFO - Using optimized stats loader for integration
2025-07-19 23:59:34,897 - INFO - Saved 9 items to persistent cache
2025-07-19 23:59:34,898 - INFO - Loaded summary data
2025-07-19 23:59:34,898 - INFO - Loaded game history page 0 (10 records)
2025-07-19 23:59:34,899 - INFO - Loaded game history metadata (total pages: 7)
2025-07-19 23:59:34,901 - INFO - Saved 9 items to cache
2025-07-19 23:59:34,901 - INFO - Background data loading completed
2025-07-19 23:59:34,905 - INFO - Database schema initialized successfully
2025-07-19 23:59:34,908 - INFO - Database schema initialized successfully
2025-07-19 23:59:34,910 - INFO - Stats database initialized successfully
2025-07-19 23:59:34,910 - INFO - Game stats integration module available
2025-07-19 23:59:34,911 - INFO - Started stats event worker thread
2025-07-19 23:59:34,911 - INFO - Stats event hooks initialized
2025-07-19 23:59:35,503 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-19 23:59:35,505 - INFO - Stats performance monitor initialized
2025-07-19 23:59:35,566 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-19 23:59:36,370 - INFO - draw_stats_page: 0.1009s
2025-07-20 00:10:13,941 - INFO - Loaded 0 items from persistent cache
2025-07-20 00:10:13,942 - INFO - Stats cache initialized
2025-07-20 00:10:13,943 - INFO - Database security initialized successfully
2025-07-20 00:10:13,945 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:13", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:10:13,945 - INFO - Created secure database connection
2025-07-20 00:10:14,041 - INFO - Database schema initialized successfully
2025-07-20 00:10:14,047 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:10:14,049 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:10:14,049 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:10:14,050 - INFO - Stats preloader initialized
2025-07-20 00:10:14,051 - INFO - Starting stats data preloading
2025-07-20 00:10:14,051 - INFO - Started stats data preloading
2025-07-20 00:10:14,051 - INFO - Loading data using optimized functions
2025-07-20 00:10:14,052 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:10:14,052 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:10:14,060 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:10:14,060 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:10:14,061 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:14", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:10:14,063 - INFO - Preloaded summary data
2025-07-20 00:10:14,064 - INFO - Preloaded game history (10 records)
2025-07-20 00:10:14,065 - INFO - Preloaded wallet data
2025-07-20 00:10:14,065 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 00:10:14,068 - INFO - Saved 8 items to persistent cache
2025-07-20 00:10:14,112 - INFO - Loaded 8 items from cache
2025-07-20 00:10:14,139 - INFO - Started background data loading
2025-07-20 00:10:14,139 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:10:14,139 - INFO - Using optimized stats loader for integration
2025-07-20 00:10:14,142 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:10:14,143 - INFO - Loaded summary data
2025-07-20 00:10:14,144 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:10:14,144 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:10:14,147 - INFO - Saved 9 items to cache
2025-07-20 00:10:14,148 - INFO - Background data loading completed
2025-07-20 00:10:14,153 - INFO - Database schema initialized successfully
2025-07-20 00:10:14,156 - INFO - Database schema initialized successfully
2025-07-20 00:10:14,164 - INFO - Stats database initialized successfully
2025-07-20 00:10:14,165 - INFO - Game stats integration module available
2025-07-20 00:10:14,167 - INFO - Started stats event worker thread
2025-07-20 00:10:14,167 - INFO - Stats event hooks initialized
2025-07-20 00:10:15,021 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:10:15,021 - INFO - Stats performance monitor initialized
2025-07-20 00:10:15,109 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:10:15,553 - INFO - draw_background: 0.0167s
2025-07-20 00:10:17,037 - INFO - Admin button added to stats page
2025-07-20 00:10:17,699 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:10:17,699 - INFO - DB Operation: {"timestamp": "2025-07-20 00:10:17", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,196 - INFO - Loaded 0 items from persistent cache
2025-07-20 00:16:59,196 - INFO - Stats cache initialized
2025-07-20 00:16:59,197 - INFO - Database security initialized successfully
2025-07-20 00:16:59,197 - INFO - DB Operation: {"timestamp": "2025-07-20 00:16:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,198 - INFO - Created secure database connection
2025-07-20 00:16:59,212 - INFO - Database schema initialized successfully
2025-07-20 00:16:59,213 - INFO - DB Operation: {"timestamp": "2025-07-20 00:16:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,213 - INFO - DB Operation: {"timestamp": "2025-07-20 00:16:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,213 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:16:59,213 - INFO - Stats preloader initialized
2025-07-20 00:16:59,213 - INFO - Starting stats data preloading
2025-07-20 00:16:59,214 - INFO - Loading data using optimized functions
2025-07-20 00:16:59,214 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:16:59,214 - INFO - Started stats data preloading
2025-07-20 00:16:59,214 - INFO - DB Operation: {"timestamp": "2025-07-20 00:16:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,221 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:16:59,221 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:16:59,222 - INFO - DB Operation: {"timestamp": "2025-07-20 00:16:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:16:59,223 - INFO - Preloaded summary data
2025-07-20 00:16:59,225 - INFO - Preloaded game history (10 records)
2025-07-20 00:16:59,225 - INFO - Preloaded wallet data
2025-07-20 00:16:59,225 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 00:16:59,226 - INFO - Loaded 0 items from cache
2025-07-20 00:16:59,228 - INFO - Saved 8 items to persistent cache
2025-07-20 00:16:59,229 - INFO - Started background data loading
2025-07-20 00:16:59,229 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:16:59,229 - INFO - Using optimized stats loader for integration
2025-07-20 00:16:59,230 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:16:59,232 - INFO - Loaded summary data
2025-07-20 00:16:59,232 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:16:59,232 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:16:59,235 - INFO - Saved 7 items to cache
2025-07-20 00:16:59,235 - INFO - Background data loading completed
2025-07-20 00:16:59,239 - INFO - Database schema initialized successfully
2025-07-20 00:16:59,241 - INFO - Database schema initialized successfully
2025-07-20 00:16:59,242 - INFO - Stats database initialized successfully
2025-07-20 00:16:59,242 - INFO - Game stats integration module available
2025-07-20 00:16:59,242 - INFO - Started stats event worker thread
2025-07-20 00:16:59,243 - INFO - Stats event hooks initialized
2025-07-20 00:16:59,872 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:16:59,873 - INFO - Stats performance monitor initialized
2025-07-20 00:16:59,933 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:17:00,749 - INFO - generate_background: 0.0020s
2025-07-20 00:17:00,779 - INFO - draw_background: 0.0326s
2025-07-20 00:17:00,800 - INFO - draw_stats_page: 0.0529s
2025-07-20 00:17:00,800 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,805 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:17:00,806 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,809 - INFO - draw_stats_page: 0.0030s
2025-07-20 00:17:00,810 - INFO - draw_background: 0.0010s
2025-07-20 00:17:00,815 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:17:00,816 - INFO - draw_background: 0.0010s
2025-07-20 00:17:00,817 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:17:00,821 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:17:00,821 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,826 - INFO - draw_stats_page: 0.0049s
2025-07-20 00:17:00,826 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,831 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:17:00,832 - INFO - draw_background: 0.0002s
2025-07-20 00:17:00,837 - INFO - draw_stats_page: 0.0048s
2025-07-20 00:17:00,838 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,842 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:17:00,842 - INFO - draw_background: 0.0000s
2025-07-20 00:17:00,843 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:17:00,851 - INFO - draw_stats_page: 0.0086s
2025-07-20 00:18:10,557 - INFO - Loaded 7 items from persistent cache
2025-07-20 00:18:10,557 - INFO - Stats cache initialized
2025-07-20 00:18:10,558 - INFO - Database security initialized successfully
2025-07-20 00:18:10,558 - INFO - DB Operation: {"timestamp": "2025-07-20 00:18:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:18:10,558 - INFO - Created secure database connection
2025-07-20 00:18:10,561 - INFO - Database schema initialized successfully
2025-07-20 00:18:10,562 - INFO - DB Operation: {"timestamp": "2025-07-20 00:18:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:18:10,562 - INFO - DB Operation: {"timestamp": "2025-07-20 00:18:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:18:10,562 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:18:10,563 - INFO - Stats preloader initialized
2025-07-20 00:18:10,563 - INFO - Starting stats data preloading
2025-07-20 00:18:10,563 - INFO - Started stats data preloading
2025-07-20 00:18:10,563 - INFO - Loading data using optimized functions
2025-07-20 00:18:10,564 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:18:10,564 - INFO - DB Operation: {"timestamp": "2025-07-20 00:18:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:18:10,571 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:18:10,571 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:18:10,572 - INFO - DB Operation: {"timestamp": "2025-07-20 00:18:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:18:10,574 - INFO - Preloaded summary data
2025-07-20 00:18:10,575 - INFO - Preloaded game history (10 records)
2025-07-20 00:18:10,575 - INFO - Preloaded wallet data
2025-07-20 00:18:10,576 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 00:18:10,580 - INFO - Saved 9 items to persistent cache
2025-07-20 00:18:10,594 - INFO - Loaded 9 items from cache
2025-07-20 00:18:10,595 - INFO - Started background data loading
2025-07-20 00:18:10,595 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:18:10,595 - INFO - Using optimized stats loader for integration
2025-07-20 00:18:10,596 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:18:10,597 - INFO - Loaded summary data
2025-07-20 00:18:10,598 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:18:10,600 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:18:10,602 - INFO - Saved 9 items to cache
2025-07-20 00:18:10,602 - INFO - Background data loading completed
2025-07-20 00:18:10,605 - INFO - Database schema initialized successfully
2025-07-20 00:18:10,608 - INFO - Database schema initialized successfully
2025-07-20 00:18:10,610 - INFO - Stats database initialized successfully
2025-07-20 00:18:10,610 - INFO - Game stats integration module available
2025-07-20 00:18:10,610 - INFO - Started stats event worker thread
2025-07-20 00:18:10,610 - INFO - Stats event hooks initialized
2025-07-20 00:18:11,330 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:18:11,331 - INFO - Stats performance monitor initialized
2025-07-20 00:18:11,391 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:18:11,917 - INFO - draw_background: 0.0268s
2025-07-20 00:18:13,391 - INFO - Admin button added to stats page
2025-07-20 00:18:15,054 - INFO - draw_background: 0.0167s
2025-07-20 00:18:15,054 - INFO - draw_stats_page: 0.0167s
2025-07-20 00:25:10,119 - INFO - Loaded 9 items from persistent cache
2025-07-20 00:25:10,119 - INFO - Stats cache initialized
2025-07-20 00:25:10,121 - INFO - Database security initialized successfully
2025-07-20 00:25:10,122 - INFO - DB Operation: {"timestamp": "2025-07-20 00:25:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:25:10,122 - INFO - Created secure database connection
2025-07-20 00:25:10,240 - INFO - Database schema initialized successfully
2025-07-20 00:25:10,242 - INFO - DB Operation: {"timestamp": "2025-07-20 00:25:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:25:10,243 - INFO - DB Operation: {"timestamp": "2025-07-20 00:25:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:25:10,244 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:25:10,244 - INFO - Stats preloader initialized
2025-07-20 00:25:10,244 - INFO - Starting stats data preloading
2025-07-20 00:25:10,244 - INFO - Started stats data preloading
2025-07-20 00:25:10,244 - INFO - Loading data using optimized functions
2025-07-20 00:25:10,245 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:25:10,246 - INFO - DB Operation: {"timestamp": "2025-07-20 00:25:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:25:10,251 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:25:10,251 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:25:10,252 - INFO - DB Operation: {"timestamp": "2025-07-20 00:25:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:25:10,256 - INFO - Preloaded summary data
2025-07-20 00:25:10,256 - INFO - Loaded 9 items from cache
2025-07-20 00:25:10,257 - INFO - Preloaded game history (10 records)
2025-07-20 00:25:10,257 - INFO - Started background data loading
2025-07-20 00:25:10,257 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:25:10,257 - INFO - Using optimized stats loader for integration
2025-07-20 00:25:10,258 - INFO - Preloaded wallet data
2025-07-20 00:25:10,258 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 00:25:10,262 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:25:10,262 - INFO - Saved 9 items to persistent cache
2025-07-20 00:25:10,263 - INFO - Loaded summary data
2025-07-20 00:25:10,263 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:25:10,264 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:25:10,266 - INFO - Saved 9 items to cache
2025-07-20 00:25:10,266 - INFO - Background data loading completed
2025-07-20 00:25:10,268 - INFO - Database schema initialized successfully
2025-07-20 00:25:10,271 - INFO - Database schema initialized successfully
2025-07-20 00:25:10,272 - INFO - Stats database initialized successfully
2025-07-20 00:25:10,273 - INFO - Game stats integration module available
2025-07-20 00:25:10,273 - INFO - Started stats event worker thread
2025-07-20 00:25:10,273 - INFO - Stats event hooks initialized
2025-07-20 00:25:10,968 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:25:10,968 - INFO - Stats performance monitor initialized
2025-07-20 00:25:11,027 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:25:11,431 - INFO - draw_background: 0.0146s
2025-07-20 00:25:12,417 - INFO - Starting migration from JSON: data\stats.json
2025-07-20 00:25:12,427 - INFO - Loaded time reference (startup #18)
2025-07-20 00:25:12,427 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-20 00:25:12,428 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11580 and this is thread id 13300.
2025-07-20 00:25:12,428 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11580 and this is thread id 13300.
2025-07-20 00:25:12,428 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11580 and this is thread id 13300.
2025-07-20 00:25:12,428 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11580 and this is thread id 13300.
2025-07-20 00:28:31,192 - INFO - Loaded 9 items from persistent cache
2025-07-20 00:28:31,193 - INFO - Stats cache initialized
2025-07-20 00:28:31,195 - INFO - Database security initialized successfully
2025-07-20 00:28:31,196 - INFO - DB Operation: {"timestamp": "2025-07-20 00:28:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:28:31,196 - INFO - Created secure database connection
2025-07-20 00:28:31,320 - INFO - Database schema initialized successfully
2025-07-20 00:28:31,322 - INFO - DB Operation: {"timestamp": "2025-07-20 00:28:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:28:31,322 - INFO - DB Operation: {"timestamp": "2025-07-20 00:28:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:28:31,322 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:28:31,322 - INFO - Stats preloader initialized
2025-07-20 00:28:31,323 - INFO - Starting stats data preloading
2025-07-20 00:28:31,323 - INFO - Started stats data preloading
2025-07-20 00:28:31,323 - INFO - Loading data using optimized functions
2025-07-20 00:28:31,323 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:28:31,324 - INFO - DB Operation: {"timestamp": "2025-07-20 00:28:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:28:31,332 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:28:31,333 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:28:31,333 - INFO - DB Operation: {"timestamp": "2025-07-20 00:28:31", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:28:31,336 - INFO - Loaded 9 items from cache
2025-07-20 00:28:31,338 - INFO - Preloaded summary data
2025-07-20 00:28:31,338 - INFO - Preloaded game history (10 records)
2025-07-20 00:28:31,339 - INFO - Preloaded wallet data
2025-07-20 00:28:31,339 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 00:28:31,341 - INFO - Saved 9 items to persistent cache
2025-07-20 00:28:31,342 - INFO - Started background data loading
2025-07-20 00:28:31,342 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:28:31,342 - INFO - Using optimized stats loader for integration
2025-07-20 00:28:31,344 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:28:31,345 - INFO - Loaded summary data
2025-07-20 00:28:31,346 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:28:31,346 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:28:31,349 - INFO - Saved 9 items to cache
2025-07-20 00:28:31,349 - INFO - Background data loading completed
2025-07-20 00:28:31,360 - INFO - Database schema initialized successfully
2025-07-20 00:28:31,365 - INFO - Database schema initialized successfully
2025-07-20 00:28:31,366 - INFO - Stats database initialized successfully
2025-07-20 00:28:31,366 - INFO - Game stats integration module available
2025-07-20 00:28:31,367 - INFO - Started stats event worker thread
2025-07-20 00:28:31,367 - INFO - Stats event hooks initialized
2025-07-20 00:28:32,172 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:28:32,173 - INFO - Stats performance monitor initialized
2025-07-20 00:28:32,253 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:28:32,882 - INFO - draw_background: 0.0181s
2025-07-20 00:28:32,924 - INFO - draw_stats_page: 0.0595s
2025-07-20 00:28:32,926 - INFO - draw_background: 0.0010s
2025-07-20 00:28:32,929 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:28:32,981 - INFO - draw_background: 0.0000s
2025-07-20 00:28:32,987 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:28:33,038 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,044 - INFO - draw_stats_page: 0.0046s
2025-07-20 00:28:33,095 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,098 - INFO - draw_stats_page: 0.0034s
2025-07-20 00:28:33,100 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:28:33,151 - INFO - draw_background: 0.0010s
2025-07-20 00:28:33,157 - INFO - draw_stats_page: 0.0070s
2025-07-20 00:28:33,208 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,212 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:28:33,262 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,266 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:28:33,316 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,322 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:28:33,373 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,377 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:28:33,378 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:28:33,430 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,433 - INFO - draw_stats_page: 0.0036s
2025-07-20 00:28:33,485 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,491 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:28:33,541 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,545 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:28:33,595 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,599 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:28:33,652 - INFO - draw_background: 0.0015s
2025-07-20 00:28:33,656 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:28:33,657 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:28:33,708 - INFO - draw_background: 0.0005s
2025-07-20 00:28:33,712 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:28:33,762 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,770 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:28:33,820 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,825 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:28:33,875 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,878 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:28:33,929 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,932 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:28:33,935 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:28:33,986 - INFO - draw_background: 0.0000s
2025-07-20 00:28:33,991 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:28:34,058 - INFO - draw_background: 0.0126s
2025-07-20 00:28:35,048 - INFO - Starting migration from JSON: data\stats.json
2025-07-20 00:28:35,062 - INFO - Loaded time reference (startup #20)
2025-07-20 00:28:35,063 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-20 00:28:35,064 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11704 and this is thread id 4636.
2025-07-20 00:28:35,064 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11704 and this is thread id 4636.
2025-07-20 00:28:35,064 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11704 and this is thread id 4636.
2025-07-20 00:28:35,064 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 11704 and this is thread id 4636.
2025-07-20 00:39:06,990 - INFO - Loaded 0 items from persistent cache
2025-07-20 00:39:06,990 - INFO - Stats cache initialized
2025-07-20 00:39:06,991 - INFO - Database security initialized successfully
2025-07-20 00:39:06,992 - INFO - DB Operation: {"timestamp": "2025-07-20 00:39:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:39:06,992 - INFO - Created secure database connection
2025-07-20 00:39:07,010 - INFO - Database schema initialized successfully
2025-07-20 00:39:07,010 - INFO - DB Operation: {"timestamp": "2025-07-20 00:39:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:39:07,011 - INFO - DB Operation: {"timestamp": "2025-07-20 00:39:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:39:07,011 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 00:39:07,011 - INFO - Stats preloader initialized
2025-07-20 00:39:07,015 - INFO - Starting stats data preloading
2025-07-20 00:39:07,015 - INFO - Started stats data preloading
2025-07-20 00:39:07,015 - INFO - Loading data using optimized functions
2025-07-20 00:39:07,017 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:39:07,017 - INFO - DB Operation: {"timestamp": "2025-07-20 00:39:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:39:07,025 - INFO - Preloaded weekly stats for 7 days
2025-07-20 00:39:07,025 - WARNING - Connection validation failed, creating new connection
2025-07-20 00:39:07,027 - INFO - DB Operation: {"timestamp": "2025-07-20 00:39:07", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 00:39:07,030 - INFO - Preloaded summary data
2025-07-20 00:39:07,030 - INFO - Preloaded game history (10 records)
2025-07-20 00:39:07,031 - INFO - Preloaded wallet data
2025-07-20 00:39:07,031 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 00:39:07,034 - INFO - Saved 8 items to persistent cache
2025-07-20 00:39:07,062 - INFO - Loaded 8 items from cache
2025-07-20 00:39:07,067 - INFO - Started background data loading
2025-07-20 00:39:07,067 - INFO - OptimizedStatsLoader initialized
2025-07-20 00:39:07,067 - INFO - Using optimized stats loader for integration
2025-07-20 00:39:07,068 - INFO - Loaded weekly stats for 7 days
2025-07-20 00:39:07,069 - INFO - Loaded summary data
2025-07-20 00:39:07,070 - INFO - Loaded game history page 0 (10 records)
2025-07-20 00:39:07,070 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 00:39:07,073 - INFO - Saved 9 items to cache
2025-07-20 00:39:07,073 - INFO - Background data loading completed
2025-07-20 00:39:07,082 - INFO - Database schema initialized successfully
2025-07-20 00:39:07,093 - INFO - Database schema initialized successfully
2025-07-20 00:39:07,100 - INFO - Stats database initialized successfully
2025-07-20 00:39:07,101 - INFO - Game stats integration module available
2025-07-20 00:39:07,101 - INFO - Started stats event worker thread
2025-07-20 00:39:07,101 - INFO - Stats event hooks initialized
2025-07-20 00:39:08,125 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 00:39:08,125 - INFO - Stats performance monitor initialized
2025-07-20 00:39:08,455 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 00:39:08,991 - INFO - draw_background: 0.0313s
2025-07-20 00:39:09,015 - INFO - draw_stats_page: 0.0550s
2025-07-20 00:39:09,065 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,072 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:39:09,122 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,127 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:09,179 - INFO - draw_background: 0.0010s
2025-07-20 00:39:09,184 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:09,235 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,241 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:09,242 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:09,293 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,299 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:09,350 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,354 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:09,406 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,410 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:09,463 - INFO - draw_background: 0.0010s
2025-07-20 00:39:09,468 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:09,519 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,523 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:09,523 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:09,574 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,580 - INFO - draw_stats_page: 0.0059s
2025-07-20 00:39:09,632 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,637 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:09,688 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,691 - INFO - draw_stats_page: 0.0036s
2025-07-20 00:39:09,741 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,748 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:09,799 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,804 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:09,806 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:09,858 - INFO - draw_background: 0.0005s
2025-07-20 00:39:09,864 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:39:09,914 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,918 - INFO - draw_stats_page: 0.0041s
2025-07-20 00:39:09,970 - INFO - draw_background: 0.0000s
2025-07-20 00:39:09,975 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:10,028 - INFO - draw_background: 0.0010s
2025-07-20 00:39:10,032 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:10,084 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,089 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:10,090 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:10,141 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,149 - INFO - draw_stats_page: 0.0079s
2025-07-20 00:39:10,199 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,203 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:10,254 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,260 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:10,310 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,316 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:10,366 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,370 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:10,372 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:10,423 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,429 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:10,480 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,486 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:10,536 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,540 - INFO - draw_stats_page: 0.0046s
2025-07-20 00:39:10,592 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,598 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:10,649 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,653 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:10,654 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:10,705 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,712 - INFO - draw_stats_page: 0.0076s
2025-07-20 00:39:10,762 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,770 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:10,821 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,826 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:10,879 - INFO - draw_background: 0.0011s
2025-07-20 00:39:10,884 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:10,935 - INFO - draw_background: 0.0005s
2025-07-20 00:39:10,940 - INFO - draw_stats_page: 0.0047s
2025-07-20 00:39:10,942 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:10,992 - INFO - draw_background: 0.0000s
2025-07-20 00:39:10,999 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:11,049 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,055 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:11,106 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,112 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:11,164 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,169 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:11,220 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,225 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:11,228 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:11,278 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,284 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:11,334 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,338 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:11,388 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,393 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:11,445 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,451 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:11,501 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,505 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:39:11,506 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:11,556 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,562 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:11,613 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,617 - INFO - draw_stats_page: 0.0041s
2025-07-20 00:39:11,669 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,673 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:11,724 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,730 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:11,782 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,785 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:11,786 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:11,837 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,843 - INFO - draw_stats_page: 0.0062s
2025-07-20 00:39:11,894 - INFO - draw_background: 0.0000s
2025-07-20 00:39:11,900 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:11,951 - INFO - draw_background: 0.0011s
2025-07-20 00:39:11,955 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:12,006 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,013 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:12,064 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,069 - INFO - draw_stats_page: 0.0036s
2025-07-20 00:39:12,070 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:12,120 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,125 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:12,178 - INFO - draw_background: 0.0006s
2025-07-20 00:39:12,183 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:12,233 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,239 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:12,291 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,298 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:12,349 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,353 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:12,354 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:12,405 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,412 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:12,462 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,469 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:12,520 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,526 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:12,577 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,584 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:39:12,635 - INFO - draw_background: 0.0010s
2025-07-20 00:39:12,640 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:12,642 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:12,693 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,698 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:12,749 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,755 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:12,807 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,813 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:12,863 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,869 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:12,920 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,928 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:12,929 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:12,979 - INFO - draw_background: 0.0000s
2025-07-20 00:39:12,984 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:13,034 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,039 - INFO - draw_stats_page: 0.0053s
2025-07-20 00:39:13,090 - INFO - draw_background: 0.0010s
2025-07-20 00:39:13,096 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:13,147 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,151 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:13,202 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,207 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:13,216 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:13,267 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,282 - INFO - draw_stats_page: 0.0146s
2025-07-20 00:39:13,333 - INFO - draw_background: 0.0010s
2025-07-20 00:39:13,337 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:13,388 - INFO - draw_background: 0.0005s
2025-07-20 00:39:13,398 - INFO - draw_stats_page: 0.0102s
2025-07-20 00:39:13,448 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,452 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:13,503 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,510 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:13,511 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:13,561 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,566 - INFO - draw_stats_page: 0.0052s
2025-07-20 00:39:13,617 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,621 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:13,675 - INFO - draw_background: 0.0016s
2025-07-20 00:39:13,683 - INFO - draw_stats_page: 0.0096s
2025-07-20 00:39:13,733 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,741 - INFO - draw_stats_page: 0.0081s
2025-07-20 00:39:13,793 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,799 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:13,800 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:13,851 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,858 - INFO - draw_stats_page: 0.0070s
2025-07-20 00:39:13,909 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,913 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:13,969 - INFO - draw_background: 0.0000s
2025-07-20 00:39:13,976 - INFO - draw_stats_page: 0.0066s
2025-07-20 00:39:14,028 - INFO - draw_background: 0.0015s
2025-07-20 00:39:14,034 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:14,084 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,094 - INFO - draw_stats_page: 0.0097s
2025-07-20 00:39:14,095 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:14,148 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,155 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:14,207 - INFO - draw_background: 0.0016s
2025-07-20 00:39:14,213 - INFO - draw_stats_page: 0.0076s
2025-07-20 00:39:14,264 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,268 - INFO - draw_stats_page: 0.0041s
2025-07-20 00:39:14,318 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,325 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:14,375 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,381 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:14,382 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:14,433 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,436 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:14,486 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,493 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:14,544 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,549 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:14,599 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,603 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:14,658 - INFO - draw_background: 0.0041s
2025-07-20 00:39:14,664 - INFO - draw_stats_page: 0.0101s
2025-07-20 00:39:14,666 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:14,719 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,732 - INFO - draw_stats_page: 0.0131s
2025-07-20 00:39:14,783 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,792 - INFO - draw_stats_page: 0.0081s
2025-07-20 00:39:14,844 - INFO - draw_background: 0.0010s
2025-07-20 00:39:14,850 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:14,901 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,909 - INFO - draw_stats_page: 0.0081s
2025-07-20 00:39:14,959 - INFO - draw_background: 0.0000s
2025-07-20 00:39:14,965 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:14,965 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:15,018 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,025 - INFO - draw_stats_page: 0.0076s
2025-07-20 00:39:15,076 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,081 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:15,132 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,140 - INFO - draw_stats_page: 0.0077s
2025-07-20 00:39:15,191 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,196 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:15,248 - INFO - draw_background: 0.0005s
2025-07-20 00:39:15,255 - INFO - draw_stats_page: 0.0081s
2025-07-20 00:39:15,258 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:15,333 - INFO - draw_background: 0.0020s
2025-07-20 00:39:15,348 - INFO - draw_stats_page: 0.0212s
2025-07-20 00:39:15,398 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,405 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:15,459 - INFO - draw_background: 0.0015s
2025-07-20 00:39:15,465 - INFO - draw_stats_page: 0.0091s
2025-07-20 00:39:15,516 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,523 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:15,574 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,579 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:15,580 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:15,631 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,649 - INFO - draw_stats_page: 0.0182s
2025-07-20 00:39:15,700 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,715 - INFO - draw_stats_page: 0.0151s
2025-07-20 00:39:15,766 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,773 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:15,826 - INFO - draw_background: 0.0005s
2025-07-20 00:39:15,834 - INFO - draw_stats_page: 0.0091s
2025-07-20 00:39:15,886 - INFO - draw_background: 0.0005s
2025-07-20 00:39:15,893 - INFO - draw_stats_page: 0.0081s
2025-07-20 00:39:15,895 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:15,945 - INFO - draw_background: 0.0000s
2025-07-20 00:39:15,949 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:16,000 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,007 - INFO - draw_stats_page: 0.0076s
2025-07-20 00:39:16,058 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,065 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:16,115 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,123 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:16,173 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,179 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:16,180 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:16,233 - INFO - draw_background: 0.0010s
2025-07-20 00:39:16,266 - INFO - draw_stats_page: 0.0337s
2025-07-20 00:39:16,316 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,323 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:16,374 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,379 - INFO - draw_stats_page: 0.0049s
2025-07-20 00:39:16,429 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,433 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:16,484 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,492 - INFO - draw_stats_page: 0.0077s
2025-07-20 00:39:16,493 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:16,543 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,548 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:16,599 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,604 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:16,655 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,660 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:16,710 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,715 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:16,806 - INFO - draw_background: 0.0025s
2025-07-20 00:39:16,815 - INFO - draw_stats_page: 0.0116s
2025-07-20 00:39:16,821 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:16,888 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,894 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:16,946 - INFO - draw_background: 0.0000s
2025-07-20 00:39:16,955 - INFO - draw_stats_page: 0.0091s
2025-07-20 00:39:17,005 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,013 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:17,064 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,073 - INFO - draw_stats_page: 0.0091s
2025-07-20 00:39:17,123 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,130 - INFO - draw_stats_page: 0.0071s
2025-07-20 00:39:17,131 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:17,183 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,191 - INFO - draw_stats_page: 0.0080s
2025-07-20 00:39:17,242 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,246 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:17,298 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,303 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:17,354 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,359 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:17,410 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,414 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:17,416 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:17,470 - INFO - draw_background: 0.0015s
2025-07-20 00:39:17,476 - INFO - draw_stats_page: 0.0091s
2025-07-20 00:39:17,527 - INFO - draw_background: 0.0005s
2025-07-20 00:39:17,531 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:17,581 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,589 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:17,640 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,643 - INFO - draw_stats_page: 0.0030s
2025-07-20 00:39:17,694 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,704 - INFO - draw_stats_page: 0.0101s
2025-07-20 00:39:17,707 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:17,758 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,764 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:17,815 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,821 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:17,872 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,877 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:17,928 - INFO - draw_background: 0.0005s
2025-07-20 00:39:17,933 - INFO - draw_stats_page: 0.0056s
2025-07-20 00:39:17,983 - INFO - draw_background: 0.0000s
2025-07-20 00:39:17,990 - INFO - draw_stats_page: 0.0070s
2025-07-20 00:39:17,991 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:18,043 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,049 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:18,100 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,107 - INFO - draw_stats_page: 0.0075s
2025-07-20 00:39:18,158 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,163 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:18,213 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,219 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:18,271 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,276 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:18,278 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:18,330 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,336 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:18,388 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,393 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:18,445 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,449 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:18,499 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,506 - INFO - draw_stats_page: 0.0065s
2025-07-20 00:39:18,557 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,562 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:18,563 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:18,613 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,620 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:18,670 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,674 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:18,725 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,730 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:18,781 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,789 - INFO - draw_stats_page: 0.0076s
2025-07-20 00:39:18,839 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,845 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:18,846 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:18,897 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,903 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:18,954 - INFO - draw_background: 0.0000s
2025-07-20 00:39:18,960 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:20,262 - INFO - draw_background: 0.0111s
2025-07-20 00:39:20,281 - INFO - draw_stats_page: 0.0308s
2025-07-20 00:39:20,384 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,388 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:20,488 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,493 - INFO - draw_stats_page: 0.0046s
2025-07-20 00:39:20,494 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:20,596 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,601 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:20,702 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,707 - INFO - draw_stats_page: 0.0055s
2025-07-20 00:39:20,807 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,811 - INFO - draw_stats_page: 0.0036s
2025-07-20 00:39:20,911 - INFO - draw_background: 0.0000s
2025-07-20 00:39:20,916 - INFO - draw_stats_page: 0.0049s
2025-07-20 00:39:21,017 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,021 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:21,022 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:21,123 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,127 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:21,228 - INFO - draw_background: 0.0009s
2025-07-20 00:39:21,235 - INFO - draw_stats_page: 0.0074s
2025-07-20 00:39:21,336 - INFO - draw_background: 0.0010s
2025-07-20 00:39:21,341 - INFO - draw_stats_page: 0.0061s
2025-07-20 00:39:21,441 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,445 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:21,545 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,551 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:21,552 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:21,653 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,656 - INFO - draw_stats_page: 0.0030s
2025-07-20 00:39:21,756 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,759 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:21,861 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,865 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:21,966 - INFO - draw_background: 0.0000s
2025-07-20 00:39:21,969 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:22,106 - INFO - draw_background: 0.0010s
2025-07-20 00:39:22,111 - INFO - draw_stats_page: 0.0060s
2025-07-20 00:39:22,112 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:22,215 - INFO - draw_background: 0.0021s
2025-07-20 00:39:22,221 - INFO - draw_stats_page: 0.0080s
2025-07-20 00:39:22,323 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,326 - INFO - draw_stats_page: 0.0030s
2025-07-20 00:39:22,426 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,431 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:22,532 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,537 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:22,637 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,641 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:22,642 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:22,742 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,746 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:39:22,847 - INFO - draw_background: 0.0010s
2025-07-20 00:39:22,851 - INFO - draw_stats_page: 0.0046s
2025-07-20 00:39:22,952 - INFO - draw_background: 0.0000s
2025-07-20 00:39:22,956 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:23,058 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,061 - INFO - draw_stats_page: 0.0036s
2025-07-20 00:39:23,161 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,165 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:23,166 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:23,266 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,269 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:23,370 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,374 - INFO - draw_stats_page: 0.0045s
2025-07-20 00:39:23,475 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,479 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:23,579 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,583 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:23,684 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,689 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:23,690 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:23,791 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,795 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:39:23,896 - INFO - draw_background: 0.0000s
2025-07-20 00:39:23,901 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:24,001 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,005 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:24,106 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,110 - INFO - draw_stats_page: 0.0034s
2025-07-20 00:39:24,211 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,215 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:24,216 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:24,317 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,321 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:39:24,422 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,426 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:24,526 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,531 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:24,632 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,636 - INFO - draw_stats_page: 0.0040s
2025-07-20 00:39:24,736 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,739 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:24,741 - INFO - Saved metrics to data\metrics\stats_metrics.json
2025-07-20 00:39:24,842 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,846 - INFO - draw_stats_page: 0.0039s
2025-07-20 00:39:24,946 - INFO - draw_background: 0.0000s
2025-07-20 00:39:24,951 - INFO - draw_stats_page: 0.0051s
2025-07-20 00:39:25,051 - INFO - draw_background: 0.0000s
2025-07-20 00:39:25,056 - INFO - draw_stats_page: 0.0050s
2025-07-20 00:39:25,156 - INFO - draw_background: 0.0000s
2025-07-20 00:39:25,161 - INFO - draw_stats_page: 0.0035s
2025-07-20 00:39:25,426 - INFO - Starting migration from JSON: data\stats.json
2025-07-20 00:39:25,430 - INFO - Loaded time reference (startup #21)
2025-07-20 00:39:25,432 - INFO - Time Manager initialized. Reference: 2025-07-17T17:02:58.116572+00:00
2025-07-20 00:39:25,432 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5704 and this is thread id 11404.
2025-07-20 00:39:25,432 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5704 and this is thread id 11404.
2025-07-20 00:39:25,432 - ERROR - Error updating daily stats: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5704 and this is thread id 11404.
2025-07-20 00:39:25,433 - ERROR - Error processing activity: SQLite objects created in a thread can only be used in that same thread. The object was created in thread id 5704 and this is thread id 11404.
2025-07-20 01:28:57,445 - INFO - Loaded 9 items from persistent cache
2025-07-20 01:28:57,445 - INFO - Stats cache initialized
2025-07-20 01:28:57,446 - INFO - Database security initialized successfully
2025-07-20 01:28:57,448 - INFO - DB Operation: {"timestamp": "2025-07-20 01:28:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:28:57,448 - INFO - Created secure database connection
2025-07-20 01:28:57,451 - INFO - Database schema initialized successfully
2025-07-20 01:28:57,452 - INFO - DB Operation: {"timestamp": "2025-07-20 01:28:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:28:57,453 - INFO - DB Operation: {"timestamp": "2025-07-20 01:28:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:28:57,453 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 01:28:57,453 - INFO - Stats preloader initialized
2025-07-20 01:28:57,454 - INFO - Starting stats data preloading
2025-07-20 01:28:57,455 - INFO - Started stats data preloading
2025-07-20 01:28:57,455 - INFO - Loading data using optimized functions
2025-07-20 01:28:57,455 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:28:57,456 - INFO - DB Operation: {"timestamp": "2025-07-20 01:28:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:28:57,462 - INFO - Preloaded weekly stats for 7 days
2025-07-20 01:28:57,462 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:28:57,463 - INFO - DB Operation: {"timestamp": "2025-07-20 01:28:57", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:28:57,463 - INFO - Loaded 9 items from cache
2025-07-20 01:28:57,464 - INFO - Started background data loading
2025-07-20 01:28:57,464 - INFO - OptimizedStatsLoader initialized
2025-07-20 01:28:57,464 - INFO - Using optimized stats loader for integration
2025-07-20 01:28:57,465 - INFO - Preloaded summary data
2025-07-20 01:28:57,466 - INFO - Loaded weekly stats for 7 days
2025-07-20 01:28:57,466 - INFO - Loaded summary data
2025-07-20 01:28:57,467 - INFO - Loaded game history page 0 (10 records)
2025-07-20 01:28:57,467 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 01:28:57,468 - INFO - Preloaded game history (10 records)
2025-07-20 01:28:57,470 - INFO - Preloaded wallet data
2025-07-20 01:28:57,470 - INFO - Saved 9 items to cache
2025-07-20 01:28:57,471 - INFO - Background data loading completed
2025-07-20 01:28:57,470 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 01:28:57,474 - INFO - Saved 9 items to persistent cache
2025-07-20 01:28:57,476 - INFO - Database schema initialized successfully
2025-07-20 01:28:57,480 - INFO - Database schema initialized successfully
2025-07-20 01:28:57,481 - INFO - Stats database initialized successfully
2025-07-20 01:28:57,481 - INFO - Game stats integration module available
2025-07-20 01:28:57,482 - INFO - Started stats event worker thread
2025-07-20 01:28:57,482 - INFO - Stats event hooks initialized
2025-07-20 01:28:58,020 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 01:28:58,020 - INFO - Stats performance monitor initialized
2025-07-20 01:28:58,075 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 01:28:58,539 - INFO - load_statistics: 0.0010s
2025-07-20 01:28:59,108 - INFO - load_statistics: 0.0035s
2025-07-20 01:38:15,272 - INFO - Loaded 0 items from persistent cache
2025-07-20 01:38:15,272 - INFO - Stats cache initialized
2025-07-20 01:38:15,273 - INFO - Database security initialized successfully
2025-07-20 01:38:15,273 - INFO - DB Operation: {"timestamp": "2025-07-20 01:38:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:38:15,273 - INFO - Created secure database connection
2025-07-20 01:38:15,287 - INFO - Database schema initialized successfully
2025-07-20 01:38:15,288 - INFO - DB Operation: {"timestamp": "2025-07-20 01:38:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:38:15,288 - INFO - DB Operation: {"timestamp": "2025-07-20 01:38:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:38:15,288 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 01:38:15,288 - INFO - Stats preloader initialized
2025-07-20 01:38:15,291 - INFO - Starting stats data preloading
2025-07-20 01:38:15,291 - INFO - Started stats data preloading
2025-07-20 01:38:15,291 - INFO - Loading data using optimized functions
2025-07-20 01:38:15,291 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:38:15,292 - INFO - DB Operation: {"timestamp": "2025-07-20 01:38:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:38:15,300 - INFO - Preloaded weekly stats for 7 days
2025-07-20 01:38:15,301 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:38:15,301 - INFO - Loaded 0 items from cache
2025-07-20 01:38:15,301 - INFO - DB Operation: {"timestamp": "2025-07-20 01:38:15", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:38:15,304 - INFO - Started background data loading
2025-07-20 01:38:15,305 - INFO - OptimizedStatsLoader initialized
2025-07-20 01:38:15,305 - INFO - Using optimized stats loader for integration
2025-07-20 01:38:15,306 - INFO - Loaded weekly stats for 7 days
2025-07-20 01:38:15,306 - INFO - Preloaded summary data
2025-07-20 01:38:15,307 - INFO - Loaded summary data
2025-07-20 01:38:15,307 - INFO - Preloaded game history (10 records)
2025-07-20 01:38:15,308 - INFO - Preloaded wallet data
2025-07-20 01:38:15,308 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 01:38:15,308 - INFO - Loaded game history page 0 (10 records)
2025-07-20 01:38:15,308 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 01:38:15,315 - INFO - Saved 7 items to cache
2025-07-20 01:38:15,315 - INFO - Background data loading completed
2025-07-20 01:38:15,315 - INFO - Saved 8 items to persistent cache
2025-07-20 01:38:15,319 - INFO - Database schema initialized successfully
2025-07-20 01:38:15,323 - INFO - Database schema initialized successfully
2025-07-20 01:38:15,324 - INFO - Stats database initialized successfully
2025-07-20 01:38:15,324 - INFO - Game stats integration module available
2025-07-20 01:38:15,325 - INFO - Started stats event worker thread
2025-07-20 01:38:15,325 - INFO - Stats event hooks initialized
2025-07-20 01:38:15,851 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 01:38:15,851 - INFO - Stats performance monitor initialized
2025-07-20 01:38:15,904 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 01:45:42,318 - INFO - Loaded 9 items from persistent cache
2025-07-20 01:45:42,318 - INFO - Stats cache initialized
2025-07-20 01:45:42,318 - INFO - Database security initialized successfully
2025-07-20 01:45:42,319 - INFO - DB Operation: {"timestamp": "2025-07-20 01:45:42", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:45:42,319 - INFO - Created secure database connection
2025-07-20 01:45:42,322 - INFO - Database schema initialized successfully
2025-07-20 01:45:42,322 - INFO - DB Operation: {"timestamp": "2025-07-20 01:45:42", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:45:42,323 - INFO - DB Operation: {"timestamp": "2025-07-20 01:45:42", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:45:42,323 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 01:45:42,323 - INFO - Stats preloader initialized
2025-07-20 01:45:42,324 - INFO - Starting stats data preloading
2025-07-20 01:45:42,324 - INFO - Started stats data preloading
2025-07-20 01:45:42,324 - INFO - Loading data using optimized functions
2025-07-20 01:45:42,325 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:45:42,328 - INFO - DB Operation: {"timestamp": "2025-07-20 01:45:42", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:45:42,334 - INFO - Preloaded weekly stats for 7 days
2025-07-20 01:45:42,334 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:45:42,335 - INFO - DB Operation: {"timestamp": "2025-07-20 01:45:42", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:45:42,337 - INFO - Preloaded summary data
2025-07-20 01:45:42,338 - INFO - Loaded 9 items from cache
2025-07-20 01:45:42,338 - INFO - Preloaded game history (10 records)
2025-07-20 01:45:42,339 - INFO - Preloaded wallet data
2025-07-20 01:45:42,339 - INFO - Started background data loading
2025-07-20 01:45:42,339 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 01:45:42,340 - INFO - OptimizedStatsLoader initialized
2025-07-20 01:45:42,340 - INFO - Using optimized stats loader for integration
2025-07-20 01:45:42,342 - INFO - Saved 9 items to persistent cache
2025-07-20 01:45:42,346 - INFO - Loaded weekly stats for 7 days
2025-07-20 01:45:42,347 - INFO - Loaded summary data
2025-07-20 01:45:42,348 - INFO - Loaded game history page 0 (10 records)
2025-07-20 01:45:42,348 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 01:45:42,351 - INFO - Saved 9 items to cache
2025-07-20 01:45:42,351 - INFO - Background data loading completed
2025-07-20 01:45:42,355 - INFO - Database schema initialized successfully
2025-07-20 01:45:42,359 - INFO - Database schema initialized successfully
2025-07-20 01:45:42,364 - INFO - Stats database initialized successfully
2025-07-20 01:45:42,365 - INFO - Game stats integration module available
2025-07-20 01:45:42,365 - INFO - Started stats event worker thread
2025-07-20 01:45:42,365 - INFO - Stats event hooks initialized
2025-07-20 01:45:42,936 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 01:45:42,937 - INFO - Stats performance monitor initialized
2025-07-20 01:45:42,995 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 01:55:28,158 - INFO - Loaded 0 items from persistent cache
2025-07-20 01:55:28,159 - INFO - Stats cache initialized
2025-07-20 01:55:28,160 - INFO - Database security initialized successfully
2025-07-20 01:55:28,160 - INFO - DB Operation: {"timestamp": "2025-07-20 01:55:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:55:28,160 - INFO - Created secure database connection
2025-07-20 01:55:28,184 - INFO - Database schema initialized successfully
2025-07-20 01:55:28,185 - INFO - DB Operation: {"timestamp": "2025-07-20 01:55:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:55:28,186 - INFO - DB Operation: {"timestamp": "2025-07-20 01:55:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:55:28,186 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 01:55:28,186 - INFO - Stats preloader initialized
2025-07-20 01:55:28,187 - INFO - Starting stats data preloading
2025-07-20 01:55:28,187 - INFO - Started stats data preloading
2025-07-20 01:55:28,188 - INFO - Loading data using optimized functions
2025-07-20 01:55:28,188 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:55:28,188 - INFO - DB Operation: {"timestamp": "2025-07-20 01:55:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:55:28,201 - INFO - Preloaded weekly stats for 7 days
2025-07-20 01:55:28,201 - WARNING - Connection validation failed, creating new connection
2025-07-20 01:55:28,202 - INFO - Loaded 0 items from cache
2025-07-20 01:55:28,203 - INFO - DB Operation: {"timestamp": "2025-07-20 01:55:28", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 01:55:28,203 - INFO - Started background data loading
2025-07-20 01:55:28,203 - INFO - OptimizedStatsLoader initialized
2025-07-20 01:55:28,203 - INFO - Using optimized stats loader for integration
2025-07-20 01:55:28,204 - INFO - Loaded weekly stats for 7 days
2025-07-20 01:55:28,205 - INFO - Preloaded summary data
2025-07-20 01:55:28,206 - INFO - Loaded summary data
2025-07-20 01:55:28,206 - INFO - Preloaded game history (10 records)
2025-07-20 01:55:28,207 - INFO - Loaded game history page 0 (10 records)
2025-07-20 01:55:28,207 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 01:55:28,207 - INFO - Preloaded wallet data
2025-07-20 01:55:28,208 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 01:55:28,211 - INFO - Saved 8 items to persistent cache
2025-07-20 01:55:28,211 - INFO - Saved 7 items to cache
2025-07-20 01:55:28,211 - INFO - Background data loading completed
2025-07-20 01:55:28,219 - INFO - Database schema initialized successfully
2025-07-20 01:55:28,221 - INFO - Database schema initialized successfully
2025-07-20 01:55:28,224 - INFO - Stats database initialized successfully
2025-07-20 01:55:28,224 - INFO - Game stats integration module available
2025-07-20 01:55:28,225 - INFO - Started stats event worker thread
2025-07-20 01:55:28,225 - INFO - Stats event hooks initialized
2025-07-20 01:55:28,852 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 01:55:28,852 - INFO - Stats performance monitor initialized
2025-07-20 01:55:28,907 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 02:35:59,737 - INFO - Loaded 0 items from persistent cache
2025-07-20 02:35:59,738 - INFO - Stats cache initialized
2025-07-20 02:35:59,738 - INFO - Database security initialized successfully
2025-07-20 02:35:59,739 - INFO - DB Operation: {"timestamp": "2025-07-20 02:35:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:35:59,739 - INFO - Created secure database connection
2025-07-20 02:35:59,750 - INFO - Database schema initialized successfully
2025-07-20 02:35:59,751 - INFO - DB Operation: {"timestamp": "2025-07-20 02:35:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:35:59,751 - INFO - DB Operation: {"timestamp": "2025-07-20 02:35:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:35:59,752 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 02:35:59,752 - INFO - Stats preloader initialized
2025-07-20 02:35:59,753 - INFO - Starting stats data preloading
2025-07-20 02:35:59,753 - INFO - Started stats data preloading
2025-07-20 02:35:59,753 - INFO - Loading data using optimized functions
2025-07-20 02:35:59,753 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:35:59,754 - INFO - DB Operation: {"timestamp": "2025-07-20 02:35:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:35:59,764 - INFO - Preloaded weekly stats for 7 days
2025-07-20 02:35:59,765 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:35:59,765 - INFO - DB Operation: {"timestamp": "2025-07-20 02:35:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:35:59,766 - INFO - Loaded 0 items from cache
2025-07-20 02:35:59,767 - INFO - Started background data loading
2025-07-20 02:35:59,767 - INFO - OptimizedStatsLoader initialized
2025-07-20 02:35:59,767 - INFO - Using optimized stats loader for integration
2025-07-20 02:35:59,768 - INFO - Preloaded summary data
2025-07-20 02:35:59,769 - INFO - Loaded weekly stats for 7 days
2025-07-20 02:35:59,769 - INFO - Preloaded game history (10 records)
2025-07-20 02:35:59,770 - INFO - Loaded summary data
2025-07-20 02:35:59,770 - INFO - Preloaded wallet data
2025-07-20 02:35:59,770 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 02:35:59,770 - INFO - Loaded game history page 0 (10 records)
2025-07-20 02:35:59,771 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 02:35:59,773 - INFO - Saved 8 items to persistent cache
2025-07-20 02:35:59,773 - INFO - Saved 7 items to cache
2025-07-20 02:35:59,774 - INFO - Background data loading completed
2025-07-20 02:35:59,775 - INFO - Database schema initialized successfully
2025-07-20 02:35:59,777 - INFO - Database schema initialized successfully
2025-07-20 02:35:59,778 - INFO - Stats database initialized successfully
2025-07-20 02:35:59,778 - INFO - Game stats integration module available
2025-07-20 02:35:59,779 - INFO - Started stats event worker thread
2025-07-20 02:35:59,779 - INFO - Stats event hooks initialized
2025-07-20 02:36:00,370 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 02:36:00,370 - INFO - Stats performance monitor initialized
2025-07-20 02:36:00,420 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 02:36:54,647 - ERROR - Error loading persistent cache: Extra data: line 1 column 7061 (char 7060)
2025-07-20 02:36:54,647 - INFO - Stats cache initialized
2025-07-20 02:36:54,647 - INFO - Database security initialized successfully
2025-07-20 02:36:54,648 - INFO - DB Operation: {"timestamp": "2025-07-20 02:36:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:36:54,648 - INFO - Created secure database connection
2025-07-20 02:36:54,650 - INFO - Database schema initialized successfully
2025-07-20 02:36:54,650 - INFO - DB Operation: {"timestamp": "2025-07-20 02:36:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:36:54,651 - INFO - DB Operation: {"timestamp": "2025-07-20 02:36:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:36:54,651 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 02:36:54,651 - INFO - Stats preloader initialized
2025-07-20 02:36:54,652 - INFO - Starting stats data preloading
2025-07-20 02:36:54,652 - INFO - Started stats data preloading
2025-07-20 02:36:54,652 - INFO - Loading data using optimized functions
2025-07-20 02:36:54,653 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:36:54,653 - INFO - DB Operation: {"timestamp": "2025-07-20 02:36:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:36:54,661 - INFO - Preloaded weekly stats for 7 days
2025-07-20 02:36:54,661 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:36:54,662 - INFO - DB Operation: {"timestamp": "2025-07-20 02:36:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:36:54,662 - ERROR - Error loading cache: Extra data: line 1 column 7061 (char 7060)
2025-07-20 02:36:54,663 - INFO - Started background data loading
2025-07-20 02:36:54,663 - INFO - OptimizedStatsLoader initialized
2025-07-20 02:36:54,664 - INFO - Using optimized stats loader for integration
2025-07-20 02:36:54,664 - INFO - Preloaded summary data
2025-07-20 02:36:54,665 - INFO - Loaded weekly stats for 7 days
2025-07-20 02:36:54,666 - INFO - Preloaded game history (10 records)
2025-07-20 02:36:54,666 - INFO - Preloaded wallet data
2025-07-20 02:36:54,667 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 02:36:54,667 - INFO - Loaded summary data
2025-07-20 02:36:54,670 - INFO - Loaded game history page 0 (10 records)
2025-07-20 02:36:54,670 - INFO - Saved 8 items to persistent cache
2025-07-20 02:36:54,670 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 02:36:54,672 - INFO - Saved 7 items to cache
2025-07-20 02:36:54,672 - INFO - Background data loading completed
2025-07-20 02:36:54,673 - INFO - Database schema initialized successfully
2025-07-20 02:36:54,677 - INFO - Database schema initialized successfully
2025-07-20 02:36:54,679 - INFO - Stats database initialized successfully
2025-07-20 02:36:54,680 - INFO - Game stats integration module available
2025-07-20 02:36:54,684 - INFO - Started stats event worker thread
2025-07-20 02:36:54,684 - INFO - Stats event hooks initialized
2025-07-20 02:36:55,353 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 02:36:55,353 - INFO - Stats performance monitor initialized
2025-07-20 02:36:55,405 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 02:37:51,062 - INFO - Loaded 7 items from persistent cache
2025-07-20 02:37:51,062 - INFO - Stats cache initialized
2025-07-20 02:37:51,063 - INFO - Database security initialized successfully
2025-07-20 02:37:51,064 - INFO - DB Operation: {"timestamp": "2025-07-20 02:37:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:37:51,064 - INFO - Created secure database connection
2025-07-20 02:37:51,077 - INFO - Database schema initialized successfully
2025-07-20 02:37:51,078 - INFO - DB Operation: {"timestamp": "2025-07-20 02:37:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:37:51,083 - INFO - DB Operation: {"timestamp": "2025-07-20 02:37:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:37:51,084 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 02:37:51,084 - INFO - Stats preloader initialized
2025-07-20 02:37:51,086 - INFO - Starting stats data preloading
2025-07-20 02:37:51,086 - INFO - Started stats data preloading
2025-07-20 02:37:51,086 - INFO - Loading data using optimized functions
2025-07-20 02:37:51,090 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:37:51,090 - INFO - DB Operation: {"timestamp": "2025-07-20 02:37:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:37:51,101 - INFO - Preloaded weekly stats for 7 days
2025-07-20 02:37:51,101 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:37:51,102 - INFO - DB Operation: {"timestamp": "2025-07-20 02:37:51", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:37:51,108 - INFO - Loaded 7 items from cache
2025-07-20 02:37:51,108 - INFO - Preloaded summary data
2025-07-20 02:37:51,110 - INFO - Started background data loading
2025-07-20 02:37:51,111 - INFO - OptimizedStatsLoader initialized
2025-07-20 02:37:51,112 - INFO - Using optimized stats loader for integration
2025-07-20 02:37:51,114 - INFO - Preloaded game history (10 records)
2025-07-20 02:37:51,115 - INFO - Loaded weekly stats for 7 days
2025-07-20 02:37:51,116 - INFO - Preloaded wallet data
2025-07-20 02:37:51,116 - INFO - Stats data preloaded successfully in 0.03 seconds
2025-07-20 02:37:51,117 - INFO - Loaded summary data
2025-07-20 02:37:51,119 - INFO - Saved 9 items to persistent cache
2025-07-20 02:37:51,123 - INFO - Loaded game history page 0 (10 records)
2025-07-20 02:37:51,124 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 02:37:51,127 - INFO - Saved 7 items to cache
2025-07-20 02:37:51,127 - INFO - Background data loading completed
2025-07-20 02:37:51,131 - INFO - Database schema initialized successfully
2025-07-20 02:37:51,134 - INFO - Database schema initialized successfully
2025-07-20 02:37:51,135 - INFO - Stats database initialized successfully
2025-07-20 02:37:51,135 - INFO - Game stats integration module available
2025-07-20 02:37:51,136 - INFO - Started stats event worker thread
2025-07-20 02:37:51,136 - INFO - Stats event hooks initialized
2025-07-20 02:37:52,416 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 02:37:52,416 - INFO - Stats performance monitor initialized
2025-07-20 02:37:52,598 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 02:53:06,649 - INFO - Loaded 0 items from persistent cache
2025-07-20 02:53:06,650 - INFO - Stats cache initialized
2025-07-20 02:53:06,650 - INFO - Database security initialized successfully
2025-07-20 02:53:06,651 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:06,651 - INFO - Created secure database connection
2025-07-20 02:53:06,654 - INFO - Database schema initialized successfully
2025-07-20 02:53:06,655 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:06,655 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:06,655 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 02:53:06,655 - INFO - Stats preloader initialized
2025-07-20 02:53:06,658 - INFO - Starting stats data preloading
2025-07-20 02:53:06,659 - INFO - Started stats data preloading
2025-07-20 02:53:06,659 - INFO - Loading data using optimized functions
2025-07-20 02:53:06,660 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:53:06,660 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:06,669 - INFO - Preloaded weekly stats for 7 days
2025-07-20 02:53:06,669 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:53:06,670 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:06", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:06,672 - INFO - Preloaded summary data
2025-07-20 02:53:06,675 - INFO - Preloaded game history (10 records)
2025-07-20 02:53:06,675 - INFO - Preloaded wallet data
2025-07-20 02:53:06,675 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 02:53:06,677 - INFO - Loaded 0 items from cache
2025-07-20 02:53:06,678 - INFO - Saved 8 items to persistent cache
2025-07-20 02:53:06,681 - INFO - Started background data loading
2025-07-20 02:53:06,681 - INFO - OptimizedStatsLoader initialized
2025-07-20 02:53:06,681 - INFO - Using optimized stats loader for integration
2025-07-20 02:53:06,683 - INFO - Loaded weekly stats for 7 days
2025-07-20 02:53:06,683 - INFO - Loaded summary data
2025-07-20 02:53:06,684 - INFO - Loaded game history page 0 (10 records)
2025-07-20 02:53:06,684 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 02:53:06,686 - INFO - Saved 7 items to cache
2025-07-20 02:53:06,687 - INFO - Background data loading completed
2025-07-20 02:53:06,690 - INFO - Database schema initialized successfully
2025-07-20 02:53:06,693 - INFO - Database schema initialized successfully
2025-07-20 02:53:06,694 - INFO - Stats database initialized successfully
2025-07-20 02:53:06,694 - INFO - Game stats integration module available
2025-07-20 02:53:06,696 - INFO - Started stats event worker thread
2025-07-20 02:53:06,696 - INFO - Stats event hooks initialized
2025-07-20 02:53:07,276 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 02:53:07,276 - INFO - Stats performance monitor initialized
2025-07-20 02:53:07,321 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 02:53:59,391 - INFO - Loaded 7 items from persistent cache
2025-07-20 02:53:59,391 - INFO - Stats cache initialized
2025-07-20 02:53:59,393 - INFO - Database security initialized successfully
2025-07-20 02:53:59,394 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:59,394 - INFO - Created secure database connection
2025-07-20 02:53:59,397 - INFO - Database schema initialized successfully
2025-07-20 02:53:59,397 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:59,398 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:59,398 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 02:53:59,398 - INFO - Stats preloader initialized
2025-07-20 02:53:59,403 - INFO - Starting stats data preloading
2025-07-20 02:53:59,403 - INFO - Started stats data preloading
2025-07-20 02:53:59,403 - INFO - Loading data using optimized functions
2025-07-20 02:53:59,424 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:53:59,425 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:59,442 - INFO - Preloaded weekly stats for 7 days
2025-07-20 02:53:59,444 - WARNING - Connection validation failed, creating new connection
2025-07-20 02:53:59,445 - INFO - DB Operation: {"timestamp": "2025-07-20 02:53:59", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 02:53:59,448 - INFO - Preloaded summary data
2025-07-20 02:53:59,449 - INFO - Preloaded game history (10 records)
2025-07-20 02:53:59,451 - INFO - Preloaded wallet data
2025-07-20 02:53:59,451 - INFO - Stats data preloaded successfully in 0.05 seconds
2025-07-20 02:53:59,453 - INFO - Saved 9 items to persistent cache
2025-07-20 02:53:59,597 - INFO - Loaded 9 items from cache
2025-07-20 02:53:59,657 - INFO - Started background data loading
2025-07-20 02:53:59,657 - INFO - OptimizedStatsLoader initialized
2025-07-20 02:53:59,657 - INFO - Using optimized stats loader for integration
2025-07-20 02:53:59,658 - INFO - Loaded weekly stats for 7 days
2025-07-20 02:53:59,659 - INFO - Loaded summary data
2025-07-20 02:53:59,660 - INFO - Loaded game history page 0 (10 records)
2025-07-20 02:53:59,660 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 02:53:59,662 - INFO - Saved 9 items to cache
2025-07-20 02:53:59,662 - INFO - Background data loading completed
2025-07-20 02:53:59,683 - INFO - Database schema initialized successfully
2025-07-20 02:53:59,686 - INFO - Database schema initialized successfully
2025-07-20 02:53:59,687 - INFO - Stats database initialized successfully
2025-07-20 02:53:59,688 - INFO - Game stats integration module available
2025-07-20 02:53:59,688 - INFO - Started stats event worker thread
2025-07-20 02:53:59,689 - INFO - Stats event hooks initialized
2025-07-20 02:54:00,486 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 02:54:00,486 - INFO - Stats performance monitor initialized
2025-07-20 02:54:00,564 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 03:09:11,846 - INFO - Loaded 0 items from persistent cache
2025-07-20 03:09:11,847 - INFO - Stats cache initialized
2025-07-20 03:09:11,847 - INFO - Database security initialized successfully
2025-07-20 03:09:11,847 - INFO - DB Operation: {"timestamp": "2025-07-20 03:09:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:09:11,848 - INFO - Created secure database connection
2025-07-20 03:09:11,850 - INFO - Database schema initialized successfully
2025-07-20 03:09:11,851 - INFO - DB Operation: {"timestamp": "2025-07-20 03:09:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:09:11,852 - INFO - DB Operation: {"timestamp": "2025-07-20 03:09:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:09:11,853 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:09:11,853 - INFO - Stats preloader initialized
2025-07-20 03:09:11,854 - INFO - Starting stats data preloading
2025-07-20 03:09:11,854 - INFO - Started stats data preloading
2025-07-20 03:09:11,855 - INFO - Loading data using optimized functions
2025-07-20 03:09:11,855 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:09:11,855 - INFO - DB Operation: {"timestamp": "2025-07-20 03:09:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,677 - INFO - Loaded 0 items from persistent cache
2025-07-20 03:14:21,678 - INFO - Stats cache initialized
2025-07-20 03:14:21,678 - INFO - Database security initialized successfully
2025-07-20 03:14:21,680 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,680 - INFO - Created secure database connection
2025-07-20 03:14:21,684 - INFO - Database schema initialized successfully
2025-07-20 03:14:21,685 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,685 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,685 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:14:21,686 - INFO - Stats preloader initialized
2025-07-20 03:14:21,687 - INFO - Starting stats data preloading
2025-07-20 03:14:21,688 - INFO - Started stats data preloading
2025-07-20 03:14:21,688 - INFO - Loading data using optimized functions
2025-07-20 03:14:21,688 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:14:21,689 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,691 - INFO - Preload thread already running
2025-07-20 03:14:21,694 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:14:21,695 - INFO - Preloaded summary data
2025-07-20 03:14:21,695 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:14:21,695 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,698 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:14:21,698 - INFO - Preloaded game history (10 records)
2025-07-20 03:14:21,699 - INFO - DB Operation: {"timestamp": "2025-07-20 03:14:21", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:14:21,699 - INFO - Preloaded wallet data
2025-07-20 03:14:21,699 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 03:14:21,701 - INFO - Saved 8 items to persistent cache
2025-07-20 03:16:05,497 - INFO - Loaded 8 items from persistent cache
2025-07-20 03:16:05,497 - INFO - Stats cache initialized
2025-07-20 03:16:05,498 - INFO - Database security initialized successfully
2025-07-20 03:16:05,499 - INFO - DB Operation: {"timestamp": "2025-07-20 03:16:05", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:16:05,499 - INFO - Created secure database connection
2025-07-20 03:16:05,534 - INFO - Database schema initialized successfully
2025-07-20 03:16:05,535 - INFO - DB Operation: {"timestamp": "2025-07-20 03:16:05", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:16:05,535 - INFO - DB Operation: {"timestamp": "2025-07-20 03:16:05", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:16:05,536 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:16:05,536 - INFO - Stats preloader initialized
2025-07-20 03:16:05,536 - INFO - Starting stats data preloading
2025-07-20 03:16:05,536 - INFO - Started stats data preloading
2025-07-20 03:16:05,536 - INFO - Loading data using optimized functions
2025-07-20 03:16:05,537 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:16:05,537 - INFO - DB Operation: {"timestamp": "2025-07-20 03:16:05", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:16:05,548 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:16:05,548 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:16:05,548 - INFO - DB Operation: {"timestamp": "2025-07-20 03:16:05", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:16:05,549 - INFO - Loaded 8 items from cache
2025-07-20 03:16:05,549 - INFO - Started background data loading
2025-07-20 03:16:05,549 - INFO - OptimizedStatsLoader initialized
2025-07-20 03:16:05,549 - INFO - Using optimized stats loader for integration
2025-07-20 03:16:05,551 - INFO - Preloaded summary data
2025-07-20 03:16:05,551 - INFO - Loaded weekly stats for 7 days
2025-07-20 03:16:05,551 - INFO - Loaded summary data
2025-07-20 03:16:05,552 - INFO - Preloaded game history (10 records)
2025-07-20 03:16:05,552 - INFO - Loaded game history page 0 (10 records)
2025-07-20 03:16:05,553 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 03:16:05,553 - INFO - Preloaded wallet data
2025-07-20 03:16:05,553 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 03:16:05,556 - INFO - Saved 9 items to cache
2025-07-20 03:16:05,556 - INFO - Background data loading completed
2025-07-20 03:16:05,556 - INFO - Saved 8 items to persistent cache
2025-07-20 03:16:05,558 - INFO - Database schema initialized successfully
2025-07-20 03:16:05,560 - INFO - Database schema initialized successfully
2025-07-20 03:16:05,562 - INFO - Stats database initialized successfully
2025-07-20 03:16:05,562 - INFO - Game stats integration module available
2025-07-20 03:16:05,563 - INFO - Started stats event worker thread
2025-07-20 03:16:05,563 - INFO - Stats event hooks initialized
2025-07-20 03:16:06,314 - INFO - Loaded 4 valid metrics from data\metrics\stats_metrics.json
2025-07-20 03:16:06,314 - INFO - Stats performance monitor initialized
2025-07-20 03:16:06,382 - ERROR - Error loading admin config: [Errno 2] No such file or directory: 'config/admin_config.json', using defaults
2025-07-20 03:17:04,385 - INFO - Loaded 8 items from persistent cache
2025-07-20 03:17:04,386 - INFO - Stats cache initialized
2025-07-20 03:17:04,388 - INFO - Database security initialized successfully
2025-07-20 03:17:04,389 - INFO - DB Operation: {"timestamp": "2025-07-20 03:17:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:17:04,389 - INFO - Created secure database connection
2025-07-20 03:17:04,392 - INFO - Database schema initialized successfully
2025-07-20 03:17:04,393 - INFO - DB Operation: {"timestamp": "2025-07-20 03:17:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:17:04,393 - INFO - DB Operation: {"timestamp": "2025-07-20 03:17:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:17:04,393 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:17:04,393 - INFO - Stats preloader initialized
2025-07-20 03:17:04,394 - INFO - Starting stats data preloading
2025-07-20 03:17:04,394 - INFO - Started stats data preloading
2025-07-20 03:17:04,394 - INFO - Loading data using optimized functions
2025-07-20 03:17:04,395 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:17:04,395 - INFO - DB Operation: {"timestamp": "2025-07-20 03:17:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:17:04,400 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:17:04,400 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:17:04,401 - INFO - DB Operation: {"timestamp": "2025-07-20 03:17:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:17:04,403 - INFO - Preloaded summary data
2025-07-20 03:17:04,404 - INFO - Preloaded game history (10 records)
2025-07-20 03:17:04,405 - INFO - Preloaded wallet data
2025-07-20 03:17:04,405 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 03:17:04,408 - INFO - Saved 8 items to persistent cache
2025-07-20 03:18:01,927 - INFO - Loaded 8 items from persistent cache
2025-07-20 03:18:01,927 - INFO - Stats cache initialized
2025-07-20 03:18:01,928 - INFO - Database security initialized successfully
2025-07-20 03:18:01,928 - INFO - DB Operation: {"timestamp": "2025-07-20 03:18:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:18:01,929 - INFO - Created secure database connection
2025-07-20 03:18:01,932 - INFO - Database schema initialized successfully
2025-07-20 03:18:01,932 - INFO - DB Operation: {"timestamp": "2025-07-20 03:18:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:18:01,933 - INFO - DB Operation: {"timestamp": "2025-07-20 03:18:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:18:01,933 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:18:01,933 - INFO - Stats preloader initialized
2025-07-20 03:18:01,934 - INFO - Starting stats data preloading
2025-07-20 03:18:01,936 - INFO - Started stats data preloading
2025-07-20 03:18:01,936 - INFO - Loading data using optimized functions
2025-07-20 03:18:01,936 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:18:01,936 - INFO - Preload thread already running
2025-07-20 03:18:01,937 - INFO - DB Operation: {"timestamp": "2025-07-20 03:18:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:18:01,946 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:18:01,946 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:18:01,947 - INFO - DB Operation: {"timestamp": "2025-07-20 03:18:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:18:01,949 - INFO - Preloaded summary data
2025-07-20 03:22:26,543 - INFO - Loaded 0 items from persistent cache
2025-07-20 03:22:26,544 - INFO - Stats cache initialized
2025-07-20 03:22:26,544 - INFO - Database security initialized successfully
2025-07-20 03:22:26,545 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:26", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:26,545 - INFO - Created secure database connection
2025-07-20 03:22:26,548 - INFO - Database schema initialized successfully
2025-07-20 03:22:26,548 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:26", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:26,549 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:26", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:26,549 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:22:26,549 - INFO - Stats preloader initialized
2025-07-20 03:22:26,550 - INFO - Starting stats data preloading
2025-07-20 03:22:26,550 - INFO - Started stats data preloading
2025-07-20 03:22:26,550 - INFO - Loading data using optimized functions
2025-07-20 03:22:26,551 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:22:26,551 - INFO - Preload thread already running
2025-07-20 03:22:26,551 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:26", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:26,559 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:22:26,559 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:22:26,560 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:26", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:26,561 - INFO - Preloaded summary data
2025-07-20 03:22:26,562 - INFO - Preloaded game history (10 records)
2025-07-20 03:22:26,563 - INFO - Preloaded wallet data
2025-07-20 03:22:26,563 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 03:22:26,566 - INFO - Saved 8 items to persistent cache
2025-07-20 03:22:39,494 - INFO - Loaded 8 items from persistent cache
2025-07-20 03:22:39,494 - INFO - Stats cache initialized
2025-07-20 03:22:39,495 - INFO - Database security initialized successfully
2025-07-20 03:22:39,495 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:39,495 - INFO - Created secure database connection
2025-07-20 03:22:39,497 - INFO - Database schema initialized successfully
2025-07-20 03:22:39,497 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:39,498 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:39,498 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:22:39,498 - INFO - Stats preloader initialized
2025-07-20 03:22:39,499 - INFO - Starting stats data preloading
2025-07-20 03:22:39,499 - INFO - Started stats data preloading
2025-07-20 03:22:39,499 - INFO - Loading data using optimized functions
2025-07-20 03:22:39,499 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:22:39,500 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:39,505 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:22:39,506 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:22:39,506 - INFO - DB Operation: {"timestamp": "2025-07-20 03:22:39", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:22:39,508 - INFO - Preloaded summary data
2025-07-20 03:52:49,437 - INFO - Loaded 0 items from persistent cache
2025-07-20 03:52:49,438 - INFO - Stats cache initialized
2025-07-20 03:52:49,438 - INFO - Database security initialized successfully
2025-07-20 03:52:49,440 - INFO - DB Operation: {"timestamp": "2025-07-20 03:52:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:52:49,440 - INFO - Created secure database connection
2025-07-20 03:52:49,443 - INFO - Database schema initialized successfully
2025-07-20 03:52:49,443 - INFO - DB Operation: {"timestamp": "2025-07-20 03:52:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:52:49,443 - INFO - DB Operation: {"timestamp": "2025-07-20 03:52:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:52:49,443 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:52:49,443 - INFO - Stats preloader initialized
2025-07-20 03:52:49,444 - INFO - Starting stats data preloading
2025-07-20 03:52:49,444 - INFO - Started stats data preloading
2025-07-20 03:52:49,445 - INFO - Loading data using optimized functions
2025-07-20 03:52:49,445 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:52:49,446 - INFO - DB Operation: {"timestamp": "2025-07-20 03:52:49", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:52:49,447 - INFO - Preload thread already running
2025-07-20 03:52:49,451 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:52:49,452 - INFO - Preloaded summary data
2025-07-20 03:52:49,452 - INFO - Preloaded game history (10 records)
2025-07-20 03:52:49,453 - INFO - Preloaded wallet data
2025-07-20 03:52:49,453 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 03:52:49,455 - INFO - Saved 8 items to persistent cache
2025-07-20 03:53:54,855 - INFO - Loaded 8 items from persistent cache
2025-07-20 03:53:54,855 - INFO - Stats cache initialized
2025-07-20 03:53:54,856 - INFO - Database security initialized successfully
2025-07-20 03:53:54,856 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,856 - INFO - Created secure database connection
2025-07-20 03:53:54,859 - INFO - Database schema initialized successfully
2025-07-20 03:53:54,859 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,860 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,860 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 03:53:54,860 - INFO - Stats preloader initialized
2025-07-20 03:53:54,862 - INFO - Starting stats data preloading
2025-07-20 03:53:54,862 - INFO - Started stats data preloading
2025-07-20 03:53:54,863 - INFO - Loading data using optimized functions
2025-07-20 03:53:54,863 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:53:54,864 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,875 - INFO - Preloaded weekly stats for 7 days
2025-07-20 03:53:54,875 - INFO - Preload thread already running
2025-07-20 03:53:54,876 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:53:54,876 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:53:54,876 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,876 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,880 - INFO - Preloaded summary data
2025-07-20 03:53:54,880 - WARNING - Connection validation failed, creating new connection
2025-07-20 03:53:54,881 - INFO - DB Operation: {"timestamp": "2025-07-20 03:53:54", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 03:53:54,887 - INFO - Preloaded game history (10 records)
2025-07-20 03:53:54,888 - INFO - Preloaded wallet data
2025-07-20 03:53:54,888 - INFO - Stats data preloaded successfully in 0.03 seconds
2025-07-20 03:53:54,892 - INFO - Saved 8 items to persistent cache
2025-07-20 03:53:54,925 - INFO - Loaded 8 items from cache
2025-07-20 03:53:54,926 - INFO - Started background data loading
2025-07-20 03:53:54,926 - INFO - OptimizedStatsLoader initialized
2025-07-20 03:53:54,926 - INFO - Using optimized stats loader for integration
2025-07-20 03:53:54,930 - INFO - Loaded weekly stats for 7 days
2025-07-20 03:53:54,936 - INFO - Loaded summary data
2025-07-20 03:53:54,937 - INFO - Loaded game history page 0 (10 records)
2025-07-20 03:53:54,937 - INFO - Loaded game history metadata (total pages: 7)
2025-07-20 03:53:54,942 - INFO - Saved 9 items to cache
2025-07-20 03:53:54,942 - INFO - Background data loading completed
2025-07-20 03:53:54,961 - INFO - Database schema initialized successfully
2025-07-20 03:53:54,970 - INFO - Database schema initialized successfully
2025-07-20 03:53:54,976 - INFO - Stats database initialized successfully
2025-07-20 03:53:54,976 - INFO - Game stats integration module available
2025-07-20 03:53:54,976 - INFO - Started stats event worker thread
2025-07-20 03:53:54,977 - INFO - Stats event hooks initialized
2025-07-20 04:06:08,533 - INFO - Loaded 0 items from persistent cache
2025-07-20 04:06:08,533 - INFO - Stats cache initialized
2025-07-20 04:06:08,534 - INFO - Database security initialized successfully
2025-07-20 04:06:08,534 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:08,534 - INFO - Created secure database connection
2025-07-20 04:06:08,644 - INFO - Database schema initialized successfully
2025-07-20 04:06:08,647 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:08,649 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:08,649 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 04:06:08,649 - INFO - Stats preloader initialized
2025-07-20 04:06:08,651 - INFO - Starting stats data preloading
2025-07-20 04:06:08,651 - INFO - Loading data using optimized functions
2025-07-20 04:06:08,651 - INFO - Started stats data preloading
2025-07-20 04:06:08,652 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:06:08,652 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:08,665 - INFO - Preloaded weekly stats for 7 days
2025-07-20 04:06:08,665 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:06:08,666 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:08,667 - INFO - Preloaded summary data
2025-07-20 04:06:08,668 - INFO - Preloaded game history (10 records)
2025-07-20 04:06:08,669 - INFO - Preloaded wallet data
2025-07-20 04:06:08,669 - INFO - Stats data preloaded successfully in 0.02 seconds
2025-07-20 04:06:08,675 - INFO - Saved 8 items to persistent cache
2025-07-20 04:06:08,957 - INFO - Saved 0 items to persistent cache
2025-07-20 04:06:08,958 - INFO - Cache cleared
2025-07-20 04:06:08,959 - INFO - Cleared all preloader cache data
2025-07-20 04:06:08,998 - INFO - Starting stats data preloading
2025-07-20 04:06:08,998 - INFO - Started stats data preloading
2025-07-20 04:06:08,999 - INFO - Loading data using optimized functions
2025-07-20 04:06:09,126 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:06:09,127 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:09,146 - INFO - Preloaded weekly stats for 7 days
2025-07-20 04:06:09,146 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:06:09,147 - INFO - DB Operation: {"timestamp": "2025-07-20 04:06:09", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:06:09,154 - INFO - Preloaded summary data
2025-07-20 04:06:09,210 - INFO - Preloaded game history (10 records)
2025-07-20 04:06:09,212 - INFO - Preloaded wallet data
2025-07-20 04:06:09,212 - INFO - Stats data preloaded successfully in 0.22 seconds
2025-07-20 04:06:09,227 - INFO - Saved 8 items to persistent cache
2025-07-20 04:07:01,745 - INFO - Loaded 8 items from persistent cache
2025-07-20 04:07:01,745 - INFO - Stats cache initialized
2025-07-20 04:07:01,746 - INFO - Database security initialized successfully
2025-07-20 04:07:01,746 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:01,746 - INFO - Created secure database connection
2025-07-20 04:07:01,749 - INFO - Database schema initialized successfully
2025-07-20 04:07:01,749 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:01,749 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:01,750 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 04:07:01,750 - INFO - Stats preloader initialized
2025-07-20 04:07:01,751 - INFO - Starting stats data preloading
2025-07-20 04:07:01,751 - INFO - Started stats data preloading
2025-07-20 04:07:01,751 - INFO - Loading data using optimized functions
2025-07-20 04:07:01,751 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:01,752 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:01,758 - INFO - Preloaded weekly stats for 7 days
2025-07-20 04:07:01,758 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:01,759 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:01", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:01,760 - INFO - Preloaded summary data
2025-07-20 04:07:01,762 - INFO - Preloaded game history (10 records)
2025-07-20 04:07:01,763 - INFO - Preloaded wallet data
2025-07-20 04:07:01,763 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 04:07:01,767 - INFO - Saved 8 items to persistent cache
2025-07-20 04:07:02,063 - INFO - Loaded 8 items from cache
2025-07-20 04:07:02,064 - INFO - Started background data loading
2025-07-20 04:07:02,064 - INFO - OptimizedStatsLoader initialized
2025-07-20 04:07:02,064 - INFO - Using optimized stats loader for integration
2025-07-20 04:07:02,066 - INFO - Loaded weekly stats for 7 days
2025-07-20 04:07:02,067 - INFO - Loaded summary data
2025-07-20 04:07:02,069 - INFO - Loaded game history page 0 (10 records)
2025-07-20 04:07:02,069 - INFO - Loaded game history metadata (total pages: 16)
2025-07-20 04:07:02,073 - INFO - Saved 9 items to cache
2025-07-20 04:07:02,073 - INFO - Background data loading completed
2025-07-20 04:07:02,076 - INFO - Database schema initialized successfully
2025-07-20 04:07:02,079 - INFO - Database schema initialized successfully
2025-07-20 04:07:02,079 - INFO - Stats database initialized successfully
2025-07-20 04:07:02,080 - INFO - Game stats integration module available
2025-07-20 04:07:02,081 - INFO - Started stats event worker thread
2025-07-20 04:07:02,081 - INFO - Stats event hooks initialized
2025-07-20 04:07:02,082 - INFO - Saved 0 items to persistent cache
2025-07-20 04:07:02,083 - INFO - Cache cleared
2025-07-20 04:07:02,083 - INFO - Cleared all preloader cache data
2025-07-20 04:07:02,083 - INFO - Starting stats data preloading
2025-07-20 04:07:02,083 - INFO - Started stats data preloading
2025-07-20 04:07:02,083 - INFO - Loading data using optimized functions
2025-07-20 04:07:02,084 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:02,084 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:02", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:02,087 - INFO - Preloaded weekly stats for 7 days
2025-07-20 04:07:02,087 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:02,087 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:02", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:02,090 - INFO - Preloaded summary data
2025-07-20 04:07:02,093 - INFO - Preloaded game history (10 records)
2025-07-20 04:07:02,094 - INFO - Preloaded wallet data
2025-07-20 04:07:02,095 - INFO - Stats data preloaded successfully in 0.01 seconds
2025-07-20 04:07:02,098 - INFO - Saved 8 items to persistent cache
2025-07-20 04:07:02,224 - INFO - BaseLoader initialized with cache directory: data/cache
2025-07-20 04:07:02,224 - INFO - PlaceholderGenerator initialized
2025-07-20 04:07:02,225 - INFO - Loaded cache metadata with 0 entries
2025-07-20 04:07:02,226 - INFO - Started cache cleanup thread
2025-07-20 04:07:02,226 - INFO - DiskCache initialized with max age: 3600s
2025-07-20 04:07:02,227 - INFO - Background worker started
2025-07-20 04:07:02,227 - INFO - BackgroundLoader initialized
2025-07-20 04:07:02,227 - INFO - UIUpdater initialized
2025-07-20 04:07:02,257 - INFO - Loaded placeholders from cache
2025-07-20 04:07:02,257 - INFO - Loaded placeholder data
2025-07-20 04:07:02,257 - INFO - Queued task weekly_stats
2025-07-20 04:07:02,259 - INFO - Queued task game_history_page_0
2025-07-20 04:07:02,259 - INFO - Queued task summary_stats
2025-07-20 04:07:02,259 - INFO - Started background loading
2025-07-20 04:07:02,259 - INFO - StatsLoader initialized
2025-07-20 04:07:02,260 - INFO - Saved cache metadata with 0 entries
2025-07-20 04:07:02,260 - INFO - Cleared 0 entries from cache in namespace stats
2025-07-20 04:07:02,260 - INFO - Cleared all function caches
2025-07-20 04:07:02,260 - INFO - Generated weekly stats placeholder
2025-07-20 04:07:02,261 - INFO - Generated game history placeholder with 10 records
2025-07-20 04:07:02,261 - INFO - Generated summary stats placeholder
2025-07-20 04:07:02,263 - INFO - Saved all placeholders to cache
2025-07-20 04:07:02,263 - INFO - Cleared all caches
2025-07-20 04:07:02,273 - INFO - Got weekly stats: 7 days
2025-07-20 04:07:02,274 - INFO - Weekly stats loaded
2025-07-20 04:07:02,274 - INFO - Task weekly_stats completed in 0.0145s
2025-07-20 04:07:02,276 - INFO - Got game history page 0: 10 records
2025-07-20 04:07:02,278 - INFO - Game history page 0 loaded
2025-07-20 04:07:02,278 - INFO - Task game_history_page_0 completed in 0.0016s
2025-07-20 04:07:02,279 - INFO - Got summary stats
2025-07-20 04:07:02,279 - INFO - Summary stats loaded
2025-07-20 04:07:02,279 - INFO - Task summary_stats completed in 0.0010s
2025-07-20 04:07:04,280 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:04,281 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:04,286 - INFO - Saved 0 items to persistent cache
2025-07-20 04:07:04,286 - INFO - Cache cleared
2025-07-20 04:07:04,286 - INFO - Cleared all preloader cache data
2025-07-20 04:07:04,286 - INFO - Starting stats data preloading
2025-07-20 04:07:04,286 - INFO - Started stats data preloading
2025-07-20 04:07:04,287 - INFO - Loading data using optimized functions
2025-07-20 04:07:04,287 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:07:04,287 - INFO - DB Operation: {"timestamp": "2025-07-20 04:07:04", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:07:04,289 - INFO - Preloaded weekly stats for 7 days
2025-07-20 04:07:04,289 - INFO - Preloaded summary data
2025-07-20 04:07:04,290 - INFO - Preloaded game history (10 records)
2025-07-20 04:07:04,290 - INFO - Preloaded wallet data
2025-07-20 04:07:04,290 - INFO - Stats data preloaded successfully in 0.00 seconds
2025-07-20 04:07:04,293 - INFO - Saved 8 items to persistent cache
2025-07-20 04:08:11,661 - INFO - Loaded 8 items from persistent cache
2025-07-20 04:08:11,661 - INFO - Stats cache initialized
2025-07-20 04:08:11,662 - INFO - Database security initialized successfully
2025-07-20 04:08:11,663 - INFO - DB Operation: {"timestamp": "2025-07-20 04:08:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:08:11,663 - INFO - Created secure database connection
2025-07-20 04:08:11,666 - INFO - Database schema initialized successfully
2025-07-20 04:08:11,666 - INFO - DB Operation: {"timestamp": "2025-07-20 04:08:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:08:11,667 - INFO - DB Operation: {"timestamp": "2025-07-20 04:08:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-07-20 04:08:11,667 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-07-20 04:08:11,667 - INFO - Stats preloader initialized
2025-07-20 04:08:11,669 - INFO - Starting stats data preloading
2025-07-20 04:08:11,669 - INFO - Started stats data preloading
2025-07-20 04:08:11,669 - INFO - Loading data using optimized functions
2025-07-20 04:08:11,670 - WARNING - Connection validation failed, creating new connection
2025-07-20 04:08:11,670 - INFO - Preload thread already running
2025-07-20 04:08:11,670 - INFO - DB Operation: {"timestamp": "2025-07-20 04:08:11", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
