@echo off
:: ================================================================
:: Bingo Game - Uv + Nuitka Build Script
:: ================================================================
:: This script installs dependencies using Uv and then builds the
:: game using the fixed Nuitka configuration
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

echo ================================================================
echo     WOW Bingo Game - Uv + Nuitka Build
echo ================================================================
echo.

:: Check if Python is available
python --version
if %errorlevel% neq 0 (
    echo Error: Python not found!
    pause
    exit /b 1
)

:: Check if Uv is installed
uv --version
if %errorlevel% neq 0 (
    echo Installing Uv package manager...
    pip install uv
    if %errorlevel% neq 0 (
        echo Error: Failed to install Uv!
        pause
        exit /b 1
    )
)

:: Install dependencies using Uv
echo.
echo Installing dependencies using Uv...
echo.

:: Core dependencies
uv pip install pygame pyperclip psutil pillow cryptography pydantic requests
if %errorlevel% neq 0 (
    echo Warning: Some core dependencies failed to install.
    echo The build may fail or have limited functionality.
)

:: Install Nuitka using Uv
echo.
echo Installing Nuitka using Uv...
uv pip install nuitka
if %errorlevel% neq 0 (
    echo Error: Failed to install Nuitka!
    pause
    exit /b 1
)

:: Install numpy using Uv (with specific version to avoid issues)
echo.
echo Installing NumPy using Uv...
uv pip install "numpy<2.0.0"
if %errorlevel% neq 0 (
    echo Warning: Failed to install NumPy.
    echo The build will continue with NumPy plugin disabled.
)

:: Clean up test files
echo.
echo Cleaning up test files...
python cleanup_test_files.py
if %errorlevel% neq 0 (
    echo Warning: Failed to clean up test files.
    echo The build will continue but may encounter issues.
)

:: Run the fixed Nuitka build
echo.
echo Starting Nuitka build with fixed configuration...
call nuitka_build_fixed.bat

echo.
echo Build process completed.
pause