@echo off
:: ================================================================
:: Bingo Game - Fixed MSVC Nuitka Build Script
:: ================================================================
:: This script compiles the Python bingo game using Nuitka with
:: fixed configuration that avoids numpy plugin issues
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PROJECT_NAME=WOW Bingo Game"
set "PROJECT_VERSION=1.6.2"
set "MAIN_SCRIPT=main.py"
set "ICON_PATH=assets\app_logo.ico"
set "BUILD_DIR=build_fixed"
set "OUTPUT_NAME=WOWBingoGame"

echo ================================================================
echo     WOW Bingo Game - Fixed MSVC Compilation
echo ================================================================
echo.

:: Check if main.py exists
if not exist "%MAIN_SCRIPT%" (
    echo Error: %MAIN_SCRIPT% not found!
    pause
    exit /b 1
)

:: Check if icon exists
if not exist "%ICON_PATH%" (
    echo Warning: Icon file %ICON_PATH% not found. Using default icon.
    set "ICON_PATH="
)

:: Clean previous build
if exist "%BUILD_DIR%" (
    echo Cleaning previous build directory...
    rd /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

:: Verify Python and Nuitka
echo Verifying Python and Nuitka installation...
python --version
if %errorlevel% neq 0 (
    echo Error: Python not found!
    pause
    exit /b 1
)

python -m nuitka --version
if %errorlevel% neq 0 (
    echo Error: Nuitka not found!
    pause
    exit /b 1
)

:: Check for required assets
echo Verifying required assets...
if not exist "assets" (
    echo Error: assets directory not found!
    pause
    exit /b 1
)

if not exist "data" (
    echo Warning: data directory not found. Creating empty data directory.
    mkdir "data"
)

:: Prepare build data using Python script
echo Preparing build data for Nuitka...
if exist "prepare_build_data.py" (
    python prepare_build_data.py
    if %errorlevel% neq 0 (
        echo Warning: Could not prepare build data. Build may fail.
    ) else (
        echo Build data preparation completed successfully.
    )
) else (
    echo Warning: prepare_build_data.py not found. Creating basic files manually...
    if not exist "data\admin_sessions.json" (
        echo Creating admin_sessions.json...
        echo {"sessions": {}, "last_cleanup": null, "session_timeout": 3600, "max_sessions": 10} > "data\admin_sessions.json"
    )
)

:: Clean app data for fresh build
echo Cleaning app data for fresh build...
if exist "clean_app.py" (
    python clean_app.py --silent
    if %errorlevel% neq 0 (
        echo Warning: Could not clean app data. Build will continue.
    ) else (
        echo App data cleaned successfully.
    )
)

:: Get CPU count for parallel compilation
set /a CPU_COUNT=%NUMBER_OF_PROCESSORS%
if %CPU_COUNT% lss 1 set CPU_COUNT=1
echo Using %CPU_COUNT% CPU cores for compilation...

echo.
echo Starting fixed MSVC Nuitka compilation...
echo This may take several minutes depending on your system.
echo.

:: Build the executable with fixed configuration that avoids numpy plugin issues
python -m nuitka ^
    --standalone ^
    --onefile ^
    --output-filename=%OUTPUT_NAME%.exe ^
    --output-dir=%BUILD_DIR% ^
    --windows-icon-from-ico=%ICON_PATH% ^
    --windows-company-name="%PROJECT_NAME%" ^
    --windows-product-name="%PROJECT_NAME%" ^
    --windows-file-version=%PROJECT_VERSION% ^
    --windows-product-version=%PROJECT_VERSION% ^
    --windows-file-description="WOW Bingo Game - Professional Bingo Gaming Application" ^
    --disable-console ^
    --assume-yes-for-downloads ^
    --include-data-dir=assets=assets ^
    --include-data-dir=data=data ^
    --include-package=pygame ^
    --include-package=json ^
    --include-package=datetime ^
    --include-package=sqlite3 ^
    --include-package=pyperclip ^
    --msvc=latest ^
    --jobs=%CPU_COUNT% ^
    --show-memory ^
    --show-progress ^
    --verbose ^
    --disable-plugin=numpy ^
    %MAIN_SCRIPT%

:: Check build result
if %errorlevel% neq 0 (
    echo.
    echo ================================================================
    echo     BUILD FAILED!
    echo ================================================================
    echo The compilation process encountered errors.
    echo Please check the output above for specific error messages.
    echo.
    pause
    exit /b 1
)

:: Success message
echo.
echo ================================================================
echo     BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.

:: Find the generated executable
set "EXECUTABLE_PATH=%BUILD_DIR%\%OUTPUT_NAME%.exe"
if exist "%EXECUTABLE_PATH%" (
    echo Executable created: %EXECUTABLE_PATH%

    :: Get file size
    for %%F in ("%EXECUTABLE_PATH%") do (
        set "FILE_SIZE=%%~zF"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1048576"
    )
    echo File size: !FILE_SIZE_MB! MB

    echo.
    echo Optimizations Applied:
    echo - MSVC compiler optimizations
    echo - Multi-threaded compilation
    echo - Standalone executable
    echo - Embedded assets and data
    echo - Production-ready build
    echo.

    echo The executable is ready for distribution!
    echo You can now run: %EXECUTABLE_PATH%
    echo.

    :: Ask if user wants to test the executable
    set /p "TEST_CHOICE=Do you want to test the executable now? (y/n): "
    if /i "!TEST_CHOICE!"=="y" (
        echo Starting the executable...
        start "" "%EXECUTABLE_PATH%"
    )

) else (
    echo Error: Executable not found at expected location!
    echo Check the %BUILD_DIR% directory for the executable.
)

echo.
echo Build process completed.
pause