@echo off
:: ================================================================
:: Bingo Game - Directory Build Script (No Onefile)
:: ================================================================
:: This script compiles the Python bingo game using Nuitka without
:: the onefile option, creating a directory with the executable
:: and all dependencies
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PROJECT_NAME=WOW Bingo Game"
set "PROJECT_VERSION=1.6.2"
set "MAIN_SCRIPT=main.py"
set "ICON_PATH=assets\app_logo.ico"
set "BUILD_DIR=build_directory"
set "OUTPUT_NAME=WOWBingoGame"

echo ================================================================
echo     WOW Bingo Game - Directory Build (No Onefile)
echo ================================================================
echo.

:: Check if main.py exists
if not exist "%MAIN_SCRIPT%" (
    echo Error: %MAIN_SCRIPT% not found!
    pause
    exit /b 1
)

:: Check if icon exists
if not exist "%ICON_PATH%" (
    echo Warning: Icon file %ICON_PATH% not found. Using default icon.
    set "ICON_PATH="
)

:: Clean previous build
if exist "%BUILD_DIR%" (
    echo Cleaning previous build directory...
    rd /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

:: Verify Python and Nuitka
echo Verifying Python and Nuitka installation...
python --version
if %errorlevel% neq 0 (
    echo Error: Python not found!
    pause
    exit /b 1
)

python -m nuitka --version
if %errorlevel% neq 0 (
    echo Error: Nuitka not found!
    pause
    exit /b 1
)

:: Get CPU count for parallel compilation
set /a CPU_COUNT=%NUMBER_OF_PROCESSORS%
if %CPU_COUNT% lss 1 set CPU_COUNT=1
echo Using %CPU_COUNT% CPU cores for compilation...

echo.
echo Starting directory build (no onefile)...
echo This may take several minutes depending on your system.
echo.

:: Build the executable WITHOUT the onefile option
python -m nuitka ^
    --standalone ^
    --output-dir=%BUILD_DIR% ^
    --windows-icon-from-ico=%ICON_PATH% ^
    --windows-company-name="%PROJECT_NAME%" ^
    --windows-product-name="%PROJECT_NAME%" ^
    --windows-file-version=%PROJECT_VERSION% ^
    --windows-product-version=%PROJECT_VERSION% ^
    --windows-file-description="WOW Bingo Game - Professional Bingo Gaming Application" ^
    --disable-console ^
    --assume-yes-for-downloads ^
    --include-data-dir=assets=assets ^
    --include-data-dir=data=data ^
    --include-package=pygame ^
    --include-package=json ^
    --include-package=datetime ^
    --include-package=sqlite3 ^
    --include-package=pyperclip ^
    --msvc=latest ^
    --jobs=%CPU_COUNT% ^
    --show-memory ^
    --show-progress ^
    --verbose ^
    --disable-plugin=numpy ^
    %MAIN_SCRIPT%

:: Check build result
if %errorlevel% neq 0 (
    echo.
    echo ================================================================
    echo     BUILD FAILED!
    echo ================================================================
    echo The compilation process encountered errors.
    echo Please check the output above for specific error messages.
    echo.
    pause
    exit /b 1
)

:: Success message
echo.
echo ================================================================
echo     BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.

:: Find the generated executable
set "EXECUTABLE_PATH=%BUILD_DIR%\%MAIN_SCRIPT%.exe"
if exist "%EXECUTABLE_PATH%" (
    echo Executable created: %EXECUTABLE_PATH%
    echo.
    echo To run the application:
    echo 1. Navigate to the %BUILD_DIR% directory
    echo 2. Run the %MAIN_SCRIPT%.exe file
    echo.
    echo Creating a shortcut for easy access...
    
    :: Create a shortcut to the executable
    echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
    echo sLinkFile = oWS.ExpandEnvironmentStrings("%USERPROFILE%\Desktop\WOW Bingo Game.lnk") >> CreateShortcut.vbs
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
    echo oLink.TargetPath = oWS.ExpandEnvironmentStrings("%CD%\%BUILD_DIR%\%MAIN_SCRIPT%.exe") >> CreateShortcut.vbs
    echo oLink.WorkingDirectory = oWS.ExpandEnvironmentStrings("%CD%\%BUILD_DIR%") >> CreateShortcut.vbs
    echo oLink.Description = "WOW Bingo Game" >> CreateShortcut.vbs
    echo oLink.IconLocation = oWS.ExpandEnvironmentStrings("%CD%\%ICON_PATH%") >> CreateShortcut.vbs
    echo oLink.Save >> CreateShortcut.vbs
    cscript //nologo CreateShortcut.vbs
    del CreateShortcut.vbs
    
    echo Shortcut created on your desktop.
    echo.

    :: Ask if user wants to test the executable
    set /p "TEST_CHOICE=Do you want to test the executable now? (y/n): "
    if /i "!TEST_CHOICE!"=="y" (
        echo Starting the executable...
        start "" "%EXECUTABLE_PATH%"
    )

) else (
    echo Error: Executable not found at expected location!
    echo Check the %BUILD_DIR% directory for the executable.
)

echo.
echo Build process completed.
pause