#!/usr/bin/env python3
"""
Fix Wednesday date to be 2024-06-25 instead of 2025-06-25
"""

import os
import sqlite3
import sys

STATS_DB_PATH = os.path.join('data', 'stats.db')

def fix_wednesday_date():
    """Fix Wednesday date to correct year."""
    
    OLD_DATE = "2025-06-25"
    CORRECT_DATE = "2024-06-25"
    WEDNESDAY_EARNINGS = 4086.667
    
    print("Fixing Wednesday date...")
    print(f"Changing {OLD_DATE} to {CORRECT_DATE}")
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Check if old date exists
        cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (OLD_DATE,))
        old_record = cursor.fetchone()
        
        if old_record:
            print(f"Found record for {OLD_DATE}: {old_record}")
            
            # Check if correct date already exists
            cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (CORRECT_DATE,))
            correct_record = cursor.fetchone()
            
            if correct_record:
                # Update existing correct date record
                cursor.execute('''
                UPDATE daily_stats 
                SET earnings = ?, games_played = 1, winners = 1, total_players = 5
                WHERE date = ?
                ''', (WEDNESDAY_EARNINGS, CORRECT_DATE))
                print(f"Updated existing record for {CORRECT_DATE}")
            else:
                # Insert new record with correct date
                cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, 1, ?, 1, 5)
                ''', (CORRECT_DATE, WEDNESDAY_EARNINGS))
                print(f"Inserted new record for {CORRECT_DATE}")
            
            # Delete the old incorrect date record
            cursor.execute('DELETE FROM daily_stats WHERE date = ?', (OLD_DATE,))
            print(f"Deleted old record for {OLD_DATE}")
            
            conn.commit()
            
            # Verify the change
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (CORRECT_DATE,))
            result = cursor.fetchone()
            if result:
                print(f"✅ Verified: {CORRECT_DATE} earnings = {result[0]}")
            
            # Verify total earnings still correct
            cursor.execute('SELECT SUM(earnings) FROM daily_stats')
            result = cursor.fetchone()
            total = result[0] if result and result[0] is not None else 0
            print(f"✅ Total earnings still: {total}")
            
        else:
            print(f"No record found for {OLD_DATE}")
            
            # Just ensure correct date exists
            cursor.execute('SELECT * FROM daily_stats WHERE date = ?', (CORRECT_DATE,))
            correct_record = cursor.fetchone()
            
            if not correct_record:
                cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, 1, ?, 1, 5)
                ''', (CORRECT_DATE, WEDNESDAY_EARNINGS))
                print(f"Inserted record for {CORRECT_DATE}")
                conn.commit()
        
        conn.close()
        print("✅ Wednesday date fixed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing Wednesday date: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    fix_wednesday_date()