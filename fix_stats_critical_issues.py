"""
Fix Critical Stats Page Issues

This script fixes the critical issues causing slow loading and stale data display.
The performance optimizations were too aggressive and are preventing fresh data loading.
"""

import os
import time
from datetime import datetime

class StatsCriticalIssueFixer:
    """Fix critical issues in the stats page."""
    
    def __init__(self):
        self.fixes_applied = 0
        
    def fix_all_critical_issues(self):
        """Fix all critical issues step by step."""
        
        print("🚨 Fixing Critical Stats Page Issues")
        print("=" * 50)
        print(f"Fix started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Create backup first
        self._create_backup()
        
        fixes = [
            ("Slow Initialization", self._fix_slow_initialization),
            ("Stale Data Caching", self._fix_stale_data_caching),
            ("Repetitive Data Loading", self._fix_repetitive_loading),
            ("Missing Current Data", self._fix_missing_current_data),
            ("Cache Timeout Issues", self._fix_cache_timeout_issues),
            ("Background Loading Problems", self._fix_background_loading),
            ("Data Refresh Issues", self._fix_data_refresh)
        ]
        
        for fix_name, fix_func in fixes:
            print(f"🔧 Fixing {fix_name}...")
            try:
                if fix_func():
                    print(f"✅ {fix_name}: FIXED")
                    self.fixes_applied += 1
                else:
                    print(f"⚠️  {fix_name}: PARTIAL FIX")
            except Exception as e:
                print(f"❌ {fix_name}: ERROR - {e}")
            print()
        
        print("=" * 50)
        print(f"🎯 Fixes Applied: {self.fixes_applied}/{len(fixes)}")
        
        if self.fixes_applied >= len(fixes) * 0.8:
            print("✅ Critical issues should now be resolved!")
            print("🔄 Please restart your application to apply the fixes.")
        else:
            print("⚠️  Some issues may persist. Check the error messages above.")
        
        return self.fixes_applied >= len(fixes) * 0.8
    
    def _create_backup(self):
        """Create a backup of the current stats page."""
        try:
            backup_path = f"d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page_critical_fix_backup_{int(time.time())}.py"
            
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"📁 Backup created: {os.path.basename(backup_path)}")
            return True
        except Exception as e:
            print(f"❌ Backup creation failed: {e}")
            return False
    
    def _fix_slow_initialization(self):
        """Fix the slow initialization by removing blocking operations."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix 1: Remove the performance enhancer from __init__ (it's causing blocking)
            old_enhancer_code = '''        # PERFORMANCE ENHANCEMENT INTEGRATION - Auto-applied
        try:
            from enhanced_stats_performance_optimizer import enhance_stats_page_performance
            self._performance_enhancer = enhance_stats_page_performance(self)
            print("✅ Performance enhancements applied to stats page")
        except ImportError:
            print("⚠️  Performance enhancer not available - using standard performance")
            self._performance_enhancer = None
        except Exception as e:
            print(f"⚠️  Error applying performance enhancements: {e}")
            self._performance_enhancer = None'''
            
            new_enhancer_code = '''        # PERFORMANCE ENHANCEMENT - Delayed loading to prevent blocking
        self._performance_enhancer = None
        # Load performance enhancer after UI is ready (non-blocking)
        import threading
        def load_enhancer_delayed():
            try:
                time.sleep(1.0)  # Wait for UI to be ready
                from enhanced_stats_performance_optimizer import enhance_stats_page_performance
                self._performance_enhancer = enhance_stats_page_performance(self)
                print("✅ Performance enhancements applied (delayed)")
            except Exception as e:
                print(f"⚠️  Performance enhancer delayed loading failed: {e}")
        
        threading.Thread(target=load_enhancer_delayed, daemon=True).start()'''
            
            if old_enhancer_code in content:
                content = content.replace(old_enhancer_code, new_enhancer_code)
                print("  ✅ Moved performance enhancer to delayed loading")
            
            # Fix 2: Remove blocking background data loading from __init__
            old_background_loading = '''        # PERFORMANCE: Start background loading without blocking initialization
        print("PERFORMANCE: Starting background data loading...")
        import threading
        loading_thread = threading.Thread(target=self._load_data_background_optimized, daemon=True)
        loading_thread.start()

        print("PERFORMANCE: Stats page initialization completed quickly")'''
            
            new_background_loading = '''        # PERFORMANCE: Initialize with minimal blocking
        print("PERFORMANCE: Stats page initialization starting...")
        
        # Initialize stats provider immediately (this is fast)
        self._initialize_stats_provider()
        
        print("PERFORMANCE: Stats page initialization completed quickly")'''
            
            if old_background_loading in content:
                content = content.replace(old_background_loading, new_background_loading)
                print("  ✅ Removed blocking background loading from init")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing initialization: {e}")
            return False
    
    def _fix_stale_data_caching(self):
        """Fix the stale data caching issues."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix 1: Reduce cache timeout from 5 seconds to 1 second for fresh data
            if "self._cache_timeout = 5" in content:
                content = content.replace(
                    "self._cache_timeout = 5  # PERFORMANCE: 5 seconds cache timeout for better responsiveness",
                    "self._cache_timeout = 1  # CRITICAL FIX: 1 second cache for fresh data"
                )
                print("  ✅ Reduced cache timeout to 1 second for fresh data")
            
            # Fix 2: Add cache invalidation on data refresh
            cache_invalidation_code = '''
    def invalidate_cache(self):
        """Invalidate all cached data to force fresh loading."""
        try:
            if hasattr(self.stats_provider, '_cache'):
                self.stats_provider._cache.clear()
                print("🔄 Cache invalidated - fresh data will be loaded")
            
            if hasattr(self, '_performance_enhancer') and self._performance_enhancer:
                if hasattr(self._performance_enhancer, 'cache'):
                    self._performance_enhancer.cache.clear()
                    print("🔄 Performance cache invalidated")
                    
        except Exception as e:
            print(f"Cache invalidation error: {e}")
'''
            
            # Insert cache invalidation method
            if "def optimize_memory_usage(self):" in content and "def invalidate_cache(self):" not in content:
                insertion_point = content.find("def optimize_memory_usage(self):")
                content = content[:insertion_point] + cache_invalidation_code + "\n" + content[insertion_point:]
                print("  ✅ Added cache invalidation method")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing stale data caching: {e}")
            return False
    
    def _fix_repetitive_loading(self):
        """Fix the repetitive data loading loops."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix: Add loading state tracking to prevent repetitive loading
            loading_state_code = '''
    def _is_data_loading(self):
        """Check if data is currently being loaded to prevent duplicate requests."""
        return getattr(self, '_data_loading_in_progress', False)
    
    def _set_data_loading(self, loading=True):
        """Set the data loading state."""
        self._data_loading_in_progress = loading
        if loading:
            self._data_loading_start_time = time.time()
        else:
            if hasattr(self, '_data_loading_start_time'):
                duration = time.time() - self._data_loading_start_time
                print(f"📊 Data loading completed in {duration:.2f} seconds")
'''
            
            # Insert loading state tracking
            if "def invalidate_cache(self):" in content and "def _is_data_loading(self):" not in content:
                insertion_point = content.find("def invalidate_cache(self):")
                content = content[:insertion_point] + loading_state_code + "\n" + content[insertion_point:]
                print("  ✅ Added loading state tracking")
            
            # Fix the repetitive background loading methods
            old_load_essential = '''    def _load_essential_data(self):
        """Load only essential data first"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            self.daily_earnings = self.stats_provider.get_daily_earnings(today)
        except Exception as e:
            print(f"Error loading essential data: {e}")
            self.daily_earnings = 0.0'''
            
            new_load_essential = '''    def _load_essential_data(self):
        """Load only essential data first - with loading state check"""
        if self._is_data_loading():
            print("📊 Data loading already in progress, skipping duplicate request")
            return
            
        self._set_data_loading(True)
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            self.daily_earnings = self.stats_provider.get_daily_earnings(today)
            print(f"📊 Essential data loaded: Daily earnings = {self.daily_earnings}")
        except Exception as e:
            print(f"Error loading essential data: {e}")
            self.daily_earnings = 0.0
        finally:
            self._set_data_loading(False)'''
            
            if old_load_essential in content:
                content = content.replace(old_load_essential, new_load_essential)
                print("  ✅ Fixed repetitive essential data loading")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing repetitive loading: {e}")
            return False
    
    def _fix_missing_current_data(self):
        """Fix the missing current data issue."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix: Add forced fresh data loading method
            fresh_data_method = '''
    def load_fresh_data(self):
        """Force load fresh data without using cache."""
        print("🔄 Loading fresh data (bypassing cache)...")
        
        try:
            # Invalidate cache first
            self.invalidate_cache()
            
            # Get current date
            today = datetime.now().strftime('%Y-%m-%d')
            
            # Load fresh daily data
            if hasattr(self.stats_provider, 'get_daily_earnings'):
                self.daily_earnings = self.stats_provider.get_daily_earnings(today)
                print(f"📊 Fresh daily earnings: {self.daily_earnings}")
            
            if hasattr(self.stats_provider, 'get_daily_games'):
                self.daily_games = self.stats_provider.get_daily_games(today)
                print(f"📊 Fresh daily games: {self.daily_games}")
            
            # Load fresh total earnings
            if hasattr(self.stats_provider, 'get_total_earnings'):
                self.total_earnings = self.stats_provider.get_total_earnings()
                print(f"📊 Fresh total earnings: {self.total_earnings}")
            
            # Load fresh game history
            if hasattr(self.stats_provider, 'get_game_history'):
                self.game_history = self.stats_provider.get_game_history(100)  # Get recent 100 games
                self.original_game_history = self.game_history.copy()
                print(f"📊 Fresh game history: {len(self.game_history)} records")
            
            # Load fresh weekly stats
            if hasattr(self.stats_provider, 'get_weekly_stats'):
                self.weekly_stats = self.stats_provider.get_weekly_stats()
                print(f"📊 Fresh weekly stats: {len(self.weekly_stats)} days")
            
            print("✅ Fresh data loading completed")
            
        except Exception as e:
            print(f"❌ Error loading fresh data: {e}")
'''
            
            # Insert fresh data loading method
            if "def _is_data_loading(self):" in content and "def load_fresh_data(self):" not in content:
                insertion_point = content.find("def _is_data_loading(self):")
                content = content[:insertion_point] + fresh_data_method + "\n" + content[insertion_point:]
                print("  ✅ Added fresh data loading method")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing missing current data: {e}")
            return False
    
    def _fix_cache_timeout_issues(self):
        """Fix cache timeout issues in stats providers."""
        try:
            # Check if performance optimized stats provider exists
            perf_provider_path = "d:/GAME PROJECTS/LAST-GAME_CONCEPT-/performance_optimized_stats_provider.py"
            
            if os.path.exists(perf_provider_path):
                with open(perf_provider_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix cache timeout in performance provider
                if "self._cache_timeout = 300" in content:  # 5 minutes
                    content = content.replace(
                        "self._cache_timeout = 300",
                        "self._cache_timeout = 30  # CRITICAL FIX: 30 seconds for fresh data"
                    )
                    print("  ✅ Fixed cache timeout in performance provider")
                
                # Write the fixed content
                with open(perf_provider_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing cache timeout: {e}")
            return False
    
    def _fix_background_loading(self):
        """Fix background loading issues."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace the problematic background loading methods
            old_background_method = '''    def _load_data_background_optimized(self):
        """Optimized background data loading with performance throttling"""
        try:
            # Add delay to prevent blocking UI initialization
            time.sleep(0.5)
            
            print("PERFORMANCE: Starting optimized background data loading...")
            
            # Load data in small chunks with delays
            self._load_essential_data()
            time.sleep(0.05)
            
            self._load_secondary_data()
            time.sleep(0.05)
            
            self._load_optional_data()
            
            print("PERFORMANCE: Optimized background loading completed")
            
        except Exception as e:
            print(f"PERFORMANCE: Error in optimized background loading: {e}")'''
            
            new_background_method = '''    def _load_data_background_optimized(self):
        """Fixed background data loading - non-blocking and efficient"""
        try:
            print("📊 Starting background data loading...")
            
            # Load fresh data immediately when requested
            self.load_fresh_data()
            
            print("📊 Background data loading completed")
            
        except Exception as e:
            print(f"❌ Error in background data loading: {e}")'''
            
            if old_background_method in content:
                content = content.replace(old_background_method, new_background_method)
                print("  ✅ Fixed background loading method")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing background loading: {e}")
            return False
    
    def _fix_data_refresh(self):
        """Fix data refresh issues."""
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add a method to refresh data on login
            refresh_on_login_code = '''
    def refresh_data_on_login(self):
        """Refresh all data when user logs in."""
        print("🔄 Refreshing data after login...")
        
        # Start background refresh
        import threading
        refresh_thread = threading.Thread(target=self._load_data_background_optimized, daemon=True)
        refresh_thread.start()
'''
            
            # Insert refresh on login method
            if "def load_fresh_data(self):" in content and "def refresh_data_on_login(self):" not in content:
                insertion_point = content.find("def load_fresh_data(self):")
                content = content[:insertion_point] + refresh_on_login_code + "\n" + content[insertion_point:]
                print("  ✅ Added refresh on login method")
            
            # Find and fix the login success handler to refresh data
            if "self.authenticated = True" in content:
                # Find the login success section and add data refresh
                login_success_pattern = "self.authenticated = True"
                login_success_index = content.find(login_success_pattern)
                
                if login_success_index != -1:
                    # Find the end of the line
                    line_end = content.find("\n", login_success_index)
                    if line_end != -1:
                        # Add refresh call after login success
                        refresh_call = "\n                    # CRITICAL FIX: Refresh data after successful login\n                    self.refresh_data_on_login()"
                        content = content[:line_end] + refresh_call + content[line_end:]
                        print("  ✅ Added data refresh on login success")
            
            # Write the fixed content
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_page.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error fixing data refresh: {e}")
            return False

def main():
    """Main function to fix critical issues."""
    
    print("🚨 Critical Stats Page Issue Fixer")
    print("=" * 50)
    print("This will fix the slow loading and stale data issues.")
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    fixer = StatsCriticalIssueFixer()
    success = fixer.fix_all_critical_issues()
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 CRITICAL ISSUES FIXED!")
        print("\n🔧 What was fixed:")
        print("• Removed blocking operations from initialization")
        print("• Fixed stale data caching (reduced timeout to 1 second)")
        print("• Added cache invalidation methods")
        print("• Fixed repetitive data loading loops")
        print("• Added fresh data loading methods")
        print("• Fixed background loading issues")
        print("• Added data refresh on login")
        
        print("\n📋 Next Steps:")
        print("1. 🔄 RESTART your application immediately")
        print("2. 📊 Test stats page loading (should be much faster)")
        print("3. 🔍 Check that current data is displayed correctly")
        print("4. 📝 Monitor console for improved performance messages")
        
        print("\n⚡ Expected Results:")
        print("• Stats page loads in 2-5 seconds (instead of 1 minute)")
        print("• Current day's data is displayed correctly")
        print("• No more repetitive loading loops")
        print("• Fresh data after login")
        
    else:
        print("⚠️  Some issues may not have been fully resolved.")
        print("Check the error messages above and try running the script again.")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()