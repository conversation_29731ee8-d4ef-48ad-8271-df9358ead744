#!/usr/bin/env python3
"""
Simple verification that the threading import issue is fixed.
"""

def verify_threading_import():
    """Verify that threading is properly imported in stats_page.py."""
    
    print("🔍 Verifying Threading Import Fix")
    print("=" * 40)
    
    try:
        # Read the stats_page.py file
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for threading import at the top level
        lines = content.split('\n')
        
        # Find import section (first 20 lines should contain imports)
        import_section = '\n'.join(lines[:20])
        
        if 'import threading' in import_section:
            print("✅ Threading import found at top level")
            
            # Find the exact line
            for i, line in enumerate(lines[:20]):
                if 'import threading' in line:
                    print(f"   Line {i+1}: {line.strip()}")
                    break
            
            return True
        else:
            print("❌ Threading import not found at top level")
            print("   Checking if it exists elsewhere...")
            
            if 'import threading' in content:
                print("   ⚠️  Threading import found but not at top level")
                # Show where it's found
                for i, line in enumerate(lines):
                    if 'import threading' in line and not line.strip().startswith('#'):
                        print(f"   Line {i+1}: {line.strip()}")
                        if i > 20:  # If it's not in the import section
                            print("   ❌ This is too late - needs to be at top level")
                            return False
            else:
                print("   ❌ Threading import not found anywhere")
                return False
            
            return True
            
    except FileNotFoundError:
        print("❌ Error: stats_page.py not found!")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def test_import_syntax():
    """Test that the file can be parsed without syntax errors."""
    
    print("\n🧪 Testing Import Syntax")
    print("-" * 25)
    
    try:
        # Try to compile the file to check for syntax errors
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'stats_page.py', 'exec')
        print("✅ File compiles without syntax errors")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in file: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error compiling file: {e}")
        return False

def check_threading_usage():
    """Check that threading is used correctly in the file."""
    
    print("\n🔍 Checking Threading Usage")
    print("-" * 30)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the specific line that was causing the error
        if 'self._loading_state_lock = threading.Lock()' in content:
            print("✅ Threading.Lock() usage found")
        else:
            print("❌ Threading.Lock() usage not found")
            return False
        
        # Check for other threading usage
        threading_usages = [
            'threading.Thread(',
            'threading.Lock(',
            'with self._loading_state_lock:',
        ]
        
        found_usages = []
        for usage in threading_usages:
            count = content.count(usage)
            if count > 0:
                found_usages.append(f"{usage} ({count} times)")
        
        if found_usages:
            print("✅ Threading usage patterns found:")
            for usage in found_usages:
                print(f"   • {usage}")
        else:
            print("❌ No threading usage patterns found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking threading usage: {e}")
        return False

def main():
    """Run all verification checks."""
    
    print("🔧 Threading Import Fix Verification")
    print("=" * 50)
    
    # Check 1: Threading import at top level
    import_ok = verify_threading_import()
    
    # Check 2: Syntax is correct
    syntax_ok = test_import_syntax()
    
    # Check 3: Threading is used correctly
    usage_ok = check_threading_usage()
    
    # Summary
    print("\n📊 Verification Results")
    print("=" * 25)
    
    if import_ok:
        print("✅ Threading import: CORRECT")
    else:
        print("❌ Threading import: INCORRECT")
    
    if syntax_ok:
        print("✅ File syntax: VALID")
    else:
        print("❌ File syntax: INVALID")
    
    if usage_ok:
        print("✅ Threading usage: CORRECT")
    else:
        print("❌ Threading usage: INCORRECT")
    
    overall_success = import_ok and syntax_ok and usage_ok
    
    if overall_success:
        print("\n🎉 THREADING FIX VERIFIED!")
        print("   The 'threading referenced before assignment' error should be resolved.")
        print("   The stats page should now open without threading-related errors.")
    else:
        print("\n❌ THREADING FIX NOT COMPLETE!")
        print("   There may still be issues with the threading import.")
        print("   Please check the error messages above.")
    
    print("\n🚀 Next Steps:")
    if overall_success:
        print("   1. Try opening the stats page in your application")
        print("   2. The threading error should no longer occur")
        print("   3. The game history section should display without blinking")
    else:
        print("   1. Review the error messages above")
        print("   2. Ensure threading is imported at the top of stats_page.py")
        print("   3. Check for any syntax errors in the file")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)