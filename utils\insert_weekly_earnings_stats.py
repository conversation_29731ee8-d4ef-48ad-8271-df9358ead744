#!/usr/bin/env python3
"""
Script to insert weekly earnings stats data for the period 09-06-2025 to 15-06-2025.
This script inserts the specified earnings data without modifying the database structure.
"""

import sqlite3
import os
import sys
from datetime import datetime, timedelta

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

def ensure_database_exists():
    """Ensure the stats database exists and has the correct schema."""
    # Create data directory if it doesn't exist
    os.makedirs(os.path.dirname(STATS_DB_PATH), exist_ok=True)
    
    # Create and initialize the database
    conn = sqlite3.connect(STATS_DB_PATH)
    cursor = conn.cursor()
    
    # Create daily_stats table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS daily_stats (
        date TEXT PRIMARY KEY,
        games_played INTEGER DEFAULT 0,
        earnings REAL DEFAULT 0,
        winners INTEGER DEFAULT 0,
        total_players INTEGER DEFAULT 0
    )
    ''')
    
    conn.commit()
    conn.close()
    print(f"✅ Database initialized at: {STATS_DB_PATH}")

def insert_weekly_earnings():
    """Insert the weekly earnings data for 09-06-2025 to 15-06-2025."""
    
    # Weekly earnings data (date: earnings)
    # Monday is blank (to be recorded from current)
    weekly_data = [
        ('2025-06-09', None),     # Monday (blank - to be recorded from current)
        ('2025-06-10', 1043.0),   # Tuesday
        ('2025-06-11', 440.0),    # Wednesday
        ('2025-06-12', 280.0),    # Thursday
        ('2025-06-13', 826.0),    # Friday
        ('2025-06-14', 1426.0),   # Saturday
        ('2025-06-15', 804.0),    # Sunday
    ]
    
    # Total earnings: 9603
    total_earnings = 9603.0
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("📊 Inserting weekly earnings data (09-06-2025 to 15-06-2025)...")
        
        # Calculate Monday's earnings based on the total and other days
        sum_other_days = sum(earnings for _, earnings in weekly_data if earnings is not None)
        monday_earnings = total_earnings - sum_other_days
        
        # Update weekly_data with calculated Monday earnings
        weekly_data[0] = ('2025-06-09', monday_earnings)
        
        for date_str, earnings in weekly_data:
            if earnings is None:
                continue
                
            # Check if record already exists
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (date_str,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                cursor.execute('''
                UPDATE daily_stats 
                SET earnings = ?, games_played = CASE WHEN ? > 0 THEN 1 ELSE 0 END
                WHERE date = ?
                ''', (earnings, earnings, date_str))
                print(f"   📝 Updated {date_str}: {earnings} ETB")
            else:
                # Insert new record
                games_played = 1 if earnings > 0 else 0
                cursor.execute('''
                INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
                VALUES (?, ?, ?, ?, ?)
                ''', (date_str, games_played, earnings, 0, 0))
                print(f"   ✅ Inserted {date_str}: {earnings} ETB")
        
        conn.commit()
        conn.close()
        print("✅ All data inserted successfully!")
        print(f"✅ Total earnings for the week: {total_earnings} ETB")
        
    except Exception as e:
        print(f"❌ Error inserting data: {e}")
        if 'conn' in locals():
            conn.close()
        return False
    
    return True

def verify_data():
    """Verify the inserted data."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("\n📋 Verifying inserted data:")
        cursor.execute('''
        SELECT date, earnings, games_played 
        FROM daily_stats 
        WHERE date BETWEEN '2025-06-09' AND '2025-06-15'
        ORDER BY date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   Date       | Earnings | Games")
            print("   -----------|----------|------")
            total = 0
            for date_str, earnings, games in results:
                # Convert date to show day name
                try:
                    dt = datetime.strptime(date_str, '%Y-%m-%d')
                    day_name = dt.strftime('%a')
                    print(f"   {date_str} ({day_name}) | {earnings:>8.1f} | {games:>5}")
                    total += earnings
                except:
                    print(f"   {date_str}     | {earnings:>8.1f} | {games:>5}")
                    total += earnings
            
            print("   -----------|----------|------")
            print(f"   Total      | {total:>8.1f} |")
        else:
            print("   No data found in the specified date range.")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying data: {e}")

def main():
    """Main function to run the data insertion."""
    print("🎯 Weekly Earnings Stats Data Insertion Script")
    print("=" * 50)
    
    # Ensure database exists
    ensure_database_exists()
    
    # Insert the data
    success = insert_weekly_earnings()
    
    if success:
        # Verify the data
        verify_data()
        print("\n🎉 Data insertion completed successfully!")
        print("💡 The stats page should now display this weekly earnings data.")
    else:
        print("\n❌ Data insertion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()