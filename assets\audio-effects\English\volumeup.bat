@echo off
setlocal enabledelayedexpansion

:: Create output directory if it doesn't exist
if not exist "output\" mkdir "output"

:: Process all common audio file formats
for %%f in (*.mp3 *.wav *.ogg *.flac *.m4a *.aac) do (
    if exist "%%f" (
        echo Processing: %%f
        ffmpeg -i "%%f" -af "volume=3.2" "output\%%f"
    )
)

echo Complete! All audio files have been processed with 2x volume.