#!/usr/bin/env python3
"""
Test script to verify stats data loading and database functionality.
"""

import sys
import os
from datetime import datetime, timedelta

def test_stats_database():
    """Test the stats database functionality"""
    print("Testing stats database functionality...")
    
    try:
        # Test stats database connection
        print("1. Testing stats database connection...")
        from stats_db import get_stats_db_manager
        stats_db = get_stats_db_manager()
        print("✓ Stats database manager created")
        
        # Test database connection
        conn = stats_db.get_connection()
        cursor = conn.cursor()
        print("✓ Database connection established")
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✓ Database tables found: {[table[0] for table in tables]}")
        
        # Test game history count
        cursor.execute("SELECT COUNT(*) FROM game_history")
        game_count = cursor.fetchone()[0]
        print(f"✓ Game history records: {game_count}")
        
        # Test daily stats
        today = datetime.now().strftime('%Y-%m-%d')
        daily_earnings = stats_db.get_daily_earnings(today)
        print(f"✓ Daily earnings for {today}: {daily_earnings}")
        
        # Test weekly stats
        weekly_stats = stats_db.get_weekly_stats()
        print(f"✓ Weekly stats retrieved: {len(weekly_stats)} days")
        
        print("\n2. Testing stats providers...")
        
        # Test performance optimized stats provider
        try:
            from performance_optimized_stats_provider import get_performance_optimized_stats_provider
            provider = get_performance_optimized_stats_provider()
            daily_earnings = provider.get_daily_earnings(today)
            print(f"✓ Performance optimized provider - Daily earnings: {daily_earnings}")
            
            weekly_stats = provider.get_weekly_stats()
            print(f"✓ Performance optimized provider - Weekly stats: {len(weekly_stats)} days")
        except Exception as e:
            print(f"⚠ Performance optimized provider error: {e}")
        
        # Test simple stats provider
        try:
            from simple_stats_provider import get_simple_stats_provider
            provider = get_simple_stats_provider()
            daily_earnings = provider.get_daily_earnings(today)
            print(f"✓ Simple stats provider - Daily earnings: {daily_earnings}")
        except Exception as e:
            print(f"⚠ Simple stats provider error: {e}")
        
        print("\n3. Testing stats integration...")
        
        # Test stats integration
        try:
            from stats_integration import get_stats_summary, get_game_history
            summary = get_stats_summary()
            print(f"✓ Stats summary: {summary}")
            
            history, total_pages = get_game_history(0, 10)
            print(f"✓ Game history: {len(history)} records, {total_pages} pages")
        except Exception as e:
            print(f"⚠ Stats integration error: {e}")
        
        print("\n4. Testing hybrid database integration...")
        
        # Test hybrid database
        try:
            from hybrid_db_integration import get_hybrid_db_integration
            hybrid_db = get_hybrid_db_integration()
            summary = hybrid_db.get_stats_summary()
            print(f"✓ Hybrid database summary: {summary}")
        except Exception as e:
            print(f"⚠ Hybrid database error: {e}")
        
        print("\n" + "="*50)
        print("✓ ALL DATABASE TESTS PASSED!")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\n" + "="*50)
        print(f"✗ DATABASE TEST FAILED: {e}")
        print("="*50)
        import traceback
        traceback.print_exc()
        return False

def test_payment_system():
    """Test the payment system integration"""
    print("\nTesting payment system integration...")
    
    try:
        from payment import get_voucher_manager
        voucher_manager = get_voucher_manager()
        
        current_credits = voucher_manager.get_current_credits()
        print(f"✓ Current credits: {current_credits}")
        
        # Test voucher validation (with a dummy voucher)
        test_voucher = "TEST-VOUCHER-123"
        is_valid = voucher_manager.validate_voucher_format(test_voucher)
        print(f"✓ Voucher format validation test: {is_valid}")
        
        print("✓ Payment system integration working")
        return True
        
    except Exception as e:
        print(f"⚠ Payment system error: {e}")
        return False

if __name__ == "__main__":
    print("WOW Bingo Stats System Test")
    print("="*50)
    
    db_success = test_stats_database()
    payment_success = test_payment_system()
    
    if db_success and payment_success:
        print("\n🎉 ALL SYSTEMS WORKING CORRECTLY!")
        sys.exit(0)
    else:
        print("\n❌ SOME SYSTEMS HAVE ISSUES")
        sys.exit(1)