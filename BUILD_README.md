# WOW Bingo Game - Build Instructions

## Build Issue Fix

The original build script was failing with the following error:
```
FATAL: dll-files: Error, failed to evaluate condition 'is_conda_package("numpy") and version("numpy") < (2,)' in this context, exception was ''NoneType' object has no attribute 'split''.
```

This error occurs because of an issue with how Nuitka handles the numpy plugin. The solution involves:

1. Using Uv for package management instead of pip
2. Disabling the numpy plugin in Nuitka
3. Cleaning up test files that might be causing conflicts

## Build Scripts

### 1. `build_with_uv.bat` (Recommended)

This is the main build script that:
- Installs Uv if not already installed
- Uses Uv to install all dependencies
- Installs numpy with a compatible version
- Cleans up test files
- Runs the fixed Nuitka build

Usage:
```
build_with_uv.bat
```

### 2. `nuitka_build_fixed.bat`

This is the fixed Nuitka build script that:
- Uses the `--disable-plugin=numpy` flag to avoid numpy-related issues
- Includes all necessary packages and data
- Optimizes the build for performance

Usage (if you already have dependencies installed):
```
nuitka_build_fixed.bat
```

### 3. `cleanup_test_files.py`

This script removes test files that might be causing conflicts with the build process.

Usage:
```
python cleanup_test_files.py
```

## Troubleshooting

If you encounter issues:

1. Make sure you have Python 3.7+ installed
2. Try running the build with Uv: `build_with_uv.bat`
3. If that fails, try the fixed build script directly: `nuitka_build_fixed.bat`
4. Check the console output for specific error messages

## Build Output

The compiled executable will be in the `build_fixed` directory with the name `WOWBingoGame.exe`.