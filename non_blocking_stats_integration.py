"""
Non-blocking Stats Integration
Prevents UI freezing by using async operations and progressive loading.
"""

import threading
import time
import pygame
import queue
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NonBlockingStatsIntegration:
    """
    Non-blocking stats integration that prevents UI freezing
    by loading data progressively and asynchronously.
    """
    
    def __init__(self, stats_page_instance):
        self.stats_page = stats_page_instance
        self.loading_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.loading_thread = None
        self.is_loading = False
        self.loading_progress = 0
        self.loading_status = "Ready"
        
        # Progressive loading states
        self.loading_stages = [
            ("basic_stats", "Loading basic statistics..."),
            ("daily_stats", "Loading daily statistics..."),
            ("weekly_stats", "Loading weekly statistics..."),
            ("monthly_stats", "Loading monthly statistics..."),
            ("game_history", "Loading game history..."),
            ("wallet_data", "Loading wallet data..."),
        ]
        
        self.loaded_data = {}
        self.loading_errors = {}
        
        # Start the loading thread
        self._start_loading_thread()
    
    def _start_loading_thread(self):
        """Start the background loading thread."""
        if self.loading_thread and self.loading_thread.is_alive():
            return
        
        def loading_worker():
            while True:
                try:
                    task = self.loading_queue.get(timeout=1.0)
                    if task is None:  # Shutdown signal
                        break
                    
                    task_type, task_data = task
                    
                    if task_type == "load_all":
                        self._load_all_data_async()
                    elif task_type == "load_stage":
                        self._load_stage_async(task_data)
                    elif task_type == "refresh":
                        self._refresh_data_async(task_data)
                    
                    self.loading_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Loading worker error: {e}")
                    self.result_queue.put(("error", str(e)))
        
        self.loading_thread = threading.Thread(target=loading_worker, daemon=True)
        self.loading_thread.start()
    
    def start_progressive_loading(self):
        """Start progressive loading of all stats data."""
        if self.is_loading:
            logger.info("Loading already in progress")
            return
        
        self.is_loading = True
        self.loading_progress = 0
        self.loading_status = "Starting..."
        self.loaded_data.clear()
        self.loading_errors.clear()
        
        # Queue the loading task
        self.loading_queue.put(("load_all", None))
        
        logger.info("Started progressive stats loading")
    
    def _load_all_data_async(self):
        """Load all data progressively in background."""
        try:
            total_stages = len(self.loading_stages)
            
            for i, (stage_name, stage_description) in enumerate(self.loading_stages):
                # Update progress
                self.loading_progress = int((i / total_stages) * 100)
                self.loading_status = stage_description
                
                # Post progress update to UI
                self._post_progress_update(self.loading_progress, stage_description)
                
                # Load this stage
                try:
                    stage_data = self._load_stage_data(stage_name)
                    self.loaded_data[stage_name] = stage_data
                    
                    # Post stage completion to UI
                    self._post_stage_complete(stage_name, stage_data)
                    
                except Exception as e:
                    logger.error(f"Error loading stage {stage_name}: {e}")
                    self.loading_errors[stage_name] = str(e)
                    
                    # Post error to UI
                    self._post_stage_error(stage_name, str(e))
                
                # Small delay to prevent overwhelming the UI
                time.sleep(0.1)
            
            # Complete
            self.loading_progress = 100
            self.loading_status = "Complete"
            self.is_loading = False
            
            # Post completion to UI
            self._post_loading_complete()
            
        except Exception as e:
            logger.error(f"Error in progressive loading: {e}")
            self.is_loading = False
            self.loading_status = f"Error: {str(e)}"
            self._post_loading_error(str(e))
    
    def _load_stage_data(self, stage_name):
        """Load data for a specific stage."""
        try:
            # Get the ultra-fast stats provider
            from ultra_fast_stats_provider import get_ultra_fast_stats_provider
            provider = get_ultra_fast_stats_provider()
            
            if stage_name == "basic_stats":
                return {
                    "total_earnings": provider.get_wallet_balance(),
                    "today_earnings": provider.get_daily_earnings(),
                    "today_games": provider.get_daily_games_played(),
                }
            
            elif stage_name == "daily_stats":
                today = datetime.now().strftime('%Y-%m-%d')
                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                return {
                    "today": {
                        "earnings": provider.get_daily_earnings(today),
                        "games": provider.get_daily_games_played(today),
                    },
                    "yesterday": {
                        "earnings": provider.get_daily_earnings(yesterday),
                        "games": provider.get_daily_games_played(yesterday),
                    }
                }
            
            elif stage_name == "weekly_stats":
                return provider.get_weekly_stats()
            
            elif stage_name == "monthly_stats":
                return provider.get_monthly_stats()
            
            elif stage_name == "game_history":
                return provider.get_game_history(page=0, page_size=20)
            
            elif stage_name == "wallet_data":
                return {
                    "balance": provider.get_wallet_balance(),
                    "transactions": []  # Load minimal transactions for now
                }
            
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Error loading stage {stage_name}: {e}")
            raise
    
    def _post_progress_update(self, progress, status):
        """Post progress update to UI thread."""
        try:
            if pygame.get_init():
                event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'loading_progress',
                    'progress': progress,
                    'status': status,
                    'timestamp': time.time()
                })
                pygame.event.post(event)
        except Exception as e:
            logger.error(f"Error posting progress update: {e}")
    
    def _post_stage_complete(self, stage_name, stage_data):
        """Post stage completion to UI thread."""
        try:
            if pygame.get_init():
                event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'stage_complete',
                    'stage_name': stage_name,
                    'stage_data': stage_data,
                    'timestamp': time.time()
                })
                pygame.event.post(event)
        except Exception as e:
            logger.error(f"Error posting stage complete: {e}")
    
    def _post_stage_error(self, stage_name, error_message):
        """Post stage error to UI thread."""
        try:
            if pygame.get_init():
                event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'stage_error',
                    'stage_name': stage_name,
                    'error_message': error_message,
                    'timestamp': time.time()
                })
                pygame.event.post(event)
        except Exception as e:
            logger.error(f"Error posting stage error: {e}")
    
    def _post_loading_complete(self):
        """Post loading completion to UI thread."""
        try:
            if pygame.get_init():
                event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'loading_complete',
                    'loaded_data': self.loaded_data,
                    'loading_errors': self.loading_errors,
                    'timestamp': time.time()
                })
                pygame.event.post(event)
        except Exception as e:
            logger.error(f"Error posting loading complete: {e}")
    
    def _post_loading_error(self, error_message):
        """Post loading error to UI thread."""
        try:
            if pygame.get_init():
                event = pygame.event.Event(pygame.USEREVENT, {
                    'stats_type': 'loading_error',
                    'error_message': error_message,
                    'timestamp': time.time()
                })
                pygame.event.post(event)
        except Exception as e:
            logger.error(f"Error posting loading error: {e}")
    
    def handle_ui_event(self, event):
        """Handle UI events related to loading."""
        if not hasattr(event, 'stats_type'):
            return False
        
        stats_type = event.stats_type
        
        if stats_type == 'loading_progress':
            self._handle_progress_update(event)
            return True
        
        elif stats_type == 'stage_complete':
            self._handle_stage_complete(event)
            return True
        
        elif stats_type == 'stage_error':
            self._handle_stage_error(event)
            return True
        
        elif stats_type == 'loading_complete':
            self._handle_loading_complete(event)
            return True
        
        elif stats_type == 'loading_error':
            self._handle_loading_error(event)
            return True
        
        return False
    
    def _handle_progress_update(self, event):
        """Handle progress update event."""
        self.loading_progress = event.progress
        self.loading_status = event.status
        
        # Update UI progress indicator
        if hasattr(self.stats_page, 'update_loading_progress'):
            self.stats_page.update_loading_progress(event.progress, event.status)
    
    def _handle_stage_complete(self, event):
        """Handle stage completion event."""
        stage_name = event.stage_name
        stage_data = event.stage_data
        
        # Update UI with stage data
        if hasattr(self.stats_page, 'update_stage_data'):
            self.stats_page.update_stage_data(stage_name, stage_data)
    
    def _handle_stage_error(self, event):
        """Handle stage error event."""
        stage_name = event.stage_name
        error_message = event.error_message
        
        logger.warning(f"Stage {stage_name} failed: {error_message}")
        
        # Update UI with error info
        if hasattr(self.stats_page, 'update_stage_error'):
            self.stats_page.update_stage_error(stage_name, error_message)
    
    def _handle_loading_complete(self, event):
        """Handle loading completion event."""
        self.is_loading = False
        
        # Update UI with all loaded data
        if hasattr(self.stats_page, 'update_all_data'):
            self.stats_page.update_all_data(event.loaded_data, event.loading_errors)
        
        logger.info("Progressive loading completed successfully")
    
    def _handle_loading_error(self, event):
        """Handle loading error event."""
        self.is_loading = False
        error_message = event.error_message
        
        logger.error(f"Progressive loading failed: {error_message}")
        
        # Update UI with error
        if hasattr(self.stats_page, 'update_loading_error'):
            self.stats_page.update_loading_error(error_message)
    
    def refresh_data(self, data_types=None):
        """Refresh specific data types."""
        if data_types is None:
            data_types = ["basic_stats", "daily_stats", "weekly_stats"]
        
        self.loading_queue.put(("refresh", data_types))
    
    def _refresh_data_async(self, data_types):
        """Refresh data asynchronously."""
        try:
            for data_type in data_types:
                stage_data = self._load_stage_data(data_type)
                self.loaded_data[data_type] = stage_data
                self._post_stage_complete(data_type, stage_data)
                
        except Exception as e:
            logger.error(f"Error refreshing data: {e}")
            self._post_loading_error(str(e))
    
    def get_loading_status(self):
        """Get current loading status."""
        return {
            "is_loading": self.is_loading,
            "progress": self.loading_progress,
            "status": self.loading_status,
            "loaded_stages": list(self.loaded_data.keys()),
            "error_stages": list(self.loading_errors.keys())
        }
    
    def shutdown(self):
        """Shutdown the integration and cleanup resources."""
        # Stop loading thread
        self.loading_queue.put(None)
        if self.loading_thread:
            self.loading_thread.join(timeout=1.0)
        
        logger.info("Non-blocking stats integration shutdown complete")


def integrate_non_blocking_stats(stats_page_instance):
    """
    Integrate non-blocking stats loading with a stats page instance.
    
    Args:
        stats_page_instance: The StatsPage instance to integrate with
        
    Returns:
        NonBlockingStatsIntegration: The integration instance
    """
    integration = NonBlockingStatsIntegration(stats_page_instance)
    
    # Add event handler to stats page
    original_handle_event = getattr(stats_page_instance, 'handle_event', None)
    
    def enhanced_handle_event(event):
        # Try non-blocking integration first
        if integration.handle_ui_event(event):
            return True
        
        # Fall back to original handler
        if original_handle_event:
            return original_handle_event(event)
        
        return False
    
    stats_page_instance.handle_event = enhanced_handle_event
    stats_page_instance.non_blocking_integration = integration
    
    # Add loading status methods
    stats_page_instance.get_loading_status = integration.get_loading_status
    stats_page_instance.start_progressive_loading = integration.start_progressive_loading
    stats_page_instance.refresh_data = integration.refresh_data
    
    logger.info("Non-blocking stats integration completed")
    return integration