"""
Universal Display Manager for WOW Games

This module provides enhanced support for all display types,
addressing common issues with display initialization and mode switching.
"""

import pygame
import os
import sys
import time
from typing import Tuple, List, Optional, Dict, Any


class UniversalDisplayManager:
    """
    Manages display connections and resolves common display issues
    """

    def __init__(self):
        self.display_info = {}
        self.available_modes = []
        self.current_display = None
        self.fallback_modes = [
            (1920, 1080), (1680, 1050), (1600, 900), (1366, 768),
            (1280, 720), (1024, 768), (800, 600)
        ]
        self.display_flags_priority = [
            pygame.RESIZABLE | pygame.DOUBLEBUF | pygame.HWSURFACE,
            pygame.RESIZABLE | pygame.DOUBLEBUF,
            pygame.RESIZABLE | pygame.HWSURFACE,
            pygame.RESIZABLE,
            pygame.DOUBLEBUF,
            0  # No flags as last resort
        ]

    def detect_displays(self) -> Dict[str, Any]:
        """
        Detect available displays and their capabilities

        Returns:
            Dict containing display information
        """
        display_info = {
            'primary_display': None,
            'available_modes': [],
            'driver': None,
            'external_detected': False,
            'refresh_rates': [],
            'color_depths': []
        }

        try:
            # Get display driver information
            display_info['driver'] = pygame.display.get_driver()
            print(f"Display driver: {display_info['driver']}")

            # Get primary display info
            try:
                screen_info = pygame.display.Info()
                display_info['primary_display'] = {
                    'width': screen_info.current_w,
                    'height': screen_info.current_h,
                    'depth': screen_info.bitsize if hasattr(screen_info, 'bitsize') else 32
                }
                print(f"Primary display: {screen_info.current_w}x{screen_info.current_h}")
            except Exception as e:
                print(f"Warning: Could not get primary display info: {e}")
                display_info['primary_display'] = {'width': 1920, 'height': 1080, 'depth': 32}

            # Get available display modes
            try:
                modes = pygame.display.list_modes()
                if modes == -1:
                    print("All display modes supported")
                    display_info['available_modes'] = self.fallback_modes
                elif modes:
                    display_info['available_modes'] = list(set(modes))  # Remove duplicates
                    print(f"Available modes: {len(display_info['available_modes'])}")
                else:
                    print("No display modes available, using fallback")
                    display_info['available_modes'] = self.fallback_modes
            except Exception as e:
                print(f"Error getting display modes: {e}")
                display_info['available_modes'] = self.fallback_modes

            # Detect external display (heuristic based on resolution changes)
            primary = display_info['primary_display']
            if primary and display_info['available_modes']:
                max_mode = max(display_info['available_modes'], key=lambda x: x[0] * x[1])
                if max_mode[0] > primary['width'] or max_mode[1] > primary['height']:
                    display_info['external_detected'] = True
                    print("External display detected (higher resolution available)")

        except Exception as e:
            print(f"Error during display detection: {e}")
            # Provide safe defaults
            display_info['primary_display'] = {'width': 1920, 'height': 1080, 'depth': 32}
            display_info['available_modes'] = self.fallback_modes

        self.display_info = display_info
        return display_info

    def create_compatible_display(self, preferred_width: int = None,
                              preferred_height: int = None,
                              fullscreen: bool = False) -> Tuple[pygame.Surface, int, int]:
        """
        Create a display surface compatible with all display types

        Args:
            preferred_width: Preferred window width
            preferred_height: Preferred window height
            fullscreen: Whether to create in fullscreen mode

        Returns:
            Tuple of (surface, actual_width, actual_height)
        """
        try:
            # Define minimum screen dimensions for proper display
            MIN_WIDTH, MIN_HEIGHT = 1024, 768
            
            # Set up double-buffering to prevent flickering
            pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
            
            if fullscreen:
                # For fullscreen, use a single set_mode call with native resolution
                screen_info = pygame.display.Info()
                
                # Ensure we're using a resolution that works well with the game
                actual_width = max(screen_info.current_w, MIN_WIDTH)
                actual_height = max(screen_info.current_h, MIN_HEIGHT)
                
                # Use SCALED flag to ensure content fits the screen without distortion
                surface = pygame.display.set_mode(
                    (actual_width, actual_height),
                    pygame.FULLSCREEN | pygame.SCALED | pygame.DOUBLEBUF
                )
                print(f"Fullscreen display created: {actual_width}x{actual_height}")
            else:
                # For windowed mode, use preferred dimensions or default
                if preferred_width and preferred_height:
                    target_width = max(preferred_width, MIN_WIDTH)
                    target_height = max(preferred_height, MIN_HEIGHT)
                else:
                    # Use a reasonable default size
                    target_width, target_height = MIN_WIDTH, MIN_HEIGHT
                
                # Create windowed display with double buffering to prevent flickering
                surface = pygame.display.set_mode(
                    (target_width, target_height),
                    pygame.RESIZABLE | pygame.DOUBLEBUF
                )
                actual_width, actual_height = target_width, target_height
                print(f"Windowed display created: {actual_width}x{actual_height}")
            
            # Apply minimal optimizations for display
            self._optimize_display(surface)
            
            # Update global scaling factors if they exist in the main module
            try:
                import sys
                main_module = sys.modules.get('__main__')
                if main_module and hasattr(main_module, 'scale_x') and hasattr(main_module, 'scale_y'):
                    # Get base dimensions from main module
                    base_width = getattr(main_module, 'BASE_WIDTH', 1024)
                    base_height = getattr(main_module, 'BASE_HEIGHT', 768)
                    
                    # Update scaling factors
                    main_module.scale_x = actual_width / base_width
                    main_module.scale_y = actual_height / base_height
                    
                    # Update game instance if it exists
                    if hasattr(main_module, 'game') and hasattr(main_module.game, 'update_scaling'):
                        main_module.game.update_scaling()
                        
                    print(f"Updated scaling factors: scale_x={main_module.scale_x}, scale_y={main_module.scale_y}")
            except Exception as e:
                print(f"Note: Could not update scaling factors: {e}")
            
            self.current_display = surface
            return surface, actual_width, actual_height
            
        except pygame.error as e:
            print(f"Failed to create display: {e}")
            # Emergency fallback
            try:
                surface = pygame.display.set_mode((800, 600))
                print("Emergency fallback display created: 800x600")
                self.current_display = surface
                return surface, 800, 600
            except pygame.error as e2:
                raise RuntimeError(f"Could not create any display mode: {e2}")

    def _validate_resolution(self, width: int, height: int) -> Tuple[int, int]:
        """
        Validate and adjust resolution against available modes

        Args:
            width: Requested width
            height: Requested height

        Returns:
            Tuple of (validated_width, validated_height)
        """
        available_modes = self.display_info.get('available_modes', self.fallback_modes)

        # If exact match exists, use it
        if (width, height) in available_modes:
            return width, height

        # Find closest compatible mode
        best_mode = None
        min_diff = float('inf')

        for mode_width, mode_height in available_modes:
            if mode_width >= width and mode_height >= height:
                diff = (mode_width - width) + (mode_height - height)
                if diff < min_diff:
                    min_diff = diff
                    best_mode = (mode_width, mode_height)

        if best_mode:
            return best_mode

        # If no larger mode found, find the largest available
        largest_mode = max(available_modes, key=lambda x: x[0] * x[1])
        return min(width, largest_mode[0]), min(height, largest_mode[1])

    def _optimize_display(self, surface: pygame.Surface):
        """
        Apply optimizations for display to prevent issues and flickering

        Args:
            surface: The display surface to optimize
        """
        try:
            # Set essential SDL environment variables for stability
            os.environ['SDL_VIDEO_WINDOW_POS'] = 'centered'
            os.environ['SDL_VIDEO_CENTERED'] = '1'
            os.environ['SDL_VIDEO_MINIMIZE_ON_FOCUS_LOSS'] = '0'
            os.environ['SDL_VIDEO_ALLOW_SCREENSAVER'] = '1'
            
            # Set additional SDL hints to prevent flickering
            pygame.display.set_caption("WOW Games")  # Set window title
            
            # Enable vertical sync to prevent tearing
            if hasattr(pygame, 'GL_CONTEXT_PROFILE_CORE'):
                try:
                    pygame.display.gl_set_attribute(pygame.GL_CONTEXT_PROFILE_MASK, 
                                                  pygame.GL_CONTEXT_PROFILE_CORE)
                    pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MAJOR_VERSION, 3)
                    pygame.display.gl_set_attribute(pygame.GL_CONTEXT_MINOR_VERSION, 3)
                except Exception as gl_error:
                    print(f"Note: Could not set OpenGL attributes: {gl_error}")
            
            # Set vsync if available
            try:
                if hasattr(pygame, 'GL_SWAP_CONTROL'):
                    pygame.display.gl_set_attribute(pygame.GL_SWAP_CONTROL, 1)
            except Exception as vsync_error:
                print(f"Note: Could not enable vsync: {vsync_error}")
            
            # Clear the surface to prevent artifacts
            surface.fill((0, 0, 0))
            pygame.display.flip()
            
            # Second clear and flip to ensure clean state
            surface.fill((0, 0, 0))
            pygame.display.flip()
            
            print("Applied enhanced display optimizations")

        except Exception as e:
            print(f"Warning: Could not apply all optimizations: {e}")
            
            # Try minimal optimizations as fallback
            try:
                surface.fill((0, 0, 0))
                pygame.display.flip()
                print("Applied minimal display optimizations")
            except Exception as e2:
                print(f"Warning: Could not apply minimal optimizations: {e2}")

    def force_display_reset(self) -> bool:
        """
        Force a complete display reset to resolve persistent issues

        Returns:
            True if reset was successful
        """
        try:
            print("Forcing display reset...")

            # Store current display info
            current_size = None
            current_fullscreen = False

            if self.current_display:
                current_size = self.current_display.get_size()
                current_fullscreen = bool(self.current_display.get_flags() & pygame.FULLSCREEN)

            # Force pygame to reinitialize display subsystem
            pygame.display.quit()
            time.sleep(0.2)  # Allow time for cleanup
            pygame.display.init()

            # Re-detect displays after reset
            self.display_info = {}
            self.detect_displays()

            # Recreate display with current settings
            if current_size:
                new_surface, _, _ = self.create_compatible_display(
                    preferred_width=current_size[0],
                    preferred_height=current_size[1],
                    fullscreen=current_fullscreen
                )
            else:
                # Use recommended settings
                width, height = self.get_recommended_resolution()
                new_surface, _, _ = self.create_compatible_display(
                    preferred_width=width,
                    preferred_height=height,
                    fullscreen=False
                )

            print("Display reset completed successfully")
            return True

        except Exception as e:
            print(f"Error during display reset: {e}")
            return False

    def handle_display_mode_switch(self, old_surface: pygame.Surface, 
                               new_width: int, new_height: int, 
                               fullscreen: bool = False) -> Optional[pygame.Surface]:
        """
        Handle display mode switching with stability

        Args:
            old_surface: Current display surface
            new_width: Target width
            new_height: Target height
            fullscreen: Whether to use fullscreen mode
            
        Returns:
            New display surface or None if failed
        """
        try:
            # Define minimum screen dimensions for proper display
            MIN_WIDTH, MIN_HEIGHT = 1024, 768
            
            # Ensure minimum dimensions
            new_width = max(new_width, MIN_WIDTH)
            new_height = max(new_height, MIN_HEIGHT)
            
            print(f"Handling display mode switch to {new_width}x{new_height} (fullscreen: {fullscreen})")
            
            # Clear current display before switching to prevent flickering
            if old_surface:
                old_surface.fill((0, 0, 0))
                pygame.display.flip()
            
            # Set up double-buffering to prevent flickering
            pygame.display.gl_set_attribute(pygame.GL_DOUBLEBUFFER, 1)
            
            # Set the new display mode with appropriate flags
            if fullscreen:
                flags = pygame.FULLSCREEN | pygame.SCALED | pygame.DOUBLEBUF
            else:
                flags = pygame.RESIZABLE | pygame.DOUBLEBUF
                
            new_surface = pygame.display.set_mode((new_width, new_height), flags)
            
            # Apply basic optimization
            self._optimize_display(new_surface)
            
            # Update global scaling factors if they exist in the main module
            try:
                import sys
                main_module = sys.modules.get('__main__')
                if main_module and hasattr(main_module, 'scale_x') and hasattr(main_module, 'scale_y'):
                    # Get base dimensions from main module
                    base_width = getattr(main_module, 'BASE_WIDTH', 1024)
                    base_height = getattr(main_module, 'BASE_HEIGHT', 768)
                    
                    # Update scaling factors
                    main_module.scale_x = new_width / base_width
                    main_module.scale_y = new_height / base_height
                    
                    # Update game instance if it exists
                    if hasattr(main_module, 'game') and hasattr(main_module.game, 'update_scaling'):
                        main_module.game.update_scaling()
                        
                    print(f"Updated scaling factors: scale_x={main_module.scale_x}, scale_y={main_module.scale_y}")
            except Exception as e:
                print(f"Note: Could not update scaling factors: {e}")
            
            return new_surface
                
        except Exception as e:
            print(f"Error in display mode switch: {e}")
            # Try fallback to original surface
            return old_surface

    def get_recommended_resolution(self) -> Tuple[int, int]:
        """
        Get recommended display resolution based on current display capabilities
        
        Returns:
            Tuple of (width, height)
        """
        try:
            # Get current screen info
            screen_info = pygame.display.Info()
            current_w, current_h = screen_info.current_w, screen_info.current_h
            
            # For windowed mode, use 85% of screen size
            windowed_w = int(current_w * 0.85)
            windowed_h = int(current_h * 0.85)
            
            # Ensure minimum size
            windowed_w = max(windowed_w, 800)
            windowed_h = max(windowed_h, 600)
            
            return windowed_w, windowed_h
            
        except Exception as e:
            print(f"Error getting recommended resolution: {e}")
            return 1024, 768  # Safe default
            
    def detect_display_change_and_recover(self) -> bool:
        """
        Detect display changes and recover if needed
        
        DISABLED to prevent automatic window resizing.
        This method was causing the window to resize automatically after some time.

        Returns:
            Always returns False to prevent any display changes
        """
        # Always return False to prevent any display changes
        # This ensures the method won't trigger any resizing
        return False


# Global instance
_display_manager = None

def get_external_display_manager() -> UniversalDisplayManager:
    """Get the global display manager instance"""
    global _display_manager
    if _display_manager is None:
        _display_manager = UniversalDisplayManager()
    return _display_manager