2025-07-21 11:08:06.167 - ensure_database_exists called from thread 4528
2025-07-21 11:08:06.169 - Creating new thread-specific database connection to data\stats.db for thread 4528
2025-07-21 11:08:06.169 - New database connection created successfully for thread 4528
2025-07-21 11:08:08.422 - Stats database initialized successfully
2025-07-21 11:08:08.427 - ensure_database_exists called from thread 4528
2025-07-21 11:08:08.431 - Using existing connection for thread 4528
2025-07-21 11:08:08.437 - Stats database initialized successfully
2025-07-21 11:08:08.441 - ensure_database_exists called from thread 4528
2025-07-21 11:08:08.444 - Using existing connection for thread 4528
2025-07-21 11:08:08.448 - Stats database initialized successfully
2025-07-21 11:10:31.525 - Using existing connection for thread 4528
2025-07-21 11:10:31.596 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:10:31.598 - New database connection created successfully for thread 16400
2025-07-21 11:10:31.611 - Database connection closed for thread 16400
2025-07-21 11:10:31.616 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:10:31.623 - New database connection created successfully for thread 16400
2025-07-21 11:10:31.632 - get_summary_stats called from thread 16400
2025-07-21 11:10:31.633 - Using existing connection for thread 16400
2025-07-21 11:10:31.634 - Total earnings from database: 0
2025-07-21 11:10:31.635 - Daily earnings from database: 0
2025-07-21 11:10:31.637 - Daily games from database: 0
2025-07-21 11:10:31.638 - Wallet balance from database: 0
2025-07-21 11:10:31.639 - Total games played from database: 0
2025-07-21 11:10:31.639 - Total winners from database: 0
2025-07-21 11:10:31.640 - Returning summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:11:00.135 - Using existing connection for thread 4528
2025-07-21 11:11:00.217 - Using existing connection for thread 16400
2025-07-21 11:11:00.749 - Database connection closed for thread 16400
2025-07-21 11:11:00.757 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:11:00.762 - New database connection created successfully for thread 16400
2025-07-21 11:11:00.774 - Database connection closed for thread 16400
2025-07-21 11:11:00.778 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:11:00.782 - New database connection created successfully for thread 16400
2025-07-21 11:11:19.295 - Using existing connection for thread 16400
2025-07-21 11:12:22.039 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.040 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 11:12:22.042 - New database connection created successfully for thread 14892
2025-07-21 11:12:22.044 - Stats database initialized successfully
2025-07-21 11:12:22.045 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.046 - Using existing connection for thread 14892
2025-07-21 11:12:22.046 - Stats database initialized successfully
2025-07-21 11:12:22.047 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.048 - Using existing connection for thread 14892
2025-07-21 11:12:22.048 - Stats database initialized successfully
2025-07-21 11:13:47.102 - Using existing connection for thread 14892
2025-07-21 11:13:47.103 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.107 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.271 - Database connection closed for thread 10768
2025-07-21 11:13:47.273 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.275 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.288 - Database connection closed for thread 10768
2025-07-21 11:13:47.290 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.292 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.346 - get_summary_stats called from thread 10768
2025-07-21 11:13:47.348 - Using existing connection for thread 10768
2025-07-21 11:13:47.349 - Total earnings from database: 0.0
2025-07-21 11:13:47.350 - Daily earnings from database: 0.0
2025-07-21 11:13:47.350 - Daily games from database: 0
2025-07-21 11:13:47.352 - Wallet balance from database: 0
2025-07-21 11:13:47.353 - Total games played from database: 0
2025-07-21 11:13:47.355 - Total winners from database: 0
2025-07-21 11:13:47.360 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:53.210 - Using existing connection for thread 10768
2025-07-21 22:30:28.847 - ensure_database_exists called from thread 8380
2025-07-21 22:30:28.915 - Creating new thread-specific database connection to data\stats.db for thread 8380
2025-07-21 22:30:28.977 - New database connection created successfully for thread 8380
2025-07-21 22:30:29.095 - Stats database initialized successfully
2025-07-21 22:30:29.097 - ensure_database_exists called from thread 8380
2025-07-21 22:30:29.099 - Using existing connection for thread 8380
2025-07-21 22:30:29.106 - Stats database initialized successfully
2025-07-21 22:30:29.111 - ensure_database_exists called from thread 8380
2025-07-21 22:30:29.115 - Using existing connection for thread 8380
2025-07-21 22:30:29.122 - Stats database initialized successfully
2025-07-21 22:32:08.725 - Using existing connection for thread 8380
2025-07-21 22:32:08.812 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:08.815 - New database connection created successfully for thread 11044
2025-07-21 22:32:08.999 - Database connection closed for thread 11044
2025-07-21 22:32:09.006 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:09.016 - New database connection created successfully for thread 11044
2025-07-21 22:32:09.059 - Database connection closed for thread 11044
2025-07-21 22:32:09.061 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:09.063 - New database connection created successfully for thread 11044
2025-07-21 22:32:09.096 - get_summary_stats called from thread 11044
2025-07-21 22:32:09.101 - Using existing connection for thread 11044
2025-07-21 22:32:09.105 - Total earnings from database: 0.0
2025-07-21 22:32:09.107 - Daily earnings from database: 0.0
2025-07-21 22:32:09.109 - Daily games from database: 0
2025-07-21 22:32:09.110 - Wallet balance from database: 0
2025-07-21 22:32:09.113 - Total games played from database: 0
2025-07-21 22:32:09.115 - Total winners from database: 0
2025-07-21 22:32:09.119 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:17.764 - Using existing connection for thread 11044
2025-07-21 22:32:30.233 - Using existing connection for thread 8380
2025-07-21 22:32:30.267 - Using existing connection for thread 11044
2025-07-21 22:32:30.296 - Database connection closed for thread 11044
2025-07-21 22:32:30.300 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:30.305 - New database connection created successfully for thread 11044
2025-07-21 22:32:30.312 - Database connection closed for thread 11044
2025-07-21 22:32:30.317 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:30.327 - New database connection created successfully for thread 11044
2025-07-21 22:32:36.316 - Using existing connection for thread 11044
2025-07-21 22:32:48.343 - Using existing connection for thread 8380
2025-07-21 22:32:48.435 - Using existing connection for thread 11044
2025-07-21 22:32:48.459 - Database connection closed for thread 11044
2025-07-21 22:32:48.464 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:48.465 - New database connection created successfully for thread 11044
2025-07-21 22:32:48.477 - Database connection closed for thread 11044
2025-07-21 22:32:48.479 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:48.482 - New database connection created successfully for thread 11044
2025-07-21 22:32:58.652 - Using existing connection for thread 11044
2025-07-21 22:35:50.419 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.421 - Creating new thread-specific database connection to data\stats.db for thread 17024
2025-07-21 22:35:50.423 - New database connection created successfully for thread 17024
2025-07-21 22:35:50.427 - Stats database initialized successfully
2025-07-21 22:35:50.429 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.431 - Using existing connection for thread 17024
2025-07-21 22:35:50.432 - Stats database initialized successfully
2025-07-21 22:35:50.434 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.436 - Using existing connection for thread 17024
2025-07-21 22:35:50.438 - Stats database initialized successfully
