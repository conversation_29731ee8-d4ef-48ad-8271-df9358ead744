2025-07-21 11:08:06.167 - ensure_database_exists called from thread 4528
2025-07-21 11:08:06.169 - Creating new thread-specific database connection to data\stats.db for thread 4528
2025-07-21 11:08:06.169 - New database connection created successfully for thread 4528
2025-07-21 11:08:08.422 - Stats database initialized successfully
2025-07-21 11:08:08.427 - ensure_database_exists called from thread 4528
2025-07-21 11:08:08.431 - Using existing connection for thread 4528
2025-07-21 11:08:08.437 - Stats database initialized successfully
2025-07-21 11:08:08.441 - ensure_database_exists called from thread 4528
2025-07-21 11:08:08.444 - Using existing connection for thread 4528
2025-07-21 11:08:08.448 - Stats database initialized successfully
2025-07-21 11:10:31.525 - Using existing connection for thread 4528
2025-07-21 11:10:31.596 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:10:31.598 - New database connection created successfully for thread 16400
2025-07-21 11:10:31.611 - Database connection closed for thread 16400
2025-07-21 11:10:31.616 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:10:31.623 - New database connection created successfully for thread 16400
2025-07-21 11:10:31.632 - get_summary_stats called from thread 16400
2025-07-21 11:10:31.633 - Using existing connection for thread 16400
2025-07-21 11:10:31.634 - Total earnings from database: 0
2025-07-21 11:10:31.635 - Daily earnings from database: 0
2025-07-21 11:10:31.637 - Daily games from database: 0
2025-07-21 11:10:31.638 - Wallet balance from database: 0
2025-07-21 11:10:31.639 - Total games played from database: 0
2025-07-21 11:10:31.639 - Total winners from database: 0
2025-07-21 11:10:31.640 - Returning summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:11:00.135 - Using existing connection for thread 4528
2025-07-21 11:11:00.217 - Using existing connection for thread 16400
2025-07-21 11:11:00.749 - Database connection closed for thread 16400
2025-07-21 11:11:00.757 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:11:00.762 - New database connection created successfully for thread 16400
2025-07-21 11:11:00.774 - Database connection closed for thread 16400
2025-07-21 11:11:00.778 - Creating new thread-specific database connection to data\stats.db for thread 16400
2025-07-21 11:11:00.782 - New database connection created successfully for thread 16400
2025-07-21 11:11:19.295 - Using existing connection for thread 16400
2025-07-21 11:12:22.039 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.040 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 11:12:22.042 - New database connection created successfully for thread 14892
2025-07-21 11:12:22.044 - Stats database initialized successfully
2025-07-21 11:12:22.045 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.046 - Using existing connection for thread 14892
2025-07-21 11:12:22.046 - Stats database initialized successfully
2025-07-21 11:12:22.047 - ensure_database_exists called from thread 14892
2025-07-21 11:12:22.048 - Using existing connection for thread 14892
2025-07-21 11:12:22.048 - Stats database initialized successfully
2025-07-21 11:13:47.102 - Using existing connection for thread 14892
2025-07-21 11:13:47.103 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.107 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.271 - Database connection closed for thread 10768
2025-07-21 11:13:47.273 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.275 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.288 - Database connection closed for thread 10768
2025-07-21 11:13:47.290 - Creating new thread-specific database connection to data\stats.db for thread 10768
2025-07-21 11:13:47.292 - New database connection created successfully for thread 10768
2025-07-21 11:13:47.346 - get_summary_stats called from thread 10768
2025-07-21 11:13:47.348 - Using existing connection for thread 10768
2025-07-21 11:13:47.349 - Total earnings from database: 0.0
2025-07-21 11:13:47.350 - Daily earnings from database: 0.0
2025-07-21 11:13:47.350 - Daily games from database: 0
2025-07-21 11:13:47.352 - Wallet balance from database: 0
2025-07-21 11:13:47.353 - Total games played from database: 0
2025-07-21 11:13:47.355 - Total winners from database: 0
2025-07-21 11:13:47.360 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 11:13:53.210 - Using existing connection for thread 10768
2025-07-21 22:30:28.847 - ensure_database_exists called from thread 8380
2025-07-21 22:30:28.915 - Creating new thread-specific database connection to data\stats.db for thread 8380
2025-07-21 22:30:28.977 - New database connection created successfully for thread 8380
2025-07-21 22:30:29.095 - Stats database initialized successfully
2025-07-21 22:30:29.097 - ensure_database_exists called from thread 8380
2025-07-21 22:30:29.099 - Using existing connection for thread 8380
2025-07-21 22:30:29.106 - Stats database initialized successfully
2025-07-21 22:30:29.111 - ensure_database_exists called from thread 8380
2025-07-21 22:30:29.115 - Using existing connection for thread 8380
2025-07-21 22:30:29.122 - Stats database initialized successfully
2025-07-21 22:32:08.725 - Using existing connection for thread 8380
2025-07-21 22:32:08.812 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:08.815 - New database connection created successfully for thread 11044
2025-07-21 22:32:08.999 - Database connection closed for thread 11044
2025-07-21 22:32:09.006 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:09.016 - New database connection created successfully for thread 11044
2025-07-21 22:32:09.059 - Database connection closed for thread 11044
2025-07-21 22:32:09.061 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:09.063 - New database connection created successfully for thread 11044
2025-07-21 22:32:09.096 - get_summary_stats called from thread 11044
2025-07-21 22:32:09.101 - Using existing connection for thread 11044
2025-07-21 22:32:09.105 - Total earnings from database: 0.0
2025-07-21 22:32:09.107 - Daily earnings from database: 0.0
2025-07-21 22:32:09.109 - Daily games from database: 0
2025-07-21 22:32:09.110 - Wallet balance from database: 0
2025-07-21 22:32:09.113 - Total games played from database: 0
2025-07-21 22:32:09.115 - Total winners from database: 0
2025-07-21 22:32:09.119 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-21 22:32:17.764 - Using existing connection for thread 11044
2025-07-21 22:32:30.233 - Using existing connection for thread 8380
2025-07-21 22:32:30.267 - Using existing connection for thread 11044
2025-07-21 22:32:30.296 - Database connection closed for thread 11044
2025-07-21 22:32:30.300 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:30.305 - New database connection created successfully for thread 11044
2025-07-21 22:32:30.312 - Database connection closed for thread 11044
2025-07-21 22:32:30.317 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:30.327 - New database connection created successfully for thread 11044
2025-07-21 22:32:36.316 - Using existing connection for thread 11044
2025-07-21 22:32:48.343 - Using existing connection for thread 8380
2025-07-21 22:32:48.435 - Using existing connection for thread 11044
2025-07-21 22:32:48.459 - Database connection closed for thread 11044
2025-07-21 22:32:48.464 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:48.465 - New database connection created successfully for thread 11044
2025-07-21 22:32:48.477 - Database connection closed for thread 11044
2025-07-21 22:32:48.479 - Creating new thread-specific database connection to data\stats.db for thread 11044
2025-07-21 22:32:48.482 - New database connection created successfully for thread 11044
2025-07-21 22:32:58.652 - Using existing connection for thread 11044
2025-07-21 22:35:50.419 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.421 - Creating new thread-specific database connection to data\stats.db for thread 17024
2025-07-21 22:35:50.423 - New database connection created successfully for thread 17024
2025-07-21 22:35:50.427 - Stats database initialized successfully
2025-07-21 22:35:50.429 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.431 - Using existing connection for thread 17024
2025-07-21 22:35:50.432 - Stats database initialized successfully
2025-07-21 22:35:50.434 - ensure_database_exists called from thread 17024
2025-07-21 22:35:50.436 - Using existing connection for thread 17024
2025-07-21 22:35:50.438 - Stats database initialized successfully
2025-07-21 22:49:29.300 - ensure_database_exists called from thread 15864
2025-07-21 22:49:29.302 - Creating new thread-specific database connection to data\stats.db for thread 15864
2025-07-21 22:49:29.305 - New database connection created successfully for thread 15864
2025-07-21 22:49:29,310 - INFO - Database schema initialized successfully
2025-07-21 22:49:29.311 - Stats database initialized successfully
2025-07-21 22:49:29,319 - INFO - Loaded 0 items from cache
2025-07-21 22:49:29,320 - INFO - Started background data loading
2025-07-21 22:49:29,320 - INFO - OptimizedStatsLoader initialized
2025-07-21 22:49:29,321 - INFO - Using optimized stats loader for integration
2025-07-21 22:49:29.321 - ensure_database_exists called from thread 15864
2025-07-21 22:49:29,323 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:49:29.321 - Using existing connection for thread 15864
2025-07-21 22:49:29,324 - INFO - Loaded summary data
2025-07-21 22:49:29,324 - INFO - Database schema initialized successfully
2025-07-21 22:49:29,324 - INFO - Loaded game history page 0 (0 records)
2025-07-21 22:49:29,324 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:49:29.324 - Stats database initialized successfully
2025-07-21 22:49:29,328 - INFO - Saved 7 items to cache
2025-07-21 22:49:29,329 - INFO - Background data loading completed
2025-07-21 22:49:29,330 - INFO - Stats database initialized successfully
2025-07-21 22:49:29,333 - INFO - Game stats integration module available
2025-07-21 22:49:29,333 - INFO - Started stats event worker thread
2025-07-21 22:49:29,333 - INFO - Stats event hooks initialized
2025-07-21 22:49:29.336 - Using existing connection for thread 15864
2025-07-21 22:49:29,341 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 22:49:29,341 - INFO - Game data: {'winner_name': 'TestPlayer', 'winner_cartella': 1, 'claim_type': 'Full House', 'game_duration': 120, 'player_count': 3, 'prize_amount': 150, 'commission_percentage': 20, 'called_numbers': [1, 2, 3, 4, 5, 10, 15, 20, 25, 30], 'is_demo_mode': False, 'date_time': '2025-07-21 22:49:29', 'stake': 50, 'bet_amount': 50}
2025-07-21 22:49:29.342 - Using existing connection for thread 15864
2025-07-21 22:49:29,491 - INFO - Game statistics recorded in database (ID: 1)
2025-07-21 22:49:29,491 - INFO - Game winner recorded via thread_safe_db.py - Winner: TestPlayer, Status: Won
2025-07-21 22:49:29.523 - Database connection closed for thread 15864
2025-07-21 22:49:29,530 - INFO - ================================================================================
2025-07-21 22:49:29,530 - INFO - RECORDING GAME COMPLETED IN GAME_STATS_INTEGRATION
2025-07-21 22:49:29,530 - INFO - Game data: {'winner_name': 'TestPlayer', 'winner_cartella': 1, 'claim_type': 'Full House', 'game_duration': 120, 'player_count': 3, 'prize_amount': 150, 'commission_percentage': 20, 'called_numbers': [1, 2, 3, 4, 5, 10, 15, 20, 25, 30], 'is_demo_mode': False, 'date_time': '2025-07-21 22:49:29', 'stake': 50, 'bet_amount': 50}
2025-07-21 22:49:29,530 - INFO - ================================================================================
2025-07-21 22:49:29.530 - ensure_database_exists called from thread 15864
2025-07-21 22:49:29.532 - Creating new thread-specific database connection to data\stats.db for thread 15864
2025-07-21 22:49:29.536 - New database connection created successfully for thread 15864
2025-07-21 22:49:29,540 - INFO - Database schema initialized successfully
2025-07-21 22:49:29.540 - Stats database initialized successfully
2025-07-21 22:49:29,541 - INFO - Verified database schema exists
2025-07-21 22:49:29.542 - Database connection closed for thread 15864
2025-07-21 22:49:29,545 - INFO - Closed existing database connection to ensure fresh data
2025-07-21 22:49:29,545 - INFO - Recording game completion via thread_safe_db
2025-07-21 22:49:29.545 - Creating new thread-specific database connection to data\stats.db for thread 15864
2025-07-21 22:49:29.546 - New database connection created successfully for thread 15864
2025-07-21 22:49:29,553 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-07-21 22:49:29,554 - INFO - Game data: {'winner_name': 'TestPlayer', 'winner_cartella': 1, 'claim_type': 'Full House', 'game_duration': 120, 'player_count': 3, 'prize_amount': 150, 'commission_percentage': 20, 'called_numbers': [1, 2, 3, 4, 5, 10, 15, 20, 25, 30], 'is_demo_mode': False, 'date_time': '2025-07-21 22:49:29', 'stake': 50, 'bet_amount': 50}
2025-07-21 22:49:29.554 - Using existing connection for thread 15864
2025-07-21 22:49:29,686 - INFO - Game statistics recorded in database (ID: 2)
2025-07-21 22:49:29,687 - INFO - Game winner recorded via thread_safe_db.py - Winner: TestPlayer, Status: Won
2025-07-21 22:49:29.745 - Database connection closed for thread 15864
2025-07-21 22:49:29,750 - INFO - Game completion recorded: True
2025-07-21 22:49:31,186 - INFO - Started background data loading
2025-07-21 22:49:31,186 - INFO - Forced data refresh
2025-07-21 22:49:31,186 - INFO - Refreshed optimized loader data after game completion
2025-07-21 22:49:31,187 - INFO - Loaded weekly stats for 7 days
2025-07-21 22:49:31.186 - Creating new thread-specific database connection to data\stats.db for thread 15864
2025-07-21 22:49:31,188 - INFO - Loaded summary data
2025-07-21 22:49:31,189 - INFO - Loaded game history page 0 (2 records)
2025-07-21 22:49:31,189 - INFO - Loaded game history metadata (total pages: 1)
2025-07-21 22:49:31.188 - New database connection created successfully for thread 15864
2025-07-21 22:49:31,191 - INFO - Saved 7 items to cache
2025-07-21 22:49:31,192 - INFO - Background data loading completed
2025-07-21 22:49:31,192 - INFO - Verified game_history now has 2 records
2025-07-21 22:49:31,193 - INFO - Latest game record: ID=2, Date=2025-07-21 22:49:29, Winner=TestPlayer, Players=3, Stake=50.0, Fee=30.0, Tips=0.0
2025-07-21 22:51:27.558 - ensure_database_exists called from thread 12216
2025-07-21 22:51:27.559 - Creating new thread-specific database connection to data\stats.db for thread 12216
2025-07-21 22:51:27.618 - New database connection created successfully for thread 12216
2025-07-21 22:51:27.623 - Stats database initialized successfully
2025-07-21 22:51:27.623 - ensure_database_exists called from thread 12216
2025-07-21 22:51:27.624 - Using existing connection for thread 12216
2025-07-21 22:51:27.625 - Stats database initialized successfully
2025-07-21 22:51:27.627 - ensure_database_exists called from thread 12216
2025-07-21 22:51:27.628 - Using existing connection for thread 12216
2025-07-21 22:51:27.629 - Stats database initialized successfully
2025-07-21 22:56:48.696 - ensure_database_exists called from thread 2888
2025-07-21 22:56:48.709 - Creating new thread-specific database connection to data\stats.db for thread 2888
2025-07-21 22:56:48.711 - New database connection created successfully for thread 2888
2025-07-21 22:56:48.714 - Stats database initialized successfully
2025-07-21 22:56:48.716 - ensure_database_exists called from thread 2888
2025-07-21 22:56:48.721 - Using existing connection for thread 2888
2025-07-21 22:56:48.723 - Stats database initialized successfully
2025-07-21 22:56:50.454 - Creating new thread-specific database connection to data\stats.db for thread 7480
2025-07-21 22:56:50.456 - New database connection created successfully for thread 7480
2025-07-21 22:58:58.467 - ensure_database_exists called from thread 13712
2025-07-21 22:58:58.471 - Creating new thread-specific database connection to data\stats.db for thread 13712
2025-07-21 22:58:58.476 - New database connection created successfully for thread 13712
2025-07-21 22:58:58.479 - Stats database initialized successfully
2025-07-21 22:58:58.488 - ensure_database_exists called from thread 13712
2025-07-21 22:58:58.498 - Using existing connection for thread 13712
2025-07-21 22:58:58.501 - Stats database initialized successfully
2025-07-21 22:58:58.505 - ensure_database_exists called from thread 13712
2025-07-21 22:58:58.507 - Using existing connection for thread 13712
2025-07-21 22:58:58.509 - Stats database initialized successfully
2025-07-21 22:59:35.303 - Using existing connection for thread 13712
2025-07-21 22:59:35.382 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 22:59:35.383 - New database connection created successfully for thread 11688
2025-07-21 22:59:35.563 - Database connection closed for thread 11688
2025-07-21 22:59:35.565 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 22:59:35.568 - New database connection created successfully for thread 11688
2025-07-21 22:59:35.624 - Database connection closed for thread 11688
2025-07-21 22:59:35.625 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 22:59:35.626 - New database connection created successfully for thread 11688
2025-07-21 22:59:35.635 - get_summary_stats called from thread 11688
2025-07-21 22:59:35.639 - Using existing connection for thread 11688
2025-07-21 22:59:35.640 - Total earnings from database: 60.0
2025-07-21 22:59:35.641 - Daily earnings from database: 60.0
2025-07-21 22:59:35.642 - Daily games from database: 2
2025-07-21 22:59:35.643 - Wallet balance from database: 0
2025-07-21 22:59:35.644 - Total games played from database: 2
2025-07-21 22:59:35.644 - Total winners from database: 2
2025-07-21 22:59:35.646 - Returning summary stats: {'total_earnings': 60.0, 'daily_earnings': 60.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-21 22:59:47.963 - Using existing connection for thread 11688
2025-07-21 22:59:48.136 - Using existing connection for thread 13712
2025-07-21 22:59:48.227 - Using existing connection for thread 11688
2025-07-21 22:59:48.229 - Using existing connection for thread 11688
2025-07-21 22:59:48.287 - Using existing connection for thread 11688
2025-07-21 22:59:48.291 - Using existing connection for thread 11688
2025-07-21 22:59:56.427 - Using existing connection for thread 13712
2025-07-21 22:59:56.487 - Using existing connection for thread 11688
2025-07-21 22:59:56.505 - Database connection closed for thread 11688
2025-07-21 22:59:56.506 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 22:59:56.507 - New database connection created successfully for thread 11688
2025-07-21 22:59:56.513 - Database connection closed for thread 11688
2025-07-21 22:59:56.516 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 22:59:56.519 - New database connection created successfully for thread 11688
2025-07-21 23:00:09.824 - Using existing connection for thread 11688
2025-07-21 23:00:09.999 - Using existing connection for thread 11688
2025-07-21 23:00:10.001 - Using existing connection for thread 11688
2025-07-21 23:00:24.996 - Using existing connection for thread 13712
2025-07-21 23:00:25.441 - Using existing connection for thread 11688
2025-07-21 23:00:25.445 - Using existing connection for thread 11688
2025-07-21 23:01:51.366 - Using existing connection for thread 13712
2025-07-21 23:01:51.412 - Using existing connection for thread 11688
2025-07-21 23:01:51.439 - Database connection closed for thread 11688
2025-07-21 23:01:51.440 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 23:01:51.441 - New database connection created successfully for thread 11688
2025-07-21 23:01:51.447 - Database connection closed for thread 11688
2025-07-21 23:01:51.448 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 23:01:51.449 - New database connection created successfully for thread 11688
2025-07-21 23:02:01.602 - Using existing connection for thread 11688
2025-07-21 23:02:01.771 - Using existing connection for thread 11688
2025-07-21 23:02:01.774 - Using existing connection for thread 11688
2025-07-21 23:02:16.717 - Using existing connection for thread 13712
2025-07-21 23:02:16.922 - Using existing connection for thread 11688
2025-07-21 23:02:16.957 - Using existing connection for thread 11688
2025-07-21 23:02:35.607 - Using existing connection for thread 13712
2025-07-21 23:02:35.638 - Using existing connection for thread 11688
2025-07-21 23:02:35.650 - Database connection closed for thread 11688
2025-07-21 23:02:35.651 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 23:02:35.653 - New database connection created successfully for thread 11688
2025-07-21 23:02:35.671 - Database connection closed for thread 11688
2025-07-21 23:02:35.676 - Creating new thread-specific database connection to data\stats.db for thread 11688
2025-07-21 23:02:35.685 - New database connection created successfully for thread 11688
2025-07-21 23:02:49.794 - Using existing connection for thread 11688
2025-07-21 23:02:49.984 - Using existing connection for thread 11688
2025-07-21 23:02:49.986 - Using existing connection for thread 11688
2025-07-21 23:03:09.950 - Using existing connection for thread 13712
2025-07-21 23:03:10.146 - Using existing connection for thread 11688
2025-07-21 23:03:10.149 - Using existing connection for thread 11688
2025-07-21 23:12:01.764 - ensure_database_exists called from thread 10924
2025-07-21 23:12:01.766 - Creating new thread-specific database connection to data\stats.db for thread 10924
2025-07-21 23:12:01.768 - New database connection created successfully for thread 10924
2025-07-21 23:12:01.769 - Stats database initialized successfully
2025-07-21 23:12:01.770 - ensure_database_exists called from thread 10924
2025-07-21 23:12:01.771 - Using existing connection for thread 10924
2025-07-21 23:12:01.772 - Stats database initialized successfully
2025-07-21 23:12:01.773 - ensure_database_exists called from thread 10924
2025-07-21 23:12:01.775 - Using existing connection for thread 10924
2025-07-21 23:12:01.776 - Stats database initialized successfully
2025-07-21 23:14:03.190 - ensure_database_exists called from thread 16580
2025-07-21 23:14:03.192 - Creating new thread-specific database connection to data\stats.db for thread 16580
2025-07-21 23:14:03.194 - New database connection created successfully for thread 16580
2025-07-21 23:14:03.197 - Stats database initialized successfully
2025-07-21 23:14:03.199 - ensure_database_exists called from thread 16580
2025-07-21 23:14:03.200 - Using existing connection for thread 16580
2025-07-21 23:14:03.201 - Stats database initialized successfully
2025-07-21 23:14:03.202 - ensure_database_exists called from thread 16580
2025-07-21 23:14:03.203 - Using existing connection for thread 16580
2025-07-21 23:14:03.204 - Stats database initialized successfully
2025-07-21 23:14:59.284 - Using existing connection for thread 16580
2025-07-21 23:14:59.324 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 23:14:59.325 - New database connection created successfully for thread 14892
2025-07-21 23:14:59.569 - Database connection closed for thread 14892
2025-07-21 23:14:59.570 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 23:14:59.571 - New database connection created successfully for thread 14892
2025-07-21 23:14:59.593 - Database connection closed for thread 14892
2025-07-21 23:14:59.597 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 23:14:59.599 - New database connection created successfully for thread 14892
2025-07-21 23:14:59.623 - get_summary_stats called from thread 14892
2025-07-21 23:14:59.624 - Using existing connection for thread 14892
2025-07-21 23:14:59.627 - Total earnings from database: 276.0
2025-07-21 23:14:59.628 - Daily earnings from database: 276.0
2025-07-21 23:14:59.629 - Daily games from database: 15
2025-07-21 23:14:59.631 - Wallet balance from database: 60.0
2025-07-21 23:14:59.632 - Total games played from database: 15
2025-07-21 23:14:59.634 - Total winners from database: 2
2025-07-21 23:14:59.638 - Returning summary stats: {'total_earnings': 276.0, 'daily_earnings': 276.0, 'daily_games': 15, 'wallet_balance': 60.0}
2025-07-21 23:15:09.304 - Using existing connection for thread 14892
2025-07-21 23:15:09.673 - Using existing connection for thread 14892
2025-07-21 23:15:09.680 - Using existing connection for thread 14892
2025-07-21 23:15:09.682 - Using existing connection for thread 16580
2025-07-21 23:15:09.839 - Using existing connection for thread 14892
2025-07-21 23:15:09.844 - Using existing connection for thread 14892
2025-07-21 23:15:21.286 - Using existing connection for thread 16580
2025-07-21 23:15:21.334 - Using existing connection for thread 14892
2025-07-21 23:15:21.363 - Database connection closed for thread 14892
2025-07-21 23:15:21.364 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 23:15:21.365 - New database connection created successfully for thread 14892
2025-07-21 23:15:21.372 - Database connection closed for thread 14892
2025-07-21 23:15:21.374 - Creating new thread-specific database connection to data\stats.db for thread 14892
2025-07-21 23:15:21.374 - New database connection created successfully for thread 14892
2025-07-21 23:15:33.220 - Using existing connection for thread 14892
2025-07-21 23:15:33.364 - Using existing connection for thread 14892
2025-07-21 23:15:33.367 - Using existing connection for thread 14892
2025-07-21 23:15:48.454 - Using existing connection for thread 16580
2025-07-21 23:15:48.584 - Using existing connection for thread 14892
2025-07-21 23:15:48.585 - Using existing connection for thread 14892
