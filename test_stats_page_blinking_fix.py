#!/usr/bin/env python3
"""
Test script to verify that the stats page blinking issue has been fixed.

This script will:
1. Import the stats page module
2. Create a stats page instance
3. Simulate the loading process
4. Check for stable loading states
5. Verify no continuous state toggling occurs
"""

import sys
import time
import threading
from unittest.mock import Mock, patch

def test_stats_page_stability():
    """Test that the stats page loading state is stable and doesn't blink."""
    
    print("🧪 Testing Stats Page Blinking Fix")
    print("=" * 50)
    
    try:
        # Import the fixed stats page
        print("📦 Importing stats page module...")
        
        # Mock pygame to avoid initialization issues
        with patch('pygame.init'), \
             patch('pygame.display.set_mode'), \
             patch('pygame.font.Font'), \
             patch('pygame.time.get_ticks', return_value=1000):
            
            # Import after mocking pygame
            from stats_page import StatsPage
            
            print("✅ Stats page module imported successfully")
            
            # Create a mock screen
            mock_screen = Mock()
            mock_screen.get_size.return_value = (1024, 768)
            
            # Create stats page instance
            print("🏗️  Creating stats page instance...")
            stats_page = StatsPage(mock_screen)
            
            print("✅ Stats page instance created successfully")
            
            # Test 1: Check if stable loading state attributes exist
            print("\n🔍 Test 1: Checking stable loading state attributes...")
            
            required_attrs = [
                '_stable_loading_complete',
                '_loading_state_lock',
                'game_history_loading_complete'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if hasattr(stats_page, attr):
                    print(f"   ✅ {attr} - Present")
                else:
                    print(f"   ❌ {attr} - Missing")
                    missing_attrs.append(attr)
            
            if missing_attrs:
                print(f"   ⚠️  Missing attributes: {missing_attrs}")
                return False
            
            # Test 2: Test loading state stability
            print("\n🔍 Test 2: Testing loading state stability...")
            
            # Simulate multiple rapid state checks (like what happens during blinking)
            stable_states = []
            for i in range(10):
                # Check the loading state multiple times rapidly
                if hasattr(stats_page, '_stable_loading_complete'):
                    stable_states.append(stats_page._stable_loading_complete)
                time.sleep(0.01)  # 10ms intervals
            
            # Check if state remained consistent
            if len(set(stable_states)) <= 1:
                print("   ✅ Loading state remained stable during rapid checks")
            else:
                print(f"   ❌ Loading state changed during rapid checks: {stable_states}")
                return False
            
            # Test 3: Test thread-safe loading state management
            print("\n🔍 Test 3: Testing thread-safe loading state management...")
            
            def simulate_loading_completion():
                """Simulate background loading completion."""
                try:
                    with stats_page._loading_state_lock:
                        stats_page.game_history_loading_complete = True
                        stats_page._stable_loading_complete = True
                    return True
                except Exception as e:
                    print(f"   ❌ Error in thread-safe loading: {e}")
                    return False
            
            # Test concurrent access
            threads = []
            results = []
            
            for i in range(5):
                def worker():
                    result = simulate_loading_completion()
                    results.append(result)
                
                thread = threading.Thread(target=worker)
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            if all(results):
                print("   ✅ Thread-safe loading state management works correctly")
            else:
                print(f"   ❌ Thread-safe loading failed: {results}")
                return False
            
            # Test 4: Test anti-blink display logic
            print("\n🔍 Test 4: Testing anti-blink display logic...")
            
            # Simulate the display logic that was causing blinking
            try:
                # This is the new logic that should prevent blinking
                loading_complete = (hasattr(stats_page, '_stable_loading_complete') and stats_page._stable_loading_complete) or \
                                 (hasattr(stats_page, 'game_history_loading_complete') and stats_page.game_history_loading_complete)
                
                print(f"   ✅ Anti-blink display logic works: loading_complete = {loading_complete}")
                
                # Test multiple rapid evaluations
                results = []
                for i in range(20):
                    loading_complete = (hasattr(stats_page, '_stable_loading_complete') and stats_page._stable_loading_complete) or \
                                     (hasattr(stats_page, 'game_history_loading_complete') and stats_page.game_history_loading_complete)
                    results.append(loading_complete)
                    time.sleep(0.005)  # 5ms intervals
                
                if len(set(results)) <= 1:
                    print("   ✅ Display logic remained stable during rapid evaluations")
                else:
                    print(f"   ❌ Display logic changed during rapid evaluations: {set(results)}")
                    return False
                
            except Exception as e:
                print(f"   ❌ Error in anti-blink display logic: {e}")
                return False
            
            print("\n🎉 All tests passed! The blinking issue should be resolved.")
            return True
            
    except ImportError as e:
        print(f"❌ Error importing stats page: {e}")
        print("   Make sure the stats_page.py file exists and is properly fixed.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")
        return False

def test_loading_message_stability():
    """Test that the loading message doesn't blink continuously."""
    
    print("\n🔍 Testing Loading Message Stability")
    print("-" * 40)
    
    try:
        # Simulate the conditions that were causing blinking
        print("📝 Simulating loading state conditions...")
        
        # Create a mock object with the fixed attributes
        class MockStatsPage:
            def __init__(self):
                self.game_history_loading_complete = False
                self._stable_loading_complete = False
                self._loading_state_lock = threading.Lock()
                self.database_has_games = None
        
        mock_stats = MockStatsPage()
        
        # Test the old problematic logic vs new stable logic
        print("\n🔄 Testing state transitions...")
        
        # Simulate loading completion
        with mock_stats._loading_state_lock:
            mock_stats.game_history_loading_complete = True
            mock_stats._stable_loading_complete = True
        
        # Test the new stable display logic
        stable_results = []
        for i in range(50):  # Test 50 rapid checks
            loading_complete = (hasattr(mock_stats, '_stable_loading_complete') and mock_stats._stable_loading_complete) or \
                             (hasattr(mock_stats, 'game_history_loading_complete') and mock_stats.game_history_loading_complete)
            stable_results.append(loading_complete)
            time.sleep(0.001)  # 1ms intervals (very rapid)
        
        # Check stability
        unique_states = set(stable_results)
        if len(unique_states) == 1:
            print(f"   ✅ Loading state remained stable: {list(unique_states)[0]}")
            print(f"   ✅ Tested {len(stable_results)} rapid state checks - no blinking detected")
        else:
            print(f"   ❌ Loading state changed: {unique_states}")
            print(f"   ❌ Blinking detected in {len(stable_results)} checks")
            return False
        
        print("\n✅ Loading message stability test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in loading message stability test: {e}")
        return False

def main():
    """Run all tests to verify the blinking fix."""
    
    print("🔧 Stats Page Blinking Fix Verification")
    print("=" * 60)
    
    # Run stability tests
    stability_test_passed = test_stats_page_stability()
    
    # Run loading message tests
    message_test_passed = test_loading_message_stability()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    if stability_test_passed:
        print("✅ Stats page stability test: PASSED")
    else:
        print("❌ Stats page stability test: FAILED")
    
    if message_test_passed:
        print("✅ Loading message stability test: PASSED")
    else:
        print("❌ Loading message stability test: FAILED")
    
    overall_success = stability_test_passed and message_test_passed
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("   The stats page blinking issue has been successfully fixed.")
        print("   The game history section should now display stably without flickering.")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   The blinking issue may not be completely resolved.")
        print("   Please check the error messages above for details.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)