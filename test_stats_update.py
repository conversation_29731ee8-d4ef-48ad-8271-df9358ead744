#!/usr/bin/env python3
"""
Test script to verify that stats are updated properly after games.
This script checks the synchronization between credit deductions and stats updates.
"""

import sys
import os
import time
from datetime import datetime

def test_stats_update():
    """Test that stats are updated properly after game completion."""
    print("=" * 60)
    print("TESTING STATS UPDATE AFTER GAME COMPLETION")
    print("=" * 60)
    
    try:
        # Import required modules
        from stats_page import CentralizedStatsProvider
        from stats_db import get_stats_db_manager
        from payment.voucher_manager import VoucherManager
        
        # Initialize components
        stats_provider = CentralizedStatsProvider()
        stats_db = get_stats_db_manager()
        voucher_manager = VoucherManager()
        
        print(f"✓ Successfully imported all required modules")
        
        # Get current date
        today = datetime.now().strftime('%Y-%m-%d')
        print(f"✓ Testing for date: {today}")
        
        # Test 1: Check current stats before clearing cache
        print("\n1. CHECKING CURRENT STATS (with cache):")
        daily_earnings_cached = stats_provider.get_daily_earnings(today)
        daily_games_cached = stats_provider._get_daily_games_played(today)
        current_balance = voucher_manager.credits
        
        print(f"   Daily earnings (cached): {daily_earnings_cached}")
        print(f"   Daily games (cached): {daily_games_cached}")
        print(f"   Current credit balance: {current_balance}")
        
        # Test 2: Force clear cache and check again
        print("\n2. CLEARING CACHE AND CHECKING FRESH DATA:")
        stats_provider.force_clear_cache()
        print("   ✓ Cache force cleared")
        
        daily_earnings_fresh = stats_provider.get_daily_earnings(today)
        daily_games_fresh = stats_provider._get_daily_games_played(today)
        
        print(f"   Daily earnings (fresh): {daily_earnings_fresh}")
        print(f"   Daily games (fresh): {daily_games_fresh}")
        
        # Test 3: Check database directly
        print("\n3. CHECKING DATABASE DIRECTLY:")
        daily_stats = stats_db.get_daily_stats(today)
        print(f"   Database daily stats: {daily_stats}")
        
        # Get game history count for today
        try:
            conn = stats_db.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM game_history 
                WHERE date(date_time) = ? 
                AND username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND status NOT LIKE '%cancelled%'
            """, (today,))
            game_count = cursor.fetchone()[0]
            conn.close()
            print(f"   Game history count for today: {game_count}")
        except Exception as e:
            print(f"   Error checking game history: {e}")
        
        # Test 4: Check cache timeout setting
        print("\n4. CHECKING CACHE CONFIGURATION:")
        cache_timeout = getattr(stats_provider, '_cache_timeout', 'Not set')
        print(f"   Cache timeout: {cache_timeout} seconds")
        
        if cache_timeout == 300:
            print("   ⚠️  WARNING: Cache timeout is 5 minutes - this may cause delayed updates!")
        elif cache_timeout <= 60:
            print("   ✓ Cache timeout is reasonable for quick updates")
        
        # Test 5: Test cache clearing methods
        print("\n5. TESTING CACHE CLEARING METHODS:")
        
        # Test regular clear_cache
        if hasattr(stats_provider, 'clear_cache'):
            stats_provider.clear_cache()
            print("   ✓ Regular clear_cache() method works")
        else:
            print("   ❌ Regular clear_cache() method not found")
        
        # Test force_clear_cache
        if hasattr(stats_provider, 'force_clear_cache'):
            stats_provider.force_clear_cache()
            print("   ✓ force_clear_cache() method works")
        else:
            print("   ❌ force_clear_cache() method not found")
        
        # Summary
        print("\n" + "=" * 60)
        print("SUMMARY:")
        print("=" * 60)
        
        if daily_earnings_cached != daily_earnings_fresh:
            print(f"✓ Cache clearing works - earnings changed from {daily_earnings_cached} to {daily_earnings_fresh}")
        else:
            print(f"ℹ️  Cache clearing test - earnings remained {daily_earnings_cached}")
        
        if daily_games_cached != daily_games_fresh:
            print(f"✓ Cache clearing works - games changed from {daily_games_cached} to {daily_games_fresh}")
        else:
            print(f"ℹ️  Cache clearing test - games remained {daily_games_cached}")
        
        print(f"💰 Current credit balance: {current_balance}")
        print(f"📊 Daily earnings: {daily_earnings_fresh}")
        print(f"🎮 Daily games: {daily_games_fresh}")
        
        if daily_games_fresh > 0 and daily_earnings_fresh > 0:
            print("✅ Stats show game activity - system appears to be working")
        elif current_balance < 1000:  # Assuming starting balance is higher
            print("⚠️  Credits have been deducted but stats may not be updated")
            print("   Try playing a game and then manually refresh the stats page")
        else:
            print("ℹ️  No recent game activity detected")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running this from the game directory")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_stats_update()
    if success:
        print("\n✅ Test completed successfully")
        print("\nIf you're still seeing old data on the stats page:")
        print("1. Try clicking the refresh button on the stats page")
        print("2. Wait up to 30 seconds for the cache to expire")
        print("3. Play another game to trigger the cache clearing")
    else:
        print("\n❌ Test failed")
    
    sys.exit(0 if success else 1)
