# This file contains enhancements for the stats page

import os
import sqlite3
from datetime import datetime, timedelta

# Import necessary modules from the main application
try:
    from stats_db import get_stats_db_manager
    STATS_DB_AVAILABLE = True
except ImportError:
    STATS_DB_AVAILABLE = False
    print("Stats database module not available in stats_enhancements.")

def calculate_daily_leftover_amount(date_str=None):
    """
    Calculate total leftover amount (commission/tips) for a specific date.

    Args:
        date_str: Date string in format 'YYYY-MM-DD', defaults to today

    Returns:
        dict: Dictionary with leftover amount information
    """
    if date_str is None:
        date_str = datetime.now().strftime('%Y-%m-%d')

    try:
        if not STATS_DB_AVAILABLE:
            return {
                'total_leftover': 0,
                'total_prize_pool': 0, 
                'total_games': 0
            }

        stats_db = get_stats_db_manager()
        
        with stats_db.get_connection_context() as conn:
            cursor = conn.cursor()

            # Calculate total leftover amount (fee) and prize pool for the date
            cursor.execute('''
            SELECT 
                SUM(fee) as total_leftover,
                SUM(total_prize) as total_prize_pool,
                COUNT(*) as total_games
            FROM game_history 
            WHERE date(date_time) = ?
            AND username NOT LIKE '%Game Reset%'
            AND username NOT LIKE '%Demo%'
            AND total_calls > 0
            AND status NOT LIKE '%cancelled%'
            AND status NOT LIKE '%aborted%'
            ''', (date_str,))

            result = cursor.fetchone()
            if result:
                total_leftover = result[0] if result[0] is not None else 0
                total_prize_pool = result[1] if result[1] is not None else 0
                total_games = result[2] if result[2] is not None else 0
                
                return {
                    'total_leftover': total_leftover,
                    'total_prize_pool': total_prize_pool,
                    'total_games': total_games
                }
            return {
                'total_leftover': 0,
                'total_prize_pool': 0,
                'total_games': 0
            }

    except Exception as e:
        print(f"Error calculating total leftover amount: {e}")
        return {
            'total_leftover': 0,
            'total_prize_pool': 0,
            'total_games': 0
        })

def draw_leftover_summary(screen, stats_page_instance, start_y, table_x, table_width):
    """
    Draw the leftover amount summary above the game history table.

    Args:
        screen: Pygame screen surface
        stats_page_instance: The StatsPage instance (for accessing fonts, scaling, etc.)
        start_y: Y position to start drawing
        table_x: X position of the game history table
        table_width: Width of the game history table
    """
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        leftover_info = calculate_daily_leftover_amount(today)
        
        summary_font = stats_page_instance.get_font("Arial", stats_page_instance.scaled_font_size(12))
        summary_y = start_y

        if leftover_info['total_games'] > 0:
            # Format amounts with commas
            total_leftover = f"{leftover_info['total_leftover']:,.2f}"
            total_prize = f"{leftover_info['total_prize_pool']:,.2f}"
            
            # Calculate percentage
            leftover_percent = (leftover_info['total_leftover'] / leftover_info['total_prize_pool'] * 100) if leftover_info['total_prize_pool'] > 0 else 0
            
            summary_text = f"Today's Summary ({leftover_info['total_games']} games): Prize Pool: {total_prize} ETB | Service Tips: {total_leftover} ETB ({leftover_percent:.1f}%)"
            summary_color = (255, 215, 0)  # Gold color for visibility
        else:
            summary_text = "No games played today"
            summary_color = (180, 180, 180) # Light gray

        summary_surface = summary_font.render(summary_text, True, summary_color)
        summary_rect = summary_surface.get_rect(x=table_x, y=summary_y)
        screen.blit(summary_surface, summary_rect)
        
        # Return the height of the summary section for layout adjustment
        return summary_rect.height + int(5 * stats_page_instance.scale_y) # Add some padding

    except Exception as e:
        print(f"Error drawing leftover summary: {e}")
        return 0 # Return 0 height on error

