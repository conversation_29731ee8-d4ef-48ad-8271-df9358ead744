"""
Enhanced Stats Page Performance Optimizer

This module provides performance enhancements for the existing stats page without replacing it.
It focuses on optimizing database queries, caching, rendering, and memory usage.
"""

import time
import threading
import queue
import weakref
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import pygame
import json
import os

class PerformanceCache:
    """Advanced caching system with TTL, memory management, and background refresh."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self._lock = threading.RLock()
        
    def get(self, key: str, default=None):
        """Get cached value if valid, otherwise return default."""
        with self._lock:
            if key not in self._cache:
                return default
                
            entry = self._cache[key]
            current_time = time.time()
            
            # Check if expired
            if current_time > entry['expires_at']:
                del self._cache[key]
                if key in self._access_times:
                    del self._access_times[key]
                return default
            
            # Update access time for LRU
            self._access_times[key] = current_time
            return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set cached value with TTL."""
        with self._lock:
            current_time = time.time()
            ttl = ttl or self.default_ttl
            
            # Clean up if cache is full
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            self._cache[key] = {
                'value': value,
                'created_at': current_time,
                'expires_at': current_time + ttl
            }
            self._access_times[key] = current_time
    
    def _evict_lru(self):
        """Evict least recently used items."""
        if not self._access_times:
            return
            
        # Remove 25% of items (LRU)
        items_to_remove = max(1, len(self._access_times) // 4)
        sorted_items = sorted(self._access_times.items(), key=lambda x: x[1])
        
        for key, _ in sorted_items[:items_to_remove]:
            if key in self._cache:
                del self._cache[key]
            if key in self._access_times:
                del self._access_times[key]
    
    def clear(self):
        """Clear all cached data."""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()

class AsyncDataLoader:
    """Asynchronous data loader with queue management and timeout handling."""
    
    def __init__(self, max_workers: int = 3, timeout: float = 2.0):
        self.max_workers = max_workers
        self.timeout = timeout
        self._workers: List[threading.Thread] = []
        self._task_queue = queue.Queue()
        self._result_cache = PerformanceCache(max_size=500, default_ttl=180)
        self._active_tasks: Dict[str, threading.Event] = {}
        self._shutdown = False
        
        # Start worker threads
        for i in range(max_workers):
            worker = threading.Thread(target=self._worker_loop, daemon=True)
            worker.start()
            self._workers.append(worker)
    
    def _worker_loop(self):
        """Worker thread loop for processing async tasks."""
        while not self._shutdown:
            try:
                task = self._task_queue.get(timeout=1.0)
                if task is None:  # Shutdown signal
                    break
                
                task_id, func, args, kwargs, result_queue = task
                
                try:
                    # Execute the task
                    result = func(*args, **kwargs)
                    result_queue.put(('success', result))
                    
                    # Cache the result
                    cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
                    self._result_cache.set(cache_key, result, ttl=300)
                    
                except Exception as e:
                    result_queue.put(('error', str(e)))
                finally:
                    # Mark task as complete
                    if task_id in self._active_tasks:
                        self._active_tasks[task_id].set()
                        del self._active_tasks[task_id]
                    
                    self._task_queue.task_done()
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Worker thread error: {e}")
    
    def load_async(self, func: Callable, *args, **kwargs) -> Any:
        """Load data asynchronously with caching and timeout."""
        # Check cache first
        cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
        cached_result = self._result_cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Check if task is already running
        task_id = f"{func.__name__}_{time.time()}"
        if task_id in self._active_tasks:
            return None  # Task already running
        
        # Create result queue and event
        result_queue = queue.Queue()
        self._active_tasks[task_id] = threading.Event()
        
        # Queue the task
        task = (task_id, func, args, kwargs, result_queue)
        self._task_queue.put(task)
        
        # Wait for result with timeout
        try:
            result_type, result_value = result_queue.get(timeout=self.timeout)
            if result_type == 'success':
                return result_value
            else:
                print(f"Async task error: {result_value}")
                return None
        except queue.Empty:
            print(f"Async task timeout: {func.__name__}")
            return None
    
    def shutdown(self):
        """Shutdown the async loader."""
        self._shutdown = True
        # Send shutdown signals
        for _ in self._workers:
            self._task_queue.put(None)

class RenderingOptimizer:
    """Optimizes rendering operations with surface caching and dirty region tracking."""
    
    def __init__(self, screen_size: tuple):
        self.screen_size = screen_size
        self._surface_cache: Dict[str, pygame.Surface] = {}
        self._dirty_regions: List[pygame.Rect] = []
        self._last_render_time = 0
        self._render_budget = 16.67  # 60 FPS budget in milliseconds
        
    def get_cached_surface(self, key: str, size: tuple, create_func: Callable = None) -> pygame.Surface:
        """Get or create a cached surface."""
        if key in self._surface_cache:
            surface = self._surface_cache[key]
            if surface.get_size() == size:
                return surface
        
        # Create new surface
        if create_func:
            surface = create_func(size)
        else:
            surface = pygame.Surface(size, pygame.SRCALPHA)
        
        self._surface_cache[key] = surface
        return surface
    
    def should_skip_render(self) -> bool:
        """Check if rendering should be skipped to maintain frame rate."""
        current_time = time.time() * 1000  # Convert to milliseconds
        time_since_last = current_time - self._last_render_time
        
        if time_since_last < self._render_budget:
            return True
        
        self._last_render_time = current_time
        return False
    
    def add_dirty_region(self, rect: pygame.Rect):
        """Add a dirty region that needs rerendering."""
        self._dirty_regions.append(rect)
    
    def clear_dirty_regions(self):
        """Clear all dirty regions."""
        self._dirty_regions.clear()
    
    def clear_cache(self):
        """Clear the surface cache."""
        self._surface_cache.clear()

class DatabaseOptimizer:
    """Optimizes database operations with connection pooling and query batching."""
    
    def __init__(self):
        self._query_cache = PerformanceCache(max_size=200, default_ttl=120)
        self._batch_queries: List[tuple] = []
        self._batch_timer = 0
        self._batch_delay = 100  # milliseconds
        
    def execute_cached_query(self, query_func: Callable, cache_key: str, *args, **kwargs):
        """Execute a database query with caching."""
        # Check cache first
        cached_result = self._query_cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        try:
            # Execute query with timeout
            result = self._execute_with_timeout(query_func, 1.0, *args, **kwargs)
            if result is not None:
                self._query_cache.set(cache_key, result, ttl=180)
            return result
        except Exception as e:
            print(f"Database query error: {e}")
            return None
    
    def _execute_with_timeout(self, func: Callable, timeout: float, *args, **kwargs):
        """Execute function with timeout."""
        result_queue = queue.Queue()
        
        def worker():
            try:
                result = func(*args, **kwargs)
                result_queue.put(('success', result))
            except Exception as e:
                result_queue.put(('error', str(e)))
        
        thread = threading.Thread(target=worker, daemon=True)
        thread.start()
        
        try:
            result_type, result_value = result_queue.get(timeout=timeout)
            if result_type == 'success':
                return result_value
            else:
                print(f"Database operation error: {result_value}")
                return None
        except queue.Empty:
            print(f"Database operation timeout")
            return None

class MemoryOptimizer:
    """Manages memory usage and garbage collection."""
    
    def __init__(self, max_memory_mb: int = 100):
        self.max_memory_mb = max_memory_mb
        self._last_cleanup = time.time()
        self._cleanup_interval = 30  # seconds
        
    def should_cleanup(self) -> bool:
        """Check if memory cleanup should be performed."""
        current_time = time.time()
        return (current_time - self._last_cleanup) > self._cleanup_interval
    
    def cleanup(self):
        """Perform memory cleanup."""
        import gc
        gc.collect()
        self._last_cleanup = time.time()
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0

class StatsPagePerformanceEnhancer:
    """Main performance enhancer that integrates all optimizations."""
    
    def __init__(self, stats_page_instance):
        self.stats_page = weakref.ref(stats_page_instance)
        
        # Initialize optimizers
        screen_size = stats_page_instance.screen.get_size()
        self.cache = PerformanceCache(max_size=1000, default_ttl=300)
        self.async_loader = AsyncDataLoader(max_workers=2, timeout=1.5)
        self.renderer = RenderingOptimizer(screen_size)
        self.db_optimizer = DatabaseOptimizer()
        self.memory_optimizer = MemoryOptimizer(max_memory_mb=150)
        
        # Performance metrics
        self.metrics = {
            'cache_hits': 0,
            'cache_misses': 0,
            'async_loads': 0,
            'render_skips': 0,
            'memory_cleanups': 0
        }
        
        # Integration flags
        self._integrated = False
        
    def integrate_with_stats_page(self):
        """Integrate performance enhancements with the stats page."""
        if self._integrated:
            return
            
        stats_page = self.stats_page()
        if not stats_page:
            return
        
        # Enhance data loading methods
        self._enhance_data_loading(stats_page)
        
        # Enhance rendering methods
        self._enhance_rendering(stats_page)
        
        # Enhance caching
        self._enhance_caching(stats_page)
        
        self._integrated = True
        print("Performance enhancements integrated with stats page")
    
    def _enhance_data_loading(self, stats_page):
        """Enhance data loading with async operations and caching."""
        # Store original methods
        if not hasattr(stats_page, '_original_get_daily_earnings'):
            stats_page._original_get_daily_earnings = getattr(stats_page.stats_provider, 'get_daily_earnings', None)
        
        # Enhanced daily earnings method
        def enhanced_get_daily_earnings(date_str):
            cache_key = f"daily_earnings_{date_str}"
            
            # Check performance cache first
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                self.metrics['cache_hits'] += 1
                return cached_result
            
            self.metrics['cache_misses'] += 1
            
            # Try async loading
            if stats_page._original_get_daily_earnings:
                result = self.async_loader.load_async(
                    stats_page._original_get_daily_earnings, 
                    date_str
                )
                
                if result is not None:
                    self.cache.set(cache_key, result, ttl=300)
                    self.metrics['async_loads'] += 1
                    return result
            
            # Fallback to zero
            self.cache.set(cache_key, 0.0, ttl=60)
            return 0.0
        
        # Replace method
        if hasattr(stats_page.stats_provider, 'get_daily_earnings'):
            stats_page.stats_provider.get_daily_earnings = enhanced_get_daily_earnings
    
    def _enhance_rendering(self, stats_page):
        """Enhance rendering with surface caching and frame skipping."""
        # Store original draw method
        if not hasattr(stats_page, '_original_draw'):
            stats_page._original_draw = stats_page.draw
        
        def enhanced_draw():
            # Check if we should skip rendering for performance
            if self.renderer.should_skip_render():
                self.metrics['render_skips'] += 1
                return
            
            # Perform memory cleanup if needed
            if self.memory_optimizer.should_cleanup():
                self.memory_optimizer.cleanup()
                self.metrics['memory_cleanups'] += 1
            
            # Call original draw method
            stats_page._original_draw()
        
        # Replace draw method
        stats_page.draw = enhanced_draw
    
    def _enhance_caching(self, stats_page):
        """Enhance caching for various data operations."""
        # Store original methods
        if not hasattr(stats_page, '_original_load_game_history'):
            stats_page._original_load_game_history = getattr(stats_page, 'load_game_history', None)
        
        def enhanced_load_game_history():
            cache_key = "game_history_full"
            
            # Check cache first
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                stats_page.game_history = cached_result.get('game_history', [])
                stats_page.original_game_history = cached_result.get('original_game_history', [])
                self.metrics['cache_hits'] += 1
                return
            
            # Load from original method
            if stats_page._original_load_game_history:
                stats_page._original_load_game_history()
                
                # Cache the result
                cache_data = {
                    'game_history': stats_page.game_history.copy(),
                    'original_game_history': stats_page.original_game_history.copy()
                }
                self.cache.set(cache_key, cache_data, ttl=180)
                self.metrics['cache_misses'] += 1
        
        # Replace method if it exists
        if hasattr(stats_page, 'load_game_history'):
            stats_page.load_game_history = enhanced_load_game_history
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        stats_page = self.stats_page()
        if not stats_page:
            return {}
        
        return {
            'cache_hit_rate': (
                self.metrics['cache_hits'] / 
                max(1, self.metrics['cache_hits'] + self.metrics['cache_misses'])
            ) * 100,
            'total_cache_operations': self.metrics['cache_hits'] + self.metrics['cache_misses'],
            'async_loads': self.metrics['async_loads'],
            'render_skips': self.metrics['render_skips'],
            'memory_cleanups': self.metrics['memory_cleanups'],
            'memory_usage_mb': self.memory_optimizer.get_memory_usage(),
            'cache_size': len(self.cache._cache),
            'integrated': self._integrated
        }
    
    def cleanup(self):
        """Cleanup resources."""
        self.async_loader.shutdown()
        self.cache.clear()
        self.renderer.clear_cache()

def enhance_stats_page_performance(stats_page_instance):
    """
    Main function to enhance stats page performance.
    
    Args:
        stats_page_instance: The StatsPage instance to enhance
        
    Returns:
        StatsPagePerformanceEnhancer: The enhancer instance for monitoring
    """
    enhancer = StatsPagePerformanceEnhancer(stats_page_instance)
    enhancer.integrate_with_stats_page()
    
    # Add performance monitoring method to stats page
    def get_performance_info():
        return enhancer.get_performance_stats()
    
    stats_page_instance.get_performance_info = get_performance_info
    
    # Add cleanup method
    def cleanup_performance_enhancements():
        enhancer.cleanup()
    
    stats_page_instance.cleanup_performance_enhancements = cleanup_performance_enhancements
    
    print("Stats page performance enhancements applied successfully!")
    print("Use stats_page.get_performance_info() to monitor performance")
    
    return enhancer

# Example usage and testing
if __name__ == "__main__":
    print("Enhanced Stats Page Performance Optimizer")
    print("This module provides performance enhancements for the existing stats page.")
    print("Import and use enhance_stats_page_performance(stats_page_instance) to apply optimizations.")