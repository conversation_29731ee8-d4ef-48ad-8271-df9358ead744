# Project Structure & Organization

## Root Directory Layout

### Core Game Files
- `main.py`: Main application entry point and game loop
- `bingo_*.py`: Core bingo game logic (card, caller, logic)
- `game_*.py`: Game state management and UI handling
- `player_*.py`: Player management and storage
- `stats_*.py`: Statistics tracking and database operations

### Configuration & Data
- `data/`: Application data directory
  - `*.json`: Configuration files (settings, players, boards)
  - `*.db`: SQLite databases (stats, vouchers, external PCs)
  - `cache/`: Temporary cache files
  - `metrics/`: Performance and usage metrics
  - `backups/`: Automated database backups

### Assets & Resources
- `assets/`: Game assets and media files
  - `audio-effects/`: Sound effects and announcements
  - `Audios/`: Multi-language audio files
  - `sounds/`: Game sound effects
  - `*.png`, `*.ico`: UI graphics and icons
  - `svg&styles/`: Vector graphics and stylesheets

### Build & Distribution
- `build_*.py`, `build_*.bat`: Build scripts and configurations
- `nuitka_*.py`, `nuitka_*.bat`: Nuitka compiler configurations
- `requirements.txt`: Python dependencies
- `pyproject.toml`: Project metadata and build configuration

## Module Organization

### Core Systems
```
├── Game Engine
│   ├── main.py (entry point)
│   ├── bingo_logic.py (core game rules)
│   ├── bingo_card.py (card management)
│   └── bingo_caller.py (number calling)
│
├── UI & Display
│   ├── game_ui_handler.py (UI management)
│   ├── settings_window.py (configuration UI)
│   ├── view_players.py (player management UI)
│   └── external_display_manager.py (multi-monitor support)
│
├── Data Management
│   ├── stats_db.py (statistics database)
│   ├── player_storage.py (player data)
│   ├── settings_manager.py (configuration)
│   └── thread_safe_db.py (database safety)
│
└── Integrations
    ├── payment/ (voucher system)
    ├── rethink_*.py (RethinkDB sync)
    └── admin_*.py (web admin interface)
```

### Specialized Modules
- `instant_loading/`: Performance optimization system
- `payment/`: Complete payment and voucher system
- `templates/`: HTML templates for web interface
- `utils/`: Database utilities and maintenance scripts
- `keys/`: Cryptographic keys (excluded from builds)

## File Naming Conventions

### Prefixes
- `bingo_*`: Core bingo game functionality
- `game_*`: Game state and session management
- `stats_*`: Statistics and analytics
- `admin_*`: Administrative interfaces
- `player_*`: Player management
- `settings_*`: Configuration management
- `rethink_*`: RethinkDB integration
- `build_*`: Build and compilation scripts
- `fix_*`: Bug fixes and patches
- `enhanced_*`: Feature improvements
- `modern_*`: Updated/modernized components

### Suffixes
- `*_integration.py`: System integration modules
- `*_manager.py`: Management and coordination classes
- `*_handler.py`: Event and UI handling
- `*_fix.py`: Bug fixes and corrections
- `*_db.py`: Database-related functionality

## Data Flow Architecture

### Game Session Flow
1. `main.py` → Game initialization
2. `game_state_handler.py` → State management
3. `bingo_logic.py` → Game rules processing
4. `stats_integration.py` → Statistics recording
5. `payment/` → Credit processing

### Database Operations
1. `thread_safe_db.py` → Connection management
2. `stats_db.py` → Data operations
3. `db_security.py` → Security validation
4. `rethink_db.py` → Real-time sync

### UI Rendering
1. `main.py` → Main game loop
2. `game_ui_handler.py` → UI coordination
3. `external_display_manager.py` → Multi-monitor handling
4. Performance caching → Optimized rendering

## Development Patterns

### Error Handling
- Comprehensive try-catch blocks with logging
- Graceful degradation for missing components
- Fallback mechanisms for critical systems

### Performance Optimization
- Lazy loading for heavy resources
- Caching systems for frequently accessed data
- Memory pools for object reuse
- Hardware-adaptive quality settings

### Modularity
- Plugin-style architecture for optional features
- Dependency injection for testability
- Clear separation of concerns
- Minimal coupling between modules