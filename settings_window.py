import pygame
import math
import os
import json
import random
import traceback
from pygame import Rect
from simple_text_input import SimpleTextInput
from announcer_language_manager import AnnouncerLanguageManager

print("Loading settings_window.py with Pattern Bonuses tab")

# Import settings manager
from settings_manager import SettingsManager

class SettingsWindow:
    """
    Modern settings window that appears as a modal overlay when the settings icon is clicked.
    Provides UI for adjusting game, display, audio, and import/export settings with a clean,
    consistent design and high readability.
    """

    def __init__(self, screen, parent=None):
        """
        Initialize the settings window

        Args:
            screen: The pygame screen surface
            parent: The parent window (main game or board selection)
        """
        self.screen = screen
        self.parent = parent

        # Get screen dimensions
        self.screen_width, self.screen_height = screen.get_size()

        # Calculate scaling factors based on base resolution
        self.BASE_WIDTH, self.BASE_HEIGHT = 1024, 768
        self.scale_x = self.screen_width / self.BASE_WIDTH
        self.scale_y = self.screen_height / self.BASE_HEIGHT

        # Initialize settings manager
        self.settings_manager = SettingsManager()

        # State variables
        self.visible = False
        self.active_tab = "game"  # Default active tab
        self.hit_areas = {}  # Store clickable areas
        self.animation_time = 0  # For animations

        # Initialize simple text input component
        self.text_input = SimpleTextInput(self)

        # Scrolling variables
        self.scroll_y = 0  # Current scroll position
        self.max_scroll = 0  # Maximum scroll value
        self.content_height = 0  # Total content height
        self.visible_content_height = 0  # Visible content height
        self.scroll_speed = 40  # Pixels per scroll event

        # Define constant colors
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)

        # Initialize theme colors
        self.init_theme_colors()

        # Initialize fonts
        self.init_fonts()

        # Initialize settings definitions
        self.init_settings_definitions()

    def init_theme_colors(self):
        """Initialize theme colors with modern, high-contrast palette"""
        # Get current theme and accent color from settings
        current_theme = self.settings_manager.get_setting('display', 'ui_theme', 'dark')
        current_accent = self.settings_manager.get_setting('display', 'ui_accent_color', 'blue')

        # Get color palette for current theme
        self.colors = self.settings_manager.get_theme_colors(current_theme, current_accent)

        # Ensure high contrast text colors regardless of theme
        if current_theme == "light":
            # For light theme, use dark text for better readability
            self.colors["light_text"] = (0, 0, 0)  # Black text
            self.colors["medium_text"] = (40, 40, 40)  # Very dark gray
            self.colors["dark_text"] = (60, 60, 60)  # Dark gray
        else:
            # For dark themes, use light text for better readability
            self.colors["light_text"] = (255, 255, 255)  # White text
            self.colors["medium_text"] = (230, 230, 230)  # Very light gray
            self.colors["dark_text"] = (200, 200, 200)  # Light gray

        # Store current theme and accent for reference
        self.current_theme = current_theme
        self.current_accent = current_accent

        # Define accent colors for different tabs with improved visibility
        self.accent_colors = {
            "blue": (0, 140, 240),  # Brighter blue
            "teal": (0, 200, 200),  # Brighter teal
            "purple": (140, 100, 220),  # Brighter purple
            "orange": (255, 140, 40),  # Brighter orange
            "green": (50, 200, 100),  # Brighter green
            "red": (255, 70, 70),  # Brighter red
            "gold": (255, 215, 0)  # Brighter gold
        }

        # Map common colors for easy access
        self.primary = self.colors["accent"]
        self.primary_hover = self.colors["accent_hover"]
        self.primary_active = self.colors["accent_active"]

        self.bg_color = self.colors["panel_bg"]
        self.header_color = self.colors["panel_header"]
        self.content_color = self.colors["panel_content"]

        self.control_bg = self.colors["control_bg"]
        self.control_border = self.colors["control_border"]

        self.success = self.colors["success"]
        self.warning = self.colors["warning"]
        self.danger = self.colors["danger"]

        self.text_light = self.colors["light_text"]
        self.text_medium = self.colors["medium_text"]
        self.text_dark = self.colors["dark_text"]

    # Class variable to cache font objects
    _font_cache = {}

    def init_fonts(self):
        """Initialize fonts with appropriate scaling for modern UI with caching"""
        # Calculate font sizes with slightly larger base sizes for better readability
        font_size_title = int(32 * min(self.scale_x, self.scale_y))
        font_size_header = int(24 * min(self.scale_x, self.scale_y))
        font_size_text = int(18 * min(self.scale_x, self.scale_y))
        font_size_small = int(14 * min(self.scale_x, self.scale_y))
        font_size_icon = int(22 * min(self.scale_x, self.scale_y))

        # Use system fonts that are known to support Amharic
        font_families = [
            "Nyala",  # Best Amharic support on Windows
            "Ebrima",  # Good Amharic support on Windows
            "Segoe UI",  # Windows modern font with some Amharic support
            "Arial Unicode MS",  # Wide Unicode support
            "Arial",  # Widely available fallback
            "sans-serif"  # Generic fallback
        ]

        # Print available fonts for debugging
        available_fonts = pygame.font.get_fonts()
        print("Available fonts:")
        for font in available_fonts:
            if "nyala" in font.lower() or "ebrima" in font.lower() or "ethiopic" in font.lower() or "unicode" in font.lower():
                print(f"  - {font} (likely supports Amharic)")

        # Find the first available font (only if not already cached)
        if not hasattr(self.__class__, '_chosen_font'):
            available_fonts = pygame.font.get_fonts()
            chosen_font = None

            for font in font_families:
                normalized_font = font.lower().replace(" ", "")
                if normalized_font in available_fonts:
                    chosen_font = font
                    break

            # If none of our preferred fonts are available, use the default
            if not chosen_font:
                chosen_font = "Arial"

            # Store as class variable to avoid rechecking
            self.__class__._chosen_font = chosen_font
        else:
            chosen_font = self.__class__._chosen_font

        # Create font objects with the chosen font (using cache)
        def get_cached_font(font_name, size, bold=False):
            cache_key = (font_name, size, bold)
            if cache_key not in self.__class__._font_cache:
                # Just use system font for now - skip custom font loading to avoid crashes
                try:
                    # Use system font directly
                    print(f"Using system font: {font_name}")
                    self.__class__._font_cache[cache_key] = pygame.font.SysFont(font_name, size, bold=bold)
                except Exception as e:
                    print(f"Error loading system font: {e}, falling back to default font")
                    # Last resort - use default font
                    self.__class__._font_cache[cache_key] = pygame.font.SysFont(None, size, bold=bold)
            return self.__class__._font_cache[cache_key]

        # Get fonts from cache
        self.title_font = get_cached_font(chosen_font, font_size_title, bold=True)
        self.header_font = get_cached_font(chosen_font, font_size_header, bold=True)
        self.text_font = get_cached_font(chosen_font, font_size_text)
        self.small_font = get_cached_font(chosen_font, font_size_small)
        self.icon_font = get_cached_font(chosen_font, font_size_icon)

        # Store the chosen font name for reference
        self.font_family = chosen_font

    def init_settings_definitions(self):
        """Initialize settings definitions for all tabs"""
        print("Initializing settings definitions with Pattern Bonuses tab")
        # Define standard item height and spacing
        self.item_height = int(80 * min(self.scale_x, self.scale_y))
        self.item_spacing = int(15 * min(self.scale_x, self.scale_y))

        # Define tabs
        self.tabs = [
            {"id": "game", "label": "Game"},
            {"id": "display", "label": "Display"},
            {"id": "animations", "label": "Animations"},
            {"id": "audio", "label": "Audio"},
            {"id": "pattern_bonuses", "label": "Pattern Bonuses"},
            {"id": "advertising", "label": "Advertising"},
            {"id": "import_export", "label": "Import/Export"}
        ]

        # Define settings for each tab
        self.settings_definitions = {
            "pattern_bonuses": [
                {
                    "key": "enable_inner_square",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Inner Square Pattern",
                        "description": "Allow Inner Square/3x3 Square pattern to be claimed as winning",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_inner_square",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Inner Square Bonus Amount",
                        "description": "Bonus ETB for claiming Inner Square pattern",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 300,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_diamond",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Diamond Pattern",
                        "description": "Allow Diamond pattern to be claimed as winning",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_diamond",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Diamond Bonus Amount",
                        "description": "Bonus ETB for claiming Diamond pattern",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 300,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_cross",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Cross Pattern",
                        "description": "Allow Cross pattern to be claimed as winning",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_cross",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Cross Bonus Amount",
                        "description": "Bonus ETB for claiming Cross pattern",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 300,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_four_corners",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Four Corners Pattern",
                        "description": "Allow Four Corners pattern to be claimed as winning (within 5 calls)",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_four_corners",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Four Corners Bonus Amount",
                        "description": "Bonus ETB for claiming Four Corners pattern",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 200,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_rows",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Row Patterns",
                        "description": "Allow Row patterns to be claimed as winning (within 5 calls)",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_rows",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Row Patterns Bonus Amount",
                        "description": "Bonus ETB for claiming Row patterns",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 100,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_columns",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Column Patterns",
                        "description": "Allow Column patterns to be claimed as winning (within 5 calls)",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_columns",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Column Patterns Bonus Amount",
                        "description": "Bonus ETB for claiming Column patterns",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 100,
                        "value_format": "{} ETB"
                    }
                },
                {
                    "key": "enable_diagonals",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Diagonal Patterns",
                        "description": "Allow Diagonal patterns to be claimed as winning (within 5 calls)",
                        "default_value": True
                    }
                },
                {
                    "key": "bonus_amount_diagonals",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Diagonal Patterns Bonus Amount",
                        "description": "Bonus ETB for claiming Diagonal patterns",
                        "min_value": 100,
                        "max_value": 1000,
                        "default_value": 200,
                        "value_format": "{} ETB"
                    }
                }
            ],
            "game": [
                {
                    "key": "number_call_delay",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Number Call Delay",
                        "description": "Time between automatic number calls in seconds",
                        "min_value": 1.0,
                        "max_value": 10.0,
                        "default_value": 3.0,
                        "value_format": "{:.1f}s"
                    }
                },
                {
                    "key": "strict_claim_timing",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Strict Claim Timing",
                        "description": "Enforce time limits for claiming bingo",
                        "default_value": True
                    }
                },
                {
                    "key": "claim_grace_period",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Claim Grace Period",
                        "description": "Time allowed to claim after a number is called",
                        "min_value": 1.0,
                        "max_value": 10.0,
                        "default_value": 5.0,
                        "value_format": "{:.1f}s"
                    }
                },
                {
                    "key": "shuffle_duration",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Shuffle Duration",
                        "description": "Duration of shuffle animation",
                        "min_value": 0.5,
                        "max_value": 5.0,
                        "default_value": 3.0,
                        "value_format": "{:.1f}s"
                    }
                },
                {
                    "key": "show_game_info",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Show Game Info",
                        "description": "Show or hide game information footer (credits, commission, share)",
                        "default_value": True
                    }
                },
                {
                    "key": "commission_percentage",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Commission Percentage",
                        "description": "Percentage deducted from total bets for prize pool calculation",
                        "min_value": 0.0,
                        "max_value": 50.0,
                        "default_value": 20.0,
                        "value_format": "{:.1f}%"
                    }
                }
            ],
            "display": [
                {
                    "key": "fullscreen",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Fullscreen Mode",
                        "description": "Run the game in fullscreen mode",
                        "default_value": False
                    }
                },
                {
                    "key": "resolution",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Resolution",
                        "description": "Screen resolution (width x height)"
                    }
                },
                {
                    "key": "animations_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Animations",
                        "description": "Show animations for visual effects",
                        "default_value": True
                    }
                },
                {
                    "key": "show_recent_calls",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Show Recent Calls",
                        "description": "Display recently called numbers",
                        "default_value": True
                    }
                },
                {
                    "key": "recent_calls_count",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Recent Calls Count",
                        "description": "Number of recent calls to display",
                        "min_value": 1,
                        "max_value": 10,
                        "default_value": 5,
                        "value_format": "{:.0f}"
                    }
                },
                {
                    "key": "ui_theme",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "UI Theme",
                        "description": "Visual theme for the user interface"
                    }
                },
                {
                    "key": "ui_accent_color",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Accent Color",
                        "description": "Highlight color for UI elements"
                    }
                }
            ],
            "advertising": [
                {
                    "key": "enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Enable Advertising",
                        "description": "Show advertising section",
                        "default_value": True
                    }
                },
                {
                    "key": "hidden",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Hide Advertising",
                        "description": "Temporarily hide the advertising section (can be toggled by clicking on it)",
                        "default_value": False
                    }
                },
                {
                    "key": "text",
                    "type": self.COMPONENT_TEXT_INPUT,
                    "params": {
                        "label": "Advertising Text",
                        "description": "Text to display in the advertising section",
                        "default_value": "PLACE THE ADVERT HERE!"
                    }
                },
                {
                    "key": "font",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Font",
                        "description": "Font for advertising text"
                    }
                },
                {
                    "key": "font_size",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Font Size",
                        "description": "Size of advertising text",
                        "min_value": 12,
                        "max_value": 48,
                        "default_value": 24,
                        "value_format": "{:.0f}px"
                    }
                },
                {
                    "key": "text_color",
                    "type": self.COMPONENT_COLOR_PICKER,
                    "params": {
                        "label": "Text Color",
                        "description": "Color of advertising text",
                        "default_value": "#F0C020"
                    }
                },
                {
                    "key": "background_color",
                    "type": self.COMPONENT_COLOR_PICKER,
                    "params": {
                        "label": "Background Color",
                        "description": "Background color for the advertising area",
                        "default_value": "#000000"
                    }
                },
                {
                    "key": "scroll_speed",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Scroll Speed",
                        "description": "Speed of the scrolling text",
                        "min_value": 0.5,
                        "max_value": 5.0,
                        "default_value": 2.0,
                        "value_format": "{:.1f}x"
                    }
                },
                {
                    "key": "bold",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Bold Text",
                        "description": "Make the advertising text bold",
                        "default_value": True
                    }
                },
                {
                    "key": "italic",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Italic Text",
                        "description": "Make the advertising text italic",
                        "default_value": False
                    }
                },
                {
                    "key": "led_style",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "LED Display Style",
                        "description": "Make the advertising section look like an LED display board",
                        "default_value": True
                    }
                },
                {
                    "key": "led_pixel_size",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "LED Pixel Size",
                        "description": "Size of the LED pixels in the display board",
                        "min_value": 2,
                        "max_value": 8,
                        "default_value": 4,
                        "value_format": "{:.0f}px"
                    }
                },
                {
                    "key": "rainbow_text",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Rainbow Text",
                        "description": "Toggle between rainbow colors and golden gradient for text",
                        "default_value": False
                    }
                },
                {
                    "key": "text_glow",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Text Glow Effect",
                        "description": "Add a glowing effect to the advertising text",
                        "default_value": True
                    }
                }
            ],
            "animations": [
                {
                    "key": "transition_animation_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Game Start Transition",
                        "description": "Enable the animated transition when starting a game",
                        "default_value": True
                    }
                },
                {
                    "key": "transition_animation_duration",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Transition Duration",
                        "description": "Duration of the game start transition animation in seconds",
                        "min_value": 3.0,
                        "max_value": 15.0,
                        "default_value": 13.0,
                        "value_format": "{:.0f}s"
                    }
                }
            ],
            "audio": [
                {
                    "key": "sound_effects_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Sound Effects",
                        "description": "Enable sound effects",
                        "default_value": True
                    }
                },
                {
                    "key": "sound_effects_volume",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Sound Effects Volume",
                        "description": "Volume level for sound effects",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "default_value": 0.7,
                        "value_format": "{:.0%}"
                    }
                },
                {
                    "key": "music_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Background Music",
                        "description": "Enable background music",
                        "default_value": True
                    }
                },
                {
                    "key": "music_volume",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Music Volume",
                        "description": "Volume level for background music",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "default_value": 0.5,
                        "value_format": "{:.0%}"
                    }
                },
                {
                    "key": "voice_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Voice Announcements",
                        "description": "Enable voice announcements",
                        "default_value": True
                    }
                },
                {
                    "key": "voice_volume",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Voice Volume",
                        "description": "Volume level for voice announcements",
                        "min_value": 0.0,
                        "max_value": 1.0,
                        "default_value": 0.8,
                        "value_format": "{:.0%}"
                    }
                },
                {
                    "key": "cartella_announcements_enabled",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Cartella Announcements",
                        "description": "Enable audio announcements when registering cartella numbers",
                        "default_value": True
                    }
                },
                {
                    "key": "announcer_language",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Announcer Language",
                        "description": "Select the language for number announcements"
                    }
                }
            ],
            "language": [
                {
                    "key": "current_language",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Current Language",
                        "description": "Select the language for the application"
                    }
                },
                {
                    "key": "import_language",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Language",
                        "description": "Import a custom language file from JSON",
                        "icon": "📥"
                    }
                },
                {
                    "key": "export_language",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Export Language",
                        "description": "Export current language settings to JSON",
                        "icon": "📤"
                    }
                }
            ],
            "boards": [
                {
                    "key": "current_preset",
                    "type": self.COMPONENT_DROPDOWN,
                    "params": {
                        "label": "Board Preset",
                        "description": "Select a board preset to use (automatically applied when selected)"
                    }
                },
                {
                    "key": "import_preset",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Board File",
                        "description": "Import a board JSON file (automatically adds to presets and applies)",
                        "icon": "📥"
                    }
                }
            ],
            "import_export": [
                {
                    "key": "auto_backup",
                    "type": self.COMPONENT_TOGGLE,
                    "params": {
                        "label": "Auto Backup",
                        "description": "Automatically backup game data",
                        "default_value": True
                    }
                },
                {
                    "key": "backup_interval",
                    "type": self.COMPONENT_SLIDER,
                    "params": {
                        "label": "Backup Interval",
                        "description": "Time between automatic backups in minutes",
                        "min_value": 5,
                        "max_value": 60,
                        "default_value": 30,
                        "value_format": "{:.0f} min"
                    }
                },
                {
                    "key": "import_boards",
                    "type": self.COMPONENT_BUTTON,
                    "params": {
                        "label": "Import Boards",
                        "description": "Import bingo boards from a file (adds to presets)",
                        "icon": "📥"
                    }
                }
            ]
        }

    def _lighten_color(self, color, amount):
        """Helper method to lighten a color by a specified amount with improved contrast"""
        r, g, b = color
        # Apply more lightening to darker colors for better visibility
        if r + g + b < 300:  # If color is relatively dark
            amount = int(amount * 1.5)  # Increase lightening amount by 50%

        # Apply non-linear lightening for more natural look
        r_factor = 1.0 - (r / 255) * 0.7  # Less lightening for already bright colors
        g_factor = 1.0 - (g / 255) * 0.7
        b_factor = 1.0 - (b / 255) * 0.7

        r_amount = int(amount * r_factor)
        g_amount = int(amount * g_factor)
        b_amount = int(amount * b_factor)

        return (min(255, r + r_amount), min(255, g + g_amount), min(255, b + b_amount))

    def _darken_color(self, color, amount):
        """Helper method to darken a color by a specified amount with improved contrast"""
        r, g, b = color
        # Apply less darkening to already dark colors to maintain visibility
        if r + g + b < 300:  # If color is relatively dark
            amount = int(amount * 0.7)  # Reduce darkening amount by 30%

        # Apply non-linear darkening for more natural look
        r_factor = (r / 255) * 0.8 + 0.2  # Less darkening for darker colors
        g_factor = (g / 255) * 0.8 + 0.2
        b_factor = (b / 255) * 0.8 + 0.2

        r_amount = int(amount * r_factor)
        g_amount = int(amount * g_factor)
        b_amount = int(amount * b_factor)

        return (max(0, r - r_amount), max(0, g - g_amount), max(0, b - b_amount))

    def render_emoji_icon(self, emoji, size, color, fallback_func=None):
        """Helper method to render emoji icons with fallback options

        Args:
            emoji: The emoji character to render
            size: The size of the icon in pixels
            color: The color to render the icon
            fallback_func: Optional function to call to create a fallback icon if emoji rendering fails
                           Function should take (surface, color) as parameters

        Returns:
            A pygame Surface containing the rendered icon
        """
        # Try multiple fonts to ensure emoji rendering works
        icon_fonts = [
            "Segoe UI Emoji",  # Windows
            "Apple Color Emoji",  # macOS
            "Noto Color Emoji",  # Linux
            "Arial",  # Fallback
            None  # System default
        ]

        # Try each font until we get one that works
        icon_surf = None
        for font_name in icon_fonts:
            try:
                icon_font = pygame.font.SysFont(font_name, size) if font_name else pygame.font.Font(None, size)
                icon_surf = icon_font.render(emoji, True, color)
                # If we got a surface with reasonable width, use it
                if icon_surf and icon_surf.get_width() > 5:
                    break
            except:
                continue

        # If all fonts failed and we have a fallback function, use it
        if (not icon_surf or icon_surf.get_width() <= 5) and fallback_func:
            icon_surf = pygame.Surface((size, size), pygame.SRCALPHA)
            fallback_func(icon_surf, color)
        # If no fallback function, create a simple colored square
        elif not icon_surf or icon_surf.get_width() <= 5:
            icon_surf = pygame.Surface((size, size), pygame.SRCALPHA)
            pygame.draw.rect(icon_surf, color, pygame.Rect(0, 0, size, size), border_radius=size//4)

        return icon_surf

    # Cache for gradient rectangles to avoid redrawing the same gradients
    _gradient_cache = {}
    _gradient_cache_size = 50  # Maximum number of cached gradients

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0, top_left_radius=-1, top_right_radius=-1,
                          bottom_left_radius=-1, bottom_right_radius=-1, horizontal=False, glossy=False):
        """Draw a rectangle with a gradient from color1 to color2 with caching for performance"""
        # Set default radii if not specified
        if top_left_radius == -1:
            top_left_radius = border_radius
        if top_right_radius == -1:
            top_right_radius = border_radius
        if bottom_left_radius == -1:
            bottom_left_radius = border_radius
        if bottom_right_radius == -1:
            bottom_right_radius = border_radius

        # Create a cache key based on the parameters
        cache_key = (rect.width, rect.height, color1, color2, top_left_radius, top_right_radius,
                    bottom_left_radius, bottom_right_radius, horizontal, glossy)

        # Check if this gradient is already in the cache
        if cache_key in self._gradient_cache:
            # Use the cached surface
            surface = self._gradient_cache[cache_key]
        else:
            # Create a new surface for the gradient
            surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

            # Draw the gradient with optimized step size
            if horizontal:
                # Horizontal gradient (left to right)
                # Use a step size to reduce the number of lines drawn
                step = max(1, rect.width // 100)  # Draw at most 100 lines
                for x in range(0, rect.width, step):
                    # Calculate color for this column
                    ratio = x / rect.width
                    r = int(color1[0] + (color2[0] - color1[0]) * ratio)
                    g = int(color1[1] + (color2[1] - color1[1]) * ratio)
                    b = int(color1[2] + (color2[2] - color1[2]) * ratio)
                    color = (r, g, b)

                    # Draw a vertical line with this color (with width=step)
                    line_width = min(step, rect.width - x)
                    pygame.draw.rect(surface, color, (x, 0, line_width, rect.height))
            else:
                # Vertical gradient (top to bottom)
                # Use a step size to reduce the number of lines drawn
                step = max(1, rect.height // 100)  # Draw at most 100 lines
                for y in range(0, rect.height, step):
                    # Calculate color for this row
                    ratio = y / rect.height
                    r = int(color1[0] + (color2[0] - color1[0]) * ratio)
                    g = int(color1[1] + (color2[1] - color1[1]) * ratio)
                    b = int(color1[2] + (color2[2] - color1[2]) * ratio)
                    color = (r, g, b)

                    # Draw a horizontal line with this color (with height=step)
                    line_height = min(step, rect.height - y)
                    pygame.draw.rect(surface, color, (0, y, rect.width, line_height))

            # Add glossy effect if requested (simplified for performance)
            if glossy:
                # Create a glossy highlight at the top
                gloss_height = int(rect.height * 0.4)  # 40% of height
                gloss_surface = pygame.Surface((rect.width, gloss_height), pygame.SRCALPHA)

                # Draw gradient with fewer steps for better performance
                step = max(1, gloss_height // 20)  # Use at most 20 steps
                for y in range(0, gloss_height, step):
                    alpha = max(0, 70 * (1 - y/gloss_height))  # Fade from top
                    line_height = min(step, gloss_height - y)
                    pygame.draw.rect(gloss_surface, (255, 255, 255, int(alpha)),
                                   (0, y, rect.width, line_height))

                # Apply the gloss to the main surface
                surface.blit(gloss_surface, (0, 0))

            # Apply rounded corners using a mask
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            mask.fill((0, 0, 0, 0))  # Transparent

            # Draw rounded rectangle on the mask
            pygame.draw.rect(mask, (255, 255, 255, 255), mask.get_rect(),
                           border_top_left_radius=top_left_radius,
                           border_top_right_radius=top_right_radius,
                           border_bottom_left_radius=bottom_left_radius,
                           border_bottom_right_radius=bottom_right_radius)

            # Apply mask to the gradient surface
            surface.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)

            # Add to cache (with size limit management)
            if len(self._gradient_cache) >= self._gradient_cache_size:
                # Remove the oldest item if cache is full
                self._gradient_cache.pop(next(iter(self._gradient_cache)))
            self._gradient_cache[cache_key] = surface

        # Draw the final surface to the screen
        self.screen.blit(surface, rect.topleft)

        return rect

    def _get_category_for_setting(self, setting_key):
        """Get the category for a setting based on its key"""
        # Use the category map from settings_manager
        category_map = self.settings_manager.get_setting_category_map()

        # Return the category if found in the map
        if setting_key in category_map:
            return category_map[setting_key]

        # Default to 'game' if not found
        return "game"

    # Define settings component types
    COMPONENT_TOGGLE = "toggle"
    COMPONENT_SLIDER = "slider"
    COMPONENT_DROPDOWN = "dropdown"
    COMPONENT_TEXT_INPUT = "text_input"
    COMPONENT_COLOR_PICKER = "color_picker"
    COMPONENT_BUTTON = "button"

    # Cache for rendered text to avoid re-rendering the same text
    _text_cache = {}
    _text_cache_size = 100  # Maximum number of cached text surfaces

    def draw_text(self, text, font, color, x, y, align="left", shadow=False, shadow_color=(0,0,0,100)):
        """Draw text with optional shadow and alignment with caching for performance"""
        # Create cache key based on text, font, color, and shadow
        cache_key = (text, font, color, shadow, shadow_color if shadow else None)

        # Check if this text is already in the cache
        if cache_key in self._text_cache:
            text_surface, shadow_surface = self._text_cache[cache_key]
        else:
            # Render text surfaces
            text_surface = font.render(text, True, color)
            shadow_surface = font.render(text, True, shadow_color) if shadow else None

            # Add to cache (with size limit management)
            if len(self._text_cache) >= self._text_cache_size:
                # Remove the oldest item if cache is full
                self._text_cache.pop(next(iter(self._text_cache)))
            self._text_cache[cache_key] = (text_surface, shadow_surface)

        # Get text rectangle
        text_rect = text_surface.get_rect()

        # Set position based on alignment
        if align == "center":
            text_rect.centerx = x
            text_rect.y = y
        elif align == "right":
            text_rect.right = x
            text_rect.y = y
        else:  # left alignment
            text_rect.x = x
            text_rect.y = y

        # Draw shadow if requested
        if shadow and shadow_surface:
            shadow_offset = int(2 * min(self.scale_x, self.scale_y))
            shadow_rect = shadow_surface.get_rect()
            shadow_rect.x = text_rect.x + shadow_offset
            shadow_rect.y = text_rect.y + shadow_offset
            self.screen.blit(shadow_surface, shadow_rect)

        # Draw main text
        self.screen.blit(text_surface, text_rect)

        return text_rect

    def draw_panel(self, rect, bg_color=None, border_radius=10, border_color=None, border_width=1):
        """Draw a modern panel with gradient, optional border and rounded corners"""
        # Use default background color if none provided
        if bg_color is None:
            bg_color = self.bg_color

        # Scale border radius and width based on screen size
        scaled_radius = int(border_radius * min(self.scale_x, self.scale_y))
        scaled_width = int(border_width * min(self.scale_x, self.scale_y))

        # Create panel surface with gradient
        panel_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Create subtle gradient (lighter at top, darker at bottom)
        top_color = bg_color
        bottom_color = self._darken_color(bg_color, 15)

        for y in range(rect.height):
            # Calculate gradient color
            ratio = y / rect.height
            r = int(top_color[0] + (bottom_color[0] - top_color[0]) * ratio)
            g = int(top_color[1] + (bottom_color[1] - top_color[1]) * ratio)
            b = int(top_color[2] + (bottom_color[2] - top_color[2]) * ratio)
            pygame.draw.line(panel_surf, (r, g, b), (0, y), (rect.width, y))

        # Apply rounded corners
        panel_mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            panel_mask,
            (255, 255, 255, 255),
            panel_mask.get_rect(),
            border_radius=scaled_radius
        )
        panel_surf.blit(panel_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)

        # Draw panel to screen
        self.screen.blit(panel_surf, rect)

        # Draw border if requested
        if border_color is not None:
            pygame.draw.rect(
                self.screen,
                border_color,
                rect,
                width=scaled_width,
                border_radius=scaled_radius
            )

        # Add subtle highlight at top for glossy effect
        if rect.height > 10:  # Only for panels tall enough
            highlight_height = min(int(rect.height * 0.2), 10)
            highlight_rect = pygame.Rect(rect.x + 1, rect.y + 1, rect.width - 2, highlight_height)
            highlight_surf = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)

            # Create gradient highlight
            for y in range(highlight_height):
                alpha = int(40 * (1 - y/highlight_height))  # Fade from top
                pygame.draw.line(highlight_surf, (255, 255, 255, alpha), (0, y), (highlight_rect.width, y))

            # Apply rounded corners to highlight
            highlight_mask = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
            pygame.draw.rect(
                highlight_mask,
                (255, 255, 255, 255),
                highlight_mask.get_rect(),
                border_radius=max(1, scaled_radius - 1)
            )
            highlight_surf.blit(highlight_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
            self.screen.blit(highlight_surf, highlight_rect)

        return rect

    def draw_button(self, rect, text, bg_color=None, text_color=None, border_radius=5,
                    is_hovered=False, is_active=False, icon=None):
        """Draw a button with text and optional icon"""
        # Determine colors based on state and theme
        if bg_color is None:
            if is_active:
                bg_color = self.primary_active
            elif is_hovered:
                bg_color = self.primary_hover
            else:
                bg_color = self.primary

        if text_color is None:
            # Auto-determine text color based on background brightness
            r, g, b = bg_color
            brightness = (r * 299 + g * 587 + b * 114) / 1000
            text_color = self.WHITE if brightness < 128 else self.BLACK

        # Scale border radius
        scaled_radius = int(border_radius * min(self.scale_x, self.scale_y))

        # Draw button background
        pygame.draw.rect(
            self.screen,
            bg_color,
            rect,
            border_radius=scaled_radius
        )

        # Prepare text
        text_surf = self.text_font.render(text, True, text_color)
        text_rect = text_surf.get_rect(center=rect.center)

        # Adjust for icon if provided
        if icon:
            # Use our helper function to render the emoji icon
            icon_size = int(22 * min(self.scale_x, self.scale_y))
            icon_surf = self.render_emoji_icon(icon, icon_size, text_color)
            icon_rect = icon_surf.get_rect(centery=rect.centery)

            # Position icon and text side by side
            total_width = icon_surf.get_width() + text_surf.get_width() + 10
            icon_rect.x = rect.centerx - total_width // 2
            text_rect.x = icon_rect.right + 10

            # Draw icon
            self.screen.blit(icon_surf, icon_rect)

        # Draw text
        self.screen.blit(text_surf, text_rect)

        # Store hit area - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_rect = rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_rect.y += content_y - self.scroll_y
        self.hit_areas[f"button_{text}"] = adjusted_rect

        return rect

    def draw_setting_component(self, rect, component_type, setting_key, params=None):
        """Generic method to draw any type of settings component"""
        if params is None:
            params = {}

        # Get default parameters
        label = params.get('label', setting_key.replace('_', ' ').title())
        description = params.get('description', None)
        default_value = params.get('default_value', None)

        # Draw the appropriate component based on type
        if component_type == self.COMPONENT_TOGGLE:
            return self._draw_toggle_component(rect, setting_key, label, description, default_value)
        elif component_type == self.COMPONENT_SLIDER:
            min_value = params.get('min_value', 0)
            max_value = params.get('max_value', 1)
            value_format = params.get('value_format', '{:.1f}')
            return self._draw_slider_component(rect, setting_key, label, description, min_value, max_value, default_value, value_format)
        elif component_type == self.COMPONENT_DROPDOWN:
            options = params.get('options', [])
            return self._draw_dropdown_component(rect, setting_key, label, description, options, default_value)
        elif component_type == self.COMPONENT_TEXT_INPUT:
            return self._draw_text_input_component(rect, setting_key, label, description, default_value)
        elif component_type == self.COMPONENT_COLOR_PICKER:
            return self._draw_color_picker_component(rect, setting_key, label, description, default_value)
        elif component_type == self.COMPONENT_BUTTON:
            icon = params.get('icon', None)
            on_click = params.get('on_click', None)
            return self._draw_button_component(rect, setting_key, label, description, icon, on_click)
        else:
            # Unknown component type, draw a placeholder
            self.draw_panel(rect, self.control_bg)
            self.draw_text(f"Unknown component: {component_type}", self.text_font, self.text_light, rect.x + 10, rect.y + 10)
            return rect

    def _draw_toggle_component(self, rect, setting_key, label=None, description=None, default_value=False):
        """Draw a toggle switch for boolean settings"""
        # Get current value
        category = self._get_category_for_setting(setting_key)
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Get mouse position for hover detection
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = rect.collidepoint(mouse_pos)

        # Draw background panel
        self.draw_panel(rect, self.control_bg)

        # Draw label if provided
        if label:
            label_x = rect.x + int(15 * self.scale_x)
            label_y = rect.y + int(15 * self.scale_y)
            self.draw_text(label, self.header_font, self.text_light, label_x, label_y)

            # Draw description if provided
            if description:
                desc_y = label_y + self.header_font.get_height() + int(5 * self.scale_y)
                self.draw_text(description, self.small_font, self.text_medium, label_x + int(5 * self.scale_x), desc_y)

        # Calculate toggle dimensions
        toggle_width = int(60 * min(self.scale_x, self.scale_y))
        toggle_height = int(30 * min(self.scale_x, self.scale_y))
        toggle_x = rect.right - toggle_width - int(20 * self.scale_x)
        toggle_y = rect.centery - toggle_height // 2
        toggle_rect = Rect(toggle_x, toggle_y, toggle_width, toggle_height)

        # Draw toggle background
        bg_color = self.success if current_value else self.control_border
        if is_hovered:
            # Lighten color when hovered
            bg_color = self._lighten_color(bg_color, 20)

        pygame.draw.rect(
            self.screen,
            bg_color,
            toggle_rect,
            border_radius=toggle_height // 2  # Fully rounded corners
        )

        # Calculate handle position
        handle_size = toggle_height - int(6 * min(self.scale_x, self.scale_y))
        handle_y = toggle_y + (toggle_height - handle_size) // 2

        if current_value:
            handle_x = toggle_x + toggle_width - handle_size - int(3 * min(self.scale_x, self.scale_y))
        else:
            handle_x = toggle_x + int(3 * min(self.scale_x, self.scale_y))

        # Draw handle
        pygame.draw.circle(
            self.screen,
            self.WHITE,
            (handle_x + handle_size // 2, handle_y + handle_size // 2),
            handle_size // 2
        )

        # Store hit area - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_rect = toggle_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_rect.y += content_y - self.scroll_y
        self.hit_areas[f"toggle_{setting_key}"] = adjusted_rect

        return toggle_rect

    # Keep the original draw_toggle method for backward compatibility
    def draw_toggle(self, rect, setting_key, default_value=False, label=None, description=None):
        """Draw a toggle switch for boolean settings (legacy method)"""
        return self._draw_toggle_component(rect, setting_key, label, description, default_value)

    def _draw_slider_component(self, rect, setting_key, label=None, description=None, min_value=0, max_value=1, default_value=0.5, value_format="{:.1f}"):
        """Draw a slider for numeric settings"""
        # Get current value
        category = self._get_category_for_setting(setting_key)
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Get mouse position for hover detection
        mouse_pos = pygame.mouse.get_pos()

        # Draw background panel
        self.draw_panel(rect, self.control_bg)

        # Draw label if provided
        if label:
            label_x = rect.x + int(15 * self.scale_x)
            label_y = rect.y + int(15 * self.scale_y)
            self.draw_text(label, self.header_font, self.text_light, label_x, label_y)

            # Draw description if provided
            if description:
                desc_y = label_y + self.header_font.get_height() + int(5 * self.scale_y)
                self.draw_text(description, self.small_font, self.text_medium, label_x + int(5 * self.scale_x), desc_y)

        # Calculate slider dimensions
        slider_width = int(rect.width * 0.6)
        slider_height = int(6 * min(self.scale_x, self.scale_y))
        slider_x = rect.x + int(15 * self.scale_x)
        slider_y = rect.bottom - slider_height - int(20 * self.scale_y)
        slider_rect = Rect(slider_x, slider_y, slider_width, slider_height)

        # Draw slider track
        pygame.draw.rect(
            self.screen,
            self.control_border,
            slider_rect,
            border_radius=slider_height // 2  # Fully rounded corners
        )

        # Calculate filled portion
        fill_ratio = (current_value - min_value) / (max_value - min_value)
        fill_width = int(slider_width * fill_ratio)
        fill_rect = Rect(slider_x, slider_y, fill_width, slider_height)

        # Draw filled portion
        if fill_width > 0:
            pygame.draw.rect(
                self.screen,
                self.primary,
                fill_rect,
                border_radius=slider_height // 2  # Fully rounded corners
            )

        # Calculate handle position
        handle_size = int(20 * min(self.scale_x, self.scale_y))
        handle_x = slider_x + fill_width - handle_size // 2
        handle_y = slider_y + slider_height // 2 - handle_size // 2
        handle_rect = Rect(handle_x, handle_y, handle_size, handle_size)

        # Check if handle is being hovered
        is_hovered = handle_rect.collidepoint(mouse_pos)
        handle_color = self._lighten_color(self.primary, 20) if is_hovered else self.primary

        # Draw handle
        pygame.draw.circle(
            self.screen,
            handle_color,
            (handle_x + handle_size // 2, handle_y + handle_size // 2),
            handle_size // 2
        )

        # Draw current value
        value_text = value_format.format(current_value)
        value_x = slider_x + slider_width + int(20 * self.scale_x)
        value_y = slider_y - int(5 * self.scale_y)
        self.draw_text(value_text, self.text_font, self.text_light, value_x, value_y)

        # Store hit area - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        slider_rect = Rect(slider_x, slider_y - handle_size, slider_width, handle_size * 2)
        adjusted_rect = slider_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_rect.y += content_y - self.scroll_y
        self.hit_areas[f"slider_{setting_key}"] = adjusted_rect

        return slider_rect

    # Keep the original draw_slider method for backward compatibility
    def draw_slider(self, rect, setting_key, min_value=0, max_value=1, default_value=0.5,
                    label=None, description=None, value_format="{:.1f}"):
        """Draw a slider for numeric settings (legacy method)"""
        return self._draw_slider_component(rect, setting_key, label, description, min_value, max_value, default_value, value_format)

    def show(self):
        """Show the settings window"""
        print("Showing settings window")
        self.visible = True
        print(f"Available tabs: {[tab['id'] for tab in self.tabs]}")
        print(f"Active tab: {self.active_tab}")

        # Clear the options cache to ensure we get fresh options
        if hasattr(self.settings_manager, 'clear_options_cache'):
            self.settings_manager.clear_options_cache('announcer_language')
            print("Cleared announcer_language options cache")

        # Apply game settings to parent when window is shown
        if self.parent:
            self.apply_game_settings_to_parent()

    def hide(self):
        """Hide the settings window and save settings"""
        # Save settings to ensure they persist
        self.settings_manager.save_settings()
        print("Settings saved to disk")
        self.visible = False

    def clear_caches(self):
        """Clear any cached surfaces and force a full redraw on next frame"""
        self._needs_full_redraw = True
        self._window_surface = None
        self.hit_areas = {}
        # Clear text cache
        self._text_cache.clear()

        # Clear gradient cache
        self._gradient_cache.clear()

        # Note: We don't clear font cache as it's shared between instances

    def _draw_dropdown_component(self, rect, setting_key, label=None, description=None, options=None, default_value=None):
        """Draw a dropdown menu for selection settings"""
        # Special handling for announcer_language dropdown
        if setting_key == "announcer_language":
            try:
                # Get options directly from AnnouncerLanguageManager
                options = AnnouncerLanguageManager.get_available_languages()
                print(f"Drawing announcer_language dropdown with options: {options}")

                # Make sure we have at least one option
                if not options or len(options) == 0:
                    options = ["Default", "Agewgna", "Amharic", "Oromo", "Somali", "Tigrigna"]
            except Exception as e:
                print(f"Error getting announcer language options: {e}")
                traceback.print_exc()  # Print full traceback for debugging
                options = ["Default", "Agewgna", "Amharic", "Oromo", "Somali", "Tigrigna"]
        elif options is None or len(options) == 0:
            # Try to get options from settings manager
            options = self.settings_manager.get_options_for_setting(setting_key)
            if options is None or len(options) == 0:
                # If still no options, create a placeholder
                options = ["No options available"]

        # Print debug info for board preset dropdown and announcer_language dropdown
        if setting_key == "current_preset" or setting_key == "announcer_language":
            print(f"Drawing {setting_key} dropdown with options: {options}")

        # Get current value
        category = self._get_category_for_setting(setting_key)
        current_value = self.settings_manager.get_setting(category, setting_key, default_value or options[0])

        # Get mouse position for hover detection
        mouse_pos = pygame.mouse.get_pos()

        # Draw background panel
        self.draw_panel(rect, self.control_bg)

        # Draw label if provided
        if label:
            label_x = rect.x + int(15 * self.scale_x)
            label_y = rect.y + int(15 * self.scale_y)
            self.draw_text(label, self.header_font, self.text_light, label_x, label_y)

            # Draw description if provided
            if description:
                desc_y = label_y + self.header_font.get_height() + int(5 * self.scale_y)
                self.draw_text(description, self.small_font, self.text_medium, label_x + int(5 * self.scale_x), desc_y)

        # Calculate dropdown dimensions
        dropdown_width = int(rect.width * 0.4)
        dropdown_height = int(40 * min(self.scale_x, self.scale_y))
        dropdown_x = rect.right - dropdown_width - int(20 * self.scale_x)
        dropdown_y = rect.centery - dropdown_height // 2
        dropdown_rect = Rect(dropdown_x, dropdown_y, dropdown_width, dropdown_height)

        # Check if dropdown is being hovered or is active
        is_hovered = dropdown_rect.collidepoint(mouse_pos)
        is_active = hasattr(self, 'active_dropdown') and self.active_dropdown == setting_key

        # Determine colors based on state
        if is_active:
            bg_color = self.primary
            text_color = self.WHITE
            border_color = self.primary_active
        elif is_hovered:
            bg_color = self._lighten_color(self.control_bg, 15)
            text_color = self.text_light
            border_color = self.primary_hover
        else:
            bg_color = self.control_bg
            text_color = self.text_light
            border_color = self.control_border

        # Draw dropdown box
        pygame.draw.rect(
            self.screen,
            bg_color,
            dropdown_rect,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw border
        pygame.draw.rect(
            self.screen,
            border_color,
            dropdown_rect,
            width=1,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw current value
        value_x = dropdown_x + int(10 * self.scale_x)
        value_y = dropdown_y + (dropdown_height - self.text_font.get_height()) // 2
        self.draw_text(str(current_value), self.text_font, text_color, value_x, value_y)

        # Draw dropdown arrow
        arrow_size = int(10 * min(self.scale_x, self.scale_y))
        arrow_x = dropdown_x + dropdown_width - arrow_size - int(10 * self.scale_x)
        arrow_y = dropdown_y + (dropdown_height - arrow_size) // 2

        # Draw arrow pointing up or down based on active state
        if is_active:
            # Up arrow
            points = [
                (arrow_x, arrow_y + arrow_size),
                (arrow_x + arrow_size, arrow_y + arrow_size),
                (arrow_x + arrow_size // 2, arrow_y)
            ]
        else:
            # Down arrow
            points = [
                (arrow_x, arrow_y),
                (arrow_x + arrow_size, arrow_y),
                (arrow_x + arrow_size // 2, arrow_y + arrow_size)
            ]

        pygame.draw.polygon(self.screen, text_color, points)

        # Draw dropdown options if active
        if is_active and options:
            option_height = int(30 * min(self.scale_x, self.scale_y))
            options_panel_height = len(options) * option_height
            options_panel_rect = Rect(dropdown_x, dropdown_y + dropdown_height, dropdown_width, options_panel_height)

            # Draw options panel
            pygame.draw.rect(
                self.screen,
                self._darken_color(self.control_bg, 10),
                options_panel_rect,
                border_radius=int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw border
            pygame.draw.rect(
                self.screen,
                self.control_border,
                options_panel_rect,
                width=1,
                border_radius=int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw each option
            for i, option in enumerate(options):
                option_rect = Rect(
                    dropdown_x,
                    dropdown_y + dropdown_height + (i * option_height),
                    dropdown_width,
                    option_height
                )

                # Check if option is being hovered
                is_option_hovered = option_rect.collidepoint(mouse_pos)

                # Highlight selected option and hovered option
                if option == current_value or is_option_hovered:
                    highlight_rect = Rect(
                        option_rect.x + 2,
                        option_rect.y + 2,
                        option_rect.width - 4,
                        option_rect.height - 4
                    )

                    highlight_color = self.primary if option == current_value else self._lighten_color(self.control_bg, 20)

                    pygame.draw.rect(
                        self.screen,
                        highlight_color,
                        highlight_rect,
                        border_radius=int(3 * min(self.scale_x, self.scale_y))
                    )

                # Draw option text
                option_text_color = self.WHITE if option == current_value else self.text_light
                option_text_x = dropdown_x + int(10 * self.scale_x)
                option_text_y = option_rect.y + (option_height - self.text_font.get_height()) // 2
                self.draw_text(str(option), self.text_font, option_text_color, option_text_x, option_text_y)

                # Store hit area for this option - adjust for content scroll position
                content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
                adjusted_rect = option_rect.copy()
                if hasattr(self, 'scroll_y'):
                    adjusted_rect.y += content_y - self.scroll_y
                self.hit_areas[f"dropdown_option_{setting_key}_{i}"] = adjusted_rect

        # Store hit area for dropdown - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_dropdown_rect = dropdown_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_dropdown_rect.y += content_y - self.scroll_y

        # Store the adjusted hit area
        self.hit_areas[f"dropdown_{setting_key}"] = adjusted_dropdown_rect

        # Print debug info for board preset dropdown and announcer_language dropdown
        if setting_key == "current_preset" or setting_key == "announcer_language":
            print(f"Dropdown hit area for {setting_key}: {adjusted_dropdown_rect}")

        return dropdown_rect

    # Keep the original draw_dropdown method for backward compatibility
    def draw_dropdown(self, rect, setting_key, options, default_value=None, label=None, description=None):
        """Draw a dropdown menu for selection settings (legacy method)"""
        return self._draw_dropdown_component(rect, setting_key, label, description, options, default_value)

    def _draw_button_component(self, rect, setting_key, label=None, description=None, icon=None, on_click=None):
        """Draw a button component"""
        # Get mouse position for hover detection
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = rect.collidepoint(mouse_pos)

        # Calculate button dimensions
        button_width = int(200 * min(self.scale_x, self.scale_y))
        button_height = int(50 * min(self.scale_x, self.scale_y))
        button_x = rect.x + int(15 * self.scale_x)

        # Draw background panel
        self.draw_panel(rect, self.control_bg)

        # Draw label if provided
        if label:
            label_y = rect.y + int(15 * self.scale_y)
            self.draw_text(label, self.header_font, self.text_light, button_x, label_y)

            # Draw description if provided
            if description:
                desc_y = label_y + self.header_font.get_height() + int(5 * self.scale_y)
                self.draw_text(description, self.small_font, self.text_medium, button_x + int(5 * self.scale_x), desc_y)
                button_y = desc_y + self.small_font.get_height() + int(15 * self.scale_y)
            else:
                button_y = label_y + self.header_font.get_height() + int(15 * self.scale_y)
        else:
            # Center button vertically if no label
            button_y = rect.centery - button_height // 2

        # Create button rectangle
        button_rect = Rect(button_x, button_y, button_width, button_height)

        # Draw button
        self.draw_button(
            button_rect,
            label or setting_key.replace('_', ' ').title(),
            bg_color=self.primary_hover if is_hovered else self.primary,
            is_hovered=is_hovered,
            icon=icon
        )

        # Store hit area - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_button_rect = button_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_button_rect.y += content_y - self.scroll_y

        # Store the adjusted hit area
        self.hit_areas[f"button_{setting_key}"] = adjusted_button_rect

        # Print debug info for import preset button
        if setting_key == "import_preset":
            print(f"Button hit area for {setting_key}: {adjusted_button_rect}")

        return button_rect

    def _draw_color_picker_component(self, rect, setting_key, label=None, description=None, default_value=None):
        """Draw a color picker component"""
        # Get current value
        category = self._get_category_for_setting(setting_key)
        current_value = self.settings_manager.get_setting(category, setting_key, default_value or "#FFFFFF")

        # Convert hex color to RGB
        try:
            rgb_color = self.settings_manager.hex_to_rgb(current_value)
        except:
            # Default to white if invalid color
            rgb_color = (255, 255, 255)
            current_value = "#FFFFFF"

        # Get mouse position for hover detection
        mouse_pos = pygame.mouse.get_pos()

        # Draw background panel
        self.draw_panel(rect, self.control_bg)

        # Draw label if provided
        if label:
            label_x = rect.x + int(15 * self.scale_x)
            label_y = rect.y + int(15 * self.scale_y)
            self.draw_text(label, self.header_font, self.text_light, label_x, label_y)

            # Draw description if provided
            if description:
                desc_y = label_y + self.header_font.get_height() + int(5 * self.scale_y)
                self.draw_text(description, self.small_font, self.text_medium, label_x + int(5 * self.scale_x), desc_y)

        # Calculate color preview dimensions
        preview_size = int(40 * min(self.scale_x, self.scale_y))
        preview_x = rect.right - preview_size - int(20 * self.scale_x)
        preview_y = rect.centery - preview_size // 2
        preview_rect = Rect(preview_x, preview_y, preview_size, preview_size)

        # Draw color preview
        pygame.draw.rect(
            self.screen,
            rgb_color,
            preview_rect,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw border around preview
        pygame.draw.rect(
            self.screen,
            self.control_border,
            preview_rect,
            width=1,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw current color value
        value_x = preview_x - int(10 * self.scale_x) - self.text_font.size(current_value)[0]
        value_y = preview_y + (preview_size - self.text_font.get_height()) // 2
        self.draw_text(current_value, self.text_font, self.text_light, value_x, value_y)

        # Store hit area for color picker - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_rect = preview_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_rect.y += content_y - self.scroll_y
        self.hit_areas[f"color_picker_{setting_key}"] = adjusted_rect

        return preview_rect

    def _draw_text_input_component(self, rect, setting_key, label=None, description=None, default_value=None):
        """Draw a text input component using the simple text input implementation"""
        # Use the simple text input component
        return self.text_input.draw(rect, setting_key, label, description, default_value)

    # Cache for the main window surface
    _window_surface = None
    _window_dimensions = None
    _last_active_tab = None
    _last_mouse_pos = None
    _needs_full_redraw = True

    def update(self, dt):
        """Update the settings window"""
        if not self.visible:
            return

        # Update animation time for cursor blinking
        self.animation_time = (pygame.time.get_ticks() / 1000) % 2

    def draw(self):
        """Draw the settings window if visible"""
        if not self.visible:
            return

        # Update animation time for cursor blinking
        self.animation_time = (pygame.time.get_ticks() / 1000) % 2

        # Debug print for tabs
        print(f"Drawing settings window with tabs: {[tab['id'] for tab in self.tabs]}")
        print(f"Active tab: {self.active_tab}")

        # Clear hit areas for this frame
        self.hit_areas = {}

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Calculate settings window dimensions with improved proportions
        window_width = int(min(950, screen_width * 0.85))  # Slightly wider for better content display
        window_height = int(min(720, screen_height * 0.85))  # Slightly taller for better content display
        window_x = (screen_width - window_width) // 2
        window_y = (screen_height - window_height) // 2

        # Store window dimensions for event handling
        self.window_rect = pygame.Rect(window_x, window_y, window_width, window_height)

        # Draw semi-transparent overlay with subtle gradient for depth
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)

        # Create gradient overlay from top to bottom
        for y in range(screen_height):
            # Calculate alpha based on position (darker at center, lighter at edges)
            center_y = screen_height / 2
            distance = abs(y - center_y) / center_y  # 0 at center, 1 at edges
            alpha = 180 - int(20 * distance)  # Vary alpha slightly for subtle effect
            overlay.fill((0, 0, 0, alpha), (0, y, screen_width, 1))

        self.screen.blit(overlay, (0, 0))

        # Create window surface
        window_surf = pygame.Surface((window_width, window_height), pygame.SRCALPHA)

        # Draw window shadow for depth perception
        border_radius = int(12 * min(self.scale_x, self.scale_y))  # Slightly reduced for modern look
        shadow_size = int(15 * min(self.scale_x, self.scale_y))
        shadow_surf = pygame.Surface((window_width + shadow_size*2, window_height + shadow_size*2), pygame.SRCALPHA)

        # Draw multi-layered shadow with decreasing opacity
        for i in range(shadow_size, 0, -2):
            alpha = 5 + (shadow_size - i) * 2  # Gradually increase opacity closer to window
            shadow_rect = pygame.Rect(i, i, window_width + shadow_size*2 - i*2, window_height + shadow_size*2 - i*2)
            pygame.draw.rect(shadow_surf, (0, 0, 0, alpha), shadow_rect, border_radius=border_radius)

        # Blit shadow to screen first (positioned behind the window)
        self.screen.blit(shadow_surf, (window_x - shadow_size, window_y - shadow_size))

        # Draw window background with subtle gradient for depth
        bg_top = self.bg_color
        bg_bottom = self._darken_color(self.bg_color, 10)

        # Create gradient background
        for y in range(window_height):
            # Calculate gradient color
            ratio = y / window_height
            r = int(bg_top[0] + (bg_bottom[0] - bg_top[0]) * ratio)
            g = int(bg_top[1] + (bg_bottom[1] - bg_top[1]) * ratio)
            b = int(bg_top[2] + (bg_bottom[2] - bg_top[2]) * ratio)
            pygame.draw.line(window_surf, (r, g, b), (0, y), (window_width, y))

        # Apply rounded corners using a mask
        mask = pygame.Surface((window_width, window_height), pygame.SRCALPHA)
        pygame.draw.rect(
            mask,
            (255, 255, 255, 255),
            pygame.Rect(0, 0, window_width, window_height),
            border_radius=border_radius
        )
        window_surf.blit(mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)

        # Draw header with gradient for modern look
        self.header_height = int(60 * min(self.scale_x, self.scale_y))
        header_rect = pygame.Rect(0, 0, window_width, self.header_height)

        # Define tab accent colors
        tab_accent_colors = {
            "game": (0, 140, 240),      # Blue
            "display": (0, 180, 180),    # Teal
            "pattern_bonuses": (220, 80, 180),  # Pink/Purple
            "advertising": (255, 180, 0), # Gold
            "audio": (140, 100, 220),    # Purple
            "language": (255, 100, 100),  # Red
            "boards": (50, 180, 100),    # Green
            "import_export": (255, 140, 40) # Orange
        }

        # Create gradient header with accent color influence
        header_start = self._darken_color(self.header_color, 5)
        header_end = self._darken_color(self.header_color, 25)

        # Get accent color based on active tab
        active_tab_accent = tab_accent_colors.get(self.active_tab, (0, 140, 240))

        # Blend a bit of the accent color into the header for cohesive design
        header_start = (
            int(header_start[0] * 0.9 + active_tab_accent[0] * 0.1),
            int(header_start[1] * 0.9 + active_tab_accent[1] * 0.1),
            int(header_start[2] * 0.9 + active_tab_accent[2] * 0.1)
        )

        # Create gradient surface
        header_surf = pygame.Surface((header_rect.width, header_rect.height), pygame.SRCALPHA)
        for y in range(header_rect.height):
            # Calculate gradient color
            ratio = y / header_rect.height
            r = int(header_start[0] + (header_end[0] - header_start[0]) * ratio)
            g = int(header_start[1] + (header_end[1] - header_start[1]) * ratio)
            b = int(header_start[2] + (header_end[2] - header_start[2]) * ratio)
            pygame.draw.line(header_surf, (r, g, b), (0, y), (header_rect.width, y))

        # Apply rounded corners to header
        header_mask = pygame.Surface((header_rect.width, header_rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            header_mask,
            (255, 255, 255, 255),
            header_mask.get_rect(),
            border_top_left_radius=border_radius,
            border_top_right_radius=border_radius
        )
        header_surf.blit(header_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        window_surf.blit(header_surf, header_rect)

        # Add subtle highlight at top of header for glossy effect
        highlight_height = int(self.header_height * 0.3)
        highlight_rect = pygame.Rect(1, 1, window_width-2, highlight_height)
        highlight_surf = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)

        # Create gradient highlight from top to bottom
        for y in range(highlight_height):
            alpha = int(60 * (1 - y/highlight_height))  # Fade from top
            pygame.draw.line(highlight_surf, (255, 255, 255, alpha), (0, y), (highlight_rect.width, y))

        # Apply rounded corners to highlight
        highlight_mask = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            highlight_mask,
            (255, 255, 255, 255),
            highlight_mask.get_rect(),
            border_top_left_radius=border_radius,
            border_top_right_radius=border_radius
        )
        highlight_surf.blit(highlight_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        window_surf.blit(highlight_surf, highlight_rect)

        # Draw title with icon for modern look
        title_text = "Settings"
        title_icon = "⚙️"  # Gear icon

        # Draw icon with improved font handling
        icon_size = int(30 * min(self.scale_x, self.scale_y))  # Slightly larger for better visibility

        # Define a fallback function to create a gear icon if emoji rendering fails
        def gear_fallback(surface, color):
            # Draw a gear-like shape
            center = (icon_size // 2, icon_size // 2)
            outer_radius = icon_size // 2 - 2
            inner_radius = icon_size // 3
            teeth = 8
            pygame.draw.circle(surface, color, center, inner_radius)
            for i in range(teeth):
                angle = i * (2 * math.pi / teeth)
                x1 = center[0] + int(inner_radius * math.cos(angle))
                y1 = center[1] + int(inner_radius * math.sin(angle))
                x2 = center[0] + int(outer_radius * math.cos(angle))
                y2 = center[1] + int(outer_radius * math.sin(angle))
                pygame.draw.line(surface, color, (x1, y1), (x2, y2), 3)

        # Use our helper function to render the emoji icon with fallback
        icon_surf = self.render_emoji_icon(title_icon, icon_size, self.text_light, gear_fallback)

        icon_x = int(20 * min(self.scale_x, self.scale_y))
        icon_y = int(self.header_height / 2 - icon_surf.get_height() / 2)
        window_surf.blit(icon_surf, (icon_x, icon_y))

        # Draw title text with subtle shadow for depth
        title_x = icon_x + icon_surf.get_width() + int(10 * min(self.scale_x, self.scale_y))
        title_y = int(self.header_height / 2 - self.title_font.get_height() / 2)

        # Draw shadow
        shadow_offset = int(2 * min(self.scale_x, self.scale_y))
        shadow_surf = self.title_font.render(title_text, True, (0, 0, 0, 100))
        window_surf.blit(shadow_surf, (title_x + shadow_offset, title_y + shadow_offset))

        # Draw main text
        title_surf = self.title_font.render(title_text, True, self.text_light)
        window_surf.blit(title_surf, (title_x, title_y))

        # Draw modern close button
        close_size = int(32 * min(self.scale_x, self.scale_y))
        close_x = window_width - close_size - int(15 * min(self.scale_x, self.scale_y))
        close_y = int(self.header_height / 2 - close_size / 2)
        close_rect = pygame.Rect(close_x, close_y, close_size, close_size)

        # Check if mouse is hovering over close button
        mouse_pos = pygame.mouse.get_pos()
        rel_mouse_pos = (mouse_pos[0] - window_x, mouse_pos[1] - window_y)
        is_close_hovered = close_rect.collidepoint(rel_mouse_pos)

        # Draw close button with hover effect
        if is_close_hovered:
            # Brighter red for hover
            close_color = (240, 80, 80)
            # Add glow effect
            glow_size = close_size + int(6 * min(self.scale_x, self.scale_y))
            glow_rect = pygame.Rect(
                close_x - (glow_size - close_size) // 2,
                close_y - (glow_size - close_size) // 2,
                glow_size,
                glow_size
            )
            glow_surf = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)
            pygame.draw.circle(glow_surf, (255, 100, 100, 70), (glow_size // 2, glow_size // 2), glow_size // 2)
            window_surf.blit(glow_surf, glow_rect)
        else:
            # Normal red
            close_color = (220, 60, 60)

        # Draw circular close button
        pygame.draw.circle(window_surf, close_color, (close_x + close_size // 2, close_y + close_size // 2), close_size // 2)

        # Draw X with improved styling
        x_size = int(close_size * 0.5)  # Slightly smaller X
        x_offset = int((close_size - x_size) / 2)
        x_thickness = int(2.5 * min(self.scale_x, self.scale_y))  # Thicker lines

        # Draw X with rounded ends for modern look
        pygame.draw.line(
            window_surf,
            self.WHITE,
            (close_x + x_offset, close_y + x_offset),
            (close_x + close_size - x_offset, close_y + close_size - x_offset),
            x_thickness
        )
        pygame.draw.line(
            window_surf,
            self.WHITE,
            (close_x + x_offset, close_y + close_size - x_offset),
            (close_x + close_size - x_offset, close_y + x_offset),
            x_thickness
        )

        # Store hit area for close button
        self.hit_areas["close_button"] = pygame.Rect(close_x, close_y, close_size, close_size)

        # Draw tab bar
        self.tab_bar_height = int(50 * min(self.scale_x, self.scale_y))
        tab_bar_y = self.header_height
        tab_bar_rect = pygame.Rect(0, tab_bar_y, window_width, self.tab_bar_height)
        pygame.draw.rect(
            window_surf,
            self._darken_color(self.bg_color, 10),
            tab_bar_rect
        )

        # Define tab icons and colors
        tab_icons = {
            "game": "🎮",       # Game controller
            "display": "🖥️",     # Display/monitor
            "animations": "✨",   # Sparkles for animations
            "pattern_bonuses": "🎯", # Target/bullseye for patterns
            "advertising": "📢",  # Megaphone/announcement
            "audio": "🔊",       # Speaker
            "language": "🌐",    # Globe
            "boards": "📋",      # Clipboard
            "import_export": "📤" # Export icon
        }

        # Define tab accent colors
        tab_accent_colors = {
            "game": (0, 140, 240),      # Blue
            "display": (0, 180, 180),    # Teal
            "animations": (255, 100, 255), # Pink/Magenta
            "pattern_bonuses": (220, 80, 180),  # Pink/Purple
            "advertising": (255, 180, 0), # Gold
            "audio": (140, 100, 220),    # Purple
            "language": (255, 100, 100),  # Red
            "boards": (50, 180, 100),    # Green
            "import_export": (255, 140, 40) # Orange
        }

        # Draw tabs - use the tabs defined in __init__
        tabs = [tab["id"] for tab in self.tabs]
        print(f"Drawing tabs: {tabs}")
        tab_width = window_width // len(tabs)

        for i, tab in enumerate(tabs):
            tab_x = i * tab_width
            tab_rect = pygame.Rect(tab_x, tab_bar_y, tab_width, self.tab_bar_height)

            # Get tab accent color
            accent_color = tab_accent_colors.get(tab, self.primary)

            # Determine tab colors
            if self.active_tab == tab:
                bg_color = self.bg_color
                text_color = accent_color
                icon_color = accent_color
                # Draw active indicator with tab-specific color
                pygame.draw.rect(window_surf, accent_color,
                               pygame.Rect(tab_x, tab_bar_y + self.tab_bar_height - 3, tab_width, 3))
            else:
                bg_color = self._darken_color(self.bg_color, 10)
                text_color = self.text_medium
                icon_color = self.text_medium

            # Draw tab background with subtle gradient for depth
            if self.active_tab == tab:
                # Active tab has lighter gradient
                pygame.draw.rect(window_surf, bg_color, tab_rect)
            else:
                # Inactive tabs have subtle gradient
                darker_bg = self._darken_color(bg_color, 5)
                # Create gradient surface
                grad_surf = pygame.Surface((tab_rect.width, tab_rect.height), pygame.SRCALPHA)
                for y in range(tab_rect.height):
                    # Calculate gradient color
                    ratio = y / tab_rect.height
                    r = int(bg_color[0] + (darker_bg[0] - bg_color[0]) * ratio)
                    g = int(bg_color[1] + (darker_bg[1] - bg_color[1]) * ratio)
                    b = int(bg_color[2] + (darker_bg[2] - bg_color[2]) * ratio)
                    pygame.draw.line(grad_surf, (r, g, b), (0, y), (tab_rect.width, y))
                window_surf.blit(grad_surf, tab_rect)

            # Get icon for this tab
            icon = tab_icons.get(tab, "⚙️")  # Default to gear icon

            # Calculate icon and text positioning for better layout
            icon_size = int(26 * min(self.scale_x, self.scale_y))  # Slightly larger for better visibility

            # Use our helper function to render the emoji icon
            icon_surf = self.render_emoji_icon(icon, icon_size, icon_color)

            # Draw tab text with condensed display name
            tab_display_name = tab.replace("_", "/").title()
            # Shorten some tab names for better fit
            if tab == "advertising":
                tab_display_name = "Ads"
            elif tab == "import_export":
                tab_display_name = "Import/Export"

            text_font_size = int(14 * min(self.scale_x, self.scale_y))
            text_font = pygame.font.SysFont("Arial", text_font_size)
            tab_text_surf = text_font.render(tab_display_name, True, text_color)

            # Position icon and text vertically stacked for cleaner layout
            icon_x = tab_rect.centerx - icon_surf.get_width() // 2
            icon_y = tab_rect.y + int(tab_rect.height * 0.25) - icon_surf.get_height() // 2
            text_x = tab_rect.centerx - tab_text_surf.get_width() // 2
            text_y = tab_rect.y + int(tab_rect.height * 0.65)

            # Draw icon and text
            window_surf.blit(icon_surf, (icon_x, icon_y))
            window_surf.blit(tab_text_surf, (text_x, text_y))

            # Add subtle hover effect
            mouse_pos = pygame.mouse.get_pos()
            rel_mouse_pos = (mouse_pos[0] - window_x, mouse_pos[1] - window_y)
            if tab_rect.collidepoint(rel_mouse_pos) and self.active_tab != tab:
                # Draw semi-transparent highlight overlay
                hover_surf = pygame.Surface((tab_rect.width, tab_rect.height), pygame.SRCALPHA)
                hover_surf.fill((255, 255, 255, 20))  # Very subtle white overlay
                window_surf.blit(hover_surf, tab_rect)

            # Store hit area for tab
            self.hit_areas[f"tab_{tab}"] = pygame.Rect(tab_x, tab_bar_y, tab_width, self.tab_bar_height)

        # Calculate content area
        content_y = tab_bar_y + self.tab_bar_height
        content_height = window_height - self.header_height - self.tab_bar_height
        self.visible_content_height = content_height

        # Create a temporary surface for content
        content_surf = pygame.Surface((window_width, 2000), pygame.SRCALPHA)

        # Save current screen
        original_screen = self.screen
        self.screen = content_surf

        # Draw content based on active tab
        content_rect = pygame.Rect(0, 0, window_width, 2000)
        if self.active_tab == "game":
            self.content_height = self.draw_game_settings(content_rect)
        elif self.active_tab == "display":
            self.content_height = self.draw_display_settings(content_rect)
        elif self.active_tab == "animations":
            self.content_height = self.draw_settings_tab(content_rect, "animations")
        elif self.active_tab == "pattern_bonuses":
            self.content_height = self.draw_pattern_bonuses(content_rect)
        elif self.active_tab == "advertising":
            print("Drawing advertising tab")
            self.content_height = self.draw_settings_tab(content_rect, "advertising")
            # Print all hit areas for debugging
            for key, rect in self.hit_areas.items():
                if key.startswith("text_input_") or key.startswith("copy_button_") or key.startswith("paste_button_"):
                    print(f"Hit area: {key}, Rect: {rect}")
        elif self.active_tab == "audio":
            self.content_height = self.draw_audio_settings(content_rect)
        elif self.active_tab == "language":
            self.content_height = self.draw_language_settings(content_rect)
        elif self.active_tab == "boards":
            self.content_height = self.draw_boards_settings(content_rect)
        elif self.active_tab == "import_export":
            self.content_height = self.draw_import_export_settings(content_rect)

        # Restore original screen
        self.screen = original_screen

        # Draw the visible portion of content
        window_surf.blit(content_surf, (0, content_y), (0, self.scroll_y, window_width, content_height))

        # Draw modern scrollbar if needed
        if self.content_height > content_height:
            self.max_scroll = self.content_height - content_height

            # Draw scroll track with thinner, more modern design
            track_width = int(8 * min(self.scale_x, self.scale_y))  # Thinner track
            track_padding = int(3 * min(self.scale_x, self.scale_y))  # Padding from edge
            track_x = window_width - track_width - track_padding
            track_rect = pygame.Rect(track_x, content_y, track_width, content_height)

            # Check if mouse is near the scrollbar area for hover effect
            mouse_pos = pygame.mouse.get_pos()
            rel_mouse_pos = (mouse_pos[0] - window_x, mouse_pos[1] - window_y)
            scrollbar_hover_area = pygame.Rect(track_x - track_padding*2, content_y,
                                             track_width + track_padding*4, content_height)
            is_scrollbar_hovered = scrollbar_hover_area.collidepoint(rel_mouse_pos)

            # Draw track with subtle gradient
            track_top_color = self._darken_color(self.bg_color, 15)
            track_bottom_color = self._darken_color(self.bg_color, 25)

            # Create gradient track
            track_surf = pygame.Surface((track_width, content_height), pygame.SRCALPHA)
            for y in range(content_height):
                # Calculate gradient color
                ratio = y / content_height
                r = int(track_top_color[0] + (track_bottom_color[0] - track_top_color[0]) * ratio)
                g = int(track_top_color[1] + (track_bottom_color[1] - track_top_color[1]) * ratio)
                b = int(track_top_color[2] + (track_bottom_color[2] - track_top_color[2]) * ratio)
                pygame.draw.line(track_surf, (r, g, b), (0, y), (track_width, y))

            # Apply rounded corners to track
            track_radius = track_width // 2
            track_mask = pygame.Surface((track_width, content_height), pygame.SRCALPHA)
            pygame.draw.rect(
                track_mask,
                (255, 255, 255, 255),
                track_mask.get_rect(),
                border_radius=track_radius
            )
            track_surf.blit(track_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
            window_surf.blit(track_surf, track_rect)

            # Calculate handle size and position with improved proportions
            handle_ratio = min(1.0, content_height / self.content_height)
            handle_height = max(int(40 * min(self.scale_x, self.scale_y)), int(content_height * handle_ratio))
            handle_y = content_y + int((self.scroll_y / self.max_scroll) * (content_height - handle_height))

            # Get accent color for handle based on active tab
            handle_color = tab_accent_colors.get(self.active_tab, (0, 140, 240))

            # Adjust handle color based on hover/drag state
            is_handle_hovered = False
            handle_rect = pygame.Rect(track_x, handle_y, track_width, handle_height)
            is_handle_hovered = handle_rect.collidepoint(rel_mouse_pos)
            is_handle_dragged = hasattr(self, 'dragging_scroll') and self.dragging_scroll

            if is_handle_dragged:
                # Brightest when being dragged
                handle_color = self._lighten_color(handle_color, 40)
            elif is_handle_hovered or is_scrollbar_hovered:
                # Brighter when hovered
                handle_color = self._lighten_color(handle_color, 20)

            # Create gradient handle (lighter at top, darker at bottom)
            handle_top_color = handle_color
            handle_bottom_color = self._darken_color(handle_color, 30)

            # Create handle surface with gradient
            handle_surf = pygame.Surface((track_width, handle_height), pygame.SRCALPHA)
            for y in range(handle_height):
                # Calculate gradient color
                ratio = y / handle_height
                r = int(handle_top_color[0] + (handle_bottom_color[0] - handle_top_color[0]) * ratio)
                g = int(handle_top_color[1] + (handle_bottom_color[1] - handle_top_color[1]) * ratio)
                b = int(handle_top_color[2] + (handle_bottom_color[2] - handle_top_color[2]) * ratio)
                pygame.draw.line(handle_surf, (r, g, b), (0, y), (track_width, y))

            # Apply rounded corners to handle
            handle_mask = pygame.Surface((track_width, handle_height), pygame.SRCALPHA)
            pygame.draw.rect(
                handle_mask,
                (255, 255, 255, 255),
                handle_mask.get_rect(),
                border_radius=track_radius
            )
            handle_surf.blit(handle_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
            window_surf.blit(handle_surf, handle_rect)

            # Add highlight to top of handle for glossy effect
            if not is_handle_dragged:  # Don't show highlight when dragging
                highlight_height = int(handle_height * 0.3)
                highlight_rect = pygame.Rect(track_x + 1, handle_y + 1, track_width - 2, highlight_height)
                highlight_surf = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)

                # Create gradient highlight
                for y in range(highlight_height):
                    alpha = int(100 * (1 - y/highlight_height))  # Fade from top
                    pygame.draw.line(highlight_surf, (255, 255, 255, alpha), (0, y), (highlight_rect.width, y))

                # Apply rounded corners to highlight
                highlight_mask = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
                pygame.draw.rect(
                    highlight_mask,
                    (255, 255, 255, 255),
                    highlight_mask.get_rect(),
                    border_radius=track_radius - 1
                )
                highlight_surf.blit(highlight_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
                window_surf.blit(highlight_surf, highlight_rect)

            # Store hit areas for scrolling - these are in window coordinates
            self.hit_areas["scroll_track"] = pygame.Rect(track_x - track_padding, content_y,
                                                      track_width + track_padding*2, content_height)
            self.hit_areas["scroll_handle"] = pygame.Rect(track_x, handle_y, track_width, handle_height)
        else:
            self.max_scroll = 0

        # Draw a modern footer with "Reset to Defaults" button
        footer_height = int(60 * min(self.scale_x, self.scale_y))
        footer_y = window_height - footer_height
        footer_rect = pygame.Rect(0, footer_y, window_width, footer_height)

        # Create gradient footer (lighter to darker)
        footer_start = self._darken_color(self.bg_color, 5)
        footer_end = self._darken_color(self.bg_color, 15)

        # Create gradient surface
        footer_surf = pygame.Surface((footer_rect.width, footer_rect.height), pygame.SRCALPHA)
        for y in range(footer_rect.height):
            # Calculate gradient color
            ratio = y / footer_rect.height
            r = int(footer_start[0] + (footer_end[0] - footer_start[0]) * ratio)
            g = int(footer_start[1] + (footer_end[1] - footer_start[1]) * ratio)
            b = int(footer_start[2] + (footer_end[2] - footer_start[2]) * ratio)
            pygame.draw.line(footer_surf, (r, g, b), (0, y), (footer_rect.width, y))

        # Apply rounded corners to footer
        footer_mask = pygame.Surface((footer_rect.width, footer_rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            footer_mask,
            (255, 255, 255, 255),
            footer_mask.get_rect(),
            border_bottom_left_radius=border_radius,
            border_bottom_right_radius=border_radius
        )
        footer_surf.blit(footer_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        window_surf.blit(footer_surf, footer_rect)

        # Add subtle separator line above footer
        separator_y = footer_y - 1
        pygame.draw.line(
            window_surf,
            self._lighten_color(self.bg_color, 10),  # Slightly lighter than background
            (0, separator_y),
            (window_width, separator_y),
            1
        )

        # Draw modern reset button with icon
        button_width = int(200 * min(self.scale_x, self.scale_y))
        button_height = int(40 * min(self.scale_x, self.scale_y))
        button_x = window_width - button_width - int(20 * self.scale_x)
        button_y = footer_y + (footer_height - button_height) // 2
        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)

        # Check if mouse is hovering over reset button
        is_reset_hovered = button_rect.collidepoint(rel_mouse_pos)

        # Get accent color for reset button
        reset_color = tab_accent_colors.get(self.active_tab, (0, 140, 240))

        # Adjust color based on hover state
        if is_reset_hovered:
            # Brighter color for hover
            reset_color = self._lighten_color(reset_color, 30)

            # Add glow effect
            glow_surf = pygame.Surface((button_width + 10, button_height + 10), pygame.SRCALPHA)
            for i in range(5):
                glow_alpha = 10 - i * 2
                glow_rect = pygame.Rect(i, i, button_width + 10 - i*2, button_height + 10 - i*2)
                pygame.draw.rect(
                    glow_surf,
                    (reset_color[0], reset_color[1], reset_color[2], glow_alpha),
                    glow_rect,
                    border_radius=int(8 * min(self.scale_x, self.scale_y))
                )
            window_surf.blit(glow_surf, (button_x - 5, button_y - 5))

        # Draw button with gradient
        button_start = reset_color
        button_end = self._darken_color(reset_color, 30)

        # Create gradient button
        button_surf = pygame.Surface((button_rect.width, button_rect.height), pygame.SRCALPHA)
        for y in range(button_rect.height):
            # Calculate gradient color
            ratio = y / button_rect.height
            r = int(button_start[0] + (button_end[0] - button_start[0]) * ratio)
            g = int(button_start[1] + (button_end[1] - button_start[1]) * ratio)
            b = int(button_start[2] + (button_end[2] - button_start[2]) * ratio)
            pygame.draw.line(button_surf, (r, g, b), (0, y), (button_rect.width, y))

        # Apply rounded corners
        button_radius = int(8 * min(self.scale_x, self.scale_y))
        button_mask = pygame.Surface((button_rect.width, button_rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            button_mask,
            (255, 255, 255, 255),
            button_mask.get_rect(),
            border_radius=button_radius
        )
        button_surf.blit(button_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        window_surf.blit(button_surf, button_rect)

        # Add highlight to top of button for glossy effect
        highlight_height = int(button_height * 0.4)
        highlight_rect = pygame.Rect(button_x + 2, button_y + 2, button_width - 4, highlight_height)
        highlight_surf = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)

        # Create gradient highlight
        for y in range(highlight_height):
            alpha = int(80 * (1 - y/highlight_height))  # Fade from top
            pygame.draw.line(highlight_surf, (255, 255, 255, alpha), (0, y), (highlight_rect.width, y))

        # Apply rounded corners to highlight
        highlight_mask = pygame.Surface((highlight_rect.width, highlight_rect.height), pygame.SRCALPHA)
        pygame.draw.rect(
            highlight_mask,
            (255, 255, 255, 255),
            highlight_mask.get_rect(),
            border_radius=button_radius - 2
        )
        highlight_surf.blit(highlight_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        window_surf.blit(highlight_surf, highlight_rect)

        # Draw button text with icon
        button_text = "Reset to Defaults"
        reset_icon = "🔄"  # Refresh/reset icon

        # Draw icon with improved font handling
        icon_size = int(22 * min(self.scale_x, self.scale_y))  # Slightly larger for better visibility

        # Define a fallback function to create a reset arrow icon if emoji rendering fails
        def reset_arrow_fallback(surface, color):
            # Draw a circular arrow
            center = (icon_size // 2, icon_size // 2)
            radius = icon_size // 2 - 2
            # Draw circle (not complete - leave a gap)
            pygame.draw.arc(surface, color,
                          pygame.Rect(2, 2, icon_size-4, icon_size-4),
                          math.pi/4, 7*math.pi/4, 2)
            # Draw arrowhead
            arrow_size = icon_size // 4
            arrow_point = (center[0] + int(radius * math.cos(7*math.pi/4)),
                          center[1] + int(radius * math.sin(7*math.pi/4)))
            pygame.draw.polygon(surface, color, [
                arrow_point,
                (arrow_point[0] + arrow_size, arrow_point[1]),
                (arrow_point[0], arrow_point[1] + arrow_size)
            ])

        # Use our helper function to render the emoji icon with fallback
        icon_surf = self.render_emoji_icon(reset_icon, icon_size, self.WHITE, reset_arrow_fallback)

        # Draw text
        text_font_size = int(18 * min(self.scale_x, self.scale_y))
        text_font = pygame.font.SysFont("Arial", text_font_size, bold=True)
        text_surf = text_font.render(button_text, True, self.WHITE)

        # Calculate positions for icon and text
        total_width = icon_surf.get_width() + text_surf.get_width() + int(10 * min(self.scale_x, self.scale_y))
        start_x = button_rect.centerx - total_width // 2

        # Draw icon and text with subtle shadow for depth
        shadow_offset = int(1.5 * min(self.scale_x, self.scale_y))

        # Draw icon shadow
        window_surf.blit(icon_surf, (start_x + shadow_offset, button_rect.centery - icon_surf.get_height()//2 + shadow_offset))
        # Draw icon
        window_surf.blit(icon_surf, (start_x, button_rect.centery - icon_surf.get_height()//2))

        # Draw text shadow
        text_x = start_x + icon_surf.get_width() + int(10 * min(self.scale_x, self.scale_y))
        window_surf.blit(text_surf, (text_x + shadow_offset, button_rect.centery - text_surf.get_height()//2 + shadow_offset))
        # Draw text
        window_surf.blit(text_surf, (text_x, button_rect.centery - text_surf.get_height()//2))

        # Store hit area for reset button - this is in window coordinates
        self.hit_areas["button_Reset to Defaults"] = pygame.Rect(button_x, button_y, button_width, button_height)

        # Finally, blit the window surface to the screen
        self.screen.blit(window_surf, (window_x, window_y))

    def _get_slider_range(self, setting_key):
        """Get the min and max values for a slider setting"""
        # Look up in settings definitions
        for _, settings in self.settings_definitions.items():
            for setting in settings:
                if setting["key"] == setting_key and setting["type"] == self.COMPONENT_SLIDER:
                    return (
                        setting["params"].get("min_value", 0),
                        setting["params"].get("max_value", 1)
                    )

        # Default range if not found
        return (0, 1)

    def handle_input(self, event):
        """Handle keyboard input for text fields using the simple text input component"""
        if not self.visible or not hasattr(self, 'active_input') or not self.active_input:
            return False

        # Use the simple text input component to handle the event
        return self.text_input.handle_event(event, self.active_input)

    def handle_event(self, event):
        """
        Handle pygame events

        Args:
            event: The pygame event

        Returns:
            bool: True if the event was handled or should be consumed, False otherwise
        """
        if not self.visible:
            return False

        # Handle keyboard input for text fields
        if hasattr(self, 'active_input') and self.active_input:
            if event.type == pygame.KEYDOWN:
                if self.handle_input(event):
                    return True

        # If the window rect isn't set, we can't handle events properly
        if not hasattr(self, 'window_rect'):
            return False

        # Get window position - absolute screen coordinates
        window_x = self.window_rect.x
        window_y = self.window_rect.y

        # Handle mouse wheel scrolling
        if event.type == pygame.MOUSEWHEEL:
            if self.window_rect.collidepoint(pygame.mouse.get_pos()):
                # Scroll up (positive y) or down (negative y)
                self.scroll_y = max(0, min(self.scroll_y - event.y * self.scroll_speed, self.max_scroll))
                return True

        # Check for mouse events within the window bounds
        if event.type in (pygame.MOUSEBUTTONDOWN, pygame.MOUSEBUTTONUP, pygame.MOUSEMOTION):
            # Only process if event is within window bounds
            if not self.window_rect.collidepoint(event.pos):
                return False

            # Convert screen coordinates to window-relative coordinates
            rel_x = event.pos[0] - window_x
            rel_y = event.pos[1] - window_y

            # Create a relative mouse position for hit testing
            rel_pos = (rel_x, rel_y)

            # Print debug info for mouse position
            print(f"Mouse event at screen pos: {event.pos}, window-relative pos: {rel_pos}")

            # Handle mousedown events
            if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                # Check for close button click
                if "close_button" in self.hit_areas and self.hit_areas["close_button"].collidepoint(rel_pos):
                    self.hide()
                    return True

                # Check for tab clicks
                print("Checking for tab clicks")
                for key, rect in self.hit_areas.items():
                    if key.startswith("tab_") and rect.collidepoint(rel_pos):
                        print(f"Changing tab from {self.active_tab} to {key[4:]}")
                        self.active_tab = key[4:]  # Remove "tab_" prefix
                        self.scroll_y = 0  # Reset scroll position
                        return True

                # Check for toggle switches
                for key, rect in self.hit_areas.items():
                    if key.startswith("toggle_") and rect.collidepoint(rel_pos):
                        setting_key = key[7:]  # Remove "toggle_" prefix
                        category = self._get_category_for_setting(setting_key)
                        current_value = self.settings_manager.get_setting(category, setting_key, False)
                        self.settings_manager.set_setting(category, setting_key, not current_value)

                        # Apply settings to parent game if it exists
                        if category == 'game' and self.parent:
                            self.apply_game_settings_to_parent()
                        return True

                # Check for slider interactions
                for key, rect in self.hit_areas.items():
                    if key.startswith("slider_") and rect.collidepoint(rel_pos):
                        # Handle slider click
                        setting_key = key[7:]  # Remove "slider_" prefix
                        category = self._get_category_for_setting(setting_key)
                        min_value, max_value = self._get_slider_range(setting_key)

                        # Calculate relative click position on slider
                        value_ratio = max(0, min(1, (rel_x - rect.x) / rect.width))
                        new_value = min_value + value_ratio * (max_value - min_value)

                        # Update setting
                        self.settings_manager.set_setting(category, setting_key, new_value)

                        # Apply settings to parent game if it exists
                        if category == 'game' and self.parent:
                            # Special handling for number_call_delay to ensure immediate application
                            if setting_key == 'number_call_delay':
                                print(f"\n==== SETTINGS WINDOW: NUMBER CALL DELAY CHANGED =====")
                                print(f"New value: {new_value}")
                                self.apply_game_settings_to_parent(specific_setting='number_call_delay')
                                print(f"==== SETTINGS WINDOW: NUMBER CALL DELAY APPLIED ====\n")
                            else:
                                self.apply_game_settings_to_parent()

                        # Start dragging the slider
                        self.dragging_slider = setting_key
                        self.dragging_slider_rect = rect
                        return True

                # Check for dropdown interactions
                for key, rect in self.hit_areas.items():
                    if key.startswith("dropdown_") and rect.collidepoint(rel_pos):
                        dropdown_key = key[9:]  # Remove "dropdown_" prefix

                        # Print debug info for dropdown clicks
                        print(f"Dropdown clicked: {dropdown_key}, Rect: {rect}, Rel pos: {rel_pos}")

                        # Toggle dropdown state
                        if hasattr(self, 'active_dropdown') and self.active_dropdown == dropdown_key:
                            delattr(self, 'active_dropdown')
                        else:
                            self.active_dropdown = dropdown_key
                        return True

                # Check for dropdown option selections
                for key, rect in self.hit_areas.items():
                    if "_option_" in key and rect.collidepoint(rel_pos):
                        parts = key.split("_option_")
                        dropdown_key = parts[0][9:]  # Remove "dropdown_" prefix
                        option_index = int(parts[1].split("_")[1])

                        # Get options for this dropdown
                        options = self.settings_manager.get_options_for_setting(dropdown_key)
                        if options and 0 <= option_index < len(options):
                            # Get the old value before updating
                            category = self._get_category_for_setting(dropdown_key)
                            old_value = self.settings_manager.get_setting(category, dropdown_key, None)
                            new_value = options[option_index]
                            
                            # Update setting
                            self.settings_manager.set_setting(category, dropdown_key, new_value)
                            
                            # Special handling for language changes
                            if dropdown_key == "current_language" and old_value != new_value:
                                print(f"=== LANGUAGE CHANGED FROM {old_value} TO {new_value} ===")
                                
                                # Save the setting immediately
                                self.settings_manager.save_settings()
                                print(f"Saved language setting: {new_value}")
                                
                                # Apply to parent game if it exists
                                if self.parent:
                                    # Update the language in the parent game
                                    self.parent.current_language = new_value
                                    
                                    # Reload audio effects if the method exists
                                    if hasattr(self.parent, 'reload_audio_effects'):
                                        print("Reloading audio effects for new language...")
                                        success = self.parent.reload_audio_effects()
                                        if success:
                                            print("Audio effects reloaded successfully")
                                        else:
                                            print("Error reloading audio effects")
                                    else:
                                        print("WARNING: reload_audio_effects method not found!")

                            # Close dropdown
                            if hasattr(self, 'active_dropdown'):
                                delattr(self, 'active_dropdown')
                            return True

                # Check for button clicks
                for key, rect in self.hit_areas.items():
                    if key.startswith("button_") and rect.collidepoint(rel_pos):
                        button_key = key[7:]  # Remove "button_" prefix
                        print(f"Button clicked: {button_key}, Rect: {rect}, Rel pos: {rel_pos}")
                        self._handle_action(button_key)
                        return True

                # Check for action button clicks (used for import/export buttons)
                for key, rect in self.hit_areas.items():
                    if key.startswith("action_") and rect.collidepoint(rel_pos):
                        action_key = key[7:]  # Remove "action_" prefix
                        print(f"Action button clicked: {action_key}")
                        self._handle_action(action_key)
                        return True

                # Check for color picker clicks
                for key, rect in self.hit_areas.items():
                    if key.startswith("color_picker_") and rect.collidepoint(rel_pos):
                        setting_key = key[13:]  # Remove "color_picker_" prefix
                        category = self._get_category_for_setting(setting_key)
                        current_value = self.settings_manager.get_setting(category, setting_key, "#FFFFFF")

                        # Simple color cycling for demonstration
                        colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#00FFFF", "#FF00FF", "#FFFFFF"]
                        next_index = (colors.index(current_value.upper()) + 1) % len(colors) if current_value.upper() in colors else 0

                        # Update setting
                        self.settings_manager.set_setting(category, setting_key, colors[next_index])
                        return True

                # Check for text input clicks
                for key, rect in self.hit_areas.items():
                    if key.startswith("text_input_") and rect.collidepoint(rel_pos):
                        self.active_input = key[11:]  # Remove "text_input_" prefix
                        print(f"Text input activated: {self.active_input}, Rect: {rect}, Click: {rel_pos}")
                        # Text input activated
                        return True

                # Check for copy button clicks
                for key, rect in self.hit_areas.items():
                    if key.startswith("copy_button_") and rect.collidepoint(rel_pos):
                        setting_key = key[12:]  # Remove "copy_button_" prefix
                        # Handle copy button click
                        return self.text_input.handle_copy_button(setting_key)

                # Check for paste button clicks
                for key, rect in self.hit_areas.items():
                    if key.startswith("paste_button_") and rect.collidepoint(rel_pos):
                        setting_key = key[13:]  # Remove "paste_button_" prefix
                        # Handle paste button click
                        return self.text_input.handle_paste_button(setting_key)

                # Check for input field clicks (alternative naming)
                for key, rect in self.hit_areas.items():
                    if key.startswith("input_") and rect.collidepoint(rel_pos):
                        self.active_input = key[6:]  # Remove "input_" prefix
                        # Input field activated
                        return True

                # Handle scroll track clicks
                if "scroll_track" in self.hit_areas and self.hit_areas["scroll_track"].collidepoint(rel_pos):
                    if "scroll_handle" in self.hit_areas and self.hit_areas["scroll_handle"].collidepoint(rel_pos):
                        # Start dragging the scroll handle
                        self.dragging_scroll = True
                        self.drag_start_y = rel_y
                        self.drag_start_scroll = self.scroll_y
                    else:
                        # Click on track - jump to that position
                        content_y = self.header_height + self.tab_bar_height
                        track_y = rel_y - content_y

                        # Calculate scroll position
                        scroll_ratio = track_y / self.visible_content_height
                        self.scroll_y = min(self.max_scroll, max(0, int(scroll_ratio * self.max_scroll)))
                    return True

                # If we get here, we've handled a click inside the window
                return True

            # Handle mouseup events
            elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
                # Stop any dragging actions
                if hasattr(self, 'dragging_scroll') and self.dragging_scroll:
                    self.dragging_scroll = False
                    return True

                if hasattr(self, 'dragging_slider'):
                    delattr(self, 'dragging_slider')
                    delattr(self, 'dragging_slider_rect')
                    return True

                return True

            # Handle mouse motion events
            elif event.type == pygame.MOUSEMOTION:
                # Handle scroll drag
                if hasattr(self, 'dragging_scroll') and self.dragging_scroll:
                    drag_distance = rel_y - self.drag_start_y

                    # Calculate scroll amount
                    track_height = self.visible_content_height
                    if track_height > 0:
                        scroll_change = drag_distance * (self.max_scroll / track_height)
                        self.scroll_y = max(0, min(self.drag_start_scroll + scroll_change, self.max_scroll))
                    return True

                # Handle slider drag
                if hasattr(self, 'dragging_slider') and hasattr(self, 'dragging_slider_rect'):
                    rect = self.dragging_slider_rect
                    setting_key = self.dragging_slider
                    category = self._get_category_for_setting(setting_key)
                    min_value, max_value = self._get_slider_range(setting_key)

                    # Calculate value based on mouse position
                    value_ratio = max(0, min(1, (rel_x - rect.x) / rect.width))
                    new_value = min_value + value_ratio * (max_value - min_value)

                    # Update setting
                    self.settings_manager.set_setting(category, setting_key, new_value)

                    # Apply settings to parent game if it exists
                    if category == 'game' and self.parent:
                        # Special handling for number_call_delay to ensure immediate application
                        if setting_key == 'number_call_delay':
                            print(f"\n==== SETTINGS WINDOW: NUMBER CALL DELAY CHANGED (DRAG) =====")
                            print(f"New value: {new_value}")
                            self.apply_game_settings_to_parent(specific_setting='number_call_delay')
                            print(f"==== SETTINGS WINDOW: NUMBER CALL DELAY APPLIED (DRAG) ====\n")
                        else:
                            self.apply_game_settings_to_parent()
                    return True

                return True

        return False

    def draw_settings_tab(self, content_rect, tab_name):
        """Generic method to draw any settings tab using the settings definitions"""
        # Calculate padding and spacing
        padding = int(20 * min(self.scale_x, self.scale_y))
        spacing = self.item_spacing

        # Calculate item dimensions
        item_width = content_rect.width - (padding * 2)
        item_height = self.item_height

        # Draw section title
        title_x = content_rect.x + padding
        title_y = content_rect.y + padding
        title_text = f"{tab_name.replace('_', ' ').title()} Settings"
        self.draw_text(title_text, self.header_font, self.text_light, title_x, title_y)

        # Draw settings items
        y_offset = title_y + self.header_font.get_height() + spacing

        # Get settings definitions for this tab
        if tab_name in self.settings_definitions:
            settings = self.settings_definitions[tab_name]

            # Draw each setting component
            for setting in settings:
                item_rect = Rect(content_rect.x + padding, y_offset, item_width, item_height)

                self.draw_setting_component(item_rect, setting["type"], setting["key"], setting["params"])
                y_offset += item_height + spacing
        else:
            # No settings defined for this tab
            self.draw_text("No settings available for this tab", self.text_font, self.text_medium,
                          title_x, y_offset)
            y_offset += self.text_font.get_height() + spacing

        # Return the actual content height
        return y_offset

    def draw_game_settings(self, content_rect):
        """Draw game settings tab content using the generic method"""
        return self.draw_settings_tab(content_rect, "game")
        
    def draw_pattern_bonuses(self, content_rect):
        """Draw pattern bonuses tab content using the generic method"""
        print("Drawing pattern bonuses tab")
        result = self.draw_settings_tab(content_rect, "pattern_bonuses")
        print(f"Pattern bonuses tab content height: {result}")
        return result

    def draw_display_settings(self, content_rect):
        """Draw display settings tab content using the generic method"""
        return self.draw_settings_tab(content_rect, "display")

    def draw_audio_settings(self, content_rect):
        """Draw audio settings tab content with explicit handling for cartella announcements"""
        # Calculate padding and spacing
        padding = int(20 * min(self.scale_x, self.scale_y))
        spacing = self.item_spacing

        # Calculate item dimensions
        item_width = content_rect.width - (padding * 2)
        item_height = self.item_height

        # Draw section title
        title_x = content_rect.x + padding
        title_y = content_rect.y + padding
        title_text = "Audio Settings"
        self.draw_text(title_text, self.header_font, self.text_light, title_x, title_y)

        # Draw settings items
        y_offset = title_y + self.header_font.get_height() + spacing

        # Manually draw each audio setting
        # 1. Sound Effects Volume
        item_rect = Rect(content_rect.x + padding, y_offset, item_width, item_height)
        self.draw_slider_setting(
            item_rect.x, item_rect.y, item_rect.width, item_rect.height,
            "Sound Effects Volume",
            "sound_effects_volume",
            0, 100, 80,
            "%",
            "Volume level for sound effects"
        )
        y_offset += item_height + spacing

        # 2. Music Volume
        item_rect = Rect(content_rect.x + padding, y_offset, item_width, item_height)
        self.draw_slider_setting(
            item_rect.x, item_rect.y, item_rect.width, item_rect.height,
            "Music Volume",
            "music_volume",
            0, 100, 70,
            "%",
            "Volume level for background music"
        )
        y_offset += item_height + spacing

        # 3. Cartella Announcements Toggle
        item_rect = Rect(content_rect.x + padding, y_offset, item_width, item_height)
        self.draw_toggle_setting(
            item_rect.x, item_rect.y, item_rect.width, item_rect.height,
            "Cartella Announcements",
            "cartella_announcements_enabled",
            True,  # default value
            "Enable audio announcements when registering cartella numbers"
        )
        y_offset += item_height + spacing

        # 4. Announcer Language Dropdown (directly added)
        item_rect = Rect(content_rect.x + padding, y_offset, item_width, item_height)

        try:
            print("Adding announcer language dropdown directly")

            # Get language options from AnnouncerLanguageManager
            languages = AnnouncerLanguageManager.get_available_languages()
            print(f"Language options from AnnouncerLanguageManager: {languages}")

            # Draw the dropdown directly using the _draw_dropdown_component method
            self._draw_dropdown_component(
                item_rect,
                "announcer_language",
                "Announcer Language",
                "Select the language for number announcements",
                languages,
                "Default"  # default value
            )

            y_offset += item_height + spacing
        except Exception as e:
            print(f"Error drawing announcer language dropdown: {e}")
            traceback.print_exc()  # Print full traceback for debugging

        # Return the actual content height
        return y_offset

    def draw_language_settings(self, content_rect):
        """Draw language settings tab content using the generic method"""
        return self.draw_settings_tab(content_rect, "language")

    def draw_import_export_settings(self, content_rect):
        """Draw import/export settings tab content using the generic method"""
        return self.draw_settings_tab(content_rect, "import_export")

    def draw_boards_settings(self, content_rect):
        """Draw boards settings tab content using the generic method"""
        return self.draw_settings_tab(content_rect, "boards")

    def apply_game_settings_to_parent(self, specific_setting=None):
        """Apply game settings to the parent game instance

        Args:
            specific_setting: Optional name of a specific setting to apply (e.g., 'number_call_delay')
        """
        if not self.parent:
            print("No parent game instance found, cannot apply settings")
            return

        try:
            print(f"\n==== APPLY_GAME_SETTINGS_TO_PARENT =====")

            # If a specific setting is provided, only update that one
            if specific_setting == 'number_call_delay':
                # Get the current value before updating
                old_value = getattr(self.parent, 'number_call_delay', 3.0)

                # Update number call delay and immediately apply it to the bingo caller
                new_value = self.settings_manager.get_setting('game', 'number_call_delay', 3.0)
                self.parent.number_call_delay = new_value

                print(f"NUMBER CALL DELAY UPDATE:")
                print(f"Previous value: {old_value}s")
                print(f"New value: {new_value}s")
                print(f"Game started: {getattr(self.parent, 'game_started', False)}")
                print(f"Has bingo_caller: {hasattr(self.parent, 'bingo_caller')}")

                # Immediately update the bingo caller's delay
                if hasattr(self.parent, 'update_bingo_caller_delay'):
                    print(f"Calling update_bingo_caller_delay()...")
                    self.parent.update_bingo_caller_delay()
                    print(f"update_bingo_caller_delay() completed")
                else:
                    print(f"WARNING: update_bingo_caller_delay method not found!")
                    # Try to save the setting directly
                    if hasattr(self.parent, 'save_game_setting'):
                        self.parent.save_game_setting('number_call_delay', new_value)
                        print(f"Saved number_call_delay={new_value}s using save_game_setting()")

                print(f"NUMBER CALL DELAY UPDATE COMPLETED")
                print(f"==== APPLY_GAME_SETTINGS_TO_PARENT: COMPLETED ====\n")
                return

            # Otherwise, load all settings
            print(f"APPLYING ALL GAME SETTINGS:")

            # Store previous values for logging
            old_number_call_delay = getattr(self.parent, 'number_call_delay', 3.0)
            
            # Check if language has changed
            old_language = getattr(self.parent, 'current_language', None)
            new_language = self.settings_manager.get_setting('language', 'current_language', 'English')
            language_changed = old_language != new_language
            
            if language_changed:
                print(f"=== APPLY_GAME_SETTINGS: LANGUAGE CHANGED FROM {old_language} TO {new_language} ===")
                
                # Check if language directories exist
                language_dir = f"assets/audio-effects/{new_language}"
                english_dir = "assets/audio-effects/English"
                
                print(f"Language directory exists: {os.path.exists(language_dir)}")
                print(f"English directory exists: {os.path.exists(english_dir)}")
                
                # Update the language in the parent game
                self.parent.current_language = new_language
                
                # Save the setting immediately
                self.settings_manager.save_settings()
                print(f"Saved language setting: {new_language}")
                
                # Force reload audio effects using the more aggressive method
                if hasattr(self.parent, 'force_reload_audio_effects'):
                    print("Force reloading audio effects for new language...")
                    success = self.parent.force_reload_audio_effects()
                    if success:
                        print("Audio effects force reloaded successfully")
                    else:
                        print("Error force reloading audio effects")
                        # Fallback to regular reload
                        if hasattr(self.parent, 'reload_audio_effects'):
                            print("Trying regular reload as fallback...")
                            self.parent.reload_audio_effects()
                elif hasattr(self.parent, 'reload_audio_effects'):
                    print("Reloading audio effects for new language...")
                    success = self.parent.reload_audio_effects()
                    if success:
                        print("Audio effects reloaded successfully")
                    else:
                        print("Error reloading audio effects")
                else:
                    print("WARNING: No audio reload methods found!")

            # Update all settings
            self.parent.number_call_delay = self.settings_manager.get_setting('game', 'number_call_delay', 3.0)
            self.parent.strict_claim_timing = self.settings_manager.get_setting('game', 'strict_claim_timing', True)
            self.parent.shuffle_duration = self.settings_manager.get_setting('game', 'shuffle_duration', 3.0)
            self.parent.commission_percentage = self.settings_manager.get_setting('game', 'commission_percentage', 20.0)
            self.parent.show_total_selected = self.settings_manager.get_setting('game', 'show_total_selected', True)

            # Log changes
            print(f"  - Number call delay: {old_number_call_delay}s -> {self.parent.number_call_delay}s")
            print(f"  - Strict claim timing: {self.parent.strict_claim_timing}")
            print(f"  - Shuffle duration: {self.parent.shuffle_duration}s")
            print(f"  - Commission percentage: {self.parent.commission_percentage}%")
            print(f"  - Show total selected: {self.parent.show_total_selected}")
            if language_changed:
                print(f"  - Language: {old_language} -> {new_language}")

            # Update bingo caller delay if it exists
            if hasattr(self.parent, 'update_bingo_caller_delay'):
                print(f"Calling update_bingo_caller_delay() to apply number_call_delay...")
                self.parent.update_bingo_caller_delay()
                print(f"update_bingo_caller_delay() completed")
            else:
                print(f"WARNING: update_bingo_caller_delay method not found!")
                # Try to save the setting directly
                if hasattr(self.parent, 'save_game_setting'):
                    self.parent.save_game_setting('number_call_delay', self.parent.number_call_delay)
                    print(f"Saved number_call_delay={self.parent.number_call_delay}s using save_game_setting()")

            # Recalculate prize pool with new commission percentage
            if hasattr(self.parent, 'calculate_prize_pool'):
                print(f"Recalculating prize pool with new commission percentage...")
                self.parent.calculate_prize_pool()
                print(f"Prize pool recalculated: {getattr(self.parent, 'prize_pool', 'N/A')}")
            else:
                print(f"WARNING: calculate_prize_pool method not found!")

            print(f"ALL GAME SETTINGS APPLIED SUCCESSFULLY")
            print(f"==== APPLY_GAME_SETTINGS_TO_PARENT: COMPLETED ====\n")
        except Exception as e:
            print(f"Error applying game settings to parent: {e}")
            import traceback
            traceback.print_exc()  # Print full traceback for debugging

    def _handle_action(self, action_key):
        """Handle action button clicks"""
        print(f"Handling action: {action_key}")

        # Import the board manager if needed
        if action_key in ["import_preset", "import_boards"]:
            from board_manager import BoardManager
            board_manager = BoardManager(self.settings_manager)

        # Handle board preset actions
        if action_key == "import_preset" or action_key == "import_boards":
            # Import a board file (automatically adds as preset and applies it)
            print("Starting board import process...")
            success, _, preset_name = board_manager.import_boards_from_file()
            if success:
                print(f"Imported and applied board file: {preset_name}")
                # The board manager now automatically adds the preset and sets it as current

                # Clear the options cache to refresh the dropdown
                if hasattr(self.settings_manager, '_options_cache'):
                    if 'current_preset' in self.settings_manager._options_cache:
                        del self.settings_manager._options_cache['current_preset']
                        print("Cleared preset options cache to refresh dropdown")
            else:
                print("Failed to import board file")
        elif action_key == "import_language":
            # Open file dialog to select language JSON file
            # This would typically use a file dialog, but for simplicity we'll use a predefined path
            # In a real implementation, you would use a file dialog or OS-specific method

            # For demonstration, we'll assume the file is in the data directory
            import tkinter as tk
            from tkinter import filedialog

            # Create a hidden root window for the file dialog
            root = tk.Tk()
            root.withdraw()  # Hide the root window

            # Show file dialog
            file_path = filedialog.askopenfilename(
                title="Select Language File",
                filetypes=[("JSON files", "*.json")],
                initialdir=os.path.join(os.getcwd(), "data")
            )

            # If a file was selected, import it
            if file_path:
                success = self.settings_manager.import_language_from_json(file_path)
                if success:
                    print(f"Successfully imported language from {file_path}")
                else:
                    print(f"Failed to import language from {file_path}")

    def draw_text_input_modern(self, x, y, width, height, label, setting_key, default_value="", description=""):
        """Draw a text input setting with modern styling"""
        # Get category for this setting
        category = self._get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # This is the modern text input style

        # Draw setting background
        bg_rect = Rect(x, y, width, height)
        border_radius = int(15 * min(self.scale_x, self.scale_y))

        # Draw background with gradient
        self.draw_gradient_rect(
            bg_rect,
            (30, 40, 60),  # Use a fixed color instead of relying on control_bg
            (40, 50, 70),
            border_radius,
            glossy=True
        )

        # Draw label
        label_font = self.header_font
        label_text = label_font.render(label, True, self.text_light)
        self.screen.blit(label_text, (x + int(15 * self.scale_x), y + int(15 * self.scale_y)))

        # Draw description if provided
        if description:
            desc_font = self.small_font
            desc_text = desc_font.render(description, True, (220, 220, 220))
            desc_y = y + label_text.get_height() + int(8 * self.scale_y)
            self.screen.blit(desc_text, (x + int(20 * self.scale_x), desc_y))

        # Draw text input field
        input_height = int(40 * min(self.scale_x, self.scale_y))
        input_y = y + height - input_height - int(15 * min(self.scale_x, self.scale_y))
        input_width = int(width * 0.9)
        input_x = x + (width - input_width) // 2
        input_rect = Rect(input_x, input_y, input_width, input_height)

        # Draw input background
        pygame.draw.rect(
            self.screen,
            (30, 30, 40),
            input_rect,
            border_radius=int(8 * min(self.scale_x, self.scale_y))
        )

        # Draw input border
        pygame.draw.rect(
            self.screen,
            (100, 100, 120),
            input_rect,
            width=1,
            border_radius=int(8 * min(self.scale_x, self.scale_y))
        )

        # Draw current value text
        value_font = self.text_font
        value_text = value_font.render(str(current_value), True, self.text_light)
        text_x = input_x + int(10 * self.scale_x)
        text_y = input_y + (input_height - value_font.get_height()) // 2
        self.screen.blit(value_text, (text_x, text_y))

        # Store hit area for text input - adjust for content scroll position
        # Note: hit areas are relative to the window, not the screen
        adjusted_rect = input_rect.copy()
        # No need to adjust for window position as hit areas are already relative to window
        if hasattr(self, 'scroll_y'):
            # Adjust for scroll position only
            adjusted_rect.y -= self.scroll_y
        self.hit_areas[f"text_input_{setting_key}"] = adjusted_rect
        print(f"Registered hit area for text input: {setting_key}, Rect: {adjusted_rect}")

    def draw_slider_setting(self, x, y, width, height, label, setting_key, min_value, max_value, default_value, unit="", description=""):
        """Draw a slider setting with enhanced modern styling"""
        # Get category for this setting
        category = self._get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Draw setting background with enhanced modern styling
        bg_rect = Rect(x, y, width, height)
        border_radius = int(15 * min(self.scale_x, self.scale_y))  # Larger radius for modern look

        # Use a subtle gradient for the background
        self.draw_gradient_rect(
            bg_rect,
            self._lighten_color(self.control_bg, 8),  # Slightly lighter
            self.control_bg,  # Base color
            border_radius,
            glossy=True
        )

        # Add subtle pattern to background
        pattern_surface = pygame.Surface((width, height), pygame.SRCALPHA)
        pattern_spacing = int(20 * min(self.scale_x, self.scale_y))
        pattern_alpha = 5  # Very subtle

        for x_pos in range(0, width, pattern_spacing):
            for y_pos in range(0, height, pattern_spacing):
                # Add slight randomness to dot positions for organic feel
                offset_x = random.randint(-2, 2)
                offset_y = random.randint(-2, 2)
                dot_size = int(1.5 * min(self.scale_x, self.scale_y))
                pygame.draw.circle(pattern_surface, (255, 255, 255, pattern_alpha),
                                (x_pos + offset_x, y_pos + offset_y), dot_size)

        self.screen.blit(pattern_surface, (x, y))

        # Draw label with enhanced styling
        label_font = self.header_font

        # Create a subtle gradient effect for the label text

        # Render the base text
        label_text = label_font.render(label, True, self.text_light)

        # Add multiple shadow layers for depth
        for i in range(2):
            shadow_offset = int((i+1) * min(self.scale_x, self.scale_y))
            shadow_alpha = 100 - i*40  # Fade out for deeper shadows
            label_shadow = label_font.render(label, True, (0, 0, 0, shadow_alpha))
            self.screen.blit(label_shadow, (x + int(15 * self.scale_x) + shadow_offset,
                                          y + int(15 * self.scale_y) + shadow_offset))

        # Draw main text with slight accent color glow
        glow_surface = pygame.Surface(label_text.get_size(), pygame.SRCALPHA)
        glow_color = (*self.primary, 40)  # Very subtle glow using primary color
        glow_surface.fill(glow_color)

        # Apply glow and draw text
        self.screen.blit(glow_surface, (x + int(15 * self.scale_x) - 2, y + int(15 * self.scale_y) - 2))
        self.screen.blit(label_text, (x + int(15 * self.scale_x), y + int(15 * self.scale_y)))

        # Draw description if provided - with enhanced styling
        if description:
            desc_font = self.small_font
            desc_text = desc_font.render(description, True, (220, 220, 220))  # Brighter text for better readability
            desc_y = y + label_text.get_height() + int(8 * self.scale_y)  # More spacing

            # Add subtle shadow for depth
            desc_shadow = desc_font.render(description, True, (0, 0, 0, 80))
            self.screen.blit(desc_shadow, (x + int(20 * self.scale_x) + 1, desc_y + 1))
            self.screen.blit(desc_text, (x + int(20 * self.scale_x), desc_y))

        # Calculate slider dimensions with modern proportions
        slider_width = int(width * 0.65)  # Wider slider for better interaction
        slider_height = int(12 * min(self.scale_x, self.scale_y))  # Thicker for better visibility and touch target
        slider_x = x + int(width * 0.2)  # Position more to the left
        slider_y = y + height - slider_height - int(25 * min(self.scale_x, self.scale_y))  # More space at bottom

        # Calculate filled portion of slider
        value_ratio = (current_value - min_value) / (max_value - min_value)
        filled_width = int(value_ratio * slider_width)

        # Create layered slider track with enhanced styling
        slider_rect = Rect(slider_x, slider_y, slider_width, slider_height)
        track_radius = int(slider_height / 2)  # Fully rounded track

        # 1. Draw track shadow for depth perception
        shadow_offset = int(3 * min(self.scale_x, self.scale_y))
        shadow_rect = Rect(slider_x, slider_y + shadow_offset/2, slider_width, slider_height)
        shadow_surface = pygame.Surface((shadow_rect.width, shadow_rect.height), pygame.SRCALPHA)

        # Create soft shadow with gradient
        for i in range(3):
            shadow_alpha = 40 - i*10  # Fade out for softer edge
            shadow_expand = i
            pygame.draw.rect(shadow_surface, (0, 0, 0, shadow_alpha),
                           (0-shadow_expand, 0-shadow_expand,
                            shadow_rect.width+shadow_expand*2, shadow_rect.height+shadow_expand*2),
                           border_radius=track_radius+shadow_expand)

        self.screen.blit(shadow_surface, shadow_rect)

        # 2. Draw unfilled track (background) with enhanced gradient
        unfilled_rect = Rect(slider_x, slider_y, slider_width, slider_height)

        # Use more sophisticated colors for unfilled track
        track_bg_color1 = (40, 50, 70)  # Slightly richer color
        track_bg_color2 = (30, 40, 60)  # Slightly darker for gradient

        self.draw_gradient_rect(
            unfilled_rect,
            track_bg_color1,
            track_bg_color2,
            track_radius,
            horizontal=True
        )

        # Add subtle pattern to track for texture
        pattern_surface = pygame.Surface((slider_width, slider_height), pygame.SRCALPHA)
        pattern_spacing = int(slider_height * 0.8)
        pattern_alpha = 5  # Very subtle

        for x_pos in range(0, slider_width, pattern_spacing):
            # Draw subtle vertical lines
            pygame.draw.line(pattern_surface, (255, 255, 255, pattern_alpha),
                           (x_pos, 0), (x_pos, slider_height))

        self.screen.blit(pattern_surface, (slider_x, slider_y))

        # 3. Draw filled track (progress) with enhanced gradient and effects
        if filled_width > 0:
            filled_rect = Rect(slider_x, slider_y, filled_width, slider_height)

            # Create animated glow effect behind filled portion
            glow_size = int(slider_height * 2.2)
            glow_surface = pygame.Surface((filled_width, glow_size), pygame.SRCALPHA)

            # Enhanced pulse animation for glow
            pulse = (math.sin(self.animation_time * 3) + 1) / 2  # 0 to 1 pulsing
            glow_alpha = int(40 + 30 * pulse)  # Pulsing alpha with wider range

            # Get active tab color for the glow (to match the active tab)
            active_tab_index = next((i for i, tab in enumerate(["game", "display", "audio", "language", "import_export"])
                                   if tab == self.active_tab), 0)

            # Define colors for each tab
            tab_colors = [
                self.primary,           # Game tab - primary color
                (0, 180, 180),          # Display tab - teal
                (180, 100, 200),        # Audio tab - purple
                (240, 140, 0),          # Language tab - orange
                (0, 180, 80)            # Import/Export tab - green
            ]

            # Use the color of the active tab
            glow_base_color = tab_colors[active_tab_index]

            # Create radial gradient glow
            for y in range(glow_size):
                # Calculate vertical alpha gradient
                y_ratio = abs((y - glow_size/2) / (glow_size/2))  # 0 at center, 1 at edges
                y_alpha = 1 - y_ratio**2  # Quadratic falloff for smoother edge

                for x in range(filled_width):
                    # Calculate horizontal alpha gradient
                    x_ratio = x / filled_width
                    x_alpha = x_ratio**0.7  # Non-linear for more interesting gradient

                    # Combine alpha components and ensure it's positive
                    combined_alpha = max(0, glow_alpha * y_alpha * x_alpha)

                    # Add slight color variation based on position
                    color_shift = int(20 * (1 - x_ratio))  # More color shift at the beginning
                    r = min(255, glow_base_color[0] + color_shift)
                    g = min(255, glow_base_color[1] + color_shift)
                    b = min(255, glow_base_color[2] + color_shift)

                    # Draw pixel with calculated color and alpha
                    if 0 <= x < filled_width and 0 <= y < glow_size and combined_alpha > 0:
                        glow_surface.set_at((x, y), (r, g, b, int(combined_alpha)))

            # Position and draw glow
            glow_y = slider_y - (glow_size - slider_height) // 2
            self.screen.blit(glow_surface, (slider_x, glow_y))

            # Draw filled portion with vibrant gradient matching active tab
            self.draw_gradient_rect(
                filled_rect,
                (min(255, glow_base_color[0]+30), min(255, glow_base_color[1]+30), min(255, glow_base_color[2]+30)),  # Brighter
                glow_base_color,  # Base color
                track_radius,
                horizontal=True,
                glossy=True  # Add glossy effect
            )

            # Add subtle animated highlight to filled portion
            highlight_height = int(slider_height * 0.4)
            highlight_surface = pygame.Surface((filled_width, highlight_height), pygame.SRCALPHA)

            # Create animated highlight
            highlight_alpha = int(70 + 30 * pulse)  # Pulsing alpha
            for y in range(highlight_height):
                line_alpha = highlight_alpha * (1 - y/highlight_height)  # Fade from top
                pygame.draw.line(highlight_surface, (255, 255, 255, int(line_alpha)),
                               (0, y), (filled_width, y))

            # Position at top of filled portion
            self.screen.blit(highlight_surface, (slider_x, slider_y))

        # 4. Draw track border with enhanced styling
        border_color = (80, 100, 130, 100)  # More vibrant border with transparency
        pygame.draw.rect(self.screen, border_color, slider_rect, width=1,
                       border_radius=track_radius)

        # Calculate handle position with modern styling
        handle_x = slider_x + filled_width
        handle_size = int(26 * min(self.scale_x, self.scale_y))  # Larger handle for better interaction and visibility
        handle_rect = Rect(
            handle_x - handle_size // 2,
            slider_y + (slider_height - handle_size) // 2,
            handle_size,
            handle_size
        )

        # Check if handle is being hovered or dragged
        is_hovered = handle_rect.collidepoint(mouse_pos)
        is_active = hasattr(self, 'active_slider') and self.active_slider == setting_key

        # Get active tab color for the handle (to match the active tab and filled portion)
        active_tab_index = next((i for i, tab in enumerate(["game", "display", "audio", "language", "import_export"])
                               if tab == self.active_tab), 0)

        # Define colors for each tab
        tab_colors = [
            self.primary,           # Game tab - primary color
            (0, 180, 180),          # Display tab - teal
            (180, 100, 200),        # Audio tab - purple
            (240, 140, 0),          # Language tab - orange
            (0, 180, 80)            # Import/Export tab - green
        ]

        # Use the color of the active tab as base color
        base_color = tab_colors[active_tab_index]

        # Determine handle colors based on state with enhanced styling
        if is_active:
            # Active state (being dragged) with enhanced effects
            outer_color = (min(255, base_color[0]+50), min(255, base_color[1]+50), min(255, base_color[2]+50))
            inner_color = self.text_light

            # Add scale animation when active
            scale_factor = 1.15  # Larger when active
            scaled_size = int(handle_size * scale_factor)

            # Adjust position to keep centered while scaling
            scaled_rect = Rect(
                handle_rect.centerx - scaled_size // 2,
                handle_rect.centery - scaled_size // 2,
                scaled_size,
                scaled_size
            )

            # Use the scaled rectangle for drawing
            draw_rect = scaled_rect

            # Enhanced glow effect with pulsing
            glow_size = int(scaled_size * 2.2)  # Larger glow
            pulse = (math.sin(self.animation_time * 6) + 1) / 2  # Faster pulsing when active
            glow_alpha = int(120 + 60 * pulse)  # Pulsing alpha with wider range

            # Create sophisticated radial gradient for glow
            glow_surf = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)

            # Draw multiple layers for smoother gradient
            for i in range(glow_size//2):
                # Calculate alpha with smooth falloff
                alpha_ratio = i / (glow_size//2)
                alpha = glow_alpha * (1 - alpha_ratio**2)  # Quadratic falloff for smoother edge

                # Add slight color variation
                color_shift = int(30 * alpha_ratio)  # More shift at the edges
                r = min(255, outer_color[0] - color_shift)
                g = min(255, outer_color[1] - color_shift)
                b = min(255, outer_color[2] - color_shift)

                pygame.draw.circle(glow_surf, (r, g, b, int(alpha)),
                                 (glow_size//2, glow_size//2), glow_size//2 - i)

            # Position and draw glow
            glow_pos = (draw_rect.centerx - glow_size//2, draw_rect.centery - glow_size//2)
            self.screen.blit(glow_surf, glow_pos)

        elif is_hovered:
            # Hover state with subtle animation and effects
            outer_color = (min(255, base_color[0]+30), min(255, base_color[1]+30), min(255, base_color[2]+30))
            inner_color = self.text_light

            # Add subtle scale animation when hovered
            pulse = (math.sin(self.animation_time * 4) + 1) / 2  # Gentle pulsing
            scale_factor = 1.08 + 0.04 * pulse  # Subtle pulsing scale
            scaled_size = int(handle_size * scale_factor)

            # Adjust position to keep centered while scaling
            scaled_rect = Rect(
                handle_rect.centerx - scaled_size // 2,
                handle_rect.centery - scaled_size // 2,
                scaled_size,
                scaled_size
            )

            # Use the scaled rectangle for drawing
            draw_rect = scaled_rect

            # Subtle glow effect with animation
            glow_size = int(scaled_size * 1.8)
            glow_alpha = int(70 + 40 * pulse)  # Pulsing alpha

            # Create glow with smoother gradient
            glow_surf = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)

            # Draw multiple layers for smoother gradient
            for i in range(glow_size//2):
                # Calculate alpha with smooth falloff
                alpha = glow_alpha * (1 - (i / (glow_size//2))**2)  # Quadratic falloff
                pygame.draw.circle(glow_surf, (*outer_color, int(alpha)),
                                 (glow_size//2, glow_size//2), glow_size//2 - i)

            # Position and draw glow
            glow_pos = (draw_rect.centerx - glow_size//2, draw_rect.centery - glow_size//2)
            self.screen.blit(glow_surf, glow_pos)

        else:
            # Normal state with refined styling
            outer_color = base_color
            inner_color = (250, 250, 250)  # Brighter inner color
            draw_rect = handle_rect

        # Draw handle with enhanced 3D effect and modern styling
        # 1. Draw layered shadow beneath handle for depth perception
        shadow_layers = 3
        max_shadow_offset = int(4 * min(self.scale_x, self.scale_y))

        for i in range(shadow_layers):
            # Calculate progressive shadow properties
            progress = i / (shadow_layers - 1)  # 0.0 to 1.0
            shadow_offset = int(max_shadow_offset * progress)
            shadow_alpha = int(60 * (1 - progress))  # Fade out for outer shadows
            shadow_size = draw_rect.width // 2 - int(progress * 2)

            # Draw shadow layer
            pygame.draw.circle(self.screen, (0, 0, 0, shadow_alpha),
                             (draw_rect.centerx + shadow_offset//2,
                              draw_rect.centery + shadow_offset//2),
                             shadow_size)

        # 2. Draw main handle body with gradient effect
        # Create a surface for the handle with gradient
        handle_surface = pygame.Surface((draw_rect.width, draw_rect.height), pygame.SRCALPHA)

        # Draw gradient circles for 3D effect
        gradient_steps = 5  # More steps for smoother gradient
        for i in range(gradient_steps):
            progress = i / (gradient_steps - 1)  # 0.0 to 1.0

            # Calculate color with gradient
            if is_active or is_hovered:
                # More vibrant gradient for active/hover states
                r = int(outer_color[0] * (1 - progress * 0.3))
                g = int(outer_color[1] * (1 - progress * 0.3))
                b = int(outer_color[2] * (1 - progress * 0.3))
            else:
                # More subtle gradient for normal state
                r = int(outer_color[0] * (1 - progress * 0.2))
                g = int(outer_color[1] * (1 - progress * 0.2))
                b = int(outer_color[2] * (1 - progress * 0.2))

            # Ensure valid color values
            r, g, b = max(0, min(255, r)), max(0, min(255, g)), max(0, min(255, b))

            # Calculate size reduction for each step
            size_reduction = int(progress * 4)

            # Draw circle with current color
            pygame.draw.circle(handle_surface, (r, g, b),
                             (draw_rect.width//2, draw_rect.height//2),
                             draw_rect.width//2 - size_reduction)

        # Apply handle surface
        self.screen.blit(handle_surface, draw_rect)

        # 3. Draw inner circle with enhanced styling
        inner_size = draw_rect.width // 2 - 6  # Slightly smaller for better proportion

        # Create inner circle surface for effects
        inner_surface = pygame.Surface((inner_size*2, inner_size*2), pygame.SRCALPHA)

        # Fill with base color
        pygame.draw.circle(inner_surface, inner_color, (inner_size, inner_size), inner_size)

        # Add subtle inner shadow for depth
        for i in range(inner_size):
            # Only draw shadow at the edges
            if i > inner_size * 0.7:
                alpha = 40 * ((i - inner_size * 0.7) / (inner_size * 0.3))  # Fade from edge
                pygame.draw.circle(inner_surface, (0, 0, 0, int(alpha)),
                                 (inner_size, inner_size), i)

        # Position and apply inner circle
        inner_pos = (draw_rect.centerx - inner_size, draw_rect.centery - inner_size)
        self.screen.blit(inner_surface, inner_pos)

        # 4. Add highlight reflection for 3D effect
        # Create highlight surface
        highlight_size = draw_rect.width // 2
        highlight_surface = pygame.Surface((highlight_size, highlight_size), pygame.SRCALPHA)

        # Draw gradient highlight
        for i in range(highlight_size):
            # Calculate alpha with smooth falloff
            alpha = 160 * (1 - (i / highlight_size)**1.5)  # Non-linear falloff for better effect

            # Draw smaller circle for each step
            pygame.draw.circle(highlight_surface, (255, 255, 255, int(alpha)),
                             (highlight_size//2, highlight_size//2), highlight_size//2 - i)

        # Position highlight in top-left quadrant for 3D lighting effect
        highlight_pos = (draw_rect.centerx - draw_rect.width//3, draw_rect.centery - draw_rect.height//3)
        self.screen.blit(highlight_surface, highlight_pos)

        # Format value text based on type with enhanced formatting
        if isinstance(current_value, float) and setting_key not in ["sound_effects_volume", "music_volume", "voice_volume"]:
            # Format with one decimal place for most floats
            value_str = f"{current_value:.1f}"
        elif setting_key in ["sound_effects_volume", "music_volume", "voice_volume"]:
            # Format as percentage for volume values
            value_str = f"{int(current_value * 100)}%"
        else:
            # Integer format for other values
            value_str = f"{current_value}"

        # Draw value text with enhanced styling
        value_font = self.text_font
        value_text = value_font.render(f"{value_str} {unit}", True, (255, 215, 0))  # Use gold for better visibility

        # Position value text with better alignment
        value_x = slider_x + slider_width + int(20 * self.scale_x)
        value_y = slider_y - int(8 * self.scale_y)

        # Create value text container with subtle background
        value_bg_width = value_text.get_width() + int(16 * min(self.scale_x, self.scale_y))
        value_bg_height = value_text.get_height() + int(8 * min(self.scale_x, self.scale_y))
        value_bg_rect = Rect(
            value_x - int(8 * min(self.scale_x, self.scale_y)),
            value_y - int(4 * min(self.scale_x, self.scale_y)),
            value_bg_width,
            value_bg_height
        )

        # Draw subtle background for value
        value_bg_radius = int(value_bg_height / 2)  # Fully rounded corners
        self.draw_gradient_rect(
            value_bg_rect,
            (40, 50, 60, 120),  # Semi-transparent background
            (30, 40, 50, 120),
            value_bg_radius
        )

        # Add subtle shadow to value text
        value_shadow = value_font.render(f"{value_str} {unit}", True, (0, 0, 0, 150))
        self.screen.blit(value_shadow, (value_x + 1, value_y + 1))
        self.screen.blit(value_text, (value_x, value_y))

        # Store hit area for slider with improved interaction area
        # Make the hit area slightly larger than the visual slider for easier interaction
        extended_slider_rect = Rect(
            slider_x - int(5 * min(self.scale_x, self.scale_y)),
            slider_y - int(5 * min(self.scale_x, self.scale_y)),
            slider_width + int(10 * min(self.scale_x, self.scale_y)),
            slider_height + int(10 * min(self.scale_x, self.scale_y))
        )
        self.hit_areas[f"slider_{setting_key}"] = extended_slider_rect
        self.hit_areas[f"slider_handle_{setting_key}"] = handle_rect

    def draw_toggle_setting(self, x, y, width, height, label, setting_key, default_value, description=""):
        """Draw a toggle switch setting with enhanced modern styling"""
        # Get category for this setting
        category = self._get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Draw setting background with enhanced modern styling
        bg_rect = Rect(x, y, width, height)
        border_radius = int(15 * min(self.scale_x, self.scale_y))  # Larger radius for modern look

        # Use a subtle gradient for the background
        self.draw_gradient_rect(
            bg_rect,
            (40, 50, 70),  # Slightly lighter
            (30, 40, 60),  # Base color
            border_radius,
            glossy=True
        )

        # Add subtle pattern to background (same as slider for consistency)
        pattern_surface = pygame.Surface((width, height), pygame.SRCALPHA)
        pattern_spacing = int(20 * min(self.scale_x, self.scale_y))
        pattern_alpha = 5  # Very subtle

        for x_pos in range(0, width, pattern_spacing):
            for y_pos in range(0, height, pattern_spacing):
                # Add slight randomness to dot positions for organic feel
                offset_x = random.randint(-2, 2)
                offset_y = random.randint(-2, 2)
                dot_size = int(1.5 * min(self.scale_x, self.scale_y))
                pygame.draw.circle(pattern_surface, (255, 255, 255, pattern_alpha),
                                (x_pos + offset_x, y_pos + offset_y), dot_size)

        self.screen.blit(pattern_surface, (x, y))

        # Draw label with enhanced styling (same as slider for consistency)
        label_font = self.header_font

        # Render the base text
        label_text = label_font.render(label, True, self.WHITE)

        # Add multiple shadow layers for depth
        for i in range(2):
            shadow_offset = int((i+1) * min(self.scale_x, self.scale_y))
            shadow_alpha = 100 - i*40  # Fade out for deeper shadows
            label_shadow = label_font.render(label, True, (0, 0, 0, shadow_alpha))
            self.screen.blit(label_shadow, (x + int(15 * self.scale_x) + shadow_offset,
                                          y + int(15 * self.scale_y) + shadow_offset))

        # Draw main text with slight accent color glow
        glow_surface = pygame.Surface(label_text.get_size(), pygame.SRCALPHA)
        glow_color = (self.ACCENT_COLOR[0], self.ACCENT_COLOR[1], self.ACCENT_COLOR[2], 40)  # Very subtle glow
        glow_surface.fill(glow_color)

        # Apply glow and draw text
        self.screen.blit(glow_surface, (x + int(15 * self.scale_x) - 2, y + int(15 * self.scale_y) - 2))
        self.screen.blit(label_text, (x + int(15 * self.scale_x), y + int(15 * self.scale_y)))

        # Draw description if provided - with enhanced styling
        if description:
            desc_font = self.small_font
            desc_text = desc_font.render(description, True, (220, 220, 220))  # Brighter text for better readability
            desc_y = y + label_text.get_height() + int(8 * self.scale_y)  # More spacing

            # Add subtle shadow for depth
            desc_shadow = desc_font.render(description, True, (0, 0, 0, 80))
            self.screen.blit(desc_shadow, (x + int(20 * self.scale_x) + 1, desc_y + 1))
            self.screen.blit(desc_text, (x + int(20 * self.scale_x), desc_y))

        # Calculate toggle dimensions with modern proportions
        toggle_width = int(70 * min(self.scale_x, self.scale_y))  # Wider for better visibility
        toggle_height = int(34 * min(self.scale_x, self.scale_y))  # Taller for better touch target
        toggle_x = x + width - toggle_width - int(25 * self.scale_x)  # More space from edge
        toggle_y = y + (height - toggle_height) // 2

        # Check if toggle is being hovered
        toggle_rect = Rect(toggle_x, toggle_y, toggle_width, toggle_height)
        is_hovered = toggle_rect.collidepoint(mouse_pos)

        # Get active tab color for the toggle (to match the active tab)
        active_tab_index = next((i for i, tab in enumerate(["game", "display", "audio", "language", "import_export"])
                               if tab == self.active_tab), 0)

        # Define colors for each tab
        tab_colors = [
            self.ACCENT_COLOR,      # Game tab
            self.ACCENT_TEAL,       # Display tab
            self.ACCENT_PURPLE,     # Audio tab
            self.ACCENT_ORANGE,     # Language tab
            self.SUCCESS_GREEN      # Import/Export tab
        ]

        # Use the color of the active tab for the toggle when on
        active_color = tab_colors[active_tab_index]

        # Create layered toggle with enhanced modern styling
        toggle_radius = toggle_height // 2  # Fully rounded corners

        # 1. Draw layered shadow beneath toggle for depth perception
        shadow_layers = 3
        max_shadow_offset = int(4 * min(self.scale_x, self.scale_y))

        for i in range(shadow_layers):
            # Calculate progressive shadow properties
            progress = i / (shadow_layers - 1)  # 0.0 to 1.0
            shadow_offset = int(max_shadow_offset * progress)
            shadow_alpha = int(50 * (1 - progress))  # Fade out for outer shadows

            # Draw shadow layer with smooth edges
            shadow_rect = Rect(
                toggle_x + shadow_offset//2,
                toggle_y + shadow_offset,
                toggle_width,
                toggle_height
            )
            shadow_surface = pygame.Surface((shadow_rect.width, shadow_rect.height), pygame.SRCALPHA)

            # Draw rounded rectangle shadow
            pygame.draw.rect(shadow_surface, (0, 0, 0, shadow_alpha),
                           (0, 0, shadow_rect.width, shadow_rect.height),
                           border_radius=toggle_radius)

            self.screen.blit(shadow_surface, shadow_rect)

        # 2. Determine toggle colors based on state with enhanced color scheme
        if current_value:
            # On state - use active tab color with enhanced gradient
            if is_hovered:
                # Brighter when hovered with more vibrant colors
                start_color = (min(255, active_color[0]+40), min(255, active_color[1]+40), min(255, active_color[2]+40))
                end_color = (min(255, active_color[0]+20), min(255, active_color[1]+20), min(255, active_color[2]+20))
                glow_color = start_color
            else:
                # Normal on state with refined colors
                start_color = (min(255, active_color[0]+20), min(255, active_color[1]+20), min(255, active_color[2]+20))
                end_color = active_color
                glow_color = start_color

            # Create animated glow effect around toggle when on
            glow_size = int(toggle_height * 1.8)
            glow_surface = pygame.Surface((toggle_width + glow_size, glow_size), pygame.SRCALPHA)

            # Enhanced pulse animation for glow
            pulse = (math.sin(self.animation_time * 3) + 1) / 2  # 0 to 1 pulsing
            glow_alpha = int(40 + 30 * pulse)  # Pulsing alpha with wider range

            # Create sophisticated radial gradient for glow
            for i in range(glow_size//2):
                # Calculate alpha with smooth falloff
                alpha_ratio = i / (glow_size//2)
                alpha = glow_alpha * (1 - alpha_ratio**2)  # Quadratic falloff for smoother edge

                # Add slight color variation
                color_shift = int(20 * alpha_ratio)  # More shift at the edges
                r = min(255, glow_color[0] - color_shift)
                g = min(255, glow_color[1] - color_shift)
                b = min(255, glow_color[2] - color_shift)

                # Draw with rounded rectangle for better matching with toggle shape
                pygame.draw.rect(glow_surface, (r, g, b, int(alpha)),
                               (i, i, toggle_width + glow_size - i*2, glow_size - i*2),
                               border_radius=glow_size//2)

            # Position and draw glow
            glow_x = toggle_x - glow_size//2
            glow_y = toggle_y - (glow_size - toggle_height)//2
            self.screen.blit(glow_surface, (glow_x, glow_y))

            # Draw toggle background with enhanced gradient and glossy effect
            self.draw_gradient_rect(toggle_rect, start_color, end_color,
                                  toggle_radius, horizontal=True, glossy=True)

            # Add subtle animated highlight to toggle for 3D effect
            highlight_height = int(toggle_height * 0.4)
            highlight_surface = pygame.Surface((toggle_width - 4, highlight_height), pygame.SRCALPHA)

            # Create animated highlight
            highlight_alpha = int(70 + 30 * pulse)  # Pulsing alpha
            for y in range(highlight_height):
                line_alpha = highlight_alpha * (1 - y/highlight_height)  # Fade from top
                pygame.draw.line(highlight_surface, (255, 255, 255, int(line_alpha)),
                               (0, y), (toggle_width - 4, y))

            # Position at top of toggle
            highlight_pos = (toggle_x + 2, toggle_y + 2)
            self.screen.blit(highlight_surface, highlight_pos)
        else:
            # Off state - refined gray gradient with modern styling
            if is_hovered:
                # Brighter when hovered
                start_color = (100, 110, 130)  # More colorful gray
                end_color = (80, 90, 110)      # Slightly darker
            else:
                # More refined off state
                start_color = (80, 90, 110)     # Slightly bluish gray
                end_color = (60, 70, 90)       # Darker end

            # Draw toggle background with enhanced gradient
            self.draw_gradient_rect(toggle_rect, start_color, end_color,
                                  toggle_radius, horizontal=True)

            # Add subtle pattern to off state for texture
            pattern_surface = pygame.Surface((toggle_width, toggle_height), pygame.SRCALPHA)
            pattern_spacing = int(toggle_height * 0.4)
            pattern_alpha = 8  # Very subtle

            for x_pos in range(0, toggle_width, pattern_spacing):
                # Draw subtle diagonal lines
                pygame.draw.line(pattern_surface, (255, 255, 255, pattern_alpha),
                               (x_pos, 0), (x_pos + toggle_height//2, toggle_height))

            # Apply pattern with rounded corners
            pattern_mask = pygame.Surface((toggle_width, toggle_height), pygame.SRCALPHA)
            pygame.draw.rect(pattern_mask, (255, 255, 255, 255),
                           (0, 0, toggle_width, toggle_height),
                           border_radius=toggle_radius)

            # Use the mask to clip the pattern
            pattern_surface.blit(pattern_mask, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            self.screen.blit(pattern_surface, (toggle_x, toggle_y))

        # Add subtle inner shadow to toggle background for enhanced depth
        inner_shadow_rect = Rect(toggle_x + 2, toggle_y + 2, toggle_width - 4, toggle_height - 4)
        pygame.draw.rect(self.screen, (0, 0, 0, 20), inner_shadow_rect, width=1,
                       border_radius=toggle_radius - 2)

        # 3. Calculate handle position with enhanced animation effects
        handle_size = toggle_height - int(6 * min(self.scale_x, self.scale_y))

        # Determine handle position with sophisticated animation
        if current_value:
            # On position - right side with enhanced animation
            # Add smooth bounce effect
            bounce = math.sin(self.animation_time * 6) * 0.7  # Slightly stronger bounce
            handle_x = toggle_x + toggle_width - handle_size - int(4 * min(self.scale_x, self.scale_y))
            handle_x += int(bounce * min(self.scale_x, self.scale_y))  # Apply bounce

            # Use white for handle when on
            handle_color = self.LIGHT_TEXT
            inner_color = (250, 250, 250)  # Brighter inner color

            # Add subtle scale animation when on
            pulse = (math.sin(self.animation_time * 4) + 1) / 2  # Gentle pulsing
            scale_factor = 1.05 + 0.03 * pulse  # Subtle pulsing scale
            handle_size = int(handle_size * scale_factor)
        else:
            # Off position - left side with enhanced animation
            bounce = math.sin(self.animation_time * 6) * 0.7  # Slightly stronger bounce
            handle_x = toggle_x + int(4 * min(self.scale_x, self.scale_y))
            handle_x += int(bounce * min(self.scale_x, self.scale_y))  # Apply bounce

            # Use light gray for handle when off
            handle_color = (240, 240, 240)
            inner_color = (230, 230, 230)

        handle_y = toggle_y + int(3 * min(self.scale_x, self.scale_y))

        # Draw handle with enhanced 3D effect and modern styling
        # 1. Draw layered shadow beneath handle for depth perception
        shadow_layers = 3
        max_shadow_offset = int(4 * min(self.scale_x, self.scale_y))

        for i in range(shadow_layers):
            # Calculate progressive shadow properties
            progress = i / (shadow_layers - 1)  # 0.0 to 1.0
            shadow_offset = int(max_shadow_offset * progress)
            shadow_alpha = int(60 * (1 - progress))  # Fade out for outer shadows
            shadow_size = handle_size - int(progress * 4)

            # Draw shadow layer
            pygame.draw.ellipse(self.screen, (0, 0, 0, shadow_alpha),
                              Rect(handle_x + shadow_offset//2, handle_y + shadow_offset//2,
                                  shadow_size, shadow_size))

        # 2. Create handle surface for advanced effects
        handle_surface = pygame.Surface((handle_size, handle_size), pygame.SRCALPHA)

        # Draw gradient ellipses for 3D effect
        gradient_steps = 5  # More steps for smoother gradient
        for i in range(gradient_steps):
            progress = i / (gradient_steps - 1)  # 0.0 to 1.0

            # Calculate color with gradient
            if current_value:
                # More vibrant gradient for on state
                r = int(handle_color[0] * (1 - progress * 0.15))
                g = int(handle_color[1] * (1 - progress * 0.15))
                b = int(handle_color[2] * (1 - progress * 0.15))
            else:
                # More subtle gradient for off state
                r = int(handle_color[0] * (1 - progress * 0.1))
                g = int(handle_color[1] * (1 - progress * 0.1))
                b = int(handle_color[2] * (1 - progress * 0.1))

            # Ensure valid color values
            r, g, b = max(0, min(255, r)), max(0, min(255, g)), max(0, min(255, b))

            # Calculate size reduction for each step
            size_reduction = int(progress * 3)

            # Draw ellipse with current color
            pygame.draw.ellipse(handle_surface, (r, g, b),
                              Rect(size_reduction//2, size_reduction//2,
                                  handle_size - size_reduction, handle_size - size_reduction))

        # 3. Add inner ellipse with enhanced styling
        inner_size = handle_size - 10  # Slightly smaller for better proportion
        inner_rect = Rect(5, 5, inner_size, inner_size)

        # Fill with base color
        pygame.draw.ellipse(handle_surface, inner_color, inner_rect)

        # Add subtle inner shadow for depth
        inner_shadow = pygame.Surface((inner_size, inner_size), pygame.SRCALPHA)
        for i in range(inner_size//2):
            # Only draw shadow at the edges
            if i > inner_size//2 * 0.7:
                alpha = 40 * ((i - inner_size//2 * 0.7) / (inner_size//2 * 0.3))  # Fade from edge
                pygame.draw.ellipse(inner_shadow, (0, 0, 0, int(alpha)),
                                  (i, i, inner_size - i*2, inner_size - i*2))

        # Apply inner shadow
        handle_surface.blit(inner_shadow, (5, 5))

        # 4. Add highlight reflection for 3D effect
        # Create highlight surface
        highlight_size = handle_size // 2
        highlight_surface = pygame.Surface((highlight_size, highlight_size), pygame.SRCALPHA)

        # Draw gradient highlight
        for i in range(highlight_size):
            # Calculate alpha with smooth falloff
            alpha = 160 * (1 - (i / highlight_size)**1.5)  # Non-linear falloff for better effect

            # Draw smaller ellipse for each step
            pygame.draw.ellipse(highlight_surface, (255, 255, 255, int(alpha)),
                              (i, i, highlight_size - i*2, highlight_size - i*2))

        # Position highlight in top-left quadrant for 3D lighting effect
        highlight_pos = (handle_size//6, handle_size//6)
        handle_surface.blit(highlight_surface, highlight_pos)

        # 5. Add subtle animated effect if toggle is on
        if current_value:
            # Add subtle glow to handle when on
            glow_size = handle_size
            glow_surface = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)

            # Get active tab color for the glow
            glow_color = active_color

            # Create pulsing effect
            pulse = (math.sin(self.animation_time * 4) + 1) / 2  # 0 to 1 pulsing
            glow_alpha = int(30 + 20 * pulse)  # Pulsing alpha

            # Draw glow with tab color
            pygame.draw.ellipse(glow_surface, (*glow_color, glow_alpha),
                              (0, 0, glow_size, glow_size))

            # Apply glow with additive blending
            handle_surface.blit(glow_surface, (0, 0), special_flags=pygame.BLEND_RGBA_ADD)

        # 6. Apply final handle to screen
        self.screen.blit(handle_surface, (handle_x, handle_y))

        # Add status text for better clarity
        status_font = self.small_font
        if current_value:
            status_text = "ON"
            status_color = (255, 255, 255, 200)  # Semi-transparent white
            status_x = toggle_x + int(5 * min(self.scale_x, self.scale_y))
        else:
            status_text = "OFF"
            status_color = (200, 200, 200, 150)  # Semi-transparent gray
            status_x = toggle_x + toggle_width - int(25 * min(self.scale_x, self.scale_y))

        status_render = status_font.render(status_text, True, status_color)
        status_y = toggle_y + (toggle_height - status_render.get_height()) // 2
        self.screen.blit(status_render, (status_x, status_y))

        # Add subtle animation effect for hovered toggle
        if is_hovered:
            # Pulsing highlight effect
            pulse_highlight = (math.sin(self.animation_time * 4) + 1) / 2  # Faster pulsing
            highlight_alpha = int(10 + 10 * pulse_highlight)  # Subtle pulsing

            highlight_surface = pygame.Surface((toggle_rect.width, toggle_rect.height), pygame.SRCALPHA)
            highlight_surface.fill((255, 255, 255, highlight_alpha))
            pygame.draw.rect(highlight_surface, (255, 255, 255, highlight_alpha),
                           (0, 0, toggle_rect.width, toggle_rect.height),
                           border_radius=toggle_radius)
            self.screen.blit(highlight_surface, toggle_rect)

        # Store hit area for toggle with improved interaction area
        # Make the hit area slightly larger than the visual toggle for easier interaction
        # Note: We're storing window-relative coordinates here
        extended_toggle_rect = Rect(
            toggle_x - int(5 * min(self.scale_x, self.scale_y)),
            toggle_y - int(5 * min(self.scale_x, self.scale_y)),
            toggle_width + int(10 * min(self.scale_x, self.scale_y)),
            toggle_height + int(10 * min(self.scale_x, self.scale_y))
        )
        self.hit_areas[f"toggle_{setting_key}"] = extended_toggle_rect

    def draw_dropdown_setting(self, x, y, width, height, label, setting_key, default_value, options, description=""):
        """Draw a dropdown setting with label and options"""
        # Get category for this setting
        category = self._get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Draw setting background with modern styling
        bg_rect = Rect(x, y, width, height)
        self.draw_gradient_rect(bg_rect, (30, 40, 60),
                              (40, 50, 70),
                              10, glossy=True)

        # Draw label with shadow for depth
        label_font = self.header_font
        label_shadow = label_font.render(label, True, (0, 0, 0, 128))
        label_text = label_font.render(label, True, self.WHITE)
        shadow_offset = int(1 * min(self.scale_x, self.scale_y))

        # Draw shadow slightly offset
        self.screen.blit(label_shadow, (x + int(10 * self.scale_x) + shadow_offset,
                                      y + int(10 * self.scale_y) + shadow_offset))
        # Draw main text
        self.screen.blit(label_text, (x + int(10 * self.scale_x), y + int(10 * self.scale_y)))

        # Draw description if provided - with improved styling and higher contrast
        if description:
            desc_font = self.small_font
            # Use higher contrast text color based on theme
            if self.current_theme == "light":
                desc_color = (40, 40, 40)  # Dark text for light theme
            else:
                desc_color = (230, 230, 230)  # Very light text for dark theme
            desc_text = desc_font.render(description, True, desc_color)  # Higher contrast text
            desc_y = y + label_text.get_height() + int(5 * self.scale_y)
            self.screen.blit(desc_text, (x + int(15 * self.scale_x), desc_y))

        # Calculate dropdown dimensions
        dropdown_width = int(width * 0.4)
        dropdown_height = int(34 * min(self.scale_x, self.scale_y))  # Slightly taller for better touch targets
        dropdown_x = x + width - dropdown_width - int(20 * self.scale_x)
        dropdown_y = y + (height - dropdown_height) // 2

        # Check if dropdown is being hovered or is active
        dropdown_rect = Rect(dropdown_x, dropdown_y, dropdown_width, dropdown_height)
        is_hovered = dropdown_rect.collidepoint(mouse_pos)
        is_active = hasattr(self, 'active_dropdown') and self.active_dropdown == setting_key

        # Draw dropdown box with enhanced styling
        if is_active:
            # Active state
            bg_color1 = self.ACCENT_COLOR
            bg_color2 = (self.ACCENT_COLOR[0]-20, self.ACCENT_COLOR[1]-20, self.ACCENT_COLOR[2]-20)
            border_color = self.ACCENT_ACTIVE
        elif is_hovered:
            # Hover state
            bg_color1 = (40, 60, 80)
            bg_color2 = (50, 70, 90)
            border_color = self.ACCENT_HOVER
        else:
            # Normal state
            bg_color1 = (30, 40, 50)
            bg_color2 = (40, 50, 60)
            border_color = self.CONTROL_BORDER

        # Draw dropdown background with gradient
        self.draw_gradient_rect(
            dropdown_rect,
            bg_color1, bg_color2,
            int(5 * min(self.scale_x, self.scale_y)),
            horizontal=False,
            glossy=is_active or is_hovered  # Add glossy effect when active or hovered
        )

        # Draw dropdown border
        pygame.draw.rect(
            self.screen,
            border_color,
            dropdown_rect,
            width=1,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw current value with shadow for depth
        value_font = self.text_font
        value_shadow = value_font.render(current_value, True, (0, 0, 0, 128))
        value_text = value_font.render(current_value, True, self.WHITE if not is_active else self.WHITE)

        value_x = dropdown_x + int(10 * self.scale_x)
        value_y = dropdown_y + (dropdown_height - value_text.get_height()) // 2

        # Special handling for accent color dropdown - show color preview
        if setting_key == "ui_accent_color":
            # Get the color for the current accent with improved visibility
            accent_colors = {
                "blue": (0, 140, 240),  # Brighter blue
                "teal": (0, 200, 200),  # Brighter teal
                "purple": (140, 100, 220),  # Brighter purple
                "orange": (255, 140, 40),  # Brighter orange
                "green": (50, 200, 100),  # Brighter green
                "red": (255, 70, 70),  # Brighter red
                "gold": (255, 215, 0)  # Brighter gold
            }

            # Draw color swatch before text
            swatch_size = int(16 * min(self.scale_x, self.scale_y))
            swatch_rect = Rect(value_x, value_y + (value_text.get_height() - swatch_size) // 2, swatch_size, swatch_size)

            # Get color or use default blue
            color = accent_colors.get(current_value, accent_colors["blue"])

            # Draw rounded rectangle swatch
            pygame.draw.rect(self.screen, color, swatch_rect, border_radius=int(4 * min(self.scale_x, self.scale_y)))
            pygame.draw.rect(self.screen, self.WHITE, swatch_rect, width=1, border_radius=int(4 * min(self.scale_x, self.scale_y)))

            # Adjust text position to appear after swatch
            value_x += swatch_size + int(8 * min(self.scale_x, self.scale_y))

        # Draw text shadow
        self.screen.blit(value_shadow, (value_x + 1, value_y + 1))
        # Draw main text
        self.screen.blit(value_text, (value_x, value_y))

        # Draw dropdown arrow with animation
        arrow_size = int(10 * min(self.scale_x, self.scale_y))
        arrow_x = dropdown_x + dropdown_width - arrow_size - int(10 * self.scale_x)
        arrow_y = dropdown_y + (dropdown_height - arrow_size) // 2

        # Animate arrow when active
        if is_active:
            # Pointing up when active
            arrow_points = [
                (arrow_x, arrow_y + arrow_size),
                (arrow_x + arrow_size, arrow_y + arrow_size),
                (arrow_x + arrow_size // 2, arrow_y)
            ]
            arrow_color = self.WHITE
        else:
            # Pointing down when inactive
            arrow_points = [
                (arrow_x, arrow_y),
                (arrow_x + arrow_size, arrow_y),
                (arrow_x + arrow_size // 2, arrow_y + arrow_size)
            ]
            arrow_color = (200, 200, 200) if is_hovered else (180, 180, 180)

        # Draw arrow with anti-aliasing for smoother appearance
        pygame.gfxdraw.filled_polygon(self.screen, arrow_points, arrow_color)
        pygame.gfxdraw.aapolygon(self.screen, arrow_points, arrow_color)

        # Draw dropdown options if active
        if is_active and options:
            option_height = int(30 * min(self.scale_x, self.scale_y))
            options_panel_height = len(options) * option_height
            options_panel_rect = Rect(dropdown_x, dropdown_y + dropdown_height, dropdown_width, options_panel_height)

            # Draw options panel background with shadow
            shadow_offset = int(5 * min(self.scale_x, self.scale_y))
            shadow_rect = Rect(
                options_panel_rect.x + shadow_offset//2,
                options_panel_rect.y + shadow_offset//2,
                options_panel_rect.width,
                options_panel_rect.height
            )
            shadow_surface = pygame.Surface((shadow_rect.width, shadow_rect.height), pygame.SRCALPHA)
            shadow_surface.fill((0, 0, 0, 100))  # Semi-transparent black
            self.screen.blit(shadow_surface, shadow_rect)

            # Draw options panel
            self.draw_gradient_rect(
                options_panel_rect,
                (40, 60, 80),
                (30, 50, 70),
                int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw border around options panel
            pygame.draw.rect(
                self.screen,
                self.CONTROL_BORDER,
                options_panel_rect,
                width=1,
                border_radius=int(5 * min(self.scale_x, self.scale_y))
            )

            # Draw each option
            for i, option in enumerate(options):
                option_rect = Rect(
                    dropdown_x,
                    dropdown_y + dropdown_height + (i * option_height),
                    dropdown_width,
                    option_height
                )

                # Check if this option is being hovered
                is_option_hovered = option_rect.collidepoint(mouse_pos)

                # Highlight selected option and hovered option
                if option == current_value or is_option_hovered:
                    highlight_rect = Rect(
                        option_rect.x + 2,
                        option_rect.y + 2,
                        option_rect.width - 4,
                        option_rect.height - 4
                    )

                    if option == current_value:
                        # Current selection highlight
                        highlight_color = self.ACCENT_COLOR
                    else:
                        # Hover highlight
                        highlight_color = (60, 80, 100)

                    pygame.draw.rect(
                        self.screen,
                        highlight_color,
                        highlight_rect,
                        border_radius=int(3 * min(self.scale_x, self.scale_y))
                    )

                # Draw option text
                option_text_color = self.WHITE if option == current_value or is_option_hovered else (200, 200, 200)
                option_text = value_font.render(option, True, option_text_color)
                option_text_x = dropdown_x + int(10 * self.scale_x)
                option_text_y = option_rect.y + (option_height - option_text.get_height()) // 2

                # Special handling for accent color dropdown - show color preview
                if setting_key == "ui_accent_color":
                    # Get the color for this accent option with improved visibility
                    accent_colors = {
                        "blue": (0, 140, 240),  # Brighter blue
                        "teal": (0, 200, 200),  # Brighter teal
                        "purple": (140, 100, 220),  # Brighter purple
                        "orange": (255, 140, 40),  # Brighter orange
                        "green": (50, 200, 100),  # Brighter green
                        "red": (255, 70, 70),  # Brighter red
                        "gold": (255, 215, 0)  # Brighter gold
                    }

                    # Draw color swatch before text
                    swatch_size = int(16 * min(self.scale_x, self.scale_y))
                    swatch_rect = Rect(option_text_x, option_text_y + (option_text.get_height() - swatch_size) // 2,
                                      swatch_size, swatch_size)

                    # Get color or use default blue
                    color = accent_colors.get(option, accent_colors["blue"])

                    # Draw rounded rectangle swatch
                    pygame.draw.rect(self.screen, color, swatch_rect,
                                   border_radius=int(4 * min(self.scale_x, self.scale_y)))
                    pygame.draw.rect(self.screen, self.WHITE, swatch_rect,
                                   width=1, border_radius=int(4 * min(self.scale_x, self.scale_y)))

                    # Adjust text position to appear after swatch
                    option_text_x += swatch_size + int(8 * min(self.scale_x, self.scale_y))

                # Draw the option text
                self.screen.blit(option_text, (option_text_x, option_text_y))

                # Store hit area for this option - adjust for content scroll position
                content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
                adjusted_rect = option_rect.copy()
                if hasattr(self, 'scroll_y'):
                    adjusted_rect.y += content_y - self.scroll_y
                self.hit_areas[f"dropdown_option_{setting_key}_{i}"] = adjusted_rect

        # Store hit area for dropdown and options
        self.hit_areas[f"dropdown_{setting_key}"] = dropdown_rect

        # Store options for this dropdown
        if not hasattr(self, 'dropdown_options'):
            self.dropdown_options = {}
        self.dropdown_options[setting_key] = options

    def draw_action_button(self, x, y, width, height, label, action_key, description="", sub_description=""):
        """Draw an action button with label and description"""
        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Draw setting background with modern styling
        bg_rect = Rect(x, y, width, height)
        self.draw_gradient_rect(bg_rect, (30, 40, 60),
                              (40, 50, 70),
                              10, glossy=True)

        # Draw label with shadow for depth
        label_font = self.header_font
        label_shadow = label_font.render(label, True, (0, 0, 0, 128))
        label_text = label_font.render(label, True, self.text_light)
        shadow_offset = int(1 * min(self.scale_x, self.scale_y))

        # Draw shadow slightly offset
        self.screen.blit(label_shadow, (x + int(10 * self.scale_x) + shadow_offset,
                                      y + int(10 * self.scale_y) + shadow_offset))
        # Draw main text
        self.screen.blit(label_text, (x + int(10 * self.scale_x), y + int(10 * self.scale_y)))

        # Draw description if provided - with improved styling and higher contrast
        if description:
            desc_font = self.text_font
            # Use higher contrast text color based on theme
            if self.current_theme == "light":
                desc_color = (40, 40, 40)  # Dark text for light theme
                sub_desc_color = (60, 60, 60)  # Slightly lighter dark text
            else:
                desc_color = (230, 230, 230)  # Very light text for dark theme
                sub_desc_color = (210, 210, 210)  # Slightly darker light text

            desc_text = desc_font.render(description, True, desc_color)  # Higher contrast text
            desc_y = y + label_text.get_height() + int(5 * self.scale_y)
            self.screen.blit(desc_text, (x + int(15 * self.scale_x), desc_y))

            # Draw sub-description if provided
            if sub_description:
                sub_desc_font = self.small_font
                sub_desc_text = sub_desc_font.render(sub_description, True, sub_desc_color)  # Higher contrast
                sub_desc_y = desc_y + desc_text.get_height() + int(3 * self.scale_y)
                self.screen.blit(sub_desc_text, (x + int(15 * self.scale_x), sub_desc_y))

        # Calculate button dimensions
        button_width = int(130 * min(self.scale_x, self.scale_y))  # Slightly wider for better touch target
        button_height = int(36 * min(self.scale_x, self.scale_y))  # Slightly taller for better touch target
        button_x = x + width - button_width - int(20 * self.scale_x)
        button_y = y + (height - button_height) // 2

        # Check if button is being hovered
        button_rect = Rect(button_x, button_y, button_width, button_height)
        is_hovered = button_rect.collidepoint(mouse_pos)

        # Determine button colors based on state
        if is_hovered:
            # Hover state
            start_color = self.ACCENT_HOVER
            end_color = (self.ACCENT_HOVER[0]-20, self.ACCENT_HOVER[1]-20, self.ACCENT_HOVER[2]-20)
        else:
            # Normal state
            start_color = self.ACCENT_COLOR
            end_color = (self.ACCENT_COLOR[0]-20, self.ACCENT_COLOR[1]-20, self.ACCENT_COLOR[2]-20)

        # Draw button with enhanced styling
        # Draw shadow beneath button for depth
        shadow_offset = int(4 * min(self.scale_x, self.scale_y))
        shadow_rect = Rect(
            button_x + shadow_offset//2,
            button_y + shadow_offset//2,
            button_width,
            button_height
        )
        shadow_surface = pygame.Surface((shadow_rect.width, shadow_rect.height), pygame.SRCALPHA)
        shadow_surface.fill((0, 0, 0, 80))  # Semi-transparent black
        self.screen.blit(shadow_surface, shadow_rect)

        # Draw main button with gradient and glossy effect
        self.draw_gradient_rect(
            button_rect,
            start_color, end_color,
            int(6 * min(self.scale_x, self.scale_y)),  # Larger border radius for modern look
            horizontal=False,
            glossy=True  # Add glossy effect
        )

        # Draw button text with shadow for depth
        button_font = self.text_font
        button_label = "Import File" if action_key in ["import_preset", "import_boards", "import_language"] else "Export"

        button_shadow = button_font.render(button_label, True, (0, 0, 0, 128))
        button_text = button_font.render(button_label, True, self.WHITE)

        # Calculate text position
        button_text_x = button_x + (button_width - button_text.get_width()) // 2
        button_text_y = button_y + (button_height - button_text.get_height()) // 2

        # Draw text shadow
        self.screen.blit(button_shadow, (button_text_x + 1, button_text_y + 1))
        # Draw main text
        self.screen.blit(button_text, (button_text_x, button_text_y))

        # Add icon to button
        icon_font = pygame.font.SysFont("Arial", int(18 * min(self.scale_x, self.scale_y)))
        icon_text = "📂" if action_key in ["import_preset", "import_boards", "import_language"] else "💾"
        icon_render = icon_font.render(icon_text, True, self.WHITE)

        # Position icon to the left of text
        icon_x = button_text_x - icon_render.get_width() - int(5 * min(self.scale_x, self.scale_y))
        icon_y = button_text_y
        self.screen.blit(icon_render, (icon_x, icon_y))

        # Add subtle animation effect when hovered
        if is_hovered:
            # Pulsing glow effect
            pulse = (math.sin(self.animation_time * 6.28) + 1) / 2  # 0 to 1 pulsing
            glow_size = int((button_width + 10) * (1 + pulse * 0.05))  # Subtle size change
            glow_rect = Rect(
                button_rect.centerx - glow_size//2,
                button_rect.centery - glow_size//2,
                glow_size,
                glow_size//2
            )

            # Create glow surface
            glow_surface = pygame.Surface((glow_rect.width, glow_rect.height), pygame.SRCALPHA)
            glow_alpha = int(40 + 20 * pulse)  # Pulsing alpha
            pygame.draw.ellipse(glow_surface, (start_color[0], start_color[1], start_color[2], glow_alpha),
                              (0, 0, glow_rect.width, glow_rect.height))

            # Apply glow beneath button
            self.screen.blit(glow_surface, glow_rect, special_flags=pygame.BLEND_RGBA_ADD)

        # Store hit area for button
        self.hit_areas[f"action_{action_key}"] = button_rect

    def draw_text_input(self, x, y, width, height, label, setting_key, default_value, description=""):
        """Draw a text input field with label"""
        # Get category for this setting
        category = self._get_category_for_setting(setting_key)

        # Get current value from settings manager or use default
        current_value = self.settings_manager.get_setting(category, setting_key, default_value)

        # This is the standard text input style

        # Get current mouse position for hover effects
        mouse_pos = pygame.mouse.get_pos()

        # Draw setting background with modern styling
        bg_rect = Rect(x, y, width, height)
        self.draw_gradient_rect(bg_rect, self.control_bg,
                              self._lighten_color(self.control_bg, 5),
                              10, glossy=True)

        # Draw label with shadow for depth
        label_font = self.header_font
        label_shadow = label_font.render(label, True, (0, 0, 0, 128))
        label_text = label_font.render(label, True, self.text_light)
        shadow_offset = int(1 * min(self.scale_x, self.scale_y))

        # Draw shadow slightly offset
        self.screen.blit(label_shadow, (x + int(10 * self.scale_x) + shadow_offset,
                                      y + int(10 * self.scale_y) + shadow_offset))
        # Draw main text
        self.screen.blit(label_text, (x + int(10 * self.scale_x), y + int(10 * self.scale_y)))

        # Draw description if provided - with improved styling
        if description:
            desc_font = self.small_font
            desc_text = desc_font.render(description, True, (200, 200, 200))  # Brighter text for better readability
            desc_y = y + label_text.get_height() + int(5 * self.scale_y)
            self.screen.blit(desc_text, (x + int(15 * self.scale_x), desc_y))

        # Calculate input field dimensions
        input_width = int(width * 0.5)
        input_height = int(34 * min(self.scale_x, self.scale_y))  # Slightly taller for better touch target
        input_x = x + width - input_width - int(20 * self.scale_x)
        input_y = y + (height - input_height) // 2

        # Check if input field is being hovered or is active
        input_rect = Rect(input_x, input_y, input_width, input_height)
        is_hovered = input_rect.collidepoint(mouse_pos)
        is_active = hasattr(self, 'active_input') and self.active_input == setting_key

        # Draw input field with enhanced styling
        if is_active:
            # Active state
            bg_color = (40, 60, 80)
            border_color = self.primary
            text_color = self.text_light
        elif is_hovered:
            # Hover state
            bg_color = (35, 50, 70)
            border_color = self.primary_hover
            text_color = self.text_light
        else:
            # Normal state
            bg_color = (30, 40, 60)
            border_color = (80, 100, 120)
            text_color = (220, 220, 220)

        # Draw input field background
        self.draw_gradient_rect(
            input_rect,
            bg_color,
            (bg_color[0]+5, bg_color[1]+5, bg_color[2]+5),
            int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw input field border
        pygame.draw.rect(
            self.screen,
            border_color,
            input_rect,
            width=1,
            border_radius=int(5 * min(self.scale_x, self.scale_y))
        )

        # Draw current value with shadow for depth
        value_font = self.text_font
        value_shadow = value_font.render(current_value, True, (0, 0, 0, 128))
        value_text = value_font.render(current_value, True, text_color)

        value_x = input_x + int(10 * self.scale_x)
        value_y = input_y + (input_height - value_text.get_height()) // 2

        # Draw text shadow
        self.screen.blit(value_shadow, (value_x + 1, value_y + 1))
        # Draw main text
        self.screen.blit(value_text, (value_x, value_y))

        # Draw cursor if input is active
        if is_active:
            # Calculate cursor position after text
            cursor_x = value_x + value_text.get_width() + int(2 * min(self.scale_x, self.scale_y))
            cursor_height = value_text.get_height() + int(4 * min(self.scale_x, self.scale_y))
            cursor_y = value_y - int(2 * min(self.scale_x, self.scale_y))

            # Animate cursor blinking
            if (self.animation_time * 2) % 1 < 0.5:  # Blink every half second
                pygame.draw.line(
                    self.screen,
                    self.text_light,
                    (cursor_x, cursor_y),
                    (cursor_x, cursor_y + cursor_height),
                    width=int(2 * min(self.scale_x, self.scale_y))
                )

        # Store hit area for input field - adjust for content scroll position
        content_y = self.header_height + self.tab_bar_height if hasattr(self, 'header_height') and hasattr(self, 'tab_bar_height') else 0
        adjusted_rect = input_rect.copy()
        if hasattr(self, 'scroll_y'):
            adjusted_rect.y += content_y - self.scroll_y
        self.hit_areas[f"text_input_{setting_key}"] = adjusted_rect

        # Hit area is now properly registered

    def draw_gradient_rect(self, rect, color1, color2, border_radius=0, top_left_radius=None, top_right_radius=None, bottom_left_radius=None, bottom_right_radius=None, horizontal=False, glossy=False):
        """Draw a rectangle with a gradient and advanced styling options"""
        surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)

        # Set default radii if not specified
        if top_left_radius is None: top_left_radius = border_radius
        if top_right_radius is None: top_right_radius = border_radius
        if bottom_left_radius is None: bottom_left_radius = border_radius
        if bottom_right_radius is None: bottom_right_radius = border_radius

        # Scale radii
        scale_factor = min(self.scale_x, self.scale_y)
        top_left_radius = max(1, int(top_left_radius * scale_factor)) if top_left_radius > 0 else 0
        top_right_radius = max(1, int(top_right_radius * scale_factor)) if top_right_radius > 0 else 0
        bottom_left_radius = max(1, int(bottom_left_radius * scale_factor)) if bottom_left_radius > 0 else 0
        bottom_right_radius = max(1, int(bottom_right_radius * scale_factor)) if bottom_right_radius > 0 else 0

        # Draw gradient
        if horizontal:
            # Horizontal gradient
            for x in range(rect.width):
                ratio = x / rect.width
                # Ensure color values are valid (0-255)
                r = max(0, min(255, int(color1[0] * (1 - ratio) + color2[0] * ratio)))
                g = max(0, min(255, int(color1[1] * (1 - ratio) + color2[1] * ratio)))
                b = max(0, min(255, int(color1[2] * (1 - ratio) + color2[2] * ratio)))
                pygame.draw.line(surface, (r, g, b), (x, 0), (x, rect.height))
        else:
            # Vertical gradient
            for y in range(rect.height):
                ratio = y / rect.height
                # Ensure color values are valid (0-255)
                r = max(0, min(255, int(color1[0] * (1 - ratio) + color2[0] * ratio)))
                g = max(0, min(255, int(color1[1] * (1 - ratio) + color2[1] * ratio)))
                b = max(0, min(255, int(color1[2] * (1 - ratio) + color2[2] * ratio)))
                pygame.draw.line(surface, (r, g, b), (0, y), (rect.width, y))

        # Add enhanced glossy effect if requested
        if glossy:
            # Create more sophisticated glossy effect with multiple layers
            # 1. Main highlight at the top
            gloss_height = int(rect.height / 2.5)  # Slightly larger highlight, ensure it's an integer
            gloss_surface = pygame.Surface((rect.width, gloss_height), pygame.SRCALPHA)

            # Draw glossy gradient with smoother transition
            for y in range(gloss_height):
                # Use quadratic falloff for smoother gradient
                progress = y / gloss_height
                alpha = max(0, min(255, int(100 * (1 - progress**1.5))))  # Fade from 100 to 0 with non-linear curve
                pygame.draw.line(gloss_surface, (255, 255, 255, alpha), (0, y), (rect.width, y))

            # Apply main highlight to the top portion
            surface.blit(gloss_surface, (0, 0), special_flags=pygame.BLEND_RGBA_ADD)

            # 2. Add subtle secondary highlight near the bottom for 3D effect
            bottom_gloss_height = int(rect.height / 6)  # Ensure it's an integer
            bottom_gloss_surface = pygame.Surface((rect.width, bottom_gloss_height), pygame.SRCALPHA)

            # Draw subtle bottom highlight
            for y in range(bottom_gloss_height):
                # Reverse direction and lower alpha for bottom highlight
                progress = 1 - (y / bottom_gloss_height)
                alpha = max(0, min(255, int(30 * progress**2)))  # Much subtler effect
                pygame.draw.line(bottom_gloss_surface, (255, 255, 255, alpha),
                               (0, y), (rect.width, y))

            # Position at bottom of rectangle
            bottom_y = rect.height - bottom_gloss_height
            surface.blit(bottom_gloss_surface, (0, bottom_y), special_flags=pygame.BLEND_RGBA_ADD)

            # 3. Add subtle edge highlights for more dimension
            edge_width = max(1, int(rect.width / 30))  # Scale with rectangle width
            edge_surface = pygame.Surface((edge_width, rect.height), pygame.SRCALPHA)

            # Left edge highlight
            for x in range(edge_width):
                progress = x / edge_width
                alpha = max(0, min(255, int(40 * (1 - progress**1.5))))
                pygame.draw.line(edge_surface, (255, 255, 255, alpha),
                               (x, 0), (x, rect.height))

            # Apply left edge highlight
            surface.blit(edge_surface, (0, 0), special_flags=pygame.BLEND_RGBA_ADD)

            # Right edge shadow (very subtle)
            edge_shadow = pygame.Surface((edge_width, rect.height), pygame.SRCALPHA)
            for x in range(edge_width):
                progress = 1 - (x / edge_width)
                alpha = max(0, min(255, int(20 * progress**2)))
                pygame.draw.line(edge_shadow, (0, 0, 0, alpha),
                               (x, 0), (x, rect.height))

            # Apply right edge shadow
            right_x = rect.width - edge_width
            surface.blit(edge_shadow, (right_x, 0), special_flags=pygame.BLEND_RGBA_ADD)

        # Apply rounded corners if needed
        if top_left_radius > 0 or top_right_radius > 0 or bottom_left_radius > 0 or bottom_right_radius > 0:
            # Create a mask with rounded corners
            mask = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            mask.fill((0, 0, 0, 0))  # Transparent

            # Draw each corner with appropriate radius
            # Top-left corner
            if top_left_radius > 0:
                pygame.draw.circle(mask, (255, 255, 255), (top_left_radius, top_left_radius), top_left_radius)

            # Top-right corner
            if top_right_radius > 0:
                pygame.draw.circle(mask, (255, 255, 255), (rect.width - top_right_radius, top_right_radius), top_right_radius)

            # Bottom-left corner
            if bottom_left_radius > 0:
                pygame.draw.circle(mask, (255, 255, 255), (bottom_left_radius, rect.height - bottom_left_radius), bottom_left_radius)

            # Bottom-right corner
            if bottom_right_radius > 0:
                pygame.draw.circle(mask, (255, 255, 255), (rect.width - bottom_right_radius, rect.height - bottom_right_radius), bottom_right_radius)

            # Fill in the middle
            pygame.draw.rect(mask, (255, 255, 255), (
                top_left_radius, 0,
                rect.width - top_left_radius - top_right_radius, rect.height
            ))
            pygame.draw.rect(mask, (255, 255, 255), (
                0, top_left_radius,
                rect.width, rect.height - top_left_radius - bottom_left_radius
            ))

            # Apply mask - using a different approach that works with all Pygame versions
            result_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
            result_surface.fill((0, 0, 0, 0))  # Transparent

            # First blit the mask to set alpha channel
            result_surface.blit(mask, (0, 0))
            # Then blit the surface with the gradient using the alpha channel
            result_surface.blit(surface, (0, 0), special_flags=pygame.BLEND_RGBA_MIN)
            surface = result_surface

        # Draw to screen
        self.screen.blit(surface, (rect.x, rect.y))

        return surface  # Return the surface for potential reuse
