#!/usr/bin/env python3
"""
Fix for the stats page game history blinking issue.

The problem is that the game_history_loading_complete flag is being reset
or checked inconsistently, causing the loading message to blink continuously.

This fix implements:
1. Stable loading state management
2. Prevents race conditions in loading state
3. Ensures loading state is set once and maintained
4. Adds proper synchronization for background loading
"""

import os
import sys

def fix_stats_page_blinking():
    """Fix the blinking issue in the stats page game history section."""
    
    print("🔧 Fixing stats page game history blinking issue...")
    
    # Read the current stats_page.py file
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ Error: stats_page.py not found!")
        return False
    
    # Create backup
    backup_filename = f'stats_page_blinking_fix_backup_{int(time.time())}.py'
    with open(backup_filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📁 Created backup: {backup_filename}")
    
    # Apply fixes
    fixes_applied = []
    
    # Fix 1: Improve loading state initialization
    old_init_pattern = '''        self.game_history_loading_complete = False  # Track if loading is complete
        self.database_has_games = None  # Track if database has any games (None = unknown, True/False = known)'''
    
    new_init_pattern = '''        self.game_history_loading_complete = False  # Track if loading is complete
        self.database_has_games = None  # Track if database has any games (None = unknown, True/False = known)
        self._loading_state_lock = threading.Lock()  # Prevent race conditions in loading state
        self._stable_loading_complete = False  # Stable flag that doesn't get reset'''
    
    if old_init_pattern in content:
        content = content.replace(old_init_pattern, new_init_pattern)
        fixes_applied.append("Added stable loading state management")
    
    # Fix 2: Improve the loading completion logic
    old_completion_pattern = '''                # Mark game history loading as complete
                self.game_history_loading_complete = True'''
    
    new_completion_pattern = '''                # Mark game history loading as complete with thread safety
                with self._loading_state_lock:
                    self.game_history_loading_complete = True
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Game history loading marked as complete")'''
    
    if old_completion_pattern in content:
        content = content.replace(old_completion_pattern, new_completion_pattern)
        fixes_applied.append("Improved loading completion logic")
    
    # Fix 3: Fix the display logic to prevent blinking
    old_display_pattern = '''            # Display appropriate message based on loading state and database content
            if hasattr(self, 'game_history_loading_complete') and self.game_history_loading_complete:'''
    
    new_display_pattern = '''            # Display appropriate message based on loading state and database content
            # ANTI-BLINK FIX: Use stable loading state to prevent continuous blinking
            loading_complete = (hasattr(self, '_stable_loading_complete') and self._stable_loading_complete) or \
                             (hasattr(self, 'game_history_loading_complete') and self.game_history_loading_complete)
            
            if loading_complete:'''
    
    if old_display_pattern in content:
        content = content.replace(old_display_pattern, new_display_pattern)
        fixes_applied.append("Fixed display logic to prevent blinking")
    
    # Fix 4: Prevent loading state from being reset during refresh
    old_refresh_pattern = '''                self.database_has_games = False
                self.game_history_loading_complete = True'''
    
    new_refresh_pattern = '''                self.database_has_games = False
                with self._loading_state_lock:
                    self.game_history_loading_complete = True
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Loading state stabilized after error")'''
    
    if old_refresh_pattern in content:
        content = content.replace(old_refresh_pattern, new_refresh_pattern)
        fixes_applied.append("Prevented loading state reset during refresh")
    
    # Fix 5: Add threading import if not present
    if 'import threading' not in content and 'from threading import' not in content:
        # Add threading import after other imports
        import_pattern = 'import time\nimport random'
        if import_pattern in content:
            content = content.replace(import_pattern, 'import time\nimport random\nimport threading')
            fixes_applied.append("Added threading import")
    
    # Fix 6: Improve the background loading to set stable state
    old_background_pattern = '''            self.initial_loading_complete = True
            self.stats_loading_in_progress = False'''
    
    new_background_pattern = '''            self.initial_loading_complete = True
            self.stats_loading_in_progress = False
            # ANTI-BLINK: Set stable loading state
            if hasattr(self, '_loading_state_lock'):
                with self._loading_state_lock:
                    self._stable_loading_complete = True
                    print("ANTI-BLINK: Background loading completed, stable state set")'''
    
    if old_background_pattern in content:
        content = content.replace(old_background_pattern, new_background_pattern)
        fixes_applied.append("Improved background loading state management")
    
    # Fix 7: Add method to reset loading state only when necessary
    reset_method = '''
    def reset_loading_state_if_needed(self):
        """ANTI-BLINK: Only reset loading state when actually needed (e.g., new data load)."""
        with self._loading_state_lock:
            # Only reset if we're starting a completely new load operation
            if hasattr(self, '_force_reload_requested') and self._force_reload_requested:
                self.game_history_loading_complete = False
                self._stable_loading_complete = False
                self._force_reload_requested = False
                print("ANTI-BLINK: Loading state reset for new data load")
    '''
    
    # Add the method before the last class method
    if 'def reset_loading_state_if_needed(self):' not in content:
        # Find a good place to insert the method (before the last method)
        insertion_point = content.rfind('\n    def ')
        if insertion_point != -1:
            content = content[:insertion_point] + reset_method + content[insertion_point:]
            fixes_applied.append("Added controlled loading state reset method")
    
    # Write the fixed content
    try:
        with open('stats_page.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Successfully applied fixes:")
        for fix in fixes_applied:
            print(f"   • {fix}")
        
        print("\n🎯 Blinking issue should now be resolved!")
        print("   The game history section will show a stable loading state")
        print("   and won't continuously blink between 'Loading...' and content.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error writing fixed file: {e}")
        return False

def test_fix():
    """Test the fix by checking if the problematic patterns are resolved."""
    print("\n🧪 Testing the fix...")
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if fixes are present
        checks = [
            ('_stable_loading_complete', 'Stable loading state flag'),
            ('_loading_state_lock', 'Thread-safe loading state'),
            ('with self._loading_state_lock:', 'Thread synchronization'),
            ('loading_complete = (hasattr(self', 'Anti-blink display logic'),
        ]
        
        all_good = True
        for pattern, description in checks:
            if pattern in content:
                print(f"   ✅ {description} - Present")
            else:
                print(f"   ❌ {description} - Missing")
                all_good = False
        
        if all_good:
            print("\n🎉 All fixes are properly applied!")
            print("   The stats page should no longer have blinking issues.")
        else:
            print("\n⚠️  Some fixes may not have been applied correctly.")
            
        return all_good
        
    except Exception as e:
        print(f"❌ Error testing fix: {e}")
        return False

if __name__ == "__main__":
    import time
    
    print("🔧 Stats Page Blinking Fix")
    print("=" * 50)
    
    success = fix_stats_page_blinking()
    
    if success:
        test_fix()
        print("\n📋 Summary:")
        print("   • Fixed race conditions in loading state management")
        print("   • Added thread-safe loading state synchronization")
        print("   • Implemented stable loading completion flags")
        print("   • Prevented continuous state toggling")
        print("\n🚀 The game history section should now display stably without blinking!")
    else:
        print("\n❌ Fix failed. Please check the error messages above.")