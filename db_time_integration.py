"""
Database Time Integration Module

This module provides database functions that use the independent time system
instead of system time. It ensures all new database records use consistent
timestamps that are immune to PC clock changes.
"""

import sqlite3
import json
from datetime import datetime
from time_manager import get_time_manager, independent_now, independent_utc_now, independent_strftime

class DatabaseTimeIntegration:
    """
    Database operations using independent time system.
    
    This class provides database functions that use the robust time manager
    for all timestamp operations, ensuring consistency even when PC clock changes.
    """
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.time_manager = get_time_manager()
    
    def add_game_record(self, username, house, stake, players, total_calls, 
                       commission_percent, fee, total_prize, details, status):
        """
        Add a game record with independent timestamp.
        
        This replaces the original function that used datetime.now().
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Use independent time system for timestamp
            timestamp = independent_strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                INSERT INTO game_history 
                (username, house, stake, players, total_calls, commission_percent, 
                 fee, total_prize, details, status, date_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                username, house, stake, players, total_calls, commission_percent,
                fee, total_prize, details, status, timestamp
            ))
            
            conn.commit()
            record_id = cursor.lastrowid
            conn.close()
            
            print(f"Game record added with independent timestamp: {timestamp}")
            return record_id
            
        except Exception as e:
            print(f"Error adding game record: {e}")
            return None
    
    def add_wallet_transaction(self, amount, transaction_type, description, balance_after):
        """
        Add a wallet transaction with independent timestamp.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Use independent time system
            timestamp = independent_strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                INSERT INTO wallet_transactions 
                (date_time, amount, transaction_type, description, balance_after)
                VALUES (?, ?, ?, ?, ?)
            ''', (timestamp, amount, transaction_type, description, balance_after))
            
            conn.commit()
            record_id = cursor.lastrowid
            conn.close()
            
            print(f"Wallet transaction added with independent timestamp: {timestamp}")
            return record_id
            
        except Exception as e:
            print(f"Error adding wallet transaction: {e}")
            return None
    
    def update_daily_stats(self, date_str=None):
        """
        Update daily stats using independent time system.
        
        Args:
            date_str: Date string, if None uses current independent date
        """
        try:
            if date_str is None:
                # Use independent time system for current date
                date_str = independent_now().strftime('%Y-%m-%d')
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calculate stats for the date
            cursor.execute('''
                SELECT COUNT(*), COALESCE(SUM(total_prize), 0)
                FROM game_history 
                WHERE date(date_time) = ?
            ''', (date_str,))
            
            games_count, total_earnings = cursor.fetchone()
            
            # Update or insert daily stats
            cursor.execute('''
                INSERT OR REPLACE INTO daily_stats 
                (date, games_played, earnings, winners, total_players)
                VALUES (?, ?, ?, 0, 0)
            ''', (date_str, games_count or 0, total_earnings or 0))
            
            conn.commit()
            conn.close()
            
            print(f"Daily stats updated for {date_str}: {games_count} games, {total_earnings} earnings")
            return True
            
        except Exception as e:
            print(f"Error updating daily stats: {e}")
            return False
    
    def get_games_by_date_range(self, start_date=None, end_date=None):
        """
        Get games within a date range using independent time calculations.
        
        Args:
            start_date: Start date string or None for 7 days ago
            end_date: End date string or None for today
        """
        try:
            if end_date is None:
                end_date = independent_now().strftime('%Y-%m-%d')
            
            if start_date is None:
                # Calculate 7 days ago using independent time
                from datetime import timedelta
                start_dt = independent_now() - timedelta(days=7)
                start_date = start_dt.strftime('%Y-%m-%d')
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM game_history 
                WHERE date(date_time) BETWEEN ? AND ?
                ORDER BY date_time DESC
            ''', (start_date, end_date))
            
            games = cursor.fetchall()
            conn.close()
            
            print(f"Retrieved {len(games)} games between {start_date} and {end_date}")
            return games
            
        except Exception as e:
            print(f"Error getting games by date range: {e}")
            return []
    
    def add_time_migration_log(self, migration_type, details):
        """
        Add a log entry for time system migration.
        
        This helps track when the independent time system was implemented.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create migration log table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS time_migration_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    migration_type TEXT,
                    details TEXT,
                    system_time TEXT,
                    independent_time TEXT,
                    timestamp TEXT
                )
            ''')
            
            # Add migration log entry
            system_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            independent_time = independent_strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                INSERT INTO time_migration_log 
                (migration_type, details, system_time, independent_time, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                migration_type, 
                json.dumps(details) if isinstance(details, dict) else str(details),
                system_time,
                independent_time,
                independent_time  # Use independent time as the official timestamp
            ))
            
            conn.commit()
            conn.close()
            
            print(f"Time migration log added: {migration_type}")
            return True
            
        except Exception as e:
            print(f"Error adding migration log: {e}")
            return False
    
    def check_time_consistency_in_db(self):
        """
        Check for time consistency issues in the database.
        
        This can help identify records that might have been affected
        by system clock changes before the independent time system.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check for future dates (impossible with independent time)
            current_time = independent_strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute('''
                SELECT COUNT(*) FROM game_history 
                WHERE date_time > ?
            ''', (current_time,))
            
            future_records = cursor.fetchone()[0]
            
            # Check for records with suspicious time gaps
            cursor.execute('''
                SELECT date_time, 
                       LAG(date_time) OVER (ORDER BY date_time) as prev_time
                FROM game_history 
                ORDER BY date_time
            ''')
            
            time_gaps = []
            rows = cursor.fetchall()
            
            for i, (current, previous) in enumerate(rows):
                if previous:
                    try:
                        curr_dt = datetime.strptime(current, '%Y-%m-%d %H:%M:%S')
                        prev_dt = datetime.strptime(previous, '%Y-%m-%d %H:%M:%S')
                        gap_hours = (curr_dt - prev_dt).total_seconds() / 3600
                        
                        # Flag gaps larger than 24 hours or negative gaps
                        if gap_hours > 24 or gap_hours < 0:
                            time_gaps.append({
                                'previous_time': previous,
                                'current_time': current,
                                'gap_hours': gap_hours
                            })
                    except:
                        pass
            
            conn.close()
            
            return {
                'future_records': future_records,
                'suspicious_gaps': len(time_gaps),
                'gap_details': time_gaps[:10],  # First 10 gaps
                'current_independent_time': current_time
            }
            
        except Exception as e:
            print(f"Error checking time consistency: {e}")
            return {'error': str(e)}

# Convenience functions for easy integration
def create_db_time_integration(db_path):
    """Create a database time integration instance."""
    return DatabaseTimeIntegration(db_path)

def log_time_system_activation(db_path):
    """Log that the independent time system has been activated."""
    db_time = DatabaseTimeIntegration(db_path)
    
    time_stats = get_time_manager().get_stats()
    
    details = {
        'action': 'independent_time_system_activated',
        'reference_time': time_stats['reference_time'],
        'startup_count': time_stats['startup_count'],
        'system_description': 'Switched to monotonic clock-based independent time system'
    }
    
    return db_time.add_time_migration_log('ACTIVATION', details)

# Test function
if __name__ == "__main__":
    print("=== Database Time Integration Test ===")
    
    # Test with a sample database path
    test_db_path = "data/test_time_integration.db"
    
    db_time = DatabaseTimeIntegration(test_db_path)
    
    # Test adding a game record
    record_id = db_time.add_game_record(
        username="TestPlayer",
        house="TestHouse", 
        stake=100,
        players=4,
        total_calls=25,
        commission_percent=10,
        fee=10,
        total_prize=360,
        details='{"pattern": "Full House", "test": true}',
        status="completed"
    )
    
    if record_id:
        print(f"Test game record created with ID: {record_id}")
    
    # Test daily stats update
    db_time.update_daily_stats()
    
    # Test time consistency check
    consistency = db_time.check_time_consistency_in_db()
    print(f"Time consistency check: {consistency}")
    
    # Log activation
    log_time_system_activation(test_db_path)
    
    print("Database time integration test completed!")