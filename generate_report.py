#!/usr/bin/env python
"""
Report Generator CLI for WOW Bingo

This script provides a command-line interface for generating comprehensive reports
from the WOW Bingo statistics database.
"""

import os
import sys
import argparse
from stats_export import export_all_data

def main():
    """Main entry point for the report generator CLI."""
    parser = argparse.ArgumentParser(description='Generate comprehensive reports from WOW Bingo statistics')
    
    # Add arguments
    parser.add_argument('--format', '-f', choices=['pdf', 'html', 'csv'], default='pdf',
                        help='Report format (default: pdf)')
    parser.add_argument('--output', '-o', type=str, default=None,
                        help='Output file path (default: auto-generated)')
    parser.add_argument('--open', '-O', action='store_true',
                        help='Automatically open the generated report')
    parser.add_argument('--simple', '-s', action='store_true',
                        help='Generate simple reports instead of comprehensive ones')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Generate report
    print(f"Generating {args.format.upper()} report...")
    
    result = export_all_data(
        format_type=args.format,
        use_comprehensive=not args.simple,
        auto_open=args.open
    )
    
    # Print results
    if 'comprehensive_report' in result:
        report_path = result['comprehensive_report']['result']
        print(f"Comprehensive report generated successfully!")
        print(f"Report saved to: {os.path.abspath(report_path)}")
        
        if not args.open:
            print("To open the report, use the --open flag or open it manually.")
    else:
        print("Individual reports generated:")
        for report_type, report_info in result.items():
            if report_type != 'opened_file':
                if report_info['success']:
                    print(f"  - {report_type}: {os.path.abspath(report_info['result'])}")
                else:
                    print(f"  - {report_type}: Failed - {report_info['result']}")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)