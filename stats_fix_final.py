#!/usr/bin/env python3
"""
FINAL PRECISE FIX for the stats page game history blinking issue.

ROOT CAUSE ANALYSIS:
The blinking occurs because draw_game_history() is called every frame, and each time
it re-evaluates the loading state, which can fluctuate due to:
1. Database queries being performed every frame
2. Loading state checks happening repeatedly
3. No stable caching of the final display decision

PRECISE SOLUTION:
1. Cache the loading state decision once it's made
2. Prevent re-evaluation of loading state on every frame
3. Only update the display state when data actually changes
4. Add frame-level caching to prevent redundant operations
"""

import time

def apply_final_precise_fix():
    """Apply the final precise fix for the blinking issue."""
    
    print("🎯 FINAL PRECISE FIX - Stats Page Blinking")
    print("=" * 60)
    
    try:
        # Read the current file
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_filename = f'stats_page_final_fix_backup_{int(time.time())}.py'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📁 Created backup: {backup_filename}")
        
        fixes_applied = []
        
        # Fix 1: Add frame-level caching to prevent re-evaluation every frame
        init_addition = '''        # FINAL FIX: Frame-level caching to prevent blinking
        self._display_state_cache = {}
        self._last_display_evaluation = 0
        self._display_cache_timeout = 1.0  # Cache display decisions for 1 second'''
        
        # Find the initialization section and add caching
        init_pattern = '''        self._refresh_debounce = {}'''
        if init_pattern in content:
            content = content.replace(init_pattern, init_pattern + '\n' + init_addition)
            fixes_applied.append("Added frame-level display state caching")
        
        # Fix 2: Create a method to get cached display state
        cached_state_method = '''
    def get_cached_display_state(self, section_name):
        """FINAL FIX: Get cached display state to prevent re-evaluation every frame."""
        current_time = time.time()
        
        # Check if we have a valid cached state
        if (section_name in self._display_state_cache and 
            current_time - self._last_display_evaluation < self._display_cache_timeout):
            return self._display_state_cache[section_name]
        
        # Need to evaluate - but only do this once per second maximum
        return None
    
    def set_cached_display_state(self, section_name, state):
        """FINAL FIX: Cache display state to prevent blinking."""
        self._display_state_cache[section_name] = state
        self._last_display_evaluation = time.time()
    '''
        
        # Add the method if it doesn't exist
        if 'def get_cached_display_state(self, section_name):' not in content:
            insertion_point = content.find('    def should_render_section(self, section_name):')
            if insertion_point != -1:
                content = content[:insertion_point] + cached_state_method + '\n' + content[insertion_point:]
                fixes_applied.append("Added cached display state methods")
        
        # Fix 3: Replace the problematic loading state evaluation with cached version
        old_loading_evaluation = '''            # Display appropriate message based on loading state and database content
            # ANTI-BLINK FIX: Use stable loading state to prevent continuous blinking
            loading_complete = (hasattr(self, '_stable_loading_complete') and self._stable_loading_complete) or \\
                             (hasattr(self, 'game_history_loading_complete') and self.game_history_loading_complete)
            
            if loading_complete:'''
        
        new_loading_evaluation = '''            # Display appropriate message based on loading state and database content
            # FINAL FIX: Use cached display state to prevent frame-by-frame re-evaluation
            cached_state = self.get_cached_display_state('game_history_loading')
            
            if cached_state is not None:
                # Use cached state to prevent blinking
                loading_complete = cached_state
            else:
                # Evaluate once and cache the result
                loading_complete = (hasattr(self, '_stable_loading_complete') and self._stable_loading_complete) or \\
                                 (hasattr(self, 'game_history_loading_complete') and self.game_history_loading_complete)
                self.set_cached_display_state('game_history_loading', loading_complete)
                print(f"FINAL FIX: Cached loading state - {loading_complete}")
            
            if loading_complete:'''
        
        if old_loading_evaluation in content:
            content = content.replace(old_loading_evaluation, new_loading_evaluation)
            fixes_applied.append("Replaced loading state evaluation with cached version")
        
        # Fix 4: Prevent database queries on every frame
        old_db_check = '''            # Check if this is a post-cleanup state
            is_post_cleanup = False
            if STATS_DB_AVAILABLE:
                try:
                    # Try to get a direct count from the database
                    from stats_db import get_stats_db_manager
                    # Use hybrid DB if available
                    if HYBRID_DB_AVAILABLE:
                        stats_db = get_hybrid_db_integration()
                    else:
                        stats_db = get_stats_db_manager()
                    with stats_db.get_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM game_history")
                        count = cursor.fetchone()[0]
                        is_post_cleanup = (count == 0)
                except Exception:
                    pass'''
        
        new_db_check = '''            # Check if this is a post-cleanup state
            # FINAL FIX: Cache database check to prevent queries every frame
            cached_cleanup_state = self.get_cached_display_state('post_cleanup_check')
            
            if cached_cleanup_state is not None:
                is_post_cleanup = cached_cleanup_state
            else:
                is_post_cleanup = False
                if STATS_DB_AVAILABLE:
                    try:
                        # Try to get a direct count from the database
                        from stats_db import get_stats_db_manager
                        # Use hybrid DB if available
                        if HYBRID_DB_AVAILABLE:
                            stats_db = get_hybrid_db_integration()
                        else:
                            stats_db = get_stats_db_manager()
                        with stats_db.get_connection_context() as conn:
                            cursor = conn.cursor()
                            cursor.execute("SELECT COUNT(*) FROM game_history")
                            count = cursor.fetchone()[0]
                            is_post_cleanup = (count == 0)
                        self.set_cached_display_state('post_cleanup_check', is_post_cleanup)
                        print(f"FINAL FIX: Cached post-cleanup state - {is_post_cleanup}")
                    except Exception:
                        pass'''
        
        if old_db_check in content:
            content = content.replace(old_db_check, new_db_check)
            fixes_applied.append("Added database query caching")
        
        # Fix 5: Add a method to invalidate cache when data actually changes
        cache_invalidation_method = '''
    def invalidate_display_cache(self, reason="data_changed"):
        """FINAL FIX: Invalidate display cache when data actually changes."""
        self._display_state_cache.clear()
        self._last_display_evaluation = 0
        print(f"FINAL FIX: Display cache invalidated - {reason}")
    '''
        
        # Add the method if it doesn't exist
        if 'def invalidate_display_cache(self, reason="data_changed"):' not in content:
            insertion_point = content.find('    def set_cached_display_state(self, section_name, state):')
            if insertion_point != -1:
                # Find the end of that method
                method_end = content.find('\n    def ', insertion_point + 1)
                if method_end != -1:
                    content = content[:method_end] + cache_invalidation_method + content[method_end:]
                    fixes_applied.append("Added cache invalidation method")
        
        # Fix 6: Invalidate cache when game history is actually updated
        old_history_update = '''                print(f"ANTI-BLINK: Game history updated - new count: {len(self.game_history)}")'''
        new_history_update = '''                print(f"ANTI-BLINK: Game history updated - new count: {len(self.game_history)}")
                # FINAL FIX: Invalidate display cache when data changes
                self.invalidate_display_cache("game_history_updated")'''
        
        if old_history_update in content:
            content = content.replace(old_history_update, new_history_update)
            fixes_applied.append("Added cache invalidation on data updates")
        
        # Write the fixed content
        with open('stats_page.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Successfully applied FINAL PRECISE FIXES:")
        for fix in fixes_applied:
            print(f"   • {fix}")
        
        if fixes_applied:
            print("\n🎯 ROOT CAUSE ADDRESSED!")
            print("   • Frame-level caching prevents re-evaluation every frame")
            print("   • Database queries cached for 1 second")
            print("   • Loading state cached to prevent fluctuation")
            print("   • Cache invalidated only when data actually changes")
            print("\n🚀 The blinking should now be COMPLETELY ELIMINATED!")
            return True
        else:
            print("\n⚠️  No fixes were applied - patterns may have changed.")
            return False
        
    except Exception as e:
        print(f"❌ Error applying final precise fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_final_precise_fix():
    """Verify that the final precise fix has been applied correctly."""
    
    print("\n🧪 Verifying Final Precise Fix")
    print("-" * 35)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key components of the final fix
        checks = [
            ('_display_state_cache', 'Display state caching'),
            ('get_cached_display_state', 'Cached state retrieval'),
            ('set_cached_display_state', 'Cached state storage'),
            ('invalidate_display_cache', 'Cache invalidation'),
            ('FINAL FIX: Cached loading state', 'Loading state caching'),
            ('FINAL FIX: Cached post-cleanup state', 'Database query caching'),
            ('_display_cache_timeout = 1.0', 'Cache timeout setting'),
        ]
        
        all_present = True
        for pattern, description in checks:
            if pattern in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - Missing")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error verifying final precise fix: {e}")
        return False

def main():
    """Apply and verify the final precise fix."""
    
    print("🎯 FINAL PRECISE STATS PAGE BLINKING FIX")
    print("=" * 70)
    
    print("📋 ROOT CAUSE IDENTIFIED:")
    print("   • draw_game_history() called every frame")
    print("   • Loading state re-evaluated every frame")
    print("   • Database queries performed repeatedly")
    print("   • No caching of display decisions")
    print()
    
    print("🔧 PRECISE SOLUTION:")
    print("   • Frame-level caching of display states")
    print("   • 1-second cache timeout for stability")
    print("   • Database query caching")
    print("   • Cache invalidation only on real data changes")
    print()
    
    # Apply the final precise fix
    fix_success = apply_final_precise_fix()
    
    if fix_success:
        # Verify the fix
        verify_success = verify_final_precise_fix()
        
        if verify_success:
            print("\n🎉 FINAL PRECISE FIX COMPLETED SUCCESSFULLY!")
            print("\n✨ BLINKING ISSUE SHOULD NOW BE COMPLETELY RESOLVED!")
            print("\n🔍 How it works:")
            print("   • Display state evaluated once per second maximum")
            print("   • Results cached to prevent frame-by-frame re-evaluation")
            print("   • Database queries cached to prevent repeated execution")
            print("   • Cache invalidated only when data actually changes")
            print("\n🚀 Expected behavior:")
            print("   • Game history section displays immediately")
            print("   • No blinking or flickering whatsoever")
            print("   • Stable display until data actually changes")
            print("   • Improved performance due to reduced database queries")
        else:
            print("\n⚠️  Fix applied but verification had issues")
    else:
        print("\n❌ Final precise fix failed to apply")
    
    print("\n📋 TESTING INSTRUCTIONS:")
    print("   1. Open the stats page in your application")
    print("   2. Watch the game history section for 30+ seconds")
    print("   3. It should remain completely stable without ANY blinking")
    print("   4. Check console for 'FINAL FIX: Cached' messages")
    print("   5. The section should only change when you add new games")
    
    return fix_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)