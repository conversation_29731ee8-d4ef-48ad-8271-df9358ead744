"""
Performance Monitoring Dashboard for Stats Page

This script provides a simple dashboard to monitor stats page performance.
Run this script while the stats page is running to see real-time performance metrics.
"""

import time
import psutil
import os
from datetime import datetime

class PerformanceDashboard:
    def __init__(self):
        self.running = False
        self.process = psutil.Process(os.getpid())
        
    def start_monitoring(self, interval=5):
        """Start the performance monitoring dashboard."""
        self.running = True
        print("📊 Performance Dashboard Started")
        print("=" * 60)
        
        try:
            while self.running:
                self.display_metrics()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n📊 Performance monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
    
    def display_metrics(self):
        """Display current performance metrics."""
        try:
            # Get system metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # Get system-wide metrics
            system_cpu = psutil.cpu_percent()
            system_memory = psutil.virtual_memory()
            
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            print(f"[{timestamp}] Stats Page Performance:")
            print(f"  CPU Usage: {cpu_percent:.1f}% (System: {system_cpu:.1f}%)")
            print(f"  Memory: {memory_mb:.1f} MB ({memory_info.rss / 1024 / 1024:.1f} MB RSS)")
            print(f"  System Memory: {system_memory.percent:.1f}% used")
            
            # Check for performance issues
            if cpu_percent > 50:
                print("  ⚠️  High CPU usage detected")
            if memory_mb > 200:
                print("  ⚠️  High memory usage detected")
            if system_cpu > 80:
                print("  ⚠️  System CPU under heavy load")
                
            print("-" * 60)
            
        except Exception as e:
            print(f"Error displaying metrics: {e}")
    
    def stop_monitoring(self):
        """Stop the performance monitoring."""
        self.running = False

def main():
    dashboard = PerformanceDashboard()
    print("Starting performance monitoring for Stats Page...")
    print("Press Ctrl+C to stop monitoring")
    dashboard.start_monitoring(interval=10)

if __name__ == "__main__":
    main()
