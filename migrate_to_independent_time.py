"""
Mi<PERSON> Script for Independent Time System

This script helps migrate your existing WOW Games application to use
the independent time system. It:

1. Backs up existing databases
2. Integrates the time manager into existing code
3. Updates key files to use independent time
4. Preserves existing historical data
5. Logs the migration process
"""

import os
import shutil
import sqlite3
from datetime import datetime
import json

def backup_databases():
    """Create backups of existing databases before migration."""
    print("🔄 Creating database backups...")
    
    backup_dir = f"backup_before_time_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # List of database files to backup
    db_files = [
        'data/stats.db',
        'data/stats_new.db', 
        'data/vouchers.db',
        'data/centralized_vouchers.db',
        'data/voucher_validation.db'
    ]
    
    backed_up = []
    for db_file in db_files:
        if os.path.exists(db_file):
            backup_path = os.path.join(backup_dir, os.path.basename(db_file))
            shutil.copy2(db_file, backup_path)
            backed_up.append(db_file)
            print(f"  ✅ Backed up: {db_file} -> {backup_path}")
    
    print(f"📁 Backup directory: {backup_dir}")
    print(f"✅ Backed up {len(backed_up)} database files")
    return backup_dir

def update_stats_db_file():
    """Update stats_db.py to use independent time system."""
    print("🔄 Updating stats_db.py...")
    
    stats_db_path = "stats_db.py"
    if not os.path.exists(stats_db_path):
        print(f"  ⚠️  {stats_db_path} not found, skipping...")
        return False
    
    # Read current file
    with open(stats_db_path, 'r') as f:
        content = f.read()
    
    # Create backup
    backup_path = f"{stats_db_path}.backup_before_time_migration"
    with open(backup_path, 'w') as f:
        f.write(content)
    print(f"  📁 Created backup: {backup_path}")
    
    # Add import for independent time system
    if 'from time_manager import' not in content:
        import_line = "from time_manager import independent_now, independent_strftime, independent_utc_now\n"
        
        # Find a good place to add the import (after existing imports)
        lines = content.split('\n')
        insert_index = 0
        
        for i, line in enumerate(lines):
            if line.startswith('from datetime import'):
                insert_index = i + 1
                break
            elif line.startswith('import ') and not line.startswith('import os'):
                insert_index = i + 1
        
        lines.insert(insert_index, import_line)
        content = '\n'.join(lines)
    
    # Replace datetime.now() calls with independent_now()
    replacements = [
        ("datetime.now().strftime('%Y-%m-%d %H:%M:%S')", "independent_strftime('%Y-%m-%d %H:%M:%S')"),
        ("datetime.now().strftime('%Y-%m-%d')", "independent_strftime('%Y-%m-%d')"),
        ("datetime.now()", "independent_now()"),
    ]
    
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"  🔄 Replaced: {old} -> {new}")
    
    # Write updated file
    with open(stats_db_path, 'w') as f:
        f.write(content)
    
    print(f"  ✅ Updated {stats_db_path}")
    return True

def update_add_test_game_data():
    """Update add_test_game_data.py to use independent time system."""
    print("🔄 Updating add_test_game_data.py...")
    
    file_path = "add_test_game_data.py"
    if not os.path.exists(file_path):
        print(f"  ⚠️  {file_path} not found, skipping...")
        return False
    
    # Read current file with proper encoding
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # Try with different encoding
        with open(file_path, 'r', encoding='latin-1') as f:
            content = f.read()
    
    # Create backup
    backup_path = f"{file_path}.backup_before_time_migration"
    with open(backup_path, 'w') as f:
        f.write(content)
    print(f"  📁 Created backup: {backup_path}")
    
    # Add import for independent time system
    if 'from time_manager import' not in content:
        import_line = "from time_manager import independent_now, independent_strftime\n"
        
        # Add after datetime import
        content = content.replace(
            'from datetime import datetime, timedelta',
            'from datetime import datetime, timedelta\n' + import_line
        )
    
    # Replace datetime.now() with independent_now()
    replacements = [
        ("today = datetime.now()", "today = independent_now()"),
        ("now = datetime.now()", "now = independent_now()"),
        ("game_time.strftime('%Y-%m-%d %H:%M:%S')", "independent_strftime('%Y-%m-%d %H:%M:%S')"),
        ("now.strftime('%Y-%m-%d %H:%M:%S')", "independent_strftime('%Y-%m-%d %H:%M:%S')"),
    ]
    
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"  🔄 Replaced: {old} -> {new}")
    
    # Write updated file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"  ✅ Updated {file_path}")
    return True

def update_stats_page():
    """Update stats_page.py to use independent time system."""
    print("🔄 Updating stats_page.py...")
    
    file_path = "stats_page.py"
    if not os.path.exists(file_path):
        print(f"  ⚠️  {file_path} not found, skipping...")
        return False
    
    # Read current file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Create backup
    backup_path = f"{file_path}.backup_before_time_migration"
    with open(backup_path, 'w') as f:
        f.write(content)
    print(f"  📁 Created backup: {backup_path}")
    
    # Add import for independent time system
    if 'from time_manager import' not in content:
        import_line = "from time_manager import independent_now, independent_strftime, get_time_stats\n"
        
        # Add after datetime import
        content = content.replace(
            'from datetime import datetime, timedelta',
            'from datetime import datetime, timedelta\n' + import_line
        )
    
    # Replace key datetime.now() calls
    replacements = [
        ("datetime.now()", "independent_now()"),
        ("end_date = datetime.now()", "end_date = independent_now()"),
        ("today = datetime.now()", "today = independent_now()"),
    ]
    
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"  🔄 Replaced: {old} -> {new}")
    
    # Write updated file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"  ✅ Updated {file_path}")
    return True

def create_migration_log():
    """Create a detailed migration log."""
    print("📝 Creating migration log...")
    
    from time_manager import get_time_manager, get_time_stats
    
    # Initialize time manager to get stats
    time_stats = get_time_stats()
    
    migration_log = {
        'migration_date': datetime.now().isoformat(),
        'independent_time_at_migration': time_stats['current_independent_time'],
        'system_time_at_migration': time_stats['current_system_time'],
        'reference_time': time_stats['reference_time'],
        'startup_count': time_stats['startup_count'],
        'migration_details': {
            'purpose': 'Migrate to independent time system immune to PC clock changes',
            'approach': 'Monotonic clock + stored reference time',
            'backward_compatibility': 'Existing data preserved, new records use independent time',
            'files_updated': [
                'stats_db.py',
                'add_test_game_data.py', 
                'stats_page.py'
            ]
        },
        'benefits': [
            'Immunity to system clock changes',
            'Consistent timestamps in database',
            'Accurate statistics and history',
            'Reliable time progression'
        ]
    }
    
    log_file = f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(log_file, 'w') as f:
        json.dump(migration_log, f, indent=2)
    
    print(f"  ✅ Migration log created: {log_file}")
    return log_file

def test_independent_time_system():
    """Test the independent time system after migration."""
    print("🧪 Testing independent time system...")
    
    try:
        from time_manager import independent_now, independent_strftime, get_time_stats, check_clock_changes
        
        # Test basic functions
        current_time = independent_now()
        formatted_time = independent_strftime()
        stats = get_time_stats()
        
        print(f"  ✅ Independent time: {current_time}")
        print(f"  ✅ Formatted time: {formatted_time}")
        print(f"  ✅ Startup count: {stats['startup_count']}")
        print(f"  ✅ Days since first startup: {stats['elapsed_since_first_startup']['days']}")
        
        # Test clock change detection
        clock_check = check_clock_changes()
        print(f"  ✅ Clock change detection: {'Working' if not clock_check.get('error') else 'Error'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing time system: {e}")
        return False

def main():
    """Run the complete migration process."""
    print("🚀 Starting migration to Independent Time System")
    print("=" * 60)
    
    try:
        # Step 1: Backup databases
        backup_dir = backup_databases()
        print()
        
        # Step 2: Update key files
        update_stats_db_file()
        print()
        
        update_add_test_game_data()
        print()
        
        update_stats_page()
        print()
        
        # Step 3: Create migration log
        log_file = create_migration_log()
        print()
        
        # Step 4: Test the system
        test_success = test_independent_time_system()
        print()
        
        # Step 5: Log to database
        try:
            from db_time_integration import log_time_system_activation
            log_time_system_activation('data/stats.db')
            print("✅ Logged activation to database")
        except Exception as e:
            print(f"⚠️  Could not log to database: {e}")
        
        print("=" * 60)
        if test_success:
            print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            print()
            print("✅ Your application now uses an independent time system that is:")
            print("   • Immune to PC clock changes")
            print("   • Based on monotonic clock + stored reference")
            print("   • Consistent across all database operations")
            print("   • Backward compatible with existing data")
            print()
            print(f"📁 Backups created in: {backup_dir}")
            print(f"📝 Migration log: {log_file}")
            print()
            print("🔄 Next steps:")
            print("   1. Test your application thoroughly")
            print("   2. Monitor the time system with get_time_stats()")
            print("   3. Check for clock changes with check_clock_changes()")
        else:
            print("❌ MIGRATION COMPLETED WITH WARNINGS")
            print("   Please check the error messages above and test manually")
        
    except Exception as e:
        print(f"❌ MIGRATION FAILED: {e}")
        print("   Please check the error and try again")
        print(f"   Your original files are backed up in: {backup_dir if 'backup_dir' in locals() else 'backup directory'}")

if __name__ == "__main__":
    main()