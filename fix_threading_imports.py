#!/usr/bin/env python3
"""
Fix all local threading imports that are causing the 'threading referenced before assignment' error.

The issue is that there are many local 'import threading' statements throughout the file
that create local variables and shadow the global threading import.
"""

import re
import time

def fix_threading_imports():
    """Remove all local threading imports and use the global one."""
    
    print("🔧 Fixing Threading Import Issues")
    print("=" * 50)
    
    try:
        # Read the current file
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_filename = f'stats_page_threading_fix_backup_{int(time.time())}.py'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📁 Created backup: {backup_filename}")
        
        # Count original local imports
        local_imports = content.count('import threading')
        print(f"🔍 Found {local_imports} threading import statements")
        
        # Remove local threading imports (but keep the global one at the top)
        lines = content.split('\n')
        fixed_lines = []
        global_import_found = False
        local_imports_removed = 0
        
        for i, line in enumerate(lines):
            # Keep the global import at the top (first 20 lines)
            if i < 20 and 'import threading' in line and not line.strip().startswith('#'):
                fixed_lines.append(line)
                global_import_found = True
                print(f"✅ Kept global threading import at line {i+1}")
            # Remove local imports in functions/methods
            elif 'import threading' in line and not line.strip().startswith('#'):
                # Check if this is a local import (indented)
                if line.startswith('    ') or line.startswith('\t'):
                    print(f"🗑️  Removed local threading import at line {i+1}: {line.strip()}")
                    local_imports_removed += 1
                    # Skip this line (don't add to fixed_lines)
                    continue
                else:
                    # Keep non-indented imports (shouldn't happen after line 20, but just in case)
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        if not global_import_found:
            print("❌ Global threading import not found! Adding it...")
            # Find the import section and add threading import
            for i, line in enumerate(fixed_lines[:20]):
                if line.startswith('import ') and 'threading' not in line:
                    # Insert threading import after other imports
                    if 'import random' in line:
                        fixed_lines.insert(i+1, 'import threading')
                        print("✅ Added global threading import")
                        break
        
        # Join the lines back
        fixed_content = '\n'.join(fixed_lines)
        
        # Write the fixed content
        with open('stats_page.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ Successfully removed {local_imports_removed} local threading imports")
        print(f"✅ Kept 1 global threading import at the top")
        
        # Verify the fix
        final_count = fixed_content.count('import threading')
        print(f"🔍 Final threading import count: {final_count}")
        
        if final_count == 1:
            print("🎉 Perfect! Only one global threading import remains")
            return True
        else:
            print(f"⚠️  Expected 1 threading import, found {final_count}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing threading imports: {e}")
        return False

def verify_fix():
    """Verify that the fix worked correctly."""
    
    print("\n🧪 Verifying Threading Fix")
    print("-" * 30)
    
    try:
        with open('stats_page.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Check for global import
        global_import_line = None
        for i, line in enumerate(lines[:20]):
            if 'import threading' in line and not line.strip().startswith('#'):
                global_import_line = i + 1
                break
        
        if global_import_line:
            print(f"✅ Global threading import found at line {global_import_line}")
        else:
            print("❌ Global threading import not found")
            return False
        
        # Check for any remaining local imports
        local_imports = []
        for i, line in enumerate(lines[20:], 21):  # Start from line 21
            if 'import threading' in line and not line.strip().startswith('#'):
                if line.startswith('    ') or line.startswith('\t'):
                    local_imports.append(i)
        
        if local_imports:
            print(f"❌ Found {len(local_imports)} remaining local imports at lines: {local_imports}")
            return False
        else:
            print("✅ No local threading imports found")
        
        # Test compilation
        try:
            compile(content, 'stats_page.py', 'exec')
            print("✅ File compiles without syntax errors")
        except SyntaxError as e:
            print(f"❌ Syntax error: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

def main():
    """Main function to fix and verify threading imports."""
    
    print("🔧 Threading Import Fix Tool")
    print("=" * 60)
    
    # Apply the fix
    fix_success = fix_threading_imports()
    
    if fix_success:
        # Verify the fix
        verify_success = verify_fix()
        
        if verify_success:
            print("\n🎉 THREADING FIX COMPLETED SUCCESSFULLY!")
            print("   • All local threading imports removed")
            print("   • Global threading import preserved")
            print("   • File compiles without errors")
            print("   • Stats page should now open without threading errors")
        else:
            print("\n⚠️  FIX APPLIED BUT VERIFICATION FAILED")
            print("   • Check the verification errors above")
            print("   • Manual review may be needed")
    else:
        print("\n❌ THREADING FIX FAILED")
        print("   • Check the error messages above")
        print("   • Manual intervention may be required")
    
    print("\n🚀 Next Steps:")
    print("   1. Try opening the stats page in your application")
    print("   2. The 'threading referenced before assignment' error should be gone")
    print("   3. If issues persist, check the console for other errors")
    
    return fix_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)