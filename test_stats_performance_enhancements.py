"""
Test Stats Page Performance Enhancements

This script tests all the performance enhancements that have been applied to the stats page
to ensure they are working correctly and providing the expected performance improvements.
"""

import time
import threading
import os
import sys
import json
from datetime import datetime
import pygame

class StatsPerformanceTester:
    """Test suite for stats page performance enhancements."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
    def run_all_tests(self):
        """Run all performance tests."""
        
        print("🧪 Testing Stats Page Performance Enhancements")
        print("=" * 60)
        print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        tests = [
            ("Configuration File", self.test_performance_config),
            ("Stats Page Import", self.test_stats_page_import),
            ("Performance Enhancer", self.test_performance_enhancer),
            ("Memory Optimization", self.test_memory_optimization),
            ("Caching System", self.test_caching_system),
            ("Database Optimization", self.test_database_optimization),
            ("Monitoring Tools", self.test_monitoring_tools),
            ("Frame Rate Control", self.test_frame_rate_control)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"🔍 Testing {test_name}...")
            try:
                result = test_func()
                if result:
                    print(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name}: FAILED")
                self.test_results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
            print()
        
        # Summary
        print("=" * 60)
        print(f"🎯 Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Performance enhancements are working correctly.")
        elif passed_tests >= total_tests * 0.75:
            print("✅ Most tests passed. Performance enhancements are mostly working.")
        else:
            print("⚠️  Several tests failed. Some performance enhancements may not be working.")
        
        print(f"Total test time: {time.time() - self.start_time:.2f} seconds")
        print("=" * 60)
        
        return passed_tests, total_tests
    
    def test_performance_config(self):
        """Test if performance configuration file exists and is valid."""
        try:
            config_path = "d:/GAME PROJECTS/LAST-GAME_CONCEPT-/data/performance_config.json"
            
            if not os.path.exists(config_path):
                print("  ❌ Performance config file not found")
                return False
            
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            required_sections = ['performance_settings', 'ui_optimizations', 'database_optimizations', 'memory_management']
            
            for section in required_sections:
                if section not in config:
                    print(f"  ❌ Missing config section: {section}")
                    return False
            
            print(f"  ✅ Config file valid with {len(config)} sections")
            return True
            
        except Exception as e:
            print(f"  ❌ Config test error: {e}")
            return False
    
    def test_stats_page_import(self):
        """Test if the enhanced stats page can be imported successfully."""
        try:
            # Test import
            from stats_page import StatsPage
            print("  ✅ Stats page imported successfully")
            
            # Test if performance enhancements are integrated
            pygame.init()
            test_screen = pygame.display.set_mode((800, 600))
            
            stats_page = StatsPage(test_screen)
            
            # Check for performance enhancer
            if hasattr(stats_page, '_performance_enhancer'):
                print("  ✅ Performance enhancer integrated")
            else:
                print("  ⚠️  Performance enhancer not found")
            
            # Check for performance monitor
            if hasattr(stats_page, '_perf_monitor'):
                print("  ✅ Performance monitor integrated")
            else:
                print("  ⚠️  Performance monitor not found")
            
            # Check for memory optimization
            if hasattr(stats_page, 'optimize_memory_usage'):
                print("  ✅ Memory optimization method available")
            else:
                print("  ❌ Memory optimization method not found")
                return False
            
            pygame.quit()
            return True
            
        except Exception as e:
            print(f"  ❌ Import test error: {e}")
            return False
    
    def test_performance_enhancer(self):
        """Test the performance enhancer functionality."""
        try:
            from enhanced_stats_performance_optimizer import enhance_stats_page_performance
            print("  ✅ Performance enhancer module imported")
            
            # Test with a mock stats page
            class MockStatsPage:
                def __init__(self):
                    self.screen = None
                    self.stats_provider = MockStatsProvider()
            
            class MockStatsProvider:
                def get_daily_earnings(self, date_str):
                    return 100.0
            
            mock_stats = MockStatsPage()
            enhancer = enhance_stats_page_performance(mock_stats)
            
            if enhancer:
                print("  ✅ Performance enhancer applied successfully")
                
                # Test performance info
                if hasattr(mock_stats, 'get_performance_info'):
                    perf_info = mock_stats.get_performance_info()
                    print(f"  ✅ Performance info available: {len(perf_info)} metrics")
                else:
                    print("  ⚠️  Performance info method not added")
                
                return True
            else:
                print("  ❌ Performance enhancer failed to apply")
                return False
                
        except Exception as e:
            print(f"  ❌ Performance enhancer test error: {e}")
            return False
    
    def test_memory_optimization(self):
        """Test memory optimization functionality."""
        try:
            # Test memory monitoring
            try:
                import psutil
                process = psutil.Process(os.getpid())
                memory_before = process.memory_info().rss / 1024 / 1024
                print(f"  ✅ Memory monitoring available: {memory_before:.1f} MB")
            except ImportError:
                print("  ⚠️  psutil not available for memory monitoring")
            
            # Test garbage collection
            import gc
            collected = gc.collect()
            print(f"  ✅ Garbage collection working: {collected} objects collected")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Memory optimization test error: {e}")
            return False
    
    def test_caching_system(self):
        """Test the caching system performance."""
        try:
            from enhanced_stats_performance_optimizer import PerformanceCache
            
            cache = PerformanceCache(max_size=100, default_ttl=5)
            
            # Test cache operations
            start_time = time.time()
            
            # Write test
            for i in range(50):
                cache.set(f"test_key_{i}", f"test_value_{i}")
            
            # Read test
            hits = 0
            for i in range(50):
                if cache.get(f"test_key_{i}") is not None:
                    hits += 1
            
            cache_time = time.time() - start_time
            
            print(f"  ✅ Cache operations: {cache_time:.3f}s for 100 operations")
            print(f"  ✅ Cache hit rate: {hits}/50 = {(hits/50)*100:.1f}%")
            
            return hits > 40  # At least 80% hit rate
            
        except Exception as e:
            print(f"  ❌ Caching test error: {e}")
            return False
    
    def test_database_optimization(self):
        """Test database optimization features."""
        try:
            # Test timeout function
            from stats_page import execute_db_query_with_timeout
            
            def mock_db_query():
                time.sleep(0.1)  # Simulate quick query
                return "test_result"
            
            def slow_db_query():
                time.sleep(2.0)  # Simulate slow query
                return "slow_result"
            
            # Test quick query
            start_time = time.time()
            result = execute_db_query_with_timeout(mock_db_query, timeout=1.0)
            quick_time = time.time() - start_time
            
            if result == "test_result":
                print(f"  ✅ Quick query completed: {quick_time:.3f}s")
            else:
                print("  ❌ Quick query failed")
                return False
            
            # Test timeout handling
            start_time = time.time()
            result = execute_db_query_with_timeout(slow_db_query, timeout=0.5)
            timeout_time = time.time() - start_time
            
            if result is None and timeout_time < 1.0:
                print(f"  ✅ Timeout handling works: {timeout_time:.3f}s")
            else:
                print("  ❌ Timeout handling failed")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ Database optimization test error: {e}")
            return False
    
    def test_monitoring_tools(self):
        """Test monitoring tools availability."""
        try:
            # Test performance dashboard
            dashboard_path = "d:/GAME PROJECTS/LAST-GAME_CONCEPT-/stats_performance_dashboard.py"
            if os.path.exists(dashboard_path):
                print("  ✅ Performance dashboard available")
            else:
                print("  ❌ Performance dashboard not found")
                return False
            
            # Test summary file
            summary_path = "d:/GAME PROJECTS/LAST-GAME_CONCEPT-/PERFORMANCE_OPTIMIZATION_SUMMARY.txt"
            if os.path.exists(summary_path):
                print("  ✅ Performance summary available")
            else:
                print("  ❌ Performance summary not found")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ Monitoring tools test error: {e}")
            return False
    
    def test_frame_rate_control(self):
        """Test frame rate control functionality."""
        try:
            # Test pygame clock functionality
            pygame.init()
            clock = pygame.time.Clock()
            
            # Test frame rate measurement
            start_time = time.time()
            frames = 0
            
            for _ in range(10):  # Test 10 frames
                clock.tick(60)  # Target 60 FPS
                frames += 1
            
            elapsed_time = time.time() - start_time
            actual_fps = frames / elapsed_time
            
            print(f"  ✅ Frame rate control: {actual_fps:.1f} FPS achieved")
            
            pygame.quit()
            
            # Frame rate should be reasonable (not too high or too low)
            return 30 <= actual_fps <= 120
            
        except Exception as e:
            print(f"  ❌ Frame rate test error: {e}")
            return False
    
    def generate_test_report(self):
        """Generate a detailed test report."""
        
        report = f"""
🧪 Stats Page Performance Enhancement Test Report
===============================================
Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Test Duration: {time.time() - self.start_time:.2f} seconds

TEST RESULTS:
"""
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            report += f"  {test_name}: {status}\n"
        
        passed = sum(1 for r in self.test_results.values() if r)
        total = len(self.test_results)
        
        report += f"""
SUMMARY:
  Tests Passed: {passed}/{total} ({(passed/total)*100:.1f}%)
  Overall Status: {"✅ SUCCESS" if passed == total else "⚠️ PARTIAL" if passed >= total*0.75 else "❌ FAILED"}

RECOMMENDATIONS:
"""
        
        if passed == total:
            report += "  🎉 All performance enhancements are working correctly!\n"
            report += "  🚀 Your stats page should be significantly faster and more responsive.\n"
        elif passed >= total * 0.75:
            report += "  ✅ Most enhancements are working. Check failed tests for issues.\n"
            report += "  🔧 Consider re-running the optimization scripts for failed components.\n"
        else:
            report += "  ⚠️  Several enhancements are not working properly.\n"
            report += "  🔧 Re-run the performance optimization scripts.\n"
            report += "  📞 Check console output for error messages.\n"
        
        report += f"""
NEXT STEPS:
  1. Review any failed tests above
  2. Restart your application to ensure all changes are loaded
  3. Monitor performance using the dashboard: python stats_performance_dashboard.py
  4. Check console output for performance metrics during normal usage

===============================================
"""
        
        try:
            with open("d:/GAME PROJECTS/LAST-GAME_CONCEPT-/PERFORMANCE_TEST_REPORT.txt", 'w', encoding='utf-8') as f:
                f.write(report)
            print("📄 Test report saved: PERFORMANCE_TEST_REPORT.txt")
        except Exception as e:
            print(f"❌ Error saving test report: {e}")
        
        return report

def main():
    """Main function to run performance tests."""
    
    print("🧪 Stats Page Performance Enhancement Testing")
    print("=" * 60)
    
    tester = StatsPerformanceTester()
    passed, total = tester.run_all_tests()
    
    print("\n📄 Generating detailed test report...")
    report = tester.generate_test_report()
    
    print("\n" + "=" * 60)
    print("🎯 TESTING COMPLETE")
    
    if passed == total:
        print("🎉 ALL PERFORMANCE ENHANCEMENTS ARE WORKING PERFECTLY!")
        print("Your stats page is now optimized and ready for high-performance usage.")
    elif passed >= total * 0.75:
        print("✅ Performance enhancements are mostly working correctly.")
        print("Check the test report for any issues that need attention.")
    else:
        print("⚠️  Some performance enhancements need attention.")
        print("Review the test report and re-run optimization scripts as needed.")
    
    print("\n📋 Files created:")
    print("• PERFORMANCE_TEST_REPORT.txt - Detailed test results")
    print("• data/performance_config.json - Performance configuration")
    print("• stats_performance_dashboard.py - Real-time monitoring")
    print("• PERFORMANCE_OPTIMIZATION_SUMMARY.txt - Complete summary")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()