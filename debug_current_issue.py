#!/usr/bin/env python3
"""
Debug script to analyze the current pattern detection issue
"""

import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_current_scenario():
    """Debug the exact scenario from the user's output"""
    try:
        from game_state_handler import GameState
        from bingo_logic import Bingo<PERSON>og<PERSON>
        from view_players import Player
        
        class MockGame:
            def __init__(self):
                self.players = [Player(cartela_no=1, bet_amount=10, deposited=0)]
                # Exact numbers from user's debug output
                self.called_numbers = [21, 72, 75, 40, 64, 48, 12, 20, 59, 1, 37, 9, 11, 28, 6, 30, 58, 15, 60, 35]
                self.current_number = 35
                self.bingo_logic = BingoLogic(self)
                self.game_started = True
                self.is_paused = True
                
            def play_warning_sound(self):
                print("🔊 Playing warning sound")
                return True
                
            def play_winner_sound(self):
                print("🎉 Playing winner sound")
                return True
        
        print("🔍 DEBUGGING CURRENT PATTERN DETECTION ISSUE")
        print("=" * 60)
        
        mock_game = MockGame()
        
        # First, let's get the card and see its layout
        from bingo_card import BingoCard
        card = BingoCard(1)  # Get cartella 1
        
        print("📋 CARTELLA 1 BOARD LAYOUT:")
        print("B   I   N   G   O")
        print("-------------------")
        for row in range(5):
            row_str = ""
            for col in range(5):
                num = card.grid[col][row]
                if num == 0:
                    row_str += "FREE*".ljust(4)
                else:
                    row_str += str(num).ljust(4)
            print(row_str)
        
        print(f"\n📞 CALLED NUMBERS: {mock_game.called_numbers}")
        print(f"📞 TOTAL CALLS: {len(mock_game.called_numbers)}")
        
        # Mark all called numbers on the card
        print(f"\n🔍 MARKING NUMBERS ON CARD...")
        for number in mock_game.called_numbers:
            marked = card.mark_number(number)
            if marked:
                print(f"✅ Marked {number}")
        
        print(f"\n📋 MARKED NUMBERS ON CARD: {sorted(card.marked)}")
        
        # Now let's manually check each pattern
        print(f"\n🔍 MANUAL PATTERN ANALYSIS:")
        
        # Check each row
        for row in range(5):
            row_numbers = []
            row_marked = []
            for col in range(5):
                num = card.grid[col][row]
                row_numbers.append(num)
                if num == 0:  # FREE space
                    row_marked.append(True)
                else:
                    row_marked.append(num in card.marked)
            
            all_marked = all(row_marked)
            print(f"Row {row+1}: {row_numbers} -> Marked: {row_marked} -> Complete: {all_marked}")
        
        # Check each column
        for col in range(5):
            col_numbers = []
            col_marked = []
            for row in range(5):
                num = card.grid[col][row]
                col_numbers.append(num)
                if num == 0:  # FREE space
                    col_marked.append(True)
                else:
                    col_marked.append(num in card.marked)
            
            all_marked = all(col_marked)
            print(f"Column {col+1}: {col_numbers} -> Marked: {col_marked} -> Complete: {all_marked}")
        
        # Check diagonals
        diag1_numbers = []
        diag1_marked = []
        for i in range(5):
            num = card.grid[i][i]
            diag1_numbers.append(num)
            if num == 0:
                diag1_marked.append(True)
            else:
                diag1_marked.append(num in card.marked)
        
        diag1_complete = all(diag1_marked)
        print(f"Diagonal 1: {diag1_numbers} -> Marked: {diag1_marked} -> Complete: {diag1_complete}")
        
        diag2_numbers = []
        diag2_marked = []
        for i in range(5):
            num = card.grid[i][4-i]
            diag2_numbers.append(num)
            if num == 0:
                diag2_marked.append(True)
            else:
                diag2_marked.append(num in card.marked)
        
        diag2_complete = all(diag2_marked)
        print(f"Diagonal 2: {diag2_numbers} -> Marked: {diag2_marked} -> Complete: {diag2_complete}")
        
        # Check four corners
        corners = [(0, 0), (0, 4), (4, 0), (4, 4)]
        corner_numbers = []
        corner_marked = []
        for col, row in corners:
            num = card.grid[col][row]
            corner_numbers.append(num)
            if num == 0:
                corner_marked.append(True)
            else:
                corner_marked.append(num in card.marked)
        
        corners_complete = all(corner_marked)
        print(f"Four Corners: {corner_numbers} -> Marked: {corner_marked} -> Complete: {corners_complete}")
        
        # Now test the actual pattern detection
        print(f"\n🧪 TESTING ACTUAL PATTERN DETECTION:")
        has_patterns = card.check_winning_patterns(mock_game.called_numbers)
        print(f"check_winning_patterns() result: {has_patterns}")
        print(f"Detected patterns: {getattr(card, 'winning_patterns', [])}")
        
        # Test with game state validation
        print(f"\n🎯 TESTING GAME STATE VALIDATION:")
        game_state = GameState(mock_game)
        result = game_state.validate_player_claim(1)
        print(f"Game state validation result: {result}")
        print(f"Claim type: {getattr(game_state, 'claim_type', 'None')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_current_scenario()