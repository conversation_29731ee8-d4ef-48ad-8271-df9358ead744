#!/usr/bin/env python3
"""
WOW Bingo Game - Test Files Cleanup Script
==========================================

This script removes test files that might interfere with the build process.
It specifically targets test files that could be causing numpy-related issues.
"""

import os
import sys
import shutil
from pathlib import Path

def main():
    """Main cleanup function."""
    print("=" * 60)
    print("WOW Bingo Game - Test Files Cleanup")
    print("=" * 60)
    print()
    
    # Get project root
    project_root = Path(__file__).parent.absolute()
    print(f"Project root: {project_root}")
    
    # Files to remove (test files that might use numpy)
    test_files_to_remove = [
        # Test files that might be causing issues
        "test_simple.py",
        "test_simple_build.py",
        "test_stats_integration.py",
        "test_stats_fixes.py",
        "test_stats_fix.py",
        "test_stats_display.py",
        "test_stats_consistency.py",
        "test_voucher_cross_platform.py",
        "test_voucher_amounts.py",
        "test_voucher.py",
        "test_transition_animation_reset.py",
        "test_transition_animation.py",
        "test_table_layout_optimization.py",
        "test_syntax.py",
        "test_stats_recording.py",
        "test_stats_pagination_fix.py",
        "test_stats_page_fix.py",
    ]
    
    # Count removed files
    removed_count = 0
    
    # Remove test files
    print("Removing test files...")
    for test_file in test_files_to_remove:
        file_path = project_root / test_file
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"  ✓ Removed: {test_file}")
                removed_count += 1
            except Exception as e:
                print(f"  ✗ Failed to remove {test_file}: {e}")
        else:
            print(f"  - Skipped (not found): {test_file}")
    
    # Remove any numpy-specific test directories
    numpy_test_dirs = [
        "numpy_tests",
        "test_numpy",
    ]
    
    for test_dir in numpy_test_dirs:
        dir_path = project_root / test_dir
        if dir_path.exists() and dir_path.is_dir():
            try:
                shutil.rmtree(dir_path)
                print(f"  ✓ Removed directory: {test_dir}")
                removed_count += 1
            except Exception as e:
                print(f"  ✗ Failed to remove directory {test_dir}: {e}")
    
    # Summary
    print()
    print("=" * 60)
    print(f"Cleanup completed: {removed_count} files/directories removed")
    print("=" * 60)
    print()
    print("You can now build the application using:")
    print("  nuitka_build_fixed.bat")
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())