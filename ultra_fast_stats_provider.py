"""
Ultra Fast Stats Provider - Performance-optimized stats data provider
Addresses all major performance bottlenecks in the stats page.
"""

import os
import json
import time
import threading
import sqlite3
from datetime import datetime, timedelta
from contextlib import contextmanager
import queue
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UltraFastStatsProvider:
    """
    Ultra-fast stats provider with aggressive caching, connection pooling,
    and non-blocking operations to prevent UI freezing.
    """
    
    def __init__(self):
        self.db_path = os.path.join('data', 'stats.db')
        self.cache_file = os.path.join('data', 'ultra_fast_cache.json')
        
        # Memory cache with timestamps
        self._memory_cache = {}
        self._cache_timestamps = {}
        self._cache_lock = threading.RLock()
        
        # Connection pool
        self._connection_pool = queue.Queue(maxsize=5)
        self._pool_lock = threading.Lock()
        self._initialize_connection_pool()
        
        # Background worker for async operations
        self._worker_queue = queue.Queue()
        self._worker_thread = None
        self._start_background_worker()
        
        # Cache settings
        self.cache_duration = 30  # 30 seconds cache
        self.max_memory_cache_size = 100
        
        # Load persistent cache
        self._load_persistent_cache()
        
        # Pre-warm cache with essential data
        self._prewarm_cache()
    
    def _initialize_connection_pool(self):
        """Initialize database connection pool for better performance."""
        try:
            # Ensure database exists
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # Create pool connections
            for _ in range(5):
                conn = sqlite3.connect(self.db_path, check_same_thread=False)
                conn.execute('PRAGMA journal_mode=WAL')  # Better concurrency
                conn.execute('PRAGMA synchronous=NORMAL')  # Faster writes
                conn.execute('PRAGMA cache_size=10000')  # Larger cache
                conn.execute('PRAGMA temp_store=MEMORY')  # Use memory for temp
                self._connection_pool.put(conn)
                
        except Exception as e:
            logger.error(f"Error initializing connection pool: {e}")
    
    @contextmanager
    def get_connection(self):
        """Get a database connection from the pool."""
        conn = None
        try:
            conn = self._connection_pool.get(timeout=1.0)
            yield conn
        except queue.Empty:
            # Fallback to new connection if pool is exhausted
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            # Return a mock connection that returns empty results
            yield MockConnection()
        finally:
            if conn and hasattr(conn, 'close'):
                try:
                    self._connection_pool.put(conn, timeout=0.1)
                except queue.Full:
                    conn.close()
    
    def _start_background_worker(self):
        """Start background worker thread for async operations."""
        if self._worker_thread and self._worker_thread.is_alive():
            return
            
        def worker():
            while True:
                try:
                    task = self._worker_queue.get(timeout=1.0)
                    if task is None:  # Shutdown signal
                        break
                    task()
                    self._worker_queue.task_done()
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Background worker error: {e}")
        
        self._worker_thread = threading.Thread(target=worker, daemon=True)
        self._worker_thread.start()
    
    def _load_persistent_cache(self):
        """Load cache from disk."""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    
                # Only load recent cache entries
                current_time = time.time()
                for key, (data, timestamp) in cache_data.items():
                    if current_time - timestamp < self.cache_duration:
                        with self._cache_lock:
                            self._memory_cache[key] = data
                            self._cache_timestamps[key] = timestamp
                            
        except Exception as e:
            logger.error(f"Error loading persistent cache: {e}")
    
    def _save_persistent_cache(self):
        """Save cache to disk asynchronously."""
        def save_task():
            try:
                with self._cache_lock:
                    cache_data = {}
                    for key in self._memory_cache:
                        cache_data[key] = (self._memory_cache[key], self._cache_timestamps[key])
                
                os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
                with open(self.cache_file, 'w') as f:
                    json.dump(cache_data, f)
                    
            except Exception as e:
                logger.error(f"Error saving persistent cache: {e}")
        
        self._worker_queue.put(save_task)
    
    def _prewarm_cache(self):
        """Pre-warm cache with essential data in background."""
        def prewarm_task():
            try:
                # Pre-load today's data
                today = datetime.now().strftime('%Y-%m-%d')
                self.get_daily_earnings(today)
                self.get_daily_games_played(today)
                
                # Pre-load weekly stats
                self.get_weekly_stats()
                
                logger.info("Cache pre-warming completed")
            except Exception as e:
                logger.error(f"Error pre-warming cache: {e}")
        
        self._worker_queue.put(prewarm_task)
    
    def _get_from_cache(self, key):
        """Get data from cache if valid."""
        with self._cache_lock:
            if key in self._memory_cache:
                timestamp = self._cache_timestamps.get(key, 0)
                if time.time() - timestamp < self.cache_duration:
                    return self._memory_cache[key]
                else:
                    # Remove expired entry
                    del self._memory_cache[key]
                    del self._cache_timestamps[key]
        return None
    
    def _set_cache(self, key, data):
        """Set data in cache."""
        with self._cache_lock:
            # Limit cache size
            if len(self._memory_cache) >= self.max_memory_cache_size:
                # Remove oldest entry
                oldest_key = min(self._cache_timestamps.keys(), 
                               key=lambda k: self._cache_timestamps[k])
                del self._memory_cache[oldest_key]
                del self._cache_timestamps[oldest_key]
            
            self._memory_cache[key] = data
            self._cache_timestamps[key] = time.time()
        
        # Save to disk asynchronously
        self._save_persistent_cache()
    
    def get_daily_earnings(self, date_str=None):
        """Get daily earnings with ultra-fast caching."""
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = f"daily_earnings_{date_str}"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        # Get from database with timeout
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Optimized query with index hint
                cursor.execute('''
                SELECT COALESCE(SUM(fee), 0) FROM game_history
                WHERE date(date_time) = ?
                AND username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                ''', (date_str,))
                
                result = cursor.fetchone()
                earnings = float(result[0]) if result and result[0] is not None else 0.0
                
                # Cache the result
                self._set_cache(cache_key, earnings)
                return earnings
                
        except Exception as e:
            logger.error(f"Error getting daily earnings for {date_str}: {e}")
            # Return cached value if available, otherwise 0
            return self._get_from_cache(cache_key) or 0.0
    
    def get_daily_games_played(self, date_str=None):
        """Get daily games played with ultra-fast caching."""
        if date_str is None:
            date_str = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = f"daily_games_{date_str}"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        # Get from database with timeout
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Optimized query
                cursor.execute('''
                SELECT COUNT(*) FROM game_history
                WHERE date(date_time) = ?
                AND username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                ''', (date_str,))
                
                result = cursor.fetchone()
                games = int(result[0]) if result and result[0] is not None else 0
                
                # Cache the result
                self._set_cache(cache_key, games)
                return games
                
        except Exception as e:
            logger.error(f"Error getting daily games for {date_str}: {e}")
            return self._get_from_cache(cache_key) or 0
    
    def get_weekly_stats(self, end_date=None):
        """Get weekly stats with ultra-fast caching."""
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        cache_key = f"weekly_stats_{end_date.strftime('%Y-%m-%d')}"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        # Generate week dates
        start_date = end_date - timedelta(days=6)
        weekly_stats = []
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Single optimized query for all week data
                cursor.execute('''
                SELECT 
                    date(date_time) as game_date,
                    COUNT(*) as games_played,
                    COALESCE(SUM(fee), 0) as earnings,
                    COUNT(CASE WHEN status = 'Won' THEN 1 END) as winners,
                    COALESCE(SUM(players), 0) as total_players
                FROM game_history
                WHERE date(date_time) BETWEEN ? AND ?
                AND username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                GROUP BY date(date_time)
                ORDER BY date(date_time)
                ''', (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
                
                db_results = {row[0]: {
                    'games_played': row[1],
                    'earnings': float(row[2]),
                    'winners': row[3],
                    'total_players': row[4]
                } for row in cursor.fetchall()}
                
                # Fill in all days of the week
                current_date = start_date
                while current_date <= end_date:
                    date_str = current_date.strftime('%Y-%m-%d')
                    day_data = db_results.get(date_str, {
                        'games_played': 0,
                        'earnings': 0.0,
                        'winners': 0,
                        'total_players': 0
                    })
                    
                    weekly_stats.append({
                        'date': date_str,
                        'games_played': day_data['games_played'],
                        'earnings': day_data['earnings'],
                        'winners': day_data['winners'],
                        'total_players': day_data['total_players']
                    })
                    
                    current_date += timedelta(days=1)
                
                # Cache the result
                self._set_cache(cache_key, weekly_stats)
                return weekly_stats
                
        except Exception as e:
            logger.error(f"Error getting weekly stats: {e}")
            # Return fallback data
            return self._generate_fallback_weekly_stats(start_date, end_date)
    
    def get_monthly_stats(self, end_date=None):
        """Get monthly stats with ultra-fast caching."""
        if end_date is None:
            end_date = datetime.now()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        cache_key = f"monthly_stats_{end_date.strftime('%Y-%m')}"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        monthly_stats = []
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get last 6 months of data in one query
                cursor.execute('''
                SELECT 
                    strftime('%Y-%m', date_time) as month,
                    COUNT(*) as games_played,
                    COALESCE(SUM(fee), 0) as earnings,
                    COUNT(CASE WHEN status = 'Won' THEN 1 END) as winners,
                    COALESCE(SUM(players), 0) as total_players
                FROM game_history
                WHERE date_time >= date(?, '-6 months')
                AND username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                GROUP BY strftime('%Y-%m', date_time)
                ORDER BY month DESC
                LIMIT 6
                ''', (end_date.strftime('%Y-%m-%d'),))
                
                results = cursor.fetchall()
                
                for row in results:
                    month_date = datetime.strptime(row[0], '%Y-%m')
                    monthly_stats.append({
                        'name': month_date.strftime('%b %Y'),
                        'date': row[0],
                        'games_played': row[1],
                        'earnings': float(row[2]),
                        'winners': row[3],
                        'total_players': row[4]
                    })
                
                # Ensure we have 6 months of data (fill with zeros if needed)
                while len(monthly_stats) < 6:
                    monthly_stats.append({
                        'name': 'No Data',
                        'date': '',
                        'games_played': 0,
                        'earnings': 0.0,
                        'winners': 0,
                        'total_players': 0
                    })
                
                # Cache the result
                self._set_cache(cache_key, monthly_stats)
                return monthly_stats
                
        except Exception as e:
            logger.error(f"Error getting monthly stats: {e}")
            return self._generate_fallback_monthly_stats()
    
    def get_game_history(self, page=0, page_size=10):
        """Get game history with ultra-fast caching."""
        cache_key = f"game_history_{page}_{page_size}"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get total count
                cursor.execute('''
                SELECT COUNT(*) FROM game_history
                WHERE username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                ''')
                
                total_games = cursor.fetchone()[0] or 0
                total_pages = max(1, (total_games + page_size - 1) // page_size)
                
                # Get page data
                offset = page * page_size
                cursor.execute('''
                SELECT id, date_time, username, house, stake, players, total_calls,
                       commission_percent, fee, total_prize, details, status, 
                       COALESCE(tips, 0) as tips
                FROM game_history
                WHERE username NOT LIKE '%Game Reset%'
                AND username NOT LIKE '%Demo%'
                AND total_calls > 0
                ORDER BY date_time DESC
                LIMIT ? OFFSET ?
                ''', (page_size, offset))
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        'id': row[0],
                        'date_time': row[1],
                        'username': row[2],
                        'house': row[3],
                        'stake': row[4],
                        'players': row[5],
                        'total_calls': row[6],
                        'commission_percent': row[7],
                        'fee': row[8],
                        'total_prize': row[9],
                        'details': row[10],
                        'status': row[11],
                        'tips': row[12]
                    })
                
                result = (history, total_pages)
                
                # Cache the result
                self._set_cache(cache_key, result)
                return result
                
        except Exception as e:
            logger.error(f"Error getting game history: {e}")
            return ([], 1)
    
    def get_wallet_balance(self):
        """Get wallet balance with ultra-fast caching."""
        cache_key = "wallet_balance"
        
        # Check cache first
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Try wallet_transactions first
                cursor.execute('''
                SELECT balance_after FROM wallet_transactions
                ORDER BY id DESC LIMIT 1
                ''')
                
                result = cursor.fetchone()
                if result and result[0] is not None:
                    balance = float(result[0])
                else:
                    # Fallback to game history
                    cursor.execute('SELECT COALESCE(SUM(fee), 0) FROM game_history')
                    result = cursor.fetchone()
                    balance = float(result[0]) if result else 0.0
                
                # Cache the result
                self._set_cache(cache_key, balance)
                return balance
                
        except Exception as e:
            logger.error(f"Error getting wallet balance: {e}")
            return 0.0
    
    def _generate_fallback_weekly_stats(self, start_date, end_date):
        """Generate fallback weekly stats."""
        weekly_stats = []
        current_date = start_date
        
        while current_date <= end_date:
            weekly_stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'games_played': 0,
                'earnings': 0.0,
                'winners': 0,
                'total_players': 0
            })
            current_date += timedelta(days=1)
        
        return weekly_stats
    
    def _generate_fallback_monthly_stats(self):
        """Generate fallback monthly stats."""
        return [{
            'name': 'No Data',
            'date': '',
            'games_played': 0,
            'earnings': 0.0,
            'winners': 0,
            'total_players': 0
        } for _ in range(6)]
    
    def clear_cache(self):
        """Clear all caches."""
        with self._cache_lock:
            self._memory_cache.clear()
            self._cache_timestamps.clear()
        
        # Remove persistent cache file
        try:
            if os.path.exists(self.cache_file):
                os.remove(self.cache_file)
        except Exception as e:
            logger.error(f"Error removing cache file: {e}")
    
    def shutdown(self):
        """Shutdown the provider and cleanup resources."""
        # Stop background worker
        self._worker_queue.put(None)
        if self._worker_thread:
            self._worker_thread.join(timeout=1.0)
        
        # Close all connections in pool
        while not self._connection_pool.empty():
            try:
                conn = self._connection_pool.get_nowait()
                conn.close()
            except:
                pass


class MockConnection:
    """Mock connection for error cases."""
    
    def cursor(self):
        return MockCursor()
    
    def close(self):
        pass


class MockCursor:
    """Mock cursor that returns empty results."""
    
    def execute(self, *args):
        pass
    
    def fetchone(self):
        return (0,)
    
    def fetchall(self):
        return []


# Global instance
_ultra_fast_provider = None

def get_ultra_fast_stats_provider():
    """Get the global ultra-fast stats provider instance."""
    global _ultra_fast_provider
    if _ultra_fast_provider is None:
        _ultra_fast_provider = UltraFastStatsProvider()
    return _ultra_fast_provider