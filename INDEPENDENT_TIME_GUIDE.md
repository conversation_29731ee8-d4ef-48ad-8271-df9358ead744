# Independent Time System for WOW Games

## Overview

This independent time system makes your application immune to PC clock changes by using a **monotonic clock + stored reference time** approach - the industry standard for time-critical applications.

## How It Works

1. **First Startup**: Records current UTC time as reference point
2. **Subsequent Runs**: Calculates time using `reference_time + elapsed_monotonic_time`
3. **Clock Changes**: System remains unaffected because it uses monotonic clock
4. **Database**: All new records use independent timestamps

## Key Benefits

✅ **Immune to PC clock changes**  
✅ **Consistent database timestamps**  
✅ **Accurate statistics and history**  
✅ **Backward compatible with existing data**  
✅ **Industry-standard approach**

## Quick Start

### 1. Run Migration (One Time)
```bash
python migrate_to_independent_time.py
```

### 2. Use in Your Code
Replace `datetime.now()` calls with:

```python
from time_manager import independent_now, independent_strftime

# Instead of: datetime.now()
current_time = independent_now()

# Instead of: datetime.now().strftime('%Y-%m-%d %H:%M:%S')
formatted_time = independent_strftime('%Y-%m-%d %H:%M:%S')
```

### 3. Database Operations
```python
from db_time_integration import DatabaseTimeIntegration

db_time = DatabaseTimeIntegration('data/stats.db')

# Add game record with independent timestamp
db_time.add_game_record(
    username="Player1",
    house="House1", 
    stake=100,
    # ... other parameters
)
```

## Available Functions

### Basic Time Functions
```python
from time_manager import (
    independent_now,           # Current local time
    independent_utc_now,       # Current UTC time  
    independent_strftime,      # Formatted time string
    independent_timestamp,     # Unix timestamp
    get_time_stats,           # System statistics
    check_clock_changes       # Detect clock changes
)
```

### Database Functions
```python
from db_time_integration import (
    DatabaseTimeIntegration,   # Main database class
    log_time_system_activation # Log activation
)
```

## Monitoring & Debugging

### Check System Status
```python
from time_manager import get_time_stats, check_clock_changes

# Get system statistics
stats = get_time_stats()
print(f"Days since first startup: {stats['elapsed_since_first_startup']['days']}")
print(f"App startup count: {stats['startup_count']}")

# Check for clock changes
clock_check = check_clock_changes()
if clock_check['clock_changed']:
    print("⚠️ System clock change detected!")
```

### Database Consistency Check
```python
from db_time_integration import DatabaseTimeIntegration

db_time = DatabaseTimeIntegration('data/stats.db')
consistency = db_time.check_time_consistency_in_db()

print(f"Future records: {consistency['future_records']}")
print(f"Suspicious gaps: {consistency['suspicious_gaps']}")
```

## File Structure

```
your_project/
├── time_manager.py              # Core time system
├── db_time_integration.py       # Database integration
├── migrate_to_independent_time.py # Migration script
├── data/
│   ├── time_config.json        # Time reference storage
│   └── stats.db                # Your database
└── backup_before_time_migration_*/ # Automatic backups
```

## Migration Details

The migration script:
1. **Backs up** all existing databases
2. **Updates** key Python files to use independent time
3. **Preserves** all existing historical data
4. **Logs** the migration process
5. **Tests** the new system

### Files Updated by Migration
- `stats_db.py` - Database operations
- `add_test_game_data.py` - Test data generation  
- `stats_page.py` - Statistics display

## Troubleshooting

### Issue: "Time reference file not found"
**Solution**: The system will automatically create a new reference on first run.

### Issue: "Large time difference detected"
**Solution**: This is normal if you're migrating from system time. The independent time will be consistent going forward.

### Issue: "Database errors after migration"
**Solution**: Check that the migration completed successfully and all imports are working.

## Technical Details

### Time Reference Storage
```json
{
  "reference_utc_timestamp": 1704067200.0,
  "reference_utc_iso": "2024-01-01T00:00:00+00:00",
  "reference_monotonic": 12345.67,
  "startup_count": 5,
  "created_at": "2024-01-01T00:00:00+00:00"
}
```

### Clock Change Detection
The system monitors for significant differences between:
- Expected system time (based on monotonic progression)
- Actual system time

Threshold: 5 seconds (configurable)

## Best Practices

1. **Always use independent time functions** for new code
2. **Monitor clock changes** in production
3. **Keep backups** of time reference file
4. **Test thoroughly** after migration
5. **Log important time events** for debugging

## Support

If you encounter issues:
1. Check the migration log file
2. Verify all imports are working
3. Test with the provided test functions
4. Check database backup integrity

The system is designed to be robust and fail gracefully, falling back to system time if needed while logging any issues.