2025-06-01 17:24:14.690 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.874 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.891 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.893 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-01 17:24:14.894 - Initializing StatsDataProvider
2025-06-01 17:24:14.915 - Loaded 8 items from cache
2025-06-01 17:24:14.922 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-01 17:24:14.925 - Starting background data loading thread
2025-06-01 17:24:14.929 - Background data loading started
2025-06-01 17:24:14.931 - StatsDataProvider initialization completed
2025-06-01 17:24:14.932 - Loading summary statistics
2025-06-01 17:24:14.933 - Created singleton instance of StatsDataProvider on module import
2025-06-01 17:24:14.933 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:24:14.936 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-01 17:24:14.937 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.942 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:24:14.942 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:24:14.943 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-01 17:24:14.948 - Successfully loaded weekly stats: 7 days
2025-06-01 17:24:14.950 - Saved 10 items to cache
2025-06-01 17:24:14.951 - Background data loading completed
2025-06-01 17:32:11.116 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:41:24.653 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.436 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.556 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.585 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.593 - Forcing refresh of all stats data
2025-06-01 17:48:01.605 - Attempting to force refresh via GameStatsIntegration
2025-06-01 17:48:01.686 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:48:01.693 - Force refresh via GameStatsIntegration successful
2025-06-01 17:48:01.696 - Clearing cached data
2025-06-01 17:48:01.698 - Posted refresh_stats event to trigger UI update
2025-06-01 17:48:01.698 - Starting background data reload
2025-06-01 17:48:01.727 - Starting background data loading thread
2025-06-01 17:48:01.731 - Background data loading started
2025-06-01 17:48:01.760 - Loading summary statistics
2025-06-01 17:48:01.800 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:48:01.803 - Data from GameStatsIntegration: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-01 17:48:01.806 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:48:01.810 - Successfully loaded summary stats: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-01 17:48:01.817 - Successfully loaded game history: 1 entries
2025-06-01 17:48:01.825 - Successfully loaded weekly stats: 7 days
2025-06-01 17:48:01.828 - Saved 3 items to cache
2025-06-01 17:48:01.830 - Background data loading completed
2025-06-01 17:48:30.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.229 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.335 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.371 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.382 - Forcing refresh of all stats data
2025-06-01 17:54:02.389 - Attempting to force refresh via GameStatsIntegration
2025-06-01 17:54:02.496 - get_stats_provider called, returning provider with initialized=True
2025-06-01 17:54:02.506 - Force refresh via GameStatsIntegration successful
2025-06-01 17:54:02.508 - Clearing cached data
2025-06-01 17:54:02.513 - Posted refresh_stats event to trigger UI update
2025-06-01 17:54:02.532 - Starting background data reload
2025-06-01 17:54:02.540 - Starting background data loading thread
2025-06-01 17:54:02.546 - Background data loading started
2025-06-01 17:54:02.578 - Loading summary statistics
2025-06-01 17:54:02.616 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 17:54:02.622 - Data from GameStatsIntegration: {'total_earnings': 80.0, 'daily_earnings': 80.0, 'daily_games': 2, 'wallet_balance': 0}
2025-06-01 17:54:02.627 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 17:54:02.634 - Successfully loaded summary stats: {'total_earnings': 80.0, 'daily_earnings': 80.0, 'daily_games': 2, 'wallet_balance': 0}
2025-06-01 17:54:02.639 - Successfully loaded game history: 2 entries
2025-06-01 17:54:02.640 - Successfully loaded weekly stats: 7 days
2025-06-01 17:54:02.645 - Saved 3 items to cache
2025-06-01 17:54:02.657 - Background data loading completed
2025-06-01 17:54:54.512 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.200 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.204 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.207 - Forcing refresh of all stats data
2025-06-01 18:05:39.209 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:05:39.284 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:05:39.297 - Force refresh via GameStatsIntegration successful
2025-06-01 18:05:39.306 - Clearing cached data
2025-06-01 18:05:39.312 - Posted refresh_stats event to trigger UI update
2025-06-01 18:05:39.323 - Starting background data reload
2025-06-01 18:05:39.327 - Starting background data loading thread
2025-06-01 18:05:39.332 - Background data loading started
2025-06-01 18:05:39.346 - Loading summary statistics
2025-06-01 18:05:39.409 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:05:39.418 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 3, 'wallet_balance': 0}
2025-06-01 18:05:39.421 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:05:39.426 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 3, 'wallet_balance': 0}
2025-06-01 18:05:39.433 - Successfully loaded game history: 3 entries
2025-06-01 18:05:39.439 - Successfully loaded weekly stats: 7 days
2025-06-01 18:05:39.448 - Saved 3 items to cache
2025-06-01 18:05:39.451 - Background data loading completed
2025-06-01 18:05:50.071 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.356 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.369 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.375 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.378 - Forcing refresh of all stats data
2025-06-01 18:15:42.378 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:15:42.393 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:15:42.396 - Force refresh via GameStatsIntegration successful
2025-06-01 18:15:42.396 - Clearing cached data
2025-06-01 18:15:42.397 - Posted refresh_stats event to trigger UI update
2025-06-01 18:15:42.398 - Starting background data reload
2025-06-01 18:15:42.398 - Starting background data loading thread
2025-06-01 18:15:42.401 - Background data loading started
2025-06-01 18:15:42.413 - Loading summary statistics
2025-06-01 18:15:42.455 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:15:42.462 - Data from GameStatsIntegration: {'total_earnings': 160.0, 'daily_earnings': 160.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-01 18:15:42.463 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:15:42.463 - Successfully loaded summary stats: {'total_earnings': 160.0, 'daily_earnings': 160.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-01 18:15:42.464 - Successfully loaded game history: 4 entries
2025-06-01 18:15:42.464 - Successfully loaded weekly stats: 7 days
2025-06-01 18:15:42.465 - Saved 3 items to cache
2025-06-01 18:15:42.467 - Background data loading completed
2025-06-01 18:16:57.986 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.067 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.084 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.086 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.087 - Forcing refresh of all stats data
2025-06-01 18:17:02.091 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:17:02.129 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:17:02.130 - Force refresh via GameStatsIntegration successful
2025-06-01 18:17:02.132 - Clearing cached data
2025-06-01 18:17:02.132 - Posted refresh_stats event to trigger UI update
2025-06-01 18:17:02.133 - Starting background data reload
2025-06-01 18:17:02.135 - Starting background data loading thread
2025-06-01 18:17:02.136 - Background data loading started
2025-06-01 18:17:02.143 - Loading summary statistics
2025-06-01 18:17:02.179 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:17:02.180 - Data from GameStatsIntegration: {'total_earnings': 184.0, 'daily_earnings': 184.0, 'daily_games': 5, 'wallet_balance': 0}
2025-06-01 18:17:02.180 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:17:02.180 - Successfully loaded summary stats: {'total_earnings': 184.0, 'daily_earnings': 184.0, 'daily_games': 5, 'wallet_balance': 0}
2025-06-01 18:17:02.181 - Successfully loaded game history: 5 entries
2025-06-01 18:17:02.181 - Successfully loaded weekly stats: 7 days
2025-06-01 18:17:02.183 - Saved 3 items to cache
2025-06-01 18:17:02.183 - Background data loading completed
2025-06-01 18:17:15.695 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.474 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.492 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.497 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.499 - Forcing refresh of all stats data
2025-06-01 18:23:56.500 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:23:56.513 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:23:56.514 - Force refresh via GameStatsIntegration successful
2025-06-01 18:23:56.515 - Clearing cached data
2025-06-01 18:23:56.516 - Posted refresh_stats event to trigger UI update
2025-06-01 18:23:56.517 - Starting background data reload
2025-06-01 18:23:56.518 - Starting background data loading thread
2025-06-01 18:23:56.519 - Background data loading started
2025-06-01 18:23:56.532 - Loading summary statistics
2025-06-01 18:23:56.558 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:23:56.559 - Data from GameStatsIntegration: {'total_earnings': 198.4, 'daily_earnings': 198.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-01 18:23:56.559 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:23:56.560 - Successfully loaded summary stats: {'total_earnings': 198.4, 'daily_earnings': 198.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-01 18:23:56.561 - Successfully loaded game history: 6 entries
2025-06-01 18:23:56.561 - Successfully loaded weekly stats: 7 days
2025-06-01 18:23:56.562 - Saved 3 items to cache
2025-06-01 18:23:56.564 - Background data loading completed
2025-06-01 18:24:32.253 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.334 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.460 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.485 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.494 - Forcing refresh of all stats data
2025-06-01 18:29:27.504 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:29:27.597 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:29:27.601 - Force refresh via GameStatsIntegration successful
2025-06-01 18:29:27.628 - Clearing cached data
2025-06-01 18:29:27.632 - Posted refresh_stats event to trigger UI update
2025-06-01 18:29:27.636 - Starting background data reload
2025-06-01 18:29:27.652 - Starting background data loading thread
2025-06-01 18:29:27.657 - Background data loading started
2025-06-01 18:29:27.666 - Loading summary statistics
2025-06-01 18:29:27.721 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:29:27.728 - Data from GameStatsIntegration: {'total_earnings': 212.8, 'daily_earnings': 212.8, 'daily_games': 7, 'wallet_balance': 0}
2025-06-01 18:29:27.729 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:29:27.730 - Successfully loaded summary stats: {'total_earnings': 212.8, 'daily_earnings': 212.8, 'daily_games': 7, 'wallet_balance': 0}
2025-06-01 18:29:27.731 - Successfully loaded game history: 7 entries
2025-06-01 18:29:27.735 - Successfully loaded weekly stats: 7 days
2025-06-01 18:29:27.761 - Saved 3 items to cache
2025-06-01 18:29:27.764 - Background data loading completed
2025-06-01 18:29:51.729 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.656 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.674 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.678 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:26.688 - Forcing refresh of all stats data
2025-06-01 18:34:26.690 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:34:26.776 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:34:27.068 - Force refresh via GameStatsIntegration successful
2025-06-01 18:34:27.075 - Clearing cached data
2025-06-01 18:34:27.081 - Posted refresh_stats event to trigger UI update
2025-06-01 18:34:27.084 - Starting background data reload
2025-06-01 18:34:27.086 - Starting background data loading thread
2025-06-01 18:34:27.098 - Background data loading started
2025-06-01 18:34:27.117 - Loading summary statistics
2025-06-01 18:34:27.179 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:34:27.186 - Data from GameStatsIntegration: {'total_earnings': 227.20000000000002, 'daily_earnings': 227.20000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-01 18:34:27.189 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:34:27.191 - Successfully loaded summary stats: {'total_earnings': 227.20000000000002, 'daily_earnings': 227.20000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-01 18:34:27.198 - Successfully loaded game history: 8 entries
2025-06-01 18:34:27.206 - Successfully loaded weekly stats: 7 days
2025-06-01 18:34:27.214 - Saved 3 items to cache
2025-06-01 18:34:27.217 - Background data loading completed
2025-06-01 18:35:18.769 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.616 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.635 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.645 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:04.665 - Forcing refresh of all stats data
2025-06-01 18:49:04.666 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:49:05.096 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:49:05.108 - Force refresh via GameStatsIntegration successful
2025-06-01 18:49:05.116 - Clearing cached data
2025-06-01 18:49:05.122 - Posted refresh_stats event to trigger UI update
2025-06-01 18:49:05.131 - Starting background data reload
2025-06-01 18:49:05.141 - Starting background data loading thread
2025-06-01 18:49:05.145 - Background data loading started
2025-06-01 18:49:05.158 - Loading summary statistics
2025-06-01 18:49:05.206 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:49:05.222 - Data from GameStatsIntegration: {'total_earnings': 241.60000000000002, 'daily_earnings': 241.60000000000002, 'daily_games': 9, 'wallet_balance': 0}
2025-06-01 18:49:05.226 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:49:05.229 - Successfully loaded summary stats: {'total_earnings': 241.60000000000002, 'daily_earnings': 241.60000000000002, 'daily_games': 9, 'wallet_balance': 0}
2025-06-01 18:49:05.238 - Successfully loaded game history: 9 entries
2025-06-01 18:49:05.245 - Successfully loaded weekly stats: 7 days
2025-06-01 18:49:05.252 - Saved 3 items to cache
2025-06-01 18:49:05.257 - Background data loading completed
2025-06-01 18:50:42.971 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.228 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.387 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.409 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.412 - Forcing refresh of all stats data
2025-06-01 18:53:18.416 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:53:18.534 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:53:18.548 - Force refresh via GameStatsIntegration successful
2025-06-01 18:53:18.553 - Clearing cached data
2025-06-01 18:53:18.562 - Posted refresh_stats event to trigger UI update
2025-06-01 18:53:18.568 - Starting background data reload
2025-06-01 18:53:18.577 - Starting background data loading thread
2025-06-01 18:53:18.584 - Background data loading started
2025-06-01 18:53:18.609 - Loading summary statistics
2025-06-01 18:53:18.663 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:53:18.666 - Data from GameStatsIntegration: {'total_earnings': 260.8, 'daily_earnings': 260.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-01 18:53:18.670 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:53:18.677 - Successfully loaded summary stats: {'total_earnings': 260.8, 'daily_earnings': 260.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-01 18:53:18.687 - Successfully loaded game history: 10 entries
2025-06-01 18:53:18.691 - Successfully loaded weekly stats: 7 days
2025-06-01 18:53:18.697 - Saved 3 items to cache
2025-06-01 18:53:18.706 - Background data loading completed
2025-06-01 18:54:28.938 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.833 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.849 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.852 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.853 - Forcing refresh of all stats data
2025-06-01 18:56:50.854 - Attempting to force refresh via GameStatsIntegration
2025-06-01 18:56:50.868 - get_stats_provider called, returning provider with initialized=True
2025-06-01 18:56:50.870 - Force refresh via GameStatsIntegration successful
2025-06-01 18:56:50.870 - Clearing cached data
2025-06-01 18:56:50.871 - Posted refresh_stats event to trigger UI update
2025-06-01 18:56:50.874 - Starting background data reload
2025-06-01 18:56:50.875 - Starting background data loading thread
2025-06-01 18:56:50.875 - Background data loading started
2025-06-01 18:56:50.887 - Loading summary statistics
2025-06-01 18:56:51.027 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 18:56:51.028 - Data from GameStatsIntegration: {'total_earnings': 280.0, 'daily_earnings': 280.0, 'daily_games': 11, 'wallet_balance': 0}
2025-06-01 18:56:51.028 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 18:56:51.029 - Successfully loaded summary stats: {'total_earnings': 280.0, 'daily_earnings': 280.0, 'daily_games': 11, 'wallet_balance': 0}
2025-06-01 18:56:51.030 - Successfully loaded game history: 10 entries
2025-06-01 18:56:51.038 - Successfully loaded weekly stats: 7 days
2025-06-01 18:56:51.041 - Saved 3 items to cache
2025-06-01 18:56:51.043 - Background data loading completed
2025-06-01 18:59:18.678 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.536 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.551 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.553 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.554 - Forcing refresh of all stats data
2025-06-01 19:01:19.555 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:01:19.571 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:01:19.574 - Force refresh via GameStatsIntegration successful
2025-06-01 19:01:19.575 - Clearing cached data
2025-06-01 19:01:19.576 - Posted refresh_stats event to trigger UI update
2025-06-01 19:01:19.579 - Starting background data reload
2025-06-01 19:01:19.579 - Starting background data loading thread
2025-06-01 19:01:19.580 - Background data loading started
2025-06-01 19:01:19.595 - Loading summary statistics
2025-06-01 19:01:19.620 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:01:19.620 - Data from GameStatsIntegration: {'total_earnings': 299.2, 'daily_earnings': 299.2, 'daily_games': 12, 'wallet_balance': 0}
2025-06-01 19:01:19.621 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:01:19.621 - Successfully loaded summary stats: {'total_earnings': 299.2, 'daily_earnings': 299.2, 'daily_games': 12, 'wallet_balance': 0}
2025-06-01 19:01:19.622 - Successfully loaded game history: 10 entries
2025-06-01 19:01:19.623 - Successfully loaded weekly stats: 7 days
2025-06-01 19:01:19.624 - Saved 3 items to cache
2025-06-01 19:01:19.624 - Background data loading completed
2025-06-01 19:04:57.902 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.592 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.607 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.611 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.612 - Forcing refresh of all stats data
2025-06-01 19:07:12.614 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:07:12.637 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:07:12.639 - Force refresh via GameStatsIntegration successful
2025-06-01 19:07:12.640 - Clearing cached data
2025-06-01 19:07:12.650 - Posted refresh_stats event to trigger UI update
2025-06-01 19:07:12.653 - Starting background data reload
2025-06-01 19:07:12.660 - Starting background data loading thread
2025-06-01 19:07:12.669 - Background data loading started
2025-06-01 19:07:12.689 - Loading summary statistics
2025-06-01 19:07:12.721 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:07:12.724 - Data from GameStatsIntegration: {'total_earnings': 323.2, 'daily_earnings': 323.2, 'daily_games': 13, 'wallet_balance': 0}
2025-06-01 19:07:12.739 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:07:12.750 - Successfully loaded summary stats: {'total_earnings': 323.2, 'daily_earnings': 323.2, 'daily_games': 13, 'wallet_balance': 0}
2025-06-01 19:07:12.754 - Successfully loaded game history: 10 entries
2025-06-01 19:07:12.763 - Successfully loaded weekly stats: 7 days
2025-06-01 19:07:12.771 - Saved 3 items to cache
2025-06-01 19:07:12.776 - Background data loading completed
2025-06-01 19:10:29.613 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.102 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.164 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.200 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.205 - Forcing refresh of all stats data
2025-06-01 19:15:07.213 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:15:07.328 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:15:07.344 - Force refresh via GameStatsIntegration successful
2025-06-01 19:15:07.348 - Clearing cached data
2025-06-01 19:15:07.350 - Posted refresh_stats event to trigger UI update
2025-06-01 19:15:07.357 - Starting background data reload
2025-06-01 19:15:07.370 - Starting background data loading thread
2025-06-01 19:15:07.384 - Background data loading started
2025-06-01 19:15:07.401 - Loading summary statistics
2025-06-01 19:15:07.453 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:15:07.456 - Data from GameStatsIntegration: {'total_earnings': 347.2, 'daily_earnings': 347.2, 'daily_games': 14, 'wallet_balance': 0}
2025-06-01 19:15:07.459 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:15:07.462 - Successfully loaded summary stats: {'total_earnings': 347.2, 'daily_earnings': 347.2, 'daily_games': 14, 'wallet_balance': 0}
2025-06-01 19:15:07.472 - Successfully loaded game history: 10 entries
2025-06-01 19:15:07.476 - Successfully loaded weekly stats: 7 days
2025-06-01 19:15:07.480 - Saved 3 items to cache
2025-06-01 19:15:07.480 - Background data loading completed
2025-06-01 19:15:58.141 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.843 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.864 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.868 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.872 - Forcing refresh of all stats data
2025-06-01 19:21:25.873 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:21:25.887 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:21:25.889 - Force refresh via GameStatsIntegration successful
2025-06-01 19:21:25.890 - Clearing cached data
2025-06-01 19:21:25.890 - Posted refresh_stats event to trigger UI update
2025-06-01 19:21:25.891 - Starting background data reload
2025-06-01 19:21:25.892 - Starting background data loading thread
2025-06-01 19:21:25.894 - Background data loading started
2025-06-01 19:21:25.895 - Loading summary statistics
2025-06-01 19:21:25.996 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:21:26.181 - Data from GameStatsIntegration: {'total_earnings': 376.0, 'daily_earnings': 376.0, 'daily_games': 15, 'wallet_balance': 0}
2025-06-01 19:21:26.183 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:21:26.186 - Successfully loaded summary stats: {'total_earnings': 376.0, 'daily_earnings': 376.0, 'daily_games': 15, 'wallet_balance': 0}
2025-06-01 19:21:26.186 - Successfully loaded game history: 10 entries
2025-06-01 19:21:26.192 - Successfully loaded weekly stats: 7 days
2025-06-01 19:21:26.218 - Saved 3 items to cache
2025-06-01 19:21:26.220 - Background data loading completed
2025-06-01 19:22:27.527 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.747 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.883 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.909 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:48.916 - Forcing refresh of all stats data
2025-06-01 19:25:48.922 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:25:49.292 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:25:49.300 - Force refresh via GameStatsIntegration successful
2025-06-01 19:25:49.304 - Clearing cached data
2025-06-01 19:25:49.308 - Posted refresh_stats event to trigger UI update
2025-06-01 19:25:49.321 - Starting background data reload
2025-06-01 19:25:49.325 - Starting background data loading thread
2025-06-01 19:25:49.331 - Background data loading started
2025-06-01 19:25:49.333 - Loading summary statistics
2025-06-01 19:25:49.368 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:25:49.400 - Data from GameStatsIntegration: {'total_earnings': 409.6, 'daily_earnings': 409.6, 'daily_games': 16, 'wallet_balance': 0}
2025-06-01 19:25:49.412 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:25:49.415 - Successfully loaded summary stats: {'total_earnings': 409.6, 'daily_earnings': 409.6, 'daily_games': 16, 'wallet_balance': 0}
2025-06-01 19:25:49.426 - Successfully loaded game history: 10 entries
2025-06-01 19:25:49.430 - Successfully loaded weekly stats: 7 days
2025-06-01 19:25:49.463 - Saved 3 items to cache
2025-06-01 19:25:49.465 - Background data loading completed
2025-06-01 19:27:08.940 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.621 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.636 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.642 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.643 - Forcing refresh of all stats data
2025-06-01 19:29:44.646 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:29:44.714 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:29:44.718 - Force refresh via GameStatsIntegration successful
2025-06-01 19:29:44.719 - Clearing cached data
2025-06-01 19:29:44.720 - Posted refresh_stats event to trigger UI update
2025-06-01 19:29:44.748 - Starting background data reload
2025-06-01 19:29:44.754 - Starting background data loading thread
2025-06-01 19:29:44.763 - Background data loading started
2025-06-01 19:29:44.784 - Loading summary statistics
2025-06-01 19:29:44.830 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:29:44.834 - Data from GameStatsIntegration: {'total_earnings': 452.8, 'daily_earnings': 452.8, 'daily_games': 17, 'wallet_balance': 0}
2025-06-01 19:29:44.839 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:29:44.930 - Successfully loaded summary stats: {'total_earnings': 452.8, 'daily_earnings': 452.8, 'daily_games': 17, 'wallet_balance': 0}
2025-06-01 19:29:45.104 - Successfully loaded game history: 10 entries
2025-06-01 19:29:45.107 - Successfully loaded weekly stats: 7 days
2025-06-01 19:29:45.119 - Saved 3 items to cache
2025-06-01 19:29:45.125 - Background data loading completed
2025-06-01 19:33:12.807 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.675 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.691 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.694 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.695 - Forcing refresh of all stats data
2025-06-01 19:37:16.699 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:37:16.872 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:37:16.878 - Force refresh via GameStatsIntegration successful
2025-06-01 19:37:16.882 - Clearing cached data
2025-06-01 19:37:16.883 - Posted refresh_stats event to trigger UI update
2025-06-01 19:37:16.886 - Starting background data reload
2025-06-01 19:37:16.909 - Starting background data loading thread
2025-06-01 19:37:16.919 - Background data loading started
2025-06-01 19:37:16.954 - Loading summary statistics
2025-06-01 19:37:16.989 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:37:16.991 - Data from GameStatsIntegration: {'total_earnings': 496.0, 'daily_earnings': 496.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-01 19:37:16.995 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:37:17.006 - Successfully loaded summary stats: {'total_earnings': 496.0, 'daily_earnings': 496.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-01 19:37:17.010 - Successfully loaded game history: 10 entries
2025-06-01 19:37:17.014 - Successfully loaded weekly stats: 7 days
2025-06-01 19:37:17.023 - Saved 3 items to cache
2025-06-01 19:37:17.023 - Background data loading completed
2025-06-01 19:42:54.719 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.151 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.169 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.172 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.174 - Forcing refresh of all stats data
2025-06-01 19:48:36.176 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:48:36.195 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:48:36.195 - Force refresh via GameStatsIntegration successful
2025-06-01 19:48:36.196 - Clearing cached data
2025-06-01 19:48:36.204 - Posted refresh_stats event to trigger UI update
2025-06-01 19:48:36.205 - Starting background data reload
2025-06-01 19:48:36.207 - Starting background data loading thread
2025-06-01 19:48:36.210 - Background data loading started
2025-06-01 19:48:36.211 - Loading summary statistics
2025-06-01 19:48:36.255 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:48:36.285 - Data from GameStatsIntegration: {'total_earnings': 544.0, 'daily_earnings': 544.0, 'daily_games': 19, 'wallet_balance': 0}
2025-06-01 19:48:36.285 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:48:36.286 - Successfully loaded summary stats: {'total_earnings': 544.0, 'daily_earnings': 544.0, 'daily_games': 19, 'wallet_balance': 0}
2025-06-01 19:48:36.292 - Successfully loaded game history: 10 entries
2025-06-01 19:48:36.299 - Successfully loaded weekly stats: 7 days
2025-06-01 19:48:36.307 - Saved 3 items to cache
2025-06-01 19:48:36.309 - Background data loading completed
2025-06-01 19:50:00.035 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.525 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.630 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.655 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.658 - Forcing refresh of all stats data
2025-06-01 19:55:10.663 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:55:10.763 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:55:10.764 - Force refresh via GameStatsIntegration successful
2025-06-01 19:55:10.776 - Clearing cached data
2025-06-01 19:55:10.794 - Posted refresh_stats event to trigger UI update
2025-06-01 19:55:10.800 - Starting background data reload
2025-06-01 19:55:10.813 - Starting background data loading thread
2025-06-01 19:55:10.822 - Background data loading started
2025-06-01 19:55:10.841 - Loading summary statistics
2025-06-01 19:55:10.889 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:55:10.892 - Data from GameStatsIntegration: {'total_earnings': 601.6, 'daily_earnings': 601.6, 'daily_games': 20, 'wallet_balance': 0}
2025-06-01 19:55:10.893 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:55:10.895 - Successfully loaded summary stats: {'total_earnings': 601.6, 'daily_earnings': 601.6, 'daily_games': 20, 'wallet_balance': 0}
2025-06-01 19:55:10.898 - Successfully loaded game history: 10 entries
2025-06-01 19:55:10.901 - Successfully loaded weekly stats: 7 days
2025-06-01 19:55:10.904 - Saved 3 items to cache
2025-06-01 19:55:10.909 - Background data loading completed
2025-06-01 19:56:42.203 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.572 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.677 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.696 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.699 - Forcing refresh of all stats data
2025-06-01 19:59:44.706 - Attempting to force refresh via GameStatsIntegration
2025-06-01 19:59:44.797 - get_stats_provider called, returning provider with initialized=True
2025-06-01 19:59:44.800 - Force refresh via GameStatsIntegration successful
2025-06-01 19:59:44.806 - Clearing cached data
2025-06-01 19:59:44.831 - Posted refresh_stats event to trigger UI update
2025-06-01 19:59:44.835 - Starting background data reload
2025-06-01 19:59:44.847 - Starting background data loading thread
2025-06-01 19:59:44.855 - Background data loading started
2025-06-01 19:59:44.879 - Loading summary statistics
2025-06-01 19:59:44.924 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 19:59:44.927 - Data from GameStatsIntegration: {'total_earnings': 668.8000000000001, 'daily_earnings': 668.8000000000001, 'daily_games': 21, 'wallet_balance': 0}
2025-06-01 19:59:44.929 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 19:59:44.931 - Successfully loaded summary stats: {'total_earnings': 668.8000000000001, 'daily_earnings': 668.8000000000001, 'daily_games': 21, 'wallet_balance': 0}
2025-06-01 19:59:44.933 - Successfully loaded game history: 10 entries
2025-06-01 19:59:44.936 - Successfully loaded weekly stats: 7 days
2025-06-01 19:59:44.941 - Saved 3 items to cache
2025-06-01 19:59:44.942 - Background data loading completed
2025-06-01 20:00:11.670 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.460 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.566 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.599 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.607 - Forcing refresh of all stats data
2025-06-01 20:03:44.616 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:03:44.708 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:03:44.723 - Force refresh via GameStatsIntegration successful
2025-06-01 20:03:44.728 - Clearing cached data
2025-06-01 20:03:44.730 - Posted refresh_stats event to trigger UI update
2025-06-01 20:03:44.737 - Starting background data reload
2025-06-01 20:03:44.749 - Starting background data loading thread
2025-06-01 20:03:44.755 - Background data loading started
2025-06-01 20:03:44.787 - Loading summary statistics
2025-06-01 20:03:44.814 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:03:44.815 - Data from GameStatsIntegration: {'total_earnings': 736.0000000000001, 'daily_earnings': 736.0000000000001, 'daily_games': 22, 'wallet_balance': 0}
2025-06-01 20:03:44.821 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:03:44.844 - Successfully loaded summary stats: {'total_earnings': 736.0000000000001, 'daily_earnings': 736.0000000000001, 'daily_games': 22, 'wallet_balance': 0}
2025-06-01 20:03:44.849 - Successfully loaded game history: 10 entries
2025-06-01 20:03:44.855 - Successfully loaded weekly stats: 7 days
2025-06-01 20:03:44.867 - Saved 3 items to cache
2025-06-01 20:03:44.873 - Background data loading completed
2025-06-01 20:06:13.066 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.530 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.728 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.762 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:49.931 - Forcing refresh of all stats data
2025-06-01 20:10:49.935 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:10:49.998 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:10:50.031 - Force refresh via GameStatsIntegration successful
2025-06-01 20:10:50.035 - Clearing cached data
2025-06-01 20:10:50.044 - Posted refresh_stats event to trigger UI update
2025-06-01 20:10:50.048 - Starting background data reload
2025-06-01 20:10:50.061 - Starting background data loading thread
2025-06-01 20:10:50.066 - Background data loading started
2025-06-01 20:10:50.081 - Loading summary statistics
2025-06-01 20:10:50.089 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:10:50.108 - Data from GameStatsIntegration: {'total_earnings': 788.8000000000001, 'daily_earnings': 788.8000000000001, 'daily_games': 23, 'wallet_balance': 0}
2025-06-01 20:10:50.142 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:10:50.144 - Successfully loaded summary stats: {'total_earnings': 788.8000000000001, 'daily_earnings': 788.8000000000001, 'daily_games': 23, 'wallet_balance': 0}
2025-06-01 20:10:50.148 - Successfully loaded game history: 10 entries
2025-06-01 20:10:50.149 - Successfully loaded weekly stats: 7 days
2025-06-01 20:10:50.152 - Saved 3 items to cache
2025-06-01 20:10:50.161 - Background data loading completed
2025-06-01 20:14:05.932 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:35.970 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.072 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.095 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.106 - Forcing refresh of all stats data
2025-06-01 20:18:36.115 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:18:36.217 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:18:36.226 - Force refresh via GameStatsIntegration successful
2025-06-01 20:18:36.232 - Clearing cached data
2025-06-01 20:18:36.239 - Posted refresh_stats event to trigger UI update
2025-06-01 20:18:36.241 - Starting background data reload
2025-06-01 20:18:36.244 - Starting background data loading thread
2025-06-01 20:18:36.261 - Background data loading started
2025-06-01 20:18:36.282 - Loading summary statistics
2025-06-01 20:18:36.338 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:18:36.345 - Data from GameStatsIntegration: {'total_earnings': 846.4000000000001, 'daily_earnings': 846.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-01 20:18:36.348 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:18:36.351 - Successfully loaded summary stats: {'total_earnings': 846.4000000000001, 'daily_earnings': 846.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-01 20:18:36.362 - Successfully loaded game history: 10 entries
2025-06-01 20:18:36.369 - Successfully loaded weekly stats: 7 days
2025-06-01 20:18:36.374 - Saved 3 items to cache
2025-06-01 20:18:36.377 - Background data loading completed
2025-06-01 20:20:19.077 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.316 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.438 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.462 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.471 - Forcing refresh of all stats data
2025-06-01 20:26:34.480 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:26:34.657 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:26:34.665 - Force refresh via GameStatsIntegration successful
2025-06-01 20:26:34.671 - Clearing cached data
2025-06-01 20:26:34.679 - Posted refresh_stats event to trigger UI update
2025-06-01 20:26:34.685 - Starting background data reload
2025-06-01 20:26:34.689 - Starting background data loading thread
2025-06-01 20:26:34.693 - Background data loading started
2025-06-01 20:26:34.702 - Loading summary statistics
2025-06-01 20:26:34.734 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:26:34.767 - Data from GameStatsIntegration: {'total_earnings': 908.8000000000001, 'daily_earnings': 908.8000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-01 20:26:34.779 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:26:34.782 - Successfully loaded summary stats: {'total_earnings': 908.8000000000001, 'daily_earnings': 908.8000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-01 20:26:34.787 - Successfully loaded game history: 10 entries
2025-06-01 20:26:34.795 - Successfully loaded weekly stats: 7 days
2025-06-01 20:26:34.804 - Saved 3 items to cache
2025-06-01 20:26:34.808 - Background data loading completed
2025-06-01 20:27:31.017 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.026 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.696 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.712 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.717 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.718 - Forcing refresh of all stats data
2025-06-01 20:30:15.719 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:30:15.734 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:30:15.735 - Force refresh via GameStatsIntegration successful
2025-06-01 20:30:15.736 - Clearing cached data
2025-06-01 20:30:15.737 - Posted refresh_stats event to trigger UI update
2025-06-01 20:30:15.737 - Starting background data reload
2025-06-01 20:30:15.737 - Starting background data loading thread
2025-06-01 20:30:15.741 - Background data loading started
2025-06-01 20:30:15.757 - Loading summary statistics
2025-06-01 20:30:15.779 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:30:15.782 - Data from GameStatsIntegration: {'total_earnings': 956.8000000000001, 'daily_earnings': 956.8000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-01 20:30:15.790 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:30:15.791 - Successfully loaded summary stats: {'total_earnings': 956.8000000000001, 'daily_earnings': 956.8000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-01 20:30:15.792 - Successfully loaded game history: 10 entries
2025-06-01 20:30:15.793 - Successfully loaded weekly stats: 7 days
2025-06-01 20:30:15.796 - Saved 3 items to cache
2025-06-01 20:30:15.797 - Background data loading completed
2025-06-01 20:34:28.976 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.024 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.028 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.033 - Forcing refresh of all stats data
2025-06-01 20:34:29.036 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:34:29.115 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:34:29.126 - Force refresh via GameStatsIntegration successful
2025-06-01 20:34:29.133 - Clearing cached data
2025-06-01 20:34:29.138 - Posted refresh_stats event to trigger UI update
2025-06-01 20:34:29.146 - Starting background data reload
2025-06-01 20:34:29.152 - Starting background data loading thread
2025-06-01 20:34:29.160 - Background data loading started
2025-06-01 20:34:29.178 - Loading summary statistics
2025-06-01 20:34:29.222 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:34:29.223 - Data from GameStatsIntegration: {'total_earnings': 1004.8000000000001, 'daily_earnings': 1004.8000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-01 20:34:29.228 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:34:29.235 - Successfully loaded summary stats: {'total_earnings': 1004.8000000000001, 'daily_earnings': 1004.8000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-01 20:34:29.254 - Successfully loaded game history: 10 entries
2025-06-01 20:34:29.258 - Successfully loaded weekly stats: 7 days
2025-06-01 20:34:29.273 - Saved 3 items to cache
2025-06-01 20:34:29.278 - Background data loading completed
2025-06-01 20:35:22.286 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.769 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.786 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.791 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.793 - Forcing refresh of all stats data
2025-06-01 20:37:59.795 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:37:59.810 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:37:59.811 - Force refresh via GameStatsIntegration successful
2025-06-01 20:37:59.812 - Clearing cached data
2025-06-01 20:37:59.812 - Posted refresh_stats event to trigger UI update
2025-06-01 20:37:59.813 - Starting background data reload
2025-06-01 20:37:59.815 - Starting background data loading thread
2025-06-01 20:37:59.818 - Background data loading started
2025-06-01 20:37:59.828 - Loading summary statistics
2025-06-01 20:37:59.856 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:37:59.856 - Data from GameStatsIntegration: {'total_earnings': 1048.0, 'daily_earnings': 1048.0, 'daily_games': 28, 'wallet_balance': 0}
2025-06-01 20:37:59.857 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:37:59.857 - Successfully loaded summary stats: {'total_earnings': 1048.0, 'daily_earnings': 1048.0, 'daily_games': 28, 'wallet_balance': 0}
2025-06-01 20:37:59.857 - Successfully loaded game history: 10 entries
2025-06-01 20:37:59.858 - Successfully loaded weekly stats: 7 days
2025-06-01 20:37:59.859 - Saved 3 items to cache
2025-06-01 20:37:59.860 - Background data loading completed
2025-06-01 20:40:10.639 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.139 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.247 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.260 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.267 - Forcing refresh of all stats data
2025-06-01 20:45:24.292 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:45:24.468 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:45:24.476 - Force refresh via GameStatsIntegration successful
2025-06-01 20:45:24.484 - Clearing cached data
2025-06-01 20:45:24.619 - Posted refresh_stats event to trigger UI update
2025-06-01 20:45:24.629 - Starting background data reload
2025-06-01 20:45:24.632 - Starting background data loading thread
2025-06-01 20:45:24.643 - Background data loading started
2025-06-01 20:45:24.660 - Loading summary statistics
2025-06-01 20:45:24.710 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:45:24.719 - Data from GameStatsIntegration: {'total_earnings': 1091.2, 'daily_earnings': 1091.2, 'daily_games': 29, 'wallet_balance': 0}
2025-06-01 20:45:24.722 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:45:24.725 - Successfully loaded summary stats: {'total_earnings': 1091.2, 'daily_earnings': 1091.2, 'daily_games': 29, 'wallet_balance': 0}
2025-06-01 20:45:24.728 - Successfully loaded game history: 10 entries
2025-06-01 20:45:24.730 - Successfully loaded weekly stats: 7 days
2025-06-01 20:45:24.734 - Saved 3 items to cache
2025-06-01 20:45:24.753 - Background data loading completed
2025-06-01 20:46:43.540 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.752 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.771 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.774 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.775 - Forcing refresh of all stats data
2025-06-01 20:49:19.776 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:49:19.792 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:49:19.794 - Force refresh via GameStatsIntegration successful
2025-06-01 20:49:19.795 - Clearing cached data
2025-06-01 20:49:19.795 - Posted refresh_stats event to trigger UI update
2025-06-01 20:49:19.796 - Starting background data reload
2025-06-01 20:49:19.798 - Starting background data loading thread
2025-06-01 20:49:19.800 - Background data loading started
2025-06-01 20:49:19.811 - Loading summary statistics
2025-06-01 20:49:19.839 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:49:19.840 - Data from GameStatsIntegration: {'total_earnings': 1139.2, 'daily_earnings': 1139.2, 'daily_games': 30, 'wallet_balance': 0}
2025-06-01 20:49:19.840 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:49:19.841 - Successfully loaded summary stats: {'total_earnings': 1139.2, 'daily_earnings': 1139.2, 'daily_games': 30, 'wallet_balance': 0}
2025-06-01 20:49:19.841 - Successfully loaded game history: 10 entries
2025-06-01 20:49:19.842 - Successfully loaded weekly stats: 7 days
2025-06-01 20:49:19.844 - Saved 3 items to cache
2025-06-01 20:49:19.844 - Background data loading completed
2025-06-01 20:52:18.922 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.758 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.887 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.912 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:23.914 - Forcing refresh of all stats data
2025-06-01 20:55:23.921 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:55:24.018 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:55:24.028 - Force refresh via GameStatsIntegration successful
2025-06-01 20:55:24.039 - Clearing cached data
2025-06-01 20:55:24.042 - Posted refresh_stats event to trigger UI update
2025-06-01 20:55:24.046 - Starting background data reload
2025-06-01 20:55:24.050 - Starting background data loading thread
2025-06-01 20:55:24.052 - Background data loading started
2025-06-01 20:55:24.065 - Loading summary statistics
2025-06-01 20:55:24.432 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:55:24.446 - Data from GameStatsIntegration: {'total_earnings': 1187.2, 'daily_earnings': 1187.2, 'daily_games': 31, 'wallet_balance': 0}
2025-06-01 20:55:24.448 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:55:24.453 - Successfully loaded summary stats: {'total_earnings': 1187.2, 'daily_earnings': 1187.2, 'daily_games': 31, 'wallet_balance': 0}
2025-06-01 20:55:24.460 - Successfully loaded game history: 10 entries
2025-06-01 20:55:24.468 - Successfully loaded weekly stats: 7 days
2025-06-01 20:55:24.477 - Saved 3 items to cache
2025-06-01 20:55:24.481 - Background data loading completed
2025-06-01 20:56:25.788 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.753 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.768 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.770 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.772 - Forcing refresh of all stats data
2025-06-01 20:59:51.776 - Attempting to force refresh via GameStatsIntegration
2025-06-01 20:59:51.791 - get_stats_provider called, returning provider with initialized=True
2025-06-01 20:59:51.792 - Force refresh via GameStatsIntegration successful
2025-06-01 20:59:51.793 - Clearing cached data
2025-06-01 20:59:51.793 - Posted refresh_stats event to trigger UI update
2025-06-01 20:59:51.794 - Starting background data reload
2025-06-01 20:59:51.796 - Starting background data loading thread
2025-06-01 20:59:51.797 - Background data loading started
2025-06-01 20:59:51.808 - Loading summary statistics
2025-06-01 20:59:51.837 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 20:59:51.838 - Data from GameStatsIntegration: {'total_earnings': 1235.2, 'daily_earnings': 1235.2, 'daily_games': 32, 'wallet_balance': 0}
2025-06-01 20:59:51.838 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 20:59:51.838 - Successfully loaded summary stats: {'total_earnings': 1235.2, 'daily_earnings': 1235.2, 'daily_games': 32, 'wallet_balance': 0}
2025-06-01 20:59:51.839 - Successfully loaded game history: 10 entries
2025-06-01 20:59:51.840 - Successfully loaded weekly stats: 7 days
2025-06-01 20:59:51.842 - Saved 3 items to cache
2025-06-01 20:59:51.843 - Background data loading completed
2025-06-01 21:03:31.709 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.203 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.219 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.224 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.225 - Forcing refresh of all stats data
2025-06-01 21:06:51.228 - Attempting to force refresh via GameStatsIntegration
2025-06-01 21:06:51.244 - get_stats_provider called, returning provider with initialized=True
2025-06-01 21:06:51.245 - Force refresh via GameStatsIntegration successful
2025-06-01 21:06:51.246 - Clearing cached data
2025-06-01 21:06:51.247 - Posted refresh_stats event to trigger UI update
2025-06-01 21:06:51.248 - Starting background data reload
2025-06-01 21:06:51.250 - Starting background data loading thread
2025-06-01 21:06:51.250 - Background data loading started
2025-06-01 21:06:51.261 - Loading summary statistics
2025-06-01 21:06:51.293 - Attempting to load summary stats from GameStatsIntegration
2025-06-01 21:06:51.293 - Data from GameStatsIntegration: {'total_earnings': 1275.2, 'daily_earnings': 1275.2, 'daily_games': 33, 'wallet_balance': 0}
2025-06-01 21:06:51.293 - Successfully loaded summary stats from GameStatsIntegration
2025-06-01 21:06:51.294 - Successfully loaded summary stats: {'total_earnings': 1275.2, 'daily_earnings': 1275.2, 'daily_games': 33, 'wallet_balance': 0}
2025-06-01 21:06:51.294 - Successfully loaded game history: 10 entries
2025-06-01 21:06:51.295 - Successfully loaded weekly stats: 7 days
2025-06-01 21:06:51.296 - Saved 3 items to cache
2025-06-01 21:06:51.297 - Background data loading completed
2025-06-02 09:24:09.345 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.386 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.414 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.415 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-02 09:24:09.415 - Initializing StatsDataProvider
2025-06-02 09:24:09.416 - Loaded 8 items from cache
2025-06-02 09:24:09.417 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-02 09:24:09.417 - Starting background data loading thread
2025-06-02 09:24:09.418 - StatsDataProvider initialization completed
2025-06-02 09:24:09.418 - Background data loading started
2025-06-02 09:24:09.418 - Created singleton instance of StatsDataProvider on module import
2025-06-02 09:24:09.419 - Loading summary statistics
2025-06-02 09:24:09.419 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-02 09:24:09.419 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 09:24:09.420 - get_stats_provider called, returning provider with initialized=True
2025-06-02 09:24:09.420 - Data from GameStatsIntegration: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.421 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 09:24:09.421 - Successfully loaded summary stats: {'total_earnings': 1275.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 09:24:09.422 - Successfully loaded game history: 10 entries
2025-06-02 09:24:09.422 - Successfully loaded weekly stats: 7 days
2025-06-02 09:24:09.425 - Saved 10 items to cache
2025-06-02 09:24:09.425 - Background data loading completed
2025-06-02 18:11:09.465 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:11:09.483 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:11:09.489 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:11:09.490 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-02 18:11:09.490 - Initializing StatsDataProvider
2025-06-02 18:11:09.502 - Loaded 8 items from cache
2025-06-02 18:11:09.502 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-02 18:11:09.503 - Starting background data loading thread
2025-06-02 18:11:09.504 - Background data loading started
2025-06-02 18:11:09.504 - StatsDataProvider initialization completed
2025-06-02 18:11:09.504 - Loading summary statistics
f StatsDataProvider on module import
2025-06-02 18:11:09.505 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-02 18:11:09.506 - Data from GameStatsIntegration: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:11:09.507 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 18:11:09.507 - Successfully loaded summary stats: {'total_earnings': 3480.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-02 18:11:09.508 - Successfully loaded game history: 10 entries
2025-06-02 18:11:09.509 - Successfully loaded weekly stats: 7 days
2025-06-02 18:11:09.512 - Saved 10 items to cache
2025-06-02 18:11:09.512 - Background data loading completed
2025-06-02 18:14:35.168 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:21:03.201 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:30:18.645 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:39:25.431 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:41:14.724 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:41:14.742 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:41:14.744 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:41:14.745 - Forcing refresh of all stats data
2025-06-02 18:41:14.745 - Attempting to force refresh via GameStatsIntegration
2025-06-02 18:41:14.762 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:41:14.763 - Force refresh via GameStatsIntegration successful
2025-06-02 18:41:14.764 - Clearing cached data
2025-06-02 18:41:14.765 - Posted refresh_stats event to trigger UI update
2025-06-02 18:41:14.766 - Starting background data reload
2025-06-02 18:41:14.766 - Starting background data loading thread
2025-06-02 18:41:14.768 - Background data loading started
2025-06-02 18:41:14.821 - Loading summary statistics
2025-06-02 18:41:14.822 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 18:41:14.823 - Data from GameStatsIntegration: {'total_earnings': 3492.8, 'daily_earnings': 12.8, 'daily_games': 1, 'wallet_balance': 0}
2025-06-02 18:41:14.823 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 18:41:14.824 - Successfully loaded summary stats: {'total_earnings': 3492.8, 'daily_earnings': 12.8, 'daily_games': 1, 'wallet_balance': 0}
2025-06-02 18:41:14.825 - Successfully loaded game history: 10 entries
2025-06-02 18:41:14.825 - Successfully loaded weekly stats: 7 days
2025-06-02 18:41:14.828 - Saved 3 items to cache
2025-06-02 18:41:14.829 - Background data loading completed
2025-06-02 18:41:54.940 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:44:29.800 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:44:29.816 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:44:29.819 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:44:29.820 - Forcing refresh of all stats data
2025-06-02 18:44:29.821 - Attempting to force refresh via GameStatsIntegration
2025-06-02 18:44:29.836 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:44:29.837 - Force refresh via GameStatsIntegration successful
2025-06-02 18:44:29.837 - Clearing cached data
2025-06-02 18:44:29.838 - Posted refresh_stats event to trigger UI update
2025-06-02 18:44:29.839 - Starting background data reload
2025-06-02 18:44:29.841 - Starting background data loading thread
2025-06-02 18:44:29.842 - Background data loading started
2025-06-02 18:44:29.878 - Loading summary statistics
2025-06-02 18:44:29.878 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 18:44:29.879 - Data from GameStatsIntegration: {'total_earnings': 3508.8, 'daily_earnings': 28.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-02 18:44:29.880 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 18:44:29.880 - Successfully loaded summary stats: {'total_earnings': 3508.8, 'daily_earnings': 28.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-02 18:44:29.881 - Successfully loaded game history: 10 entries
2025-06-02 18:44:29.882 - Successfully loaded weekly stats: 7 days
2025-06-02 18:44:29.884 - Saved 3 items to cache
2025-06-02 18:44:29.884 - Background data loading completed
2025-06-02 18:45:46.460 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:48:40.756 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:48:40.772 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:48:40.774 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:48:40.775 - Forcing refresh of all stats data
2025-06-02 18:48:40.776 - Attempting to force refresh via GameStatsIntegration
2025-06-02 18:48:40.792 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:48:40.793 - Force refresh via GameStatsIntegration successful
2025-06-02 18:48:40.794 - Clearing cached data
2025-06-02 18:48:40.795 - Posted refresh_stats event to trigger UI update
2025-06-02 18:48:40.796 - Starting background data reload
2025-06-02 18:48:40.796 - Starting background data loading thread
2025-06-02 18:48:40.798 - Background data loading started
2025-06-02 18:48:40.831 - Loading summary statistics
2025-06-02 18:48:40.839 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 18:48:40.840 - Data from GameStatsIntegration: {'total_earnings': 3524.8, 'daily_earnings': 44.8, 'daily_games': 3, 'wallet_balance': 0}
2025-06-02 18:48:40.848 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 18:48:40.849 - Successfully loaded summary stats: {'total_earnings': 3524.8, 'daily_earnings': 44.8, 'daily_games': 3, 'wallet_balance': 0}
2025-06-02 18:48:40.850 - Successfully loaded game history: 10 entries
2025-06-02 18:48:40.852 - Successfully loaded weekly stats: 7 days
2025-06-02 18:48:40.862 - Saved 3 items to cache
2025-06-02 18:48:40.862 - Background data loading completed
2025-06-02 18:52:53.020 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:55:02.456 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:55:02.471 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:55:02.473 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:55:02.474 - Forcing refresh of all stats data
2025-06-02 18:55:02.475 - Attempting to force refresh via GameStatsIntegration
2025-06-02 18:55:02.488 - get_stats_provider called, returning provider with initialized=True
2025-06-02 18:55:02.488 - Force refresh via GameStatsIntegration successful
2025-06-02 18:55:02.489 - Clearing cached data
2025-06-02 18:55:02.490 - Posted refresh_stats event to trigger UI update
2025-06-02 18:55:02.491 - Starting background data reload
2025-06-02 18:55:02.492 - Starting background data loading thread
2025-06-02 18:55:02.493 - Background data loading started
2025-06-02 18:55:02.522 - Loading summary statistics
2025-06-02 18:55:02.532 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 18:55:02.533 - Data from GameStatsIntegration: {'total_earnings': 3544.0, 'daily_earnings': 64.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-02 18:55:02.534 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 18:55:02.535 - Successfully loaded summary stats: {'total_earnings': 3544.0, 'daily_earnings': 64.0, 'daily_games': 4, 'wallet_balance': 0}
2025-06-02 18:55:02.536 - Successfully loaded game history: 10 entries
2025-06-02 18:55:02.537 - Successfully loaded weekly stats: 7 days
2025-06-02 18:55:02.539 - Saved 3 items to cache
2025-06-02 18:55:02.540 - Background data loading completed
2025-06-02 18:58:05.273 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:05:20.274 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:05:20.289 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:05:20.291 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:05:20.292 - Forcing refresh of all stats data
2025-06-02 19:05:20.293 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:05:20.310 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:05:20.311 - Force refresh via GameStatsIntegration successful
2025-06-02 19:05:20.312 - Clearing cached data
2025-06-02 19:05:20.313 - Posted refresh_stats event to trigger UI update
2025-06-02 19:05:20.314 - Starting background data reload
2025-06-02 19:05:20.314 - Starting background data loading thread
2025-06-02 19:05:20.316 - Background data loading started
2025-06-02 19:05:20.351 - Loading summary statistics
2025-06-02 19:05:20.354 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:05:20.355 - Data from GameStatsIntegration: {'total_earnings': 3577.6, 'daily_earnings': 97.6, 'daily_games': 5, 'wallet_balance': 0}
2025-06-02 19:05:20.356 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:05:20.356 - Successfully loaded summary stats: {'total_earnings': 3577.6, 'daily_earnings': 97.6, 'daily_games': 5, 'wallet_balance': 0}
2025-06-02 19:05:20.357 - Successfully loaded game history: 10 entries
2025-06-02 19:05:20.358 - Successfully loaded weekly stats: 7 days
2025-06-02 19:05:20.359 - Saved 3 items to cache
2025-06-02 19:05:20.359 - Background data loading completed
2025-06-02 19:08:28.268 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:13:21.782 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:13:21.796 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:13:21.799 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:13:21.800 - Forcing refresh of all stats data
2025-06-02 19:13:21.802 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:13:21.820 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:13:21.822 - Force refresh via GameStatsIntegration successful
2025-06-02 19:13:21.822 - Clearing cached data
2025-06-02 19:13:21.824 - Posted refresh_stats event to trigger UI update
2025-06-02 19:13:21.825 - Starting background data reload
2025-06-02 19:13:21.825 - Starting background data loading thread
2025-06-02 19:13:21.828 - Background data loading started
2025-06-02 19:13:21.862 - Loading summary statistics
2025-06-02 19:13:21.863 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:13:21.863 - Data from GameStatsIntegration: {'total_earnings': 3630.4, 'daily_earnings': 150.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-02 19:13:21.864 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:13:21.864 - Successfully loaded summary stats: {'total_earnings': 3630.4, 'daily_earnings': 150.4, 'daily_games': 6, 'wallet_balance': 0}
2025-06-02 19:13:21.865 - Successfully loaded game history: 10 entries
2025-06-02 19:13:21.866 - Successfully loaded weekly stats: 7 days
2025-06-02 19:13:21.867 - Saved 3 items to cache
2025-06-02 19:13:21.867 - Background data loading completed
2025-06-02 19:16:00.093 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:21:10.521 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:21:10.537 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:21:10.539 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:21:10.540 - Forcing refresh of all stats data
2025-06-02 19:21:10.541 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:21:10.556 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:21:10.557 - Force refresh via GameStatsIntegration successful
2025-06-02 19:21:10.558 - Clearing cached data
2025-06-02 19:21:10.559 - Posted refresh_stats event to trigger UI update
2025-06-02 19:21:10.560 - Starting background data reload
2025-06-02 19:21:10.561 - Starting background data loading thread
2025-06-02 19:21:10.562 - Background data loading started
2025-06-02 19:21:10.591 - Loading summary statistics
2025-06-02 19:21:10.604 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:21:10.605 - Data from GameStatsIntegration: {'total_earnings': 3678.4, 'daily_earnings': 198.4, 'daily_games': 7, 'wallet_balance': 0}
2025-06-02 19:21:10.605 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:21:10.606 - Successfully loaded summary stats: {'total_earnings': 3678.4, 'daily_earnings': 198.4, 'daily_games': 7, 'wallet_balance': 0}
2025-06-02 19:21:10.607 - Successfully loaded game history: 10 entries
2025-06-02 19:21:10.607 - Successfully loaded weekly stats: 7 days
2025-06-02 19:21:10.610 - Saved 3 items to cache
2025-06-02 19:21:10.610 - Background data loading completed
2025-06-02 19:26:39.780 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:28:36.101 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:28:36.118 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:28:36.121 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:28:36.122 - Forcing refresh of all stats data
2025-06-02 19:28:36.123 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:28:36.143 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:28:36.144 - Force refresh via GameStatsIntegration successful
2025-06-02 19:28:36.145 - Clearing cached data
2025-06-02 19:28:36.146 - Posted refresh_stats event to trigger UI update
2025-06-02 19:28:36.147 - Starting background data reload
2025-06-02 19:28:36.148 - Starting background data loading thread
2025-06-02 19:28:36.149 - Background data loading started
2025-06-02 19:28:36.183 - Loading summary statistics
2025-06-02 19:28:36.184 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:28:36.185 - Data from GameStatsIntegration: {'total_earnings': 3721.6, 'daily_earnings': 241.60000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-02 19:28:36.187 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:28:36.187 - Successfully loaded summary stats: {'total_earnings': 3721.6, 'daily_earnings': 241.60000000000002, 'daily_games': 8, 'wallet_balance': 0}
2025-06-02 19:28:36.188 - Successfully loaded game history: 10 entries
2025-06-02 19:28:36.188 - Successfully loaded weekly stats: 7 days
2025-06-02 19:28:36.191 - Saved 3 items to cache
2025-06-02 19:28:36.191 - Background data loading completed
2025-06-02 19:32:32.891 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:34:06.017 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:34:06.031 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:34:06.034 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:34:06.035 - Forcing refresh of all stats data
2025-06-02 19:34:06.036 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:34:06.049 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:34:06.050 - Force refresh via GameStatsIntegration successful
2025-06-02 19:34:06.051 - Clearing cached data
2025-06-02 19:34:06.052 - Posted refresh_stats event to trigger UI update
2025-06-02 19:34:06.053 - Starting background data reload
2025-06-02 19:34:06.054 - Starting background data loading thread
2025-06-02 19:34:06.055 - Background data loading started
2025-06-02 19:34:06.091 - Loading summary statistics
2025-06-02 19:34:06.092 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:34:06.093 - Data from GameStatsIntegration: {'total_earnings': 3764.8, 'daily_earnings': 284.8, 'daily_games': 9, 'wallet_balance': 0}
2025-06-02 19:34:06.094 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:34:06.094 - Successfully loaded summary stats: {'total_earnings': 3764.8, 'daily_earnings': 284.8, 'daily_games': 9, 'wallet_balance': 0}
2025-06-02 19:34:06.095 - Successfully loaded game history: 10 entries
2025-06-02 19:34:06.096 - Successfully loaded weekly stats: 7 days
2025-06-02 19:34:06.098 - Saved 3 items to cache
2025-06-02 19:34:06.098 - Background data loading completed
2025-06-02 19:37:59.266 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:41:59.492 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:41:59.507 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:41:59.510 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:41:59.511 - Forcing refresh of all stats data
2025-06-02 19:41:59.512 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:41:59.526 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:41:59.527 - Force refresh via GameStatsIntegration successful
2025-06-02 19:41:59.528 - Clearing cached data
2025-06-02 19:41:59.530 - Posted refresh_stats event to trigger UI update
2025-06-02 19:41:59.530 - Starting background data reload
2025-06-02 19:41:59.531 - Starting background data loading thread
2025-06-02 19:41:59.532 - Background data loading started
2025-06-02 19:41:59.569 - Loading summary statistics
2025-06-02 19:41:59.571 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:41:59.572 - Data from GameStatsIntegration: {'total_earnings': 3812.8, 'daily_earnings': 332.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-02 19:41:59.572 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:41:59.573 - Successfully loaded summary stats: {'total_earnings': 3812.8, 'daily_earnings': 332.8, 'daily_games': 10, 'wallet_balance': 0}
2025-06-02 19:41:59.573 - Successfully loaded game history: 10 entries
2025-06-02 19:41:59.574 - Successfully loaded weekly stats: 7 days
2025-06-02 19:41:59.576 - Saved 3 items to cache
2025-06-02 19:41:59.576 - Background data loading completed
2025-06-02 19:46:59.539 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:49:39.168 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:49:39.193 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:49:39.195 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:49:39.196 - Forcing refresh of all stats data
2025-06-02 19:49:39.197 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:49:39.216 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:49:39.217 - Force refresh via GameStatsIntegration successful
2025-06-02 19:49:39.218 - Clearing cached data
2025-06-02 19:49:39.219 - Posted refresh_stats event to trigger UI update
2025-06-02 19:49:39.220 - Starting background data reload
2025-06-02 19:49:39.222 - Starting background data loading thread
2025-06-02 19:49:39.223 - Background data loading started
2025-06-02 19:49:39.293 - Loading summary statistics
2025-06-02 19:49:39.294 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:49:39.295 - Data from GameStatsIntegration: {'total_earnings': 3870.4, 'daily_earnings': 390.40000000000003, 'daily_games': 11, 'wallet_balance': 0}
2025-06-02 19:49:39.296 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:49:39.296 - Successfully loaded summary stats: {'total_earnings': 3870.4, 'daily_earnings': 390.40000000000003, 'daily_games': 11, 'wallet_balance': 0}
2025-06-02 19:49:39.297 - Successfully loaded game history: 10 entries
2025-06-02 19:49:39.298 - Successfully loaded weekly stats: 7 days
2025-06-02 19:49:39.299 - Saved 3 items to cache
2025-06-02 19:49:39.300 - Background data loading completed
2025-06-02 19:52:59.272 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:58:38.676 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:58:38.690 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:58:38.692 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:58:38.694 - Forcing refresh of all stats data
2025-06-02 19:58:38.694 - Attempting to force refresh via GameStatsIntegration
2025-06-02 19:58:38.709 - get_stats_provider called, returning provider with initialized=True
2025-06-02 19:58:38.710 - Force refresh via GameStatsIntegration successful
2025-06-02 19:58:38.712 - Clearing cached data
2025-06-02 19:58:38.712 - Posted refresh_stats event to trigger UI update
2025-06-02 19:58:38.713 - Starting background data reload
2025-06-02 19:58:38.714 - Starting background data loading thread
2025-06-02 19:58:38.717 - Background data loading started
2025-06-02 19:58:38.752 - Loading summary statistics
2025-06-02 19:58:38.752 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 19:58:38.754 - Data from GameStatsIntegration: {'total_earnings': 3918.4, 'daily_earnings': 438.40000000000003, 'daily_games': 12, 'wallet_balance': 0}
2025-06-02 19:58:38.754 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 19:58:38.755 - Successfully loaded summary stats: {'total_earnings': 3918.4, 'daily_earnings': 438.40000000000003, 'daily_games': 12, 'wallet_balance': 0}
2025-06-02 19:58:38.755 - Successfully loaded game history: 10 entries
2025-06-02 19:58:38.756 - Successfully loaded weekly stats: 7 days
2025-06-02 19:58:38.758 - Saved 3 items to cache
2025-06-02 19:58:38.758 - Background data loading completed
2025-06-02 19:59:55.067 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:04:31.305 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:04:31.319 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:04:31.322 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:04:31.322 - Forcing refresh of all stats data
2025-06-02 20:04:31.324 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:04:31.339 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:04:31.340 - Force refresh via GameStatsIntegration successful
2025-06-02 20:04:31.342 - Clearing cached data
2025-06-02 20:04:31.342 - Posted refresh_stats event to trigger UI update
2025-06-02 20:04:31.343 - Starting background data reload
2025-06-02 20:04:31.344 - Starting background data loading thread
2025-06-02 20:04:31.345 - Background data loading started
2025-06-02 20:04:31.381 - Loading summary statistics
2025-06-02 20:04:31.381 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:04:31.382 - Data from GameStatsIntegration: {'total_earnings': 3976.0, 'daily_earnings': 496.00000000000006, 'daily_games': 13, 'wallet_balance': 0}
2025-06-02 20:04:31.382 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:04:31.382 - Successfully loaded summary stats: {'total_earnings': 3976.0, 'daily_earnings': 496.00000000000006, 'daily_games': 13, 'wallet_balance': 0}
2025-06-02 20:04:31.383 - Successfully loaded game history: 10 entries
2025-06-02 20:04:31.384 - Successfully loaded weekly stats: 7 days
2025-06-02 20:04:31.386 - Saved 3 items to cache
2025-06-02 20:04:31.386 - Background data loading completed
2025-06-02 20:07:39.460 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:10:51.242 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:10:51.256 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:10:51.259 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:10:51.259 - Forcing refresh of all stats data
2025-06-02 20:10:51.261 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:10:51.274 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:10:51.276 - Force refresh via GameStatsIntegration successful
2025-06-02 20:10:51.276 - Clearing cached data
2025-06-02 20:10:51.278 - Posted refresh_stats event to trigger UI update
2025-06-02 20:10:51.280 - Starting background data reload
2025-06-02 20:10:51.281 - Starting background data loading thread
2025-06-02 20:10:51.282 - Background data loading started
2025-06-02 20:10:51.315 - Loading summary statistics
2025-06-02 20:10:51.316 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:10:51.317 - Data from GameStatsIntegration: {'total_earnings': 4033.6, 'daily_earnings': 553.6, 'daily_games': 14, 'wallet_balance': 0}
2025-06-02 20:10:51.318 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:10:51.318 - Successfully loaded summary stats: {'total_earnings': 4033.6, 'daily_earnings': 553.6, 'daily_games': 14, 'wallet_balance': 0}
2025-06-02 20:10:51.318 - Successfully loaded game history: 10 entries
2025-06-02 20:10:51.319 - Successfully loaded weekly stats: 7 days
2025-06-02 20:10:51.321 - Saved 3 items to cache
2025-06-02 20:10:51.322 - Background data loading completed
2025-06-02 20:17:17.894 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:20:37.167 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:20:37.182 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:20:37.184 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:20:37.185 - Forcing refresh of all stats data
2025-06-02 20:20:37.186 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:20:37.202 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:20:37.203 - Force refresh via GameStatsIntegration successful
2025-06-02 20:20:37.204 - Clearing cached data
2025-06-02 20:20:37.206 - Posted refresh_stats event to trigger UI update
2025-06-02 20:20:37.207 - Starting background data reload
2025-06-02 20:20:37.208 - Starting background data loading thread
2025-06-02 20:20:37.210 - Background data loading started
2025-06-02 20:20:37.246 - Loading summary statistics
2025-06-02 20:20:37.247 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:20:37.249 - Data from GameStatsIntegration: {'total_earnings': 4086.4, 'daily_earnings': 606.4, 'daily_games': 15, 'wallet_balance': 0}
2025-06-02 20:20:37.250 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:20:37.251 - Successfully loaded summary stats: {'total_earnings': 4086.4, 'daily_earnings': 606.4, 'daily_games': 15, 'wallet_balance': 0}
2025-06-02 20:20:37.251 - Successfully loaded game history: 10 entries
2025-06-02 20:20:37.251 - Successfully loaded weekly stats: 7 days
2025-06-02 20:20:37.254 - Saved 3 items to cache
2025-06-02 20:20:37.255 - Background data loading completed
2025-06-02 20:23:21.978 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:26:20.690 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:26:20.705 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:26:20.707 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:26:20.708 - Forcing refresh of all stats data
2025-06-02 20:26:20.709 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:26:20.724 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:26:20.725 - Force refresh via GameStatsIntegration successful
2025-06-02 20:26:20.726 - Clearing cached data
2025-06-02 20:26:20.727 - Posted refresh_stats event to trigger UI update
2025-06-02 20:26:20.727 - Starting background data reload
2025-06-02 20:26:20.730 - Starting background data loading thread
2025-06-02 20:26:20.733 - Background data loading started
2025-06-02 20:26:20.767 - Loading summary statistics
2025-06-02 20:26:20.767 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:26:20.768 - Data from GameStatsIntegration: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:26:20.769 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:26:20.769 - Successfully loaded summary stats: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:26:20.770 - Successfully loaded game history: 10 entries
2025-06-02 20:26:20.771 - Successfully loaded weekly stats: 7 days
2025-06-02 20:26:20.773 - Saved 3 items to cache
2025-06-02 20:26:20.774 - Background data loading completed
2025-06-02 20:31:43.547 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:31:43.549 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:31:43.582 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:31:43.587 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-02 20:31:43.589 - Initializing StatsDataProvider
2025-06-02 20:31:43.608 - Loaded 8 items from cache
2025-06-02 20:31:43.609 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-02 20:31:43.610 - Starting background data loading thread
2025-06-02 20:31:43.612 - Background data loading started
2025-06-02 20:31:43.612 - StatsDataProvider initialization completed
2025-06-02 20:31:43.613 - Created singleton instance of StatsDataProvider on module import
2025-06-02 20:31:43.613 - Loading summary statistics
2025-06-02 20:31:43.615 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:31:43.615 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-02 20:31:43.617 - Data from GameStatsIntegration: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:31:43.617 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:31:43.618 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:31:43.620 - Successfully loaded summary stats: {'total_earnings': 4120.0, 'daily_earnings': 640.0, 'daily_games': 16, 'wallet_balance': 0}
2025-06-02 20:31:43.622 - Successfully loaded game history: 10 entries
2025-06-02 20:31:43.626 - Successfully loaded weekly stats: 7 days
2025-06-02 20:31:43.636 - Saved 10 items to cache
2025-06-02 20:31:43.638 - Background data loading completed
2025-06-02 20:33:54.215 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:33:54.231 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:33:54.233 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:33:54.235 - Forcing refresh of all stats data
2025-06-02 20:33:54.236 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:33:54.255 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:33:54.257 - Force refresh via GameStatsIntegration successful
2025-06-02 20:33:54.257 - Clearing cached data
2025-06-02 20:33:54.258 - Posted refresh_stats event to trigger UI update
2025-06-02 20:33:54.259 - Starting background data reload
2025-06-02 20:33:54.260 - Starting background data loading thread
2025-06-02 20:33:54.261 - Background data loading started
2025-06-02 20:33:54.284 - Loading summary statistics
2025-06-02 20:33:54.285 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:33:54.286 - Data from GameStatsIntegration: {'total_earnings': 4168.0, 'daily_earnings': 688.0, 'daily_games': 17, 'wallet_balance': 0}
2025-06-02 20:33:54.287 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:33:54.287 - Successfully loaded summary stats: {'total_earnings': 4168.0, 'daily_earnings': 688.0, 'daily_games': 17, 'wallet_balance': 0}
2025-06-02 20:33:54.288 - Successfully loaded game history: 10 entries
2025-06-02 20:33:54.288 - Successfully loaded weekly stats: 7 days
2025-06-02 20:33:54.290 - Saved 3 items to cache
2025-06-02 20:33:54.291 - Background data loading completed
2025-06-02 20:37:57.623 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:39:56.189 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:39:56.208 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:39:56.209 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:39:56.211 - Forcing refresh of all stats data
2025-06-02 20:39:56.212 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:39:56.227 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:39:56.228 - Force refresh via GameStatsIntegration successful
2025-06-02 20:39:56.229 - Clearing cached data
2025-06-02 20:39:56.230 - Posted refresh_stats event to trigger UI update
2025-06-02 20:39:56.231 - Starting background data reload
2025-06-02 20:39:56.231 - Starting background data loading thread
2025-06-02 20:39:56.233 - Background data loading started
2025-06-02 20:39:56.255 - Loading summary statistics
2025-06-02 20:39:56.256 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:39:56.257 - Data from GameStatsIntegration: {'total_earnings': 4216.0, 'daily_earnings': 736.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-02 20:39:56.257 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:39:56.258 - Successfully loaded summary stats: {'total_earnings': 4216.0, 'daily_earnings': 736.0, 'daily_games': 18, 'wallet_balance': 0}
2025-06-02 20:39:56.259 - Successfully loaded game history: 10 entries
2025-06-02 20:39:56.259 - Successfully loaded weekly stats: 7 days
2025-06-02 20:39:56.261 - Saved 3 items to cache
2025-06-02 20:39:56.261 - Background data loading completed
2025-06-02 20:40:34.020 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:44:14.116 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.647 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.846 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.870 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.874 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.877 - Forcing refresh of all stats data
2025-06-02 20:46:47.880 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:46:47.902 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:46:47.905 - Force refresh via GameStatsIntegration successful
2025-06-02 20:46:47.906 - Clearing cached data
2025-06-02 20:46:47.908 - Posted refresh_stats event to trigger UI update
2025-06-02 20:46:47.909 - Starting background data reload
2025-06-02 20:46:47.911 - Starting background data loading thread
2025-06-02 20:46:47.916 - Background data loading started
2025-06-02 20:46:47.939 - Loading summary statistics
2025-06-02 20:46:47.943 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:46:47.944 - Data from GameStatsIntegration: {'total_earnings': 4254.4, 'daily_earnings': 774.4, 'daily_games': 19, 'wallet_balance': 0}
2025-06-02 20:46:47.946 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:46:47.948 - Successfully loaded summary stats: {'total_earnings': 4254.4, 'daily_earnings': 774.4, 'daily_games': 19, 'wallet_balance': 0}
2025-06-02 20:46:47.954 - Successfully loaded game history: 10 entries
2025-06-02 20:46:47.956 - Successfully loaded weekly stats: 7 days
2025-06-02 20:46:47.963 - Saved 3 items to cache
2025-06-02 20:46:47.964 - Background data loading completed
2025-06-02 20:50:19.375 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:50:19.388 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:50:19.393 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:50:19.395 - Forcing refresh of all stats data
2025-06-02 20:50:19.397 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:50:19.411 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:50:19.412 - Force refresh via GameStatsIntegration successful
2025-06-02 20:50:19.413 - Clearing cached data
2025-06-02 20:50:19.414 - Posted refresh_stats event to trigger UI update
2025-06-02 20:50:19.415 - Starting background data reload
2025-06-02 20:50:19.415 - Starting background data loading thread
2025-06-02 20:50:19.417 - Background data loading started
2025-06-02 20:50:19.438 - Loading summary statistics
2025-06-02 20:50:19.439 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:50:19.440 - Data from GameStatsIntegration: {'total_earnings': 4292.8, 'daily_earnings': 812.8, 'daily_games': 20, 'wallet_balance': 0}
2025-06-02 20:50:19.441 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:50:19.442 - Successfully loaded summary stats: {'total_earnings': 4292.8, 'daily_earnings': 812.8, 'daily_games': 20, 'wallet_balance': 0}
2025-06-02 20:50:19.442 - Successfully loaded game history: 10 entries
2025-06-02 20:50:19.443 - Successfully loaded weekly stats: 7 days
2025-06-02 20:50:19.445 - Saved 3 items to cache
2025-06-02 20:50:19.445 - Background data loading completed
2025-06-02 20:51:42.902 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:53:22.258 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:53:22.275 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:53:22.277 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:53:22.278 - Forcing refresh of all stats data
2025-06-02 20:53:22.279 - Attempting to force refresh via GameStatsIntegration
2025-06-02 20:53:22.296 - get_stats_provider called, returning provider with initialized=True
2025-06-02 20:53:22.297 - Force refresh via GameStatsIntegration successful
2025-06-02 20:53:22.298 - Clearing cached data
2025-06-02 20:53:22.299 - Posted refresh_stats event to trigger UI update
2025-06-02 20:53:22.300 - Starting background data reload
2025-06-02 20:53:22.301 - Starting background data loading thread
2025-06-02 20:53:22.302 - Background data loading started
2025-06-02 20:53:22.326 - Loading summary statistics
2025-06-02 20:53:22.326 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 20:53:22.327 - Data from GameStatsIntegration: {'total_earnings': 4326.4, 'daily_earnings': 846.4, 'daily_games': 21, 'wallet_balance': 0}
2025-06-02 20:53:22.328 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 20:53:22.328 - Successfully loaded summary stats: {'total_earnings': 4326.4, 'daily_earnings': 846.4, 'daily_games': 21, 'wallet_balance': 0}
2025-06-02 20:53:22.328 - Successfully loaded game history: 10 entries
2025-06-02 20:53:22.329 - Successfully loaded weekly stats: 7 days
2025-06-02 20:53:22.332 - Saved 3 items to cache
2025-06-02 20:53:22.332 - Background data loading completed
2025-06-02 20:55:56.193 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:01:25.855 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:01:25.869 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:01:25.871 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:01:25.873 - Forcing refresh of all stats data
2025-06-02 21:01:25.874 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:01:25.889 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:01:25.890 - Force refresh via GameStatsIntegration successful
2025-06-02 21:01:25.890 - Clearing cached data
2025-06-02 21:01:25.891 - Posted refresh_stats event to trigger UI update
2025-06-02 21:01:25.892 - Starting background data reload
2025-06-02 21:01:25.893 - Starting background data loading thread
2025-06-02 21:01:25.894 - Background data loading started
2025-06-02 21:01:25.916 - Loading summary statistics
2025-06-02 21:01:25.917 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:01:25.918 - Data from GameStatsIntegration: {'total_earnings': 4355.2, 'daily_earnings': 875.1999999999999, 'daily_games': 22, 'wallet_balance': 0}
2025-06-02 21:01:25.919 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:01:25.919 - Successfully loaded summary stats: {'total_earnings': 4355.2, 'daily_earnings': 875.1999999999999, 'daily_games': 22, 'wallet_balance': 0}
2025-06-02 21:01:25.920 - Successfully loaded game history: 10 entries
2025-06-02 21:01:25.921 - Successfully loaded weekly stats: 7 days
2025-06-02 21:01:25.923 - Saved 3 items to cache
2025-06-02 21:01:25.924 - Background data loading completed
2025-06-02 21:03:00.849 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:06:56.317 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:06:56.328 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:06:56.333 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:06:56.334 - Forcing refresh of all stats data
2025-06-02 21:06:56.335 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:06:56.350 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:06:56.351 - Force refresh via GameStatsIntegration successful
2025-06-02 21:06:56.352 - Clearing cached data
2025-06-02 21:06:56.353 - Posted refresh_stats event to trigger UI update
2025-06-02 21:06:56.353 - Starting background data reload
2025-06-02 21:06:56.354 - Starting background data loading thread
2025-06-02 21:06:56.355 - Background data loading started
2025-06-02 21:06:56.378 - Loading summary statistics
2025-06-02 21:06:56.380 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:06:56.380 - Data from GameStatsIntegration: {'total_earnings': 4388.8, 'daily_earnings': 908.8, 'daily_games': 23, 'wallet_balance': 0}
2025-06-02 21:06:56.381 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:06:56.381 - Successfully loaded summary stats: {'total_earnings': 4388.8, 'daily_earnings': 908.8, 'daily_games': 23, 'wallet_balance': 0}
2025-06-02 21:06:56.382 - Successfully loaded game history: 10 entries
2025-06-02 21:06:56.383 - Successfully loaded weekly stats: 7 days
2025-06-02 21:06:56.393 - Saved 3 items to cache
2025-06-02 21:06:56.394 - Background data loading completed
2025-06-02 21:07:10.342 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:09:48.868 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:09:48.882 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:09:48.884 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:09:48.886 - Forcing refresh of all stats data
2025-06-02 21:09:48.887 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:09:48.901 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:09:48.902 - Force refresh via GameStatsIntegration successful
2025-06-02 21:09:48.903 - Clearing cached data
2025-06-02 21:09:48.904 - Posted refresh_stats event to trigger UI update
2025-06-02 21:09:48.905 - Starting background data reload
2025-06-02 21:09:48.906 - Starting background data loading thread
2025-06-02 21:09:48.907 - Background data loading started
2025-06-02 21:09:48.930 - Loading summary statistics
2025-06-02 21:09:48.931 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:09:48.932 - Data from GameStatsIntegration: {'total_earnings': 4422.4, 'daily_earnings': 942.4, 'daily_games': 24, 'wallet_balance': 0}
2025-06-02 21:09:48.933 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:09:48.933 - Successfully loaded summary stats: {'total_earnings': 4422.4, 'daily_earnings': 942.4, 'daily_games': 24, 'wallet_balance': 0}
2025-06-02 21:09:48.934 - Successfully loaded game history: 10 entries
2025-06-02 21:09:48.935 - Successfully loaded weekly stats: 7 days
2025-06-02 21:09:48.937 - Saved 3 items to cache
2025-06-02 21:09:48.937 - Background data loading completed
2025-06-02 21:14:04.473 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:17:44.551 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:17:44.566 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:17:44.568 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:17:44.569 - Forcing refresh of all stats data
2025-06-02 21:17:44.570 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:17:44.591 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:17:44.592 - Force refresh via GameStatsIntegration successful
2025-06-02 21:17:44.592 - Clearing cached data
2025-06-02 21:17:44.594 - Posted refresh_stats event to trigger UI update
2025-06-02 21:17:44.595 - Starting background data reload
2025-06-02 21:17:44.595 - Starting background data loading thread
2025-06-02 21:17:44.596 - Background data loading started
2025-06-02 21:17:44.622 - Loading summary statistics
2025-06-02 21:17:44.623 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:17:44.624 - Data from GameStatsIntegration: {'total_earnings': 4460.8, 'daily_earnings': 980.8, 'daily_games': 25, 'wallet_balance': 0}
2025-06-02 21:17:44.624 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:17:44.625 - Successfully loaded summary stats: {'total_earnings': 4460.8, 'daily_earnings': 980.8, 'daily_games': 25, 'wallet_balance': 0}
2025-06-02 21:17:44.626 - Successfully loaded game history: 10 entries
2025-06-02 21:17:44.626 - Successfully loaded weekly stats: 7 days
2025-06-02 21:17:44.629 - Saved 3 items to cache
2025-06-02 21:17:44.630 - Background data loading completed
2025-06-02 21:19:31.286 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:21:52.915 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:21:52.929 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:21:52.931 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:21:52.933 - Forcing refresh of all stats data
2025-06-02 21:21:52.933 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:21:52.950 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:21:52.951 - Force refresh via GameStatsIntegration successful
2025-06-02 21:21:52.952 - Clearing cached data
2025-06-02 21:21:52.955 - Posted refresh_stats event to trigger UI update
2025-06-02 21:21:52.956 - Starting background data reload
2025-06-02 21:21:52.958 - Starting background data loading thread
2025-06-02 21:21:52.960 - Background data loading started
2025-06-02 21:21:52.982 - Loading summary statistics
2025-06-02 21:21:52.984 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:21:52.984 - Data from GameStatsIntegration: {'total_earnings': 4494.4, 'daily_earnings': 1014.4, 'daily_games': 26, 'wallet_balance': 0}
2025-06-02 21:21:52.985 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:21:52.985 - Successfully loaded summary stats: {'total_earnings': 4494.4, 'daily_earnings': 1014.4, 'daily_games': 26, 'wallet_balance': 0}
2025-06-02 21:21:52.985 - Successfully loaded game history: 10 entries
2025-06-02 21:21:52.986 - Successfully loaded weekly stats: 7 days
2025-06-02 21:21:52.988 - Saved 3 items to cache
2025-06-02 21:21:52.988 - Background data loading completed
2025-06-02 21:24:36.415 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:27:49.457 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:27:49.507 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:27:49.509 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:27:49.511 - Forcing refresh of all stats data
2025-06-02 21:27:49.511 - Attempting to force refresh via GameStatsIntegration
2025-06-02 21:27:49.527 - get_stats_provider called, returning provider with initialized=True
2025-06-02 21:27:49.528 - Force refresh via GameStatsIntegration successful
2025-06-02 21:27:49.529 - Clearing cached data
2025-06-02 21:27:49.529 - Posted refresh_stats event to trigger UI update
2025-06-02 21:27:49.530 - Starting background data reload
2025-06-02 21:27:49.531 - Starting background data loading thread
2025-06-02 21:27:49.534 - Background data loading started
2025-06-02 21:27:49.555 - Loading summary statistics
2025-06-02 21:27:49.555 - Attempting to load summary stats from GameStatsIntegration
2025-06-02 21:27:49.557 - Data from GameStatsIntegration: {'total_earnings': 4523.2, 'daily_earnings': 1043.2, 'daily_games': 27, 'wallet_balance': 0}
2025-06-02 21:27:49.557 - Successfully loaded summary stats from GameStatsIntegration
2025-06-02 21:27:49.559 - Successfully loaded summary stats: {'total_earnings': 4523.2, 'daily_earnings': 1043.2, 'daily_games': 27, 'wallet_balance': 0}
2025-06-02 21:27:49.559 - Successfully loaded game history: 10 entries
2025-06-02 21:27:49.560 - Successfully loaded weekly stats: 7 days
2025-06-02 21:27:49.562 - Saved 3 items to cache
2025-06-02 21:27:49.563 - Background data loading completed
2025-06-04 17:15:46.900 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:15:46.961 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:15:46.973 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:15:46.977 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-04 17:15:46.981 - Initializing StatsDataProvider
2025-06-04 17:15:46.999 - Loaded 8 items from cache
2025-06-04 17:15:47.000 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-04 17:15:47.001 - Starting background data loading thread
2025-06-04 17:15:47.002 - Background data loading started
2025-06-04 17:15:47.002 - StatsDataProvider initialization completed
2025-06-04 17:15:47.003 - Loading summary statistics
f StatsDataProvider on module import
2025-06-04 17:15:47.006 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:15:47.006 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-04 17:15:47.007 - Data from GameStatsIntegration: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:15:47.008 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:15:47.010 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:15:47.013 - Successfully loaded summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:15:47.016 - Successfully loaded game history: 10 entries
2025-06-04 17:15:47.018 - Successfully loaded weekly stats: 7 days
2025-06-04 17:15:47.020 - Saved 10 items to cache
2025-06-04 17:15:47.022 - Background data loading completed
2025-06-04 17:22:59.226 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:22:59.227 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:22:59.247 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:22:59.248 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-04 17:22:59.249 - Initializing StatsDataProvider
2025-06-04 17:22:59.265 - Loaded 8 items from cache
2025-06-04 17:22:59.269 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-04 17:22:59.270 - Starting background data loading thread
2025-06-04 17:22:59.275 - StatsDataProvider initialization completed
2025-06-04 17:22:59.275 - Background data loading started
2025-06-04 17:22:59.279 - Created singleton instance of StatsDataProvider on module import
2025-06-04 17:22:59.279 - Loading summary statistics
2025-06-04 17:22:59.280 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-04 17:22:59.280 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:22:59.280 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:22:59.280 - Data from GameStatsIntegration: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:22:59.282 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:22:59.282 - Successfully loaded summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:22:59.283 - Successfully loaded game history: 10 entries
2025-06-04 17:22:59.283 - Successfully loaded weekly stats: 7 days
2025-06-04 17:22:59.286 - Saved 10 items to cache
2025-06-04 17:22:59.286 - Background data loading completed
2025-06-04 17:23:44.248 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:29:46.555 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:29:46.562 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:29:46.582 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:29:46.583 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-04 17:29:46.584 - Initializing StatsDataProvider
2025-06-04 17:29:46.605 - Loaded 8 items from cache
2025-06-04 17:29:46.607 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-04 17:29:46.610 - Starting background data loading thread
2025-06-04 17:29:46.614 - StatsDataProvider initialization completed
2025-06-04 17:29:46.614 - Background data loading started
2025-06-04 17:29:46.618 - Created singleton instance of StatsDataProvider on module import
2025-06-04 17:29:46.622 - Loading summary statistics
2025-06-04 17:29:46.623 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-04 17:29:46.623 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:29:46.624 - get_stats_provider called, returning provider with initialized=True
nings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:29:46.626 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:29:46.628 - Successfully loaded summary stats: {'total_earnings': 4523.2, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-04 17:29:46.630 - Successfully loaded game history: 10 entries
2025-06-04 17:29:46.631 - Successfully loaded weekly stats: 7 days
2025-06-04 17:29:46.637 - Saved 10 items to cache
2025-06-04 17:29:46.639 - Background data loading completed
2025-06-04 17:35:47.832 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:00.980 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:03.508 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:03.524 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:03.526 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:03.527 - Forcing refresh of all stats data
2025-06-04 17:42:03.528 - Attempting to force refresh via GameStatsIntegration
2025-06-04 17:42:03.544 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:42:03.546 - Force refresh via GameStatsIntegration successful
2025-06-04 17:42:03.546 - Clearing cached data
2025-06-04 17:42:03.548 - Posted refresh_stats event to trigger UI update
2025-06-04 17:42:03.548 - Starting background data reload
2025-06-04 17:42:03.549 - Starting background data loading thread
2025-06-04 17:42:03.551 - Background data loading started
2025-06-04 17:42:03.603 - Loading summary statistics
2025-06-04 17:42:03.604 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:42:03.606 - Data from GameStatsIntegration: {'total_earnings': 4555.2, 'daily_earnings': 32.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-04 17:42:03.606 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:42:03.607 - Successfully loaded summary stats: {'total_earnings': 4555.2, 'daily_earnings': 32.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-04 17:42:03.607 - Successfully loaded game history: 10 entries
2025-06-04 17:42:03.608 - Successfully loaded weekly stats: 7 days
2025-06-04 17:42:03.610 - Saved 3 items to cache
2025-06-04 17:42:03.610 - Background data loading completed
2025-06-04 17:42:20.363 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:47:01.660 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:47:01.680 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:47:01.682 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:47:01.683 - Forcing refresh of all stats data
2025-06-04 17:47:01.684 - Attempting to force refresh via GameStatsIntegration
2025-06-04 17:47:01.700 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:47:01.701 - Force refresh via GameStatsIntegration successful
2025-06-04 17:47:01.701 - Clearing cached data
2025-06-04 17:47:01.703 - Posted refresh_stats event to trigger UI update
2025-06-04 17:47:01.704 - Starting background data reload
2025-06-04 17:47:01.704 - Starting background data loading thread
2025-06-04 17:47:01.706 - Background data loading started
2025-06-04 17:47:01.727 - Loading summary statistics
2025-06-04 17:47:01.728 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:47:01.729 - Data from GameStatsIntegration: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:47:01.729 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:47:01.730 - Successfully loaded summary stats: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:47:01.730 - Successfully loaded game history: 10 entries
2025-06-04 17:47:01.731 - Successfully loaded weekly stats: 7 days
2025-06-04 17:47:01.733 - Saved 3 items to cache
2025-06-04 17:47:01.734 - Background data loading completed
2025-06-04 17:51:38.390 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:51:38.392 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:51:38.408 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:51:38.409 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-04 17:51:38.410 - Initializing StatsDataProvider
2025-06-04 17:51:38.424 - Loaded 8 items from cache
2025-06-04 17:51:38.428 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-04 17:51:38.429 - Starting background data loading thread
2025-06-04 17:51:38.433 - Background data loading started
2025-06-04 17:51:38.433 - StatsDataProvider initialization completed
2025-06-04 17:51:38.434 - Loading summary statistics
2025-06-04 17:51:38.434 - Created singleton instance of StatsDataProvider on module import
2025-06-04 17:51:38.435 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:51:38.435 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-04 17:51:38.436 - get_stats_provider called, returning provider with initialized=True
nings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:51:38.437 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:51:38.437 - Successfully loaded summary stats: {'total_earnings': 4568.0, 'daily_earnings': 44.8, 'daily_games': 2, 'wallet_balance': 0}
2025-06-04 17:51:38.438 - Successfully loaded game history: 10 entries
2025-06-04 17:51:38.439 - Successfully loaded weekly stats: 7 days
2025-06-04 17:51:38.441 - Saved 10 items to cache
2025-06-04 17:51:38.442 - Background data loading completed
2025-06-04 17:55:15.189 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:55:15.204 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:55:15.206 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:55:15.207 - Forcing refresh of all stats data
2025-06-04 17:55:15.208 - Attempting to force refresh via GameStatsIntegration
2025-06-04 17:55:15.226 - get_stats_provider called, returning provider with initialized=True
2025-06-04 17:55:15.228 - Force refresh via GameStatsIntegration successful
2025-06-04 17:55:15.230 - Clearing cached data
2025-06-04 17:55:15.231 - Posted refresh_stats event to trigger UI update
2025-06-04 17:55:15.231 - Starting background data reload
2025-06-04 17:55:15.232 - Starting background data loading thread
2025-06-04 17:55:15.234 - Background data loading started
2025-06-04 17:55:15.257 - Loading summary statistics
2025-06-04 17:55:15.258 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 17:55:15.258 - Data from GameStatsIntegration: {'total_earnings': 4580.8, 'daily_earnings': 57.599999999999994, 'daily_games': 3, 'wallet_balance': 0}
2025-06-04 17:55:15.258 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 17:55:15.259 - Successfully loaded summary stats: {'total_earnings': 4580.8, 'daily_earnings': 57.599999999999994, 'daily_games': 3, 'wallet_balance': 0}
2025-06-04 17:55:15.259 - Successfully loaded game history: 10 entries
2025-06-04 17:55:15.260 - Successfully loaded weekly stats: 7 days
2025-06-04 17:55:15.262 - Saved 3 items to cache
2025-06-04 17:55:15.262 - Background data loading completed
2025-06-04 17:59:33.189 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:03:21.741 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:03:21.757 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:03:21.759 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:03:21.761 - Forcing refresh of all stats data
2025-06-04 18:03:21.763 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:03:21.780 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:03:21.781 - Force refresh via GameStatsIntegration successful
2025-06-04 18:03:21.781 - Clearing cached data
2025-06-04 18:03:21.783 - Posted refresh_stats event to trigger UI update
2025-06-04 18:03:21.783 - Starting background data reload
2025-06-04 18:03:21.784 - Starting background data loading thread
2025-06-04 18:03:21.785 - Background data loading started
2025-06-04 18:03:21.806 - Loading summary statistics
2025-06-04 18:03:21.806 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:03:21.808 - Data from GameStatsIntegration: {'total_earnings': 4593.6, 'daily_earnings': 70.39999999999999, 'daily_games': 4, 'wallet_balance': 0}
2025-06-04 18:03:21.809 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:03:21.809 - Successfully loaded summary stats: {'total_earnings': 4593.6, 'daily_earnings': 70.39999999999999, 'daily_games': 4, 'wallet_balance': 0}
2025-06-04 18:03:21.810 - Successfully loaded game history: 10 entries
2025-06-04 18:03:21.811 - Successfully loaded weekly stats: 7 days
2025-06-04 18:03:21.812 - Saved 3 items to cache
2025-06-04 18:03:21.813 - Background data loading completed
2025-06-04 18:06:10.811 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:10:12.689 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:10:12.760 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:10:12.764 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:10:12.766 - Forcing refresh of all stats data
2025-06-04 18:10:12.767 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:10:12.782 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:10:12.783 - Force refresh via GameStatsIntegration successful
2025-06-04 18:10:12.783 - Clearing cached data
2025-06-04 18:10:12.785 - Posted refresh_stats event to trigger UI update
2025-06-04 18:10:12.786 - Starting background data reload
2025-06-04 18:10:12.787 - Starting background data loading thread
2025-06-04 18:10:12.790 - Background data loading started
2025-06-04 18:10:12.811 - Loading summary statistics
2025-06-04 18:10:12.812 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:10:12.812 - Data from GameStatsIntegration: {'total_earnings': 4609.6, 'daily_earnings': 86.39999999999999, 'daily_games': 5, 'wallet_balance': 0}
2025-06-04 18:10:12.813 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:10:12.813 - Successfully loaded summary stats: {'total_earnings': 4609.6, 'daily_earnings': 86.39999999999999, 'daily_games': 5, 'wallet_balance': 0}
2025-06-04 18:10:12.814 - Successfully loaded game history: 10 entries
2025-06-04 18:10:12.814 - Successfully loaded weekly stats: 7 days
2025-06-04 18:10:12.816 - Saved 3 items to cache
2025-06-04 18:10:12.816 - Background data loading completed
2025-06-04 18:10:57.238 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:11:01.071 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:11:01.084 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:11:01.085 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:11:01.088 - Forcing refresh of all stats data
2025-06-04 18:11:01.088 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:11:01.101 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:11:01.102 - Force refresh via GameStatsIntegration successful
2025-06-04 18:11:01.102 - Clearing cached data
2025-06-04 18:11:01.103 - Posted refresh_stats event to trigger UI update
2025-06-04 18:11:01.104 - Starting background data reload
2025-06-04 18:11:01.105 - Starting background data loading thread
2025-06-04 18:11:01.108 - Background data loading started
2025-06-04 18:11:01.124 - Loading summary statistics
2025-06-04 18:11:01.135 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:11:01.137 - Data from GameStatsIntegration: {'total_earnings': 4625.6, 'daily_earnings': 102.39999999999999, 'daily_games': 6, 'wallet_balance': 0}
2025-06-04 18:11:01.137 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:11:01.138 - Successfully loaded summary stats: {'total_earnings': 4625.6, 'daily_earnings': 102.39999999999999, 'daily_games': 6, 'wallet_balance': 0}
2025-06-04 18:11:01.138 - Successfully loaded game history: 10 entries
2025-06-04 18:11:01.139 - Successfully loaded weekly stats: 7 days
2025-06-04 18:11:01.141 - Saved 3 items to cache
2025-06-04 18:11:01.141 - Background data loading completed
2025-06-04 18:11:17.860 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:13:37.754 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:13:37.768 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:13:37.771 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:13:37.772 - Forcing refresh of all stats data
2025-06-04 18:13:37.773 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:13:37.788 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:13:37.789 - Force refresh via GameStatsIntegration successful
2025-06-04 18:13:37.790 - Clearing cached data
2025-06-04 18:13:37.791 - Posted refresh_stats event to trigger UI update
2025-06-04 18:13:37.792 - Starting background data reload
2025-06-04 18:13:37.793 - Starting background data loading thread
2025-06-04 18:13:37.796 - Background data loading started
2025-06-04 18:13:37.820 - Loading summary statistics
2025-06-04 18:13:37.820 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:13:37.820 - Data from GameStatsIntegration: {'total_earnings': 4649.6, 'daily_earnings': 126.39999999999999, 'daily_games': 7, 'wallet_balance': 0}
2025-06-04 18:13:37.821 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:13:37.821 - Successfully loaded summary stats: {'total_earnings': 4649.6, 'daily_earnings': 126.39999999999999, 'daily_games': 7, 'wallet_balance': 0}
2025-06-04 18:13:37.822 - Successfully loaded game history: 10 entries
2025-06-04 18:13:37.822 - Successfully loaded weekly stats: 7 days
2025-06-04 18:13:37.824 - Saved 3 items to cache
2025-06-04 18:13:37.825 - Background data loading completed
2025-06-04 18:15:26.971 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:18:31.520 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:18:31.537 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:18:31.539 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:18:31.541 - Forcing refresh of all stats data
2025-06-04 18:18:31.542 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:18:31.555 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:18:31.557 - Force refresh via GameStatsIntegration successful
2025-06-04 18:18:31.558 - Clearing cached data
2025-06-04 18:18:31.558 - Posted refresh_stats event to trigger UI update
2025-06-04 18:18:31.559 - Starting background data reload
2025-06-04 18:18:31.560 - Starting background data loading thread
2025-06-04 18:18:31.562 - Background data loading started
2025-06-04 18:18:31.584 - Loading summary statistics
2025-06-04 18:18:31.584 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:18:31.585 - Data from GameStatsIntegration: {'total_earnings': 4673.6, 'daily_earnings': 150.39999999999998, 'daily_games': 8, 'wallet_balance': 0}
2025-06-04 18:18:31.585 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:18:31.586 - Successfully loaded summary stats: {'total_earnings': 4673.6, 'daily_earnings': 150.39999999999998, 'daily_games': 8, 'wallet_balance': 0}
2025-06-04 18:18:31.587 - Successfully loaded game history: 10 entries
2025-06-04 18:18:31.587 - Successfully loaded weekly stats: 7 days
2025-06-04 18:18:31.589 - Saved 3 items to cache
2025-06-04 18:18:31.589 - Background data loading completed
2025-06-04 18:19:41.571 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:22:31.144 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:22:31.158 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:22:31.160 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:22:31.162 - Forcing refresh of all stats data
2025-06-04 18:22:31.162 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:22:31.178 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:22:31.179 - Force refresh via GameStatsIntegration successful
2025-06-04 18:22:31.179 - Clearing cached data
2025-06-04 18:22:31.181 - Posted refresh_stats event to trigger UI update
2025-06-04 18:22:31.182 - Starting background data reload
2025-06-04 18:22:31.182 - Starting background data loading thread
2025-06-04 18:22:31.184 - Background data loading started
2025-06-04 18:22:31.205 - Loading summary statistics
2025-06-04 18:22:31.207 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:22:31.207 - Data from GameStatsIntegration: {'total_earnings': 4702.4, 'daily_earnings': 179.2, 'daily_games': 9, 'wallet_balance': 0}
2025-06-04 18:22:31.208 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:22:31.208 - Successfully loaded summary stats: {'total_earnings': 4702.4, 'daily_earnings': 179.2, 'daily_games': 9, 'wallet_balance': 0}
2025-06-04 18:22:31.209 - Successfully loaded game history: 10 entries
2025-06-04 18:22:31.210 - Successfully loaded weekly stats: 7 days
2025-06-04 18:22:31.212 - Saved 3 items to cache
2025-06-04 18:22:31.213 - Background data loading completed
2025-06-04 18:24:13.645 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:29:34.579 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:29:34.596 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:29:34.598 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:29:34.599 - Forcing refresh of all stats data
2025-06-04 18:29:34.599 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:29:34.618 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:29:34.619 - Force refresh via GameStatsIntegration successful
2025-06-04 18:29:34.619 - Clearing cached data
2025-06-04 18:29:34.620 - Posted refresh_stats event to trigger UI update
2025-06-04 18:29:34.620 - Starting background data reload
2025-06-04 18:29:34.621 - Starting background data loading thread
2025-06-04 18:29:34.622 - Background data loading started
2025-06-04 18:29:34.644 - Loading summary statistics
2025-06-04 18:29:34.645 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:29:34.647 - Data from GameStatsIntegration: {'total_earnings': 4731.2, 'daily_earnings': 208.0, 'daily_games': 10, 'wallet_balance': 0}
2025-06-04 18:29:34.647 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:29:34.648 - Successfully loaded summary stats: {'total_earnings': 4731.2, 'daily_earnings': 208.0, 'daily_games': 10, 'wallet_balance': 0}
2025-06-04 18:29:34.648 - Successfully loaded game history: 10 entries
2025-06-04 18:29:34.648 - Successfully loaded weekly stats: 7 days
2025-06-04 18:29:34.651 - Saved 3 items to cache
2025-06-04 18:29:34.651 - Background data loading completed
2025-06-04 18:30:43.265 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:35:07.816 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:35:07.830 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:35:07.832 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:35:07.834 - Forcing refresh of all stats data
2025-06-04 18:35:07.834 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:35:07.850 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:35:07.851 - Force refresh via GameStatsIntegration successful
2025-06-04 18:35:07.852 - Clearing cached data
2025-06-04 18:35:07.853 - Posted refresh_stats event to trigger UI update
2025-06-04 18:35:07.854 - Starting background data reload
2025-06-04 18:35:07.854 - Starting background data loading thread
2025-06-04 18:35:07.856 - Background data loading started
2025-06-04 18:35:07.880 - Loading summary statistics
2025-06-04 18:35:07.881 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:35:07.882 - Data from GameStatsIntegration: {'total_earnings': 4760.0, 'daily_earnings': 236.8, 'daily_games': 11, 'wallet_balance': 0}
2025-06-04 18:35:07.882 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:35:07.883 - Successfully loaded summary stats: {'total_earnings': 4760.0, 'daily_earnings': 236.8, 'daily_games': 11, 'wallet_balance': 0}
2025-06-04 18:35:07.883 - Successfully loaded game history: 10 entries
2025-06-04 18:35:07.884 - Successfully loaded weekly stats: 7 days
2025-06-04 18:35:07.886 - Saved 3 items to cache
2025-06-04 18:35:07.887 - Background data loading completed
2025-06-04 18:36:22.166 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:40:05.460 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:40:05.474 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:40:05.476 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:40:05.477 - Forcing refresh of all stats data
2025-06-04 18:40:05.478 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:40:05.494 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:40:05.495 - Force refresh via GameStatsIntegration successful
2025-06-04 18:40:05.496 - Clearing cached data
2025-06-04 18:40:05.497 - Posted refresh_stats event to trigger UI update
2025-06-04 18:40:05.497 - Starting background data reload
2025-06-04 18:40:05.498 - Starting background data loading thread
2025-06-04 18:40:05.501 - Background data loading started
2025-06-04 18:40:05.526 - Loading summary statistics
2025-06-04 18:40:05.532 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:40:05.534 - Data from GameStatsIntegration: {'total_earnings': 4793.6, 'daily_earnings': 270.40000000000003, 'daily_games': 12, 'wallet_balance': 0}
2025-06-04 18:40:05.535 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:40:05.536 - Successfully loaded summary stats: {'total_earnings': 4793.6, 'daily_earnings': 270.40000000000003, 'daily_games': 12, 'wallet_balance': 0}
2025-06-04 18:40:05.544 - Successfully loaded game history: 10 entries
2025-06-04 18:40:05.546 - Successfully loaded weekly stats: 7 days
2025-06-04 18:40:05.550 - Saved 3 items to cache
2025-06-04 18:40:05.555 - Background data loading completed
2025-06-04 18:40:55.264 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:11.752 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:11.962 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:12.000 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:12.005 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:12.010 - Forcing refresh of all stats data
2025-06-04 18:43:12.015 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:43:12.043 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:43:12.045 - Force refresh via GameStatsIntegration successful
2025-06-04 18:43:12.047 - Clearing cached data
2025-06-04 18:43:12.048 - Posted refresh_stats event to trigger UI update
2025-06-04 18:43:12.050 - Starting background data reload
2025-06-04 18:43:12.052 - Starting background data loading thread
2025-06-04 18:43:12.054 - Background data loading started
2025-06-04 18:43:12.077 - Loading summary statistics
2025-06-04 18:43:12.080 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:43:12.080 - Data from GameStatsIntegration: {'total_earnings': 4817.6, 'daily_earnings': 294.40000000000003, 'daily_games': 13, 'wallet_balance': 0}
2025-06-04 18:43:12.082 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:43:12.083 - Successfully loaded summary stats: {'total_earnings': 4817.6, 'daily_earnings': 294.40000000000003, 'daily_games': 13, 'wallet_balance': 0}
2025-06-04 18:43:12.087 - Successfully loaded game history: 10 entries
2025-06-04 18:43:12.090 - Successfully loaded weekly stats: 7 days
2025-06-04 18:43:12.094 - Saved 3 items to cache
2025-06-04 18:43:12.095 - Background data loading completed
2025-06-04 18:48:14.946 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:48:14.970 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:48:14.972 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:48:14.973 - Forcing refresh of all stats data
2025-06-04 18:48:14.975 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:48:14.992 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:48:14.994 - Force refresh via GameStatsIntegration successful
2025-06-04 18:48:14.995 - Clearing cached data
2025-06-04 18:48:14.996 - Posted refresh_stats event to trigger UI update
2025-06-04 18:48:14.997 - Starting background data reload
2025-06-04 18:48:14.998 - Starting background data loading thread
2025-06-04 18:48:15.000 - Background data loading started
2025-06-04 18:48:15.022 - Loading summary statistics
2025-06-04 18:48:15.022 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:48:15.023 - Data from GameStatsIntegration: {'total_earnings': 4841.6, 'daily_earnings': 318.40000000000003, 'daily_games': 14, 'wallet_balance': 0}
2025-06-04 18:48:15.023 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:48:15.024 - Successfully loaded summary stats: {'total_earnings': 4841.6, 'daily_earnings': 318.40000000000003, 'daily_games': 14, 'wallet_balance': 0}
2025-06-04 18:48:15.024 - Successfully loaded game history: 10 entries
2025-06-04 18:48:15.025 - Successfully loaded weekly stats: 7 days
2025-06-04 18:48:15.027 - Saved 3 items to cache
2025-06-04 18:48:15.028 - Background data loading completed
2025-06-04 18:49:05.565 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:53:41.143 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:53:41.156 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:53:41.160 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:53:41.161 - Forcing refresh of all stats data
2025-06-04 18:53:41.163 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:53:41.180 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:53:41.181 - Force refresh via GameStatsIntegration successful
2025-06-04 18:53:41.182 - Clearing cached data
2025-06-04 18:53:41.183 - Posted refresh_stats event to trigger UI update
2025-06-04 18:53:41.184 - Starting background data reload
2025-06-04 18:53:41.184 - Starting background data loading thread
2025-06-04 18:53:41.186 - Background data loading started
2025-06-04 18:53:41.209 - Loading summary statistics
2025-06-04 18:53:41.210 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:53:41.210 - Data from GameStatsIntegration: {'total_earnings': 4865.6, 'daily_earnings': 342.40000000000003, 'daily_games': 15, 'wallet_balance': 0}
2025-06-04 18:53:41.210 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:53:41.211 - Successfully loaded summary stats: {'total_earnings': 4865.6, 'daily_earnings': 342.40000000000003, 'daily_games': 15, 'wallet_balance': 0}
2025-06-04 18:53:41.211 - Successfully loaded game history: 10 entries
2025-06-04 18:53:41.212 - Successfully loaded weekly stats: 7 days
2025-06-04 18:53:41.214 - Saved 3 items to cache
2025-06-04 18:53:41.214 - Background data loading completed
2025-06-04 18:54:50.575 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:57:38.575 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:57:38.587 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:57:38.588 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:57:38.589 - Forcing refresh of all stats data
2025-06-04 18:57:38.592 - Attempting to force refresh via GameStatsIntegration
2025-06-04 18:57:38.605 - get_stats_provider called, returning provider with initialized=True
2025-06-04 18:57:38.606 - Force refresh via GameStatsIntegration successful
2025-06-04 18:57:38.606 - Clearing cached data
2025-06-04 18:57:38.607 - Posted refresh_stats event to trigger UI update
2025-06-04 18:57:38.608 - Starting background data reload
2025-06-04 18:57:38.609 - Starting background data loading thread
2025-06-04 18:57:38.610 - Background data loading started
2025-06-04 18:57:38.631 - Loading summary statistics
2025-06-04 18:57:38.633 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 18:57:38.634 - Data from GameStatsIntegration: {'total_earnings': 4889.6, 'daily_earnings': 366.40000000000003, 'daily_games': 16, 'wallet_balance': 0}
2025-06-04 18:57:38.634 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 18:57:38.634 - Successfully loaded summary stats: {'total_earnings': 4889.6, 'daily_earnings': 366.40000000000003, 'daily_games': 16, 'wallet_balance': 0}
2025-06-04 18:57:38.635 - Successfully loaded game history: 10 entries
2025-06-04 18:57:38.636 - Successfully loaded weekly stats: 7 days
2025-06-04 18:57:38.637 - Saved 3 items to cache
2025-06-04 18:57:38.639 - Background data loading completed
2025-06-04 19:00:48.311 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:05:05.280 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:05:05.297 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:05:05.300 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:05:05.301 - Forcing refresh of all stats data
2025-06-04 19:05:05.302 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:05:05.316 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:05:05.317 - Force refresh via GameStatsIntegration successful
2025-06-04 19:05:05.319 - Clearing cached data
2025-06-04 19:05:05.320 - Posted refresh_stats event to trigger UI update
2025-06-04 19:05:05.320 - Starting background data reload
2025-06-04 19:05:05.321 - Starting background data loading thread
2025-06-04 19:05:05.323 - Background data loading started
2025-06-04 19:05:05.346 - Loading summary statistics
2025-06-04 19:05:05.346 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:05:05.346 - Data from GameStatsIntegration: {'total_earnings': 4913.6, 'daily_earnings': 390.40000000000003, 'daily_games': 17, 'wallet_balance': 0}
2025-06-04 19:05:05.347 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:05:05.347 - Successfully loaded summary stats: {'total_earnings': 4913.6, 'daily_earnings': 390.40000000000003, 'daily_games': 17, 'wallet_balance': 0}
2025-06-04 19:05:05.347 - Successfully loaded game history: 10 entries
2025-06-04 19:05:05.349 - Successfully loaded weekly stats: 7 days
2025-06-04 19:05:05.351 - Saved 3 items to cache
2025-06-04 19:05:05.351 - Background data loading completed
2025-06-04 19:05:31.713 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:08:57.080 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:08:57.097 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:08:57.099 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:08:57.100 - Forcing refresh of all stats data
2025-06-04 19:08:57.101 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:08:57.120 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:08:57.121 - Force refresh via GameStatsIntegration successful
2025-06-04 19:08:57.123 - Clearing cached data
2025-06-04 19:08:57.124 - Posted refresh_stats event to trigger UI update
2025-06-04 19:08:57.124 - Starting background data reload
2025-06-04 19:08:57.125 - Starting background data loading thread
2025-06-04 19:08:57.127 - Background data loading started
2025-06-04 19:08:57.149 - Loading summary statistics
2025-06-04 19:08:57.150 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:08:57.150 - Data from GameStatsIntegration: {'total_earnings': 4937.6, 'daily_earnings': 414.40000000000003, 'daily_games': 18, 'wallet_balance': 0}
2025-06-04 19:08:57.151 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:08:57.151 - Successfully loaded summary stats: {'total_earnings': 4937.6, 'daily_earnings': 414.40000000000003, 'daily_games': 18, 'wallet_balance': 0}
2025-06-04 19:08:57.152 - Successfully loaded game history: 10 entries
2025-06-04 19:08:57.153 - Successfully loaded weekly stats: 7 days
2025-06-04 19:08:57.155 - Saved 3 items to cache
2025-06-04 19:08:57.155 - Background data loading completed
2025-06-04 19:09:44.808 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:15:08.997 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:15:09.013 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:15:09.015 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:15:09.016 - Forcing refresh of all stats data
2025-06-04 19:15:09.017 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:15:09.031 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:15:09.032 - Force refresh via GameStatsIntegration successful
2025-06-04 19:15:09.034 - Clearing cached data
2025-06-04 19:15:09.035 - Posted refresh_stats event to trigger UI update
2025-06-04 19:15:09.036 - Starting background data reload
2025-06-04 19:15:09.038 - Starting background data loading thread
2025-06-04 19:15:09.042 - Background data loading started
2025-06-04 19:15:09.066 - Loading summary statistics
2025-06-04 19:15:09.067 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:15:09.067 - Data from GameStatsIntegration: {'total_earnings': 4961.6, 'daily_earnings': 438.40000000000003, 'daily_games': 19, 'wallet_balance': 0}
2025-06-04 19:15:09.068 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:15:09.068 - Successfully loaded summary stats: {'total_earnings': 4961.6, 'daily_earnings': 438.40000000000003, 'daily_games': 19, 'wallet_balance': 0}
2025-06-04 19:15:09.069 - Successfully loaded game history: 10 entries
2025-06-04 19:15:09.070 - Successfully loaded weekly stats: 7 days
2025-06-04 19:15:09.071 - Saved 3 items to cache
2025-06-04 19:15:09.071 - Background data loading completed
2025-06-04 19:15:47.532 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.622 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.805 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.829 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.833 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.836 - Forcing refresh of all stats data
2025-06-04 19:20:10.837 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:20:10.877 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:20:10.882 - Force refresh via GameStatsIntegration successful
2025-06-04 19:20:10.889 - Clearing cached data
2025-06-04 19:20:10.891 - Posted refresh_stats event to trigger UI update
2025-06-04 19:20:10.893 - Starting background data reload
2025-06-04 19:20:10.895 - Starting background data loading thread
2025-06-04 19:20:10.902 - Background data loading started
2025-06-04 19:20:10.924 - Loading summary statistics
2025-06-04 19:20:10.925 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:20:10.927 - Data from GameStatsIntegration: {'total_earnings': 4976.0, 'daily_earnings': 452.8, 'daily_games': 20, 'wallet_balance': 0}
2025-06-04 19:20:10.928 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:20:10.928 - Successfully loaded summary stats: {'total_earnings': 4976.0, 'daily_earnings': 452.8, 'daily_games': 20, 'wallet_balance': 0}
2025-06-04 19:20:10.931 - Successfully loaded game history: 10 entries
2025-06-04 19:20:10.932 - Successfully loaded weekly stats: 7 days
2025-06-04 19:20:10.935 - Saved 3 items to cache
2025-06-04 19:20:10.935 - Background data loading completed
2025-06-04 19:28:17.564 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:28:17.582 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:28:17.585 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:28:17.588 - Forcing refresh of all stats data
2025-06-04 19:28:17.589 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:28:17.603 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:28:17.604 - Force refresh via GameStatsIntegration successful
2025-06-04 19:28:17.605 - Clearing cached data
2025-06-04 19:28:17.606 - Posted refresh_stats event to trigger UI update
2025-06-04 19:28:17.607 - Starting background data reload
2025-06-04 19:28:17.608 - Starting background data loading thread
2025-06-04 19:28:17.609 - Background data loading started
2025-06-04 19:28:17.633 - Loading summary statistics
2025-06-04 19:28:17.633 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:28:17.634 - Data from GameStatsIntegration: {'total_earnings': 4990.4, 'daily_earnings': 467.2, 'daily_games': 21, 'wallet_balance': 0}
2025-06-04 19:28:17.634 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:28:17.635 - Successfully loaded summary stats: {'total_earnings': 4990.4, 'daily_earnings': 467.2, 'daily_games': 21, 'wallet_balance': 0}
2025-06-04 19:28:17.635 - Successfully loaded game history: 10 entries
2025-06-04 19:28:17.636 - Successfully loaded weekly stats: 7 days
2025-06-04 19:28:17.638 - Saved 3 items to cache
2025-06-04 19:28:17.638 - Background data loading completed
2025-06-04 19:28:37.306 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:42:52.769 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:45:48.448 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:45:48.462 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:45:48.464 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:45:48.464 - Forcing refresh of all stats data
2025-06-04 19:45:48.465 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:45:48.479 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:45:48.480 - Force refresh via GameStatsIntegration successful
2025-06-04 19:45:48.483 - Clearing cached data
2025-06-04 19:45:48.484 - Posted refresh_stats event to trigger UI update
2025-06-04 19:45:48.485 - Starting background data reload
2025-06-04 19:45:48.486 - Starting background data loading thread
2025-06-04 19:45:48.488 - Background data loading started
2025-06-04 19:45:48.512 - Loading summary statistics
2025-06-04 19:45:48.512 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:45:48.513 - Data from GameStatsIntegration: {'total_earnings': 5014.4, 'daily_earnings': 491.2, 'daily_games': 22, 'wallet_balance': 0}
2025-06-04 19:45:48.514 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:45:48.514 - Successfully loaded summary stats: {'total_earnings': 5014.4, 'daily_earnings': 491.2, 'daily_games': 22, 'wallet_balance': 0}
2025-06-04 19:45:48.514 - Successfully loaded game history: 10 entries
2025-06-04 19:45:48.515 - Successfully loaded weekly stats: 7 days
2025-06-04 19:45:48.517 - Saved 3 items to cache
2025-06-04 19:45:48.518 - Background data loading completed
2025-06-04 19:48:28.526 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:51:04.292 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:51:04.306 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:51:04.309 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:51:04.309 - Forcing refresh of all stats data
2025-06-04 19:51:04.310 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:51:04.325 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:51:04.327 - Force refresh via GameStatsIntegration successful
2025-06-04 19:51:04.328 - Clearing cached data
2025-06-04 19:51:04.329 - Posted refresh_stats event to trigger UI update
2025-06-04 19:51:04.330 - Starting background data reload
2025-06-04 19:51:04.330 - Starting background data loading thread
2025-06-04 19:51:04.332 - Background data loading started
2025-06-04 19:51:04.353 - Loading summary statistics
2025-06-04 19:51:04.355 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:51:04.355 - Data from GameStatsIntegration: {'total_earnings': 5038.4, 'daily_earnings': 515.2, 'daily_games': 23, 'wallet_balance': 0}
2025-06-04 19:51:04.357 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:51:04.357 - Successfully loaded summary stats: {'total_earnings': 5038.4, 'daily_earnings': 515.2, 'daily_games': 23, 'wallet_balance': 0}
2025-06-04 19:51:04.358 - Successfully loaded game history: 10 entries
2025-06-04 19:51:04.358 - Successfully loaded weekly stats: 7 days
2025-06-04 19:51:04.360 - Saved 3 items to cache
2025-06-04 19:51:04.361 - Background data loading completed
2025-06-04 19:52:43.774 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:56:06.201 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:56:06.216 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:56:06.218 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:56:06.218 - Forcing refresh of all stats data
2025-06-04 19:56:06.219 - Attempting to force refresh via GameStatsIntegration
2025-06-04 19:56:06.234 - get_stats_provider called, returning provider with initialized=True
2025-06-04 19:56:06.235 - Force refresh via GameStatsIntegration successful
2025-06-04 19:56:06.236 - Clearing cached data
2025-06-04 19:56:06.237 - Posted refresh_stats event to trigger UI update
2025-06-04 19:56:06.237 - Starting background data reload
2025-06-04 19:56:06.238 - Starting background data loading thread
2025-06-04 19:56:06.239 - Background data loading started
2025-06-04 19:56:06.263 - Loading summary statistics
2025-06-04 19:56:06.263 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 19:56:06.265 - Data from GameStatsIntegration: {'total_earnings': 5057.6, 'daily_earnings': 534.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-04 19:56:06.265 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 19:56:06.265 - Successfully loaded summary stats: {'total_earnings': 5057.6, 'daily_earnings': 534.4000000000001, 'daily_games': 24, 'wallet_balance': 0}
2025-06-04 19:56:06.267 - Successfully loaded game history: 10 entries
2025-06-04 19:56:06.267 - Successfully loaded weekly stats: 7 days
2025-06-04 19:56:06.269 - Saved 3 items to cache
2025-06-04 19:56:06.270 - Background data loading completed
2025-06-04 19:56:28.679 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:00:34.688 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:00:34.705 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:00:34.710 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:00:34.711 - Forcing refresh of all stats data
2025-06-04 20:00:34.712 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:00:34.726 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:00:34.728 - Force refresh via GameStatsIntegration successful
2025-06-04 20:00:34.729 - Clearing cached data
2025-06-04 20:00:34.730 - Posted refresh_stats event to trigger UI update
2025-06-04 20:00:34.730 - Starting background data reload
2025-06-04 20:00:34.731 - Starting background data loading thread
2025-06-04 20:00:34.732 - Background data loading started
2025-06-04 20:00:34.754 - Loading summary statistics
2025-06-04 20:00:34.757 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:00:34.757 - Data from GameStatsIntegration: {'total_earnings': 5076.8, 'daily_earnings': 553.6000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-04 20:00:34.758 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:00:34.758 - Successfully loaded summary stats: {'total_earnings': 5076.8, 'daily_earnings': 553.6000000000001, 'daily_games': 25, 'wallet_balance': 0}
2025-06-04 20:00:34.759 - Successfully loaded game history: 10 entries
2025-06-04 20:00:34.759 - Successfully loaded weekly stats: 7 days
2025-06-04 20:00:34.761 - Saved 3 items to cache
2025-06-04 20:00:34.762 - Background data loading completed
2025-06-04 20:02:47.809 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:06:17.660 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:06:17.677 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:06:17.679 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:06:17.680 - Forcing refresh of all stats data
2025-06-04 20:06:17.681 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:06:17.696 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:06:17.698 - Force refresh via GameStatsIntegration successful
2025-06-04 20:06:17.698 - Clearing cached data
2025-06-04 20:06:17.699 - Posted refresh_stats event to trigger UI update
2025-06-04 20:06:17.700 - Starting background data reload
2025-06-04 20:06:17.700 - Starting background data loading thread
2025-06-04 20:06:17.702 - Background data loading started
2025-06-04 20:06:17.723 - Loading summary statistics
2025-06-04 20:06:17.725 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:06:17.726 - Data from GameStatsIntegration: {'total_earnings': 5100.8, 'daily_earnings': 577.6000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-04 20:06:17.727 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:06:17.727 - Successfully loaded summary stats: {'total_earnings': 5100.8, 'daily_earnings': 577.6000000000001, 'daily_games': 26, 'wallet_balance': 0}
2025-06-04 20:06:17.728 - Successfully loaded game history: 10 entries
2025-06-04 20:06:17.728 - Successfully loaded weekly stats: 7 days
2025-06-04 20:06:17.730 - Saved 3 items to cache
2025-06-04 20:06:17.731 - Background data loading completed
2025-06-04 20:08:22.233 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:11:21.730 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:11:21.748 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:11:21.751 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:11:21.752 - Forcing refresh of all stats data
2025-06-04 20:11:21.752 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:11:21.771 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:11:21.772 - Force refresh via GameStatsIntegration successful
2025-06-04 20:11:21.772 - Clearing cached data
2025-06-04 20:11:21.773 - Posted refresh_stats event to trigger UI update
2025-06-04 20:11:21.773 - Starting background data reload
2025-06-04 20:11:21.774 - Starting background data loading thread
2025-06-04 20:11:21.775 - Background data loading started
2025-06-04 20:11:21.798 - Loading summary statistics
2025-06-04 20:11:21.799 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:11:21.801 - Data from GameStatsIntegration: {'total_earnings': 5124.8, 'daily_earnings': 601.6000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-04 20:11:21.802 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:11:21.802 - Successfully loaded summary stats: {'total_earnings': 5124.8, 'daily_earnings': 601.6000000000001, 'daily_games': 27, 'wallet_balance': 0}
2025-06-04 20:11:21.803 - Successfully loaded game history: 10 entries
2025-06-04 20:11:21.803 - Successfully loaded weekly stats: 7 days
2025-06-04 20:11:21.805 - Saved 3 items to cache
2025-06-04 20:11:21.805 - Background data loading completed
2025-06-04 20:12:20.924 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:15:45.019 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:15:45.094 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:15:45.111 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:15:45.120 - Forcing refresh of all stats data
2025-06-04 20:15:45.124 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:15:45.217 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:15:45.219 - Force refresh via GameStatsIntegration successful
2025-06-04 20:15:45.225 - Clearing cached data
2025-06-04 20:15:45.246 - Posted refresh_stats event to trigger UI update
2025-06-04 20:15:45.251 - Starting background data reload
2025-06-04 20:15:45.261 - Starting background data loading thread
2025-06-04 20:15:45.271 - Background data loading started
2025-06-04 20:15:45.314 - Loading summary statistics
2025-06-04 20:15:45.319 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:15:45.321 - Data from GameStatsIntegration: {'total_earnings': 5148.8, 'daily_earnings': 625.6000000000001, 'daily_games': 28, 'wallet_balance': 0}
2025-06-04 20:15:45.323 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:15:45.324 - Successfully loaded summary stats: {'total_earnings': 5148.8, 'daily_earnings': 625.6000000000001, 'daily_games': 28, 'wallet_balance': 0}
2025-06-04 20:15:45.327 - Successfully loaded game history: 10 entries
2025-06-04 20:15:45.340 - Successfully loaded weekly stats: 7 days
2025-06-04 20:15:45.356 - Saved 3 items to cache
2025-06-04 20:15:45.361 - Background data loading completed
2025-06-04 20:16:39.446 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:20:52.397 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:20:52.410 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:20:52.412 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:20:52.413 - Forcing refresh of all stats data
2025-06-04 20:20:52.416 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:20:52.430 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:20:52.431 - Force refresh via GameStatsIntegration successful
2025-06-04 20:20:52.431 - Clearing cached data
2025-06-04 20:20:52.433 - Posted refresh_stats event to trigger UI update
2025-06-04 20:20:52.433 - Starting background data reload
2025-06-04 20:20:52.434 - Starting background data loading thread
2025-06-04 20:20:52.436 - Background data loading started
2025-06-04 20:20:52.459 - Loading summary statistics
2025-06-04 20:20:52.467 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:20:52.468 - Data from GameStatsIntegration: {'total_earnings': 5168.0, 'daily_earnings': 644.8000000000002, 'daily_games': 29, 'wallet_balance': 0}
2025-06-04 20:20:52.469 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:20:52.470 - Successfully loaded summary stats: {'total_earnings': 5168.0, 'daily_earnings': 644.8000000000002, 'daily_games': 29, 'wallet_balance': 0}
2025-06-04 20:20:52.479 - Successfully loaded game history: 10 entries
2025-06-04 20:20:52.479 - Successfully loaded weekly stats: 7 days
2025-06-04 20:20:52.482 - Saved 3 items to cache
2025-06-04 20:20:52.487 - Background data loading completed
2025-06-04 20:21:10.208 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:25:14.771 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:25:14.783 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:25:14.785 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:25:14.788 - Forcing refresh of all stats data
2025-06-04 20:25:14.788 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:25:14.800 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:25:14.801 - Force refresh via GameStatsIntegration successful
2025-06-04 20:25:14.802 - Clearing cached data
2025-06-04 20:25:14.803 - Posted refresh_stats event to trigger UI update
2025-06-04 20:25:14.803 - Starting background data reload
2025-06-04 20:25:14.804 - Starting background data loading thread
2025-06-04 20:25:14.805 - Background data loading started
2025-06-04 20:25:14.828 - Loading summary statistics
2025-06-04 20:25:14.830 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:25:14.831 - Data from GameStatsIntegration: {'total_earnings': 5187.200000000001, 'daily_earnings': 664.0000000000002, 'daily_games': 30, 'wallet_balance': 0}
2025-06-04 20:25:14.831 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:25:14.832 - Successfully loaded summary stats: {'total_earnings': 5187.200000000001, 'daily_earnings': 664.0000000000002, 'daily_games': 30, 'wallet_balance': 0}
2025-06-04 20:25:14.834 - Successfully loaded game history: 10 entries
2025-06-04 20:25:14.835 - Successfully loaded weekly stats: 7 days
2025-06-04 20:25:14.836 - Saved 3 items to cache
2025-06-04 20:25:14.837 - Background data loading completed
2025-06-04 20:25:34.158 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:28:17.188 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:28:17.208 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:28:17.210 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:28:17.212 - Forcing refresh of all stats data
2025-06-04 20:28:17.213 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:28:17.230 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:28:17.231 - Force refresh via GameStatsIntegration successful
2025-06-04 20:28:17.232 - Clearing cached data
2025-06-04 20:28:17.234 - Posted refresh_stats event to trigger UI update
2025-06-04 20:28:17.235 - Starting background data reload
2025-06-04 20:28:17.235 - Starting background data loading thread
2025-06-04 20:28:17.237 - Background data loading started
2025-06-04 20:28:17.267 - Loading summary statistics
2025-06-04 20:28:17.267 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:28:17.269 - Data from GameStatsIntegration: {'total_earnings': 5206.400000000001, 'daily_earnings': 683.2000000000003, 'daily_games': 31, 'wallet_balance': 0}
2025-06-04 20:28:17.269 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:28:17.270 - Successfully loaded summary stats: {'total_earnings': 5206.400000000001, 'daily_earnings': 683.2000000000003, 'daily_games': 31, 'wallet_balance': 0}
2025-06-04 20:28:17.271 - Successfully loaded game history: 10 entries
2025-06-04 20:28:17.272 - Successfully loaded weekly stats: 7 days
2025-06-04 20:28:17.275 - Saved 3 items to cache
2025-06-04 20:28:17.277 - Background data loading completed
2025-06-04 20:28:44.585 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:32:37.953 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:32:37.968 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:32:37.971 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:32:37.972 - Forcing refresh of all stats data
2025-06-04 20:32:37.973 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:32:37.994 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:32:37.995 - Force refresh via GameStatsIntegration successful
2025-06-04 20:32:37.995 - Clearing cached data
2025-06-04 20:32:37.997 - Posted refresh_stats event to trigger UI update
2025-06-04 20:32:37.998 - Starting background data reload
2025-06-04 20:32:37.999 - Starting background data loading thread
2025-06-04 20:32:38.001 - Background data loading started
2025-06-04 20:32:38.022 - Loading summary statistics
2025-06-04 20:32:38.022 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:32:38.023 - Data from GameStatsIntegration: {'total_earnings': 5225.6, 'daily_earnings': 702.4000000000003, 'daily_games': 32, 'wallet_balance': 0}
2025-06-04 20:32:38.023 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:32:38.024 - Successfully loaded summary stats: {'total_earnings': 5225.6, 'daily_earnings': 702.4000000000003, 'daily_games': 32, 'wallet_balance': 0}
2025-06-04 20:32:38.024 - Successfully loaded game history: 10 entries
2025-06-04 20:32:38.025 - Successfully loaded weekly stats: 7 days
2025-06-04 20:32:38.027 - Saved 3 items to cache
2025-06-04 20:32:38.027 - Background data loading completed
2025-06-04 20:33:17.069 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:36:30.750 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:36:30.765 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:36:30.768 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:36:30.770 - Forcing refresh of all stats data
2025-06-04 20:36:30.771 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:36:30.787 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:36:30.788 - Force refresh via GameStatsIntegration successful
2025-06-04 20:36:30.788 - Clearing cached data
2025-06-04 20:36:30.789 - Posted refresh_stats event to trigger UI update
2025-06-04 20:36:30.790 - Starting background data reload
2025-06-04 20:36:30.791 - Starting background data loading thread
2025-06-04 20:36:30.792 - Background data loading started
2025-06-04 20:36:30.814 - Loading summary statistics
2025-06-04 20:36:30.817 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:36:30.818 - Data from GameStatsIntegration: {'total_earnings': 5249.6, 'daily_earnings': 726.4000000000003, 'daily_games': 33, 'wallet_balance': 0}
2025-06-04 20:36:30.818 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:36:30.819 - Successfully loaded summary stats: {'total_earnings': 5249.6, 'daily_earnings': 726.4000000000003, 'daily_games': 33, 'wallet_balance': 0}
2025-06-04 20:36:30.819 - Successfully loaded game history: 10 entries
2025-06-04 20:36:30.820 - Successfully loaded weekly stats: 7 days
2025-06-04 20:36:30.823 - Saved 3 items to cache
2025-06-04 20:36:30.825 - Background data loading completed
2025-06-04 20:36:53.572 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:40:09.447 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:40:09.461 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:40:09.463 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:40:09.465 - Forcing refresh of all stats data
2025-06-04 20:40:09.470 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:40:09.481 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:40:09.482 - Force refresh via GameStatsIntegration successful
2025-06-04 20:40:09.484 - Clearing cached data
2025-06-04 20:40:09.485 - Posted refresh_stats event to trigger UI update
2025-06-04 20:40:09.486 - Starting background data reload
2025-06-04 20:40:09.487 - Starting background data loading thread
2025-06-04 20:40:09.490 - Background data loading started
2025-06-04 20:40:09.514 - Loading summary statistics
2025-06-04 20:40:09.515 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:40:09.515 - Data from GameStatsIntegration: {'total_earnings': 5273.6, 'daily_earnings': 750.4000000000003, 'daily_games': 34, 'wallet_balance': 0}
2025-06-04 20:40:09.516 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:40:09.517 - Successfully loaded summary stats: {'total_earnings': 5273.6, 'daily_earnings': 750.4000000000003, 'daily_games': 34, 'wallet_balance': 0}
2025-06-04 20:40:09.517 - Successfully loaded game history: 10 entries
2025-06-04 20:40:09.518 - Successfully loaded weekly stats: 7 days
2025-06-04 20:40:09.520 - Saved 3 items to cache
2025-06-04 20:40:09.520 - Background data loading completed
2025-06-04 20:40:53.151 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:44:08.870 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:44:08.887 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:44:08.890 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:44:08.891 - Forcing refresh of all stats data
2025-06-04 20:44:08.892 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:44:08.907 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:44:08.908 - Force refresh via GameStatsIntegration successful
2025-06-04 20:44:08.908 - Clearing cached data
2025-06-04 20:44:08.909 - Posted refresh_stats event to trigger UI update
2025-06-04 20:44:08.910 - Starting background data reload
2025-06-04 20:44:08.911 - Starting background data loading thread
2025-06-04 20:44:08.912 - Background data loading started
2025-06-04 20:44:08.935 - Loading summary statistics
2025-06-04 20:44:08.936 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:44:08.937 - Data from GameStatsIntegration: {'total_earnings': 5292.8, 'daily_earnings': 769.6000000000004, 'daily_games': 35, 'wallet_balance': 0}
2025-06-04 20:44:08.937 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:44:08.938 - Successfully loaded summary stats: {'total_earnings': 5292.8, 'daily_earnings': 769.6000000000004, 'daily_games': 35, 'wallet_balance': 0}
2025-06-04 20:44:08.938 - Successfully loaded game history: 10 entries
2025-06-04 20:44:08.939 - Successfully loaded weekly stats: 7 days
2025-06-04 20:44:08.940 - Saved 3 items to cache
2025-06-04 20:44:08.941 - Background data loading completed
2025-06-04 20:45:14.472 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:48:55.099 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:48:55.122 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:48:55.124 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:48:55.127 - Forcing refresh of all stats data
2025-06-04 20:48:55.128 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:48:55.144 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:48:55.145 - Force refresh via GameStatsIntegration successful
2025-06-04 20:48:55.146 - Clearing cached data
2025-06-04 20:48:55.147 - Posted refresh_stats event to trigger UI update
2025-06-04 20:48:55.147 - Starting background data reload
2025-06-04 20:48:55.148 - Starting background data loading thread
2025-06-04 20:48:55.149 - Background data loading started
2025-06-04 20:48:55.172 - Loading summary statistics
2025-06-04 20:48:55.173 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:48:55.173 - Data from GameStatsIntegration: {'total_earnings': 5312.0, 'daily_earnings': 788.8000000000004, 'daily_games': 36, 'wallet_balance': 0}
2025-06-04 20:48:55.173 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:48:55.174 - Successfully loaded summary stats: {'total_earnings': 5312.0, 'daily_earnings': 788.8000000000004, 'daily_games': 36, 'wallet_balance': 0}
2025-06-04 20:48:55.176 - Successfully loaded game history: 10 entries
2025-06-04 20:48:55.176 - Successfully loaded weekly stats: 7 days
2025-06-04 20:48:55.178 - Saved 3 items to cache
2025-06-04 20:48:55.178 - Background data loading completed
2025-06-04 20:50:00.903 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:52:59.971 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:52:59.985 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:52:59.988 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:52:59.989 - Forcing refresh of all stats data
2025-06-04 20:52:59.990 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:53:00.004 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:53:00.006 - Force refresh via GameStatsIntegration successful
2025-06-04 20:53:00.007 - Clearing cached data
2025-06-04 20:53:00.008 - Posted refresh_stats event to trigger UI update
2025-06-04 20:53:00.009 - Starting background data reload
2025-06-04 20:53:00.010 - Starting background data loading thread
2025-06-04 20:53:00.013 - Background data loading started
2025-06-04 20:53:00.036 - Loading summary statistics
2025-06-04 20:53:00.037 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:53:00.037 - Data from GameStatsIntegration: {'total_earnings': 5340.8, 'daily_earnings': 817.6000000000004, 'daily_games': 37, 'wallet_balance': 0}
2025-06-04 20:53:00.038 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:53:00.038 - Successfully loaded summary stats: {'total_earnings': 5340.8, 'daily_earnings': 817.6000000000004, 'daily_games': 37, 'wallet_balance': 0}
2025-06-04 20:53:00.039 - Successfully loaded game history: 10 entries
2025-06-04 20:53:00.039 - Successfully loaded weekly stats: 7 days
2025-06-04 20:53:00.041 - Saved 3 items to cache
2025-06-04 20:53:00.042 - Background data loading completed
2025-06-04 20:54:57.604 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:57:31.267 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:57:31.291 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:57:31.295 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:57:31.296 - Forcing refresh of all stats data
2025-06-04 20:57:31.296 - Attempting to force refresh via GameStatsIntegration
2025-06-04 20:57:31.316 - get_stats_provider called, returning provider with initialized=True
2025-06-04 20:57:31.317 - Force refresh via GameStatsIntegration successful
2025-06-04 20:57:31.317 - Clearing cached data
2025-06-04 20:57:31.319 - Posted refresh_stats event to trigger UI update
2025-06-04 20:57:31.320 - Starting background data reload
2025-06-04 20:57:31.320 - Starting background data loading thread
2025-06-04 20:57:31.322 - Background data loading started
2025-06-04 20:57:31.344 - Loading summary statistics
2025-06-04 20:57:31.347 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 20:57:31.348 - Data from GameStatsIntegration: {'total_earnings': 5360.0, 'daily_earnings': 836.8000000000004, 'daily_games': 38, 'wallet_balance': 0}
2025-06-04 20:57:31.348 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 20:57:31.348 - Successfully loaded summary stats: {'total_earnings': 5360.0, 'daily_earnings': 836.8000000000004, 'daily_games': 38, 'wallet_balance': 0}
2025-06-04 20:57:31.349 - Successfully loaded game history: 10 entries
2025-06-04 20:57:31.350 - Successfully loaded weekly stats: 7 days
2025-06-04 20:57:31.351 - Saved 3 items to cache
2025-06-04 20:57:31.351 - Background data loading completed
2025-06-04 20:58:14.814 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:02:53.178 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:02:53.197 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:02:53.199 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:02:53.200 - Forcing refresh of all stats data
2025-06-04 21:02:53.201 - Attempting to force refresh via GameStatsIntegration
2025-06-04 21:02:53.216 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:02:53.217 - Force refresh via GameStatsIntegration successful
2025-06-04 21:02:53.218 - Clearing cached data
2025-06-04 21:02:53.219 - Posted refresh_stats event to trigger UI update
2025-06-04 21:02:53.220 - Starting background data reload
2025-06-04 21:02:53.220 - Starting background data loading thread
2025-06-04 21:02:53.222 - Background data loading started
2025-06-04 21:02:53.245 - Loading summary statistics
2025-06-04 21:02:53.245 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 21:02:53.246 - Data from GameStatsIntegration: {'total_earnings': 5388.8, 'daily_earnings': 865.6000000000004, 'daily_games': 39, 'wallet_balance': 0}
2025-06-04 21:02:53.246 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 21:02:53.247 - Successfully loaded summary stats: {'total_earnings': 5388.8, 'daily_earnings': 865.6000000000004, 'daily_games': 39, 'wallet_balance': 0}
2025-06-04 21:02:53.247 - Successfully loaded game history: 10 entries
2025-06-04 21:02:53.248 - Successfully loaded weekly stats: 7 days
2025-06-04 21:02:53.250 - Saved 3 items to cache
2025-06-04 21:02:53.250 - Background data loading completed
2025-06-04 21:04:13.329 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:07:12.085 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:07:12.100 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:07:12.102 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:07:12.103 - Forcing refresh of all stats data
2025-06-04 21:07:12.103 - Attempting to force refresh via GameStatsIntegration
2025-06-04 21:07:12.122 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:07:12.123 - Force refresh via GameStatsIntegration successful
2025-06-04 21:07:12.125 - Clearing cached data
2025-06-04 21:07:12.126 - Posted refresh_stats event to trigger UI update
2025-06-04 21:07:12.126 - Starting background data reload
2025-06-04 21:07:12.127 - Starting background data loading thread
2025-06-04 21:07:12.129 - Background data loading started
2025-06-04 21:07:12.152 - Loading summary statistics
2025-06-04 21:07:12.153 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 21:07:12.153 - Data from GameStatsIntegration: {'total_earnings': 5417.6, 'daily_earnings': 894.4000000000003, 'daily_games': 40, 'wallet_balance': 0}
2025-06-04 21:07:12.154 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 21:07:12.154 - Successfully loaded summary stats: {'total_earnings': 5417.6, 'daily_earnings': 894.4000000000003, 'daily_games': 40, 'wallet_balance': 0}
2025-06-04 21:07:12.155 - Successfully loaded game history: 10 entries
2025-06-04 21:07:12.155 - Successfully loaded weekly stats: 7 days
2025-06-04 21:07:12.157 - Saved 3 items to cache
2025-06-04 21:07:12.158 - Background data loading completed
2025-06-04 21:08:23.648 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:11:19.927 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:11:19.939 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:11:19.941 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:11:19.942 - Forcing refresh of all stats data
2025-06-04 21:11:19.942 - Attempting to force refresh via GameStatsIntegration
2025-06-04 21:11:19.959 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:11:19.960 - Force refresh via GameStatsIntegration successful
2025-06-04 21:11:19.961 - Clearing cached data
2025-06-04 21:11:19.963 - Posted refresh_stats event to trigger UI update
2025-06-04 21:11:19.964 - Starting background data reload
2025-06-04 21:11:19.966 - Starting background data loading thread
2025-06-04 21:11:19.967 - Background data loading started
2025-06-04 21:11:19.989 - Loading summary statistics
2025-06-04 21:11:19.991 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 21:11:19.992 - Data from GameStatsIntegration: {'total_earnings': 5436.8, 'daily_earnings': 913.6000000000004, 'daily_games': 41, 'wallet_balance': 0}
2025-06-04 21:11:19.992 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 21:11:19.992 - Successfully loaded summary stats: {'total_earnings': 5436.8, 'daily_earnings': 913.6000000000004, 'daily_games': 41, 'wallet_balance': 0}
2025-06-04 21:11:19.993 - Successfully loaded game history: 10 entries
2025-06-04 21:11:19.993 - Successfully loaded weekly stats: 7 days
2025-06-04 21:11:19.996 - Saved 3 items to cache
2025-06-04 21:11:19.996 - Background data loading completed
2025-06-04 21:11:56.831 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:16:03.475 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:16:03.490 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:16:03.492 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:16:03.493 - Forcing refresh of all stats data
2025-06-04 21:16:03.495 - Attempting to force refresh via GameStatsIntegration
2025-06-04 21:16:03.510 - get_stats_provider called, returning provider with initialized=True
2025-06-04 21:16:03.511 - Force refresh via GameStatsIntegration successful
2025-06-04 21:16:03.512 - Clearing cached data
2025-06-04 21:16:03.512 - Posted refresh_stats event to trigger UI update
2025-06-04 21:16:03.513 - Starting background data reload
2025-06-04 21:16:03.514 - Starting background data loading thread
2025-06-04 21:16:03.515 - Background data loading started
2025-06-04 21:16:03.539 - Loading summary statistics
2025-06-04 21:16:03.547 - Attempting to load summary stats from GameStatsIntegration
2025-06-04 21:16:03.549 - Data from GameStatsIntegration: {'total_earnings': 5456.0, 'daily_earnings': 932.8000000000004, 'daily_games': 42, 'wallet_balance': 0}
2025-06-04 21:16:03.550 - Successfully loaded summary stats from GameStatsIntegration
2025-06-04 21:16:03.551 - Successfully loaded summary stats: {'total_earnings': 5456.0, 'daily_earnings': 932.8000000000004, 'daily_games': 42, 'wallet_balance': 0}
2025-06-04 21:16:03.560 - Successfully loaded game history: 10 entries
2025-06-04 21:16:03.560 - Successfully loaded weekly stats: 7 days
2025-06-04 21:16:03.565 - Saved 3 items to cache
2025-06-04 21:16:03.569 - Background data loading completed
