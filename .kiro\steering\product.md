# WOW Bingo Game

A comprehensive desktop bingo game application built with Python and Pygame, featuring advanced game management, statistics tracking, and payment systems.

## Core Features

- **Bingo Game Engine**: Full-featured bingo game with automatic number calling, multiple winning patterns, and real-time gameplay
- **Player Management**: Add, track, and manage players with persistent storage
- **Statistics & Analytics**: Comprehensive game statistics with daily/weekly reporting and database integration
- **Payment System**: Voucher-based credit system with cryptographic validation
- **Multi-language Support**: Amharic and English language support with audio announcements
- **Admin Dashboard**: Web-based admin interface for game management and monitoring
- **Real-time Sync**: RethinkDB integration for real-time data synchronization across multiple instances

## Target Users

- Bingo game operators and referees
- Gaming establishments and entertainment venues
- Community centers and social clubs

## Technical Highlights

- Cross-platform desktop application (Windows primary target)
- Modern UI with responsive design and animations
- Performance optimizations for older hardware
- Comprehensive audio system with multi-language announcements
- Secure database operations with connection pooling
- Modular architecture with plugin-style integrations