#!/usr/bin/env python3
"""
Script to generate realistic game history data that matches the daily earnings data.
This creates detailed game session records with proper calculations.
"""

import sqlite3
import os
import json
import random
from datetime import datetime, timedelta

# Database path
STATS_DB_PATH = os.path.join('data', 'stats.db')

# Game configuration based on codebase analysis
GAME_CONFIG = {
    'commission_percentage': 20.0,
    'default_bet_amount': 50,
    'typical_players_per_game': [3, 4, 5, 6, 7, 8],  # Realistic player counts
    'typical_call_counts': range(15, 45),  # 15-44 calls per game
    'house_patterns': ['Full House', 'Line', 'Four Corners', 'X Pattern', 'T Pattern'],
    'game_statuses': ['Won', 'Completed', 'Finished']
}

# Realistic player names (Ethiopian/International mix)
PLAYER_NAMES = [
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> Getachew', '<PERSON><PERSON> <PERSON><PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON> <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> <PERSON>',
    '<PERSON> <PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>',
    '<PERSON> <PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON> <PERSON><PERSON>'
]

def calculate_game_financials(players_count, bet_amount=50, commission_percent=20.0):
    """Calculate game financial details."""
    total_bets = players_count * bet_amount
    commission_amount = total_bets * (commission_percent / 100.0)
    prize_pool = total_bets - commission_amount
    
    return {
        'total_bets': total_bets,
        'commission_amount': commission_amount,
        'prize_pool': prize_pool,
        'stake_per_player': bet_amount
    }

def generate_called_numbers(count):
    """Generate a realistic sequence of called bingo numbers."""
    # Bingo numbers are 1-75, distributed across columns B(1-15), I(16-30), N(31-45), G(46-60), O(61-75)
    all_numbers = list(range(1, 76))
    return sorted(random.sample(all_numbers, min(count, 75)))

def create_game_details(winner_cartella, claim_type, game_duration, called_numbers):
    """Create game details JSON."""
    return json.dumps({
        'winner_cartella': winner_cartella,
        'claim_type': claim_type,
        'game_duration': game_duration,
        'called_numbers': called_numbers,
        'game_type': 'Standard Bingo',
        'pattern_achieved': random.choice(GAME_CONFIG['house_patterns'])
    })

def generate_games_for_date(date_str, target_earnings):
    """Generate realistic game sessions for a specific date to match target earnings."""
    if target_earnings == 0:
        return []  # No games on days with zero earnings
    
    games = []
    total_commission_earned = 0
    game_count = 0
    
    # Determine number of games needed (typically 1-4 games per day)
    # Higher earnings = more games or higher stakes
    if target_earnings <= 200:
        num_games = random.randint(1, 2)
    elif target_earnings <= 500:
        num_games = random.randint(2, 3)
    else:
        num_games = random.randint(3, 5)
    
    # Generate games throughout the day
    base_date = datetime.strptime(date_str, '%Y-%m-%d')
    
    for i in range(num_games):
        # Calculate remaining earnings needed
        remaining_earnings = target_earnings - total_commission_earned
        if remaining_earnings <= 0:
            break
            
        # Generate game time (spread throughout the day)
        hour = random.randint(9, 22)  # Games between 9 AM and 10 PM
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        game_time = base_date.replace(hour=hour, minute=minute, second=second)
        
        # Determine players count and bet amount
        players_count = random.choice(GAME_CONFIG['typical_players_per_game'])
        bet_amount = GAME_CONFIG['default_bet_amount']
        
        # Adjust bet amount if needed to reach target earnings
        if i == num_games - 1:  # Last game - adjust to hit target exactly
            needed_commission = remaining_earnings
            needed_total_bets = needed_commission / (GAME_CONFIG['commission_percentage'] / 100.0)
            adjusted_bet = needed_total_bets / players_count
            if adjusted_bet >= 25 and adjusted_bet <= 200:  # Reasonable bet range
                bet_amount = round(adjusted_bet)
        
        # Calculate financials
        financials = calculate_game_financials(players_count, bet_amount, GAME_CONFIG['commission_percentage'])
        
        # Generate game details
        winner_name = random.choice(PLAYER_NAMES)
        winner_cartella = random.randint(1, 100)
        house_pattern = random.choice(GAME_CONFIG['house_patterns'])
        total_calls = random.choice(GAME_CONFIG['typical_call_counts'])
        called_numbers = generate_called_numbers(total_calls)
        game_duration = total_calls * 3 + random.randint(-30, 60)  # ~3 seconds per call + variation
        
        # Create game record
        game = {
            'date_time': game_time.strftime('%Y-%m-%d %H:%M:%S'),
            'username': winner_name,
            'house': house_pattern,
            'stake': bet_amount,
            'players': players_count,
            'total_calls': total_calls,
            'commission_percent': GAME_CONFIG['commission_percentage'],
            'fee': financials['commission_amount'],
            'total_prize': financials['prize_pool'],
            'details': create_game_details(winner_cartella, 'Full House', game_duration, called_numbers),
            'status': random.choice(GAME_CONFIG['game_statuses'])
        }
        
        games.append(game)
        total_commission_earned += financials['commission_amount']
        game_count += 1
        
        # Stop if we've reached or exceeded target earnings
        if total_commission_earned >= target_earnings:
            break
    
    print(f"   Generated {game_count} games for {date_str}: Target {target_earnings} ETB, Actual {total_commission_earned:.1f} ETB")
    return games

def insert_game_history():
    """Insert generated game history data into the database."""
    
    # Daily earnings data (matching the previously inserted data)
    daily_earnings = [
        ('2025-05-26', 500.0),  # Monday
        ('2025-05-27', 500.0),  # Tuesday  
        ('2025-05-28', 0.0),    # Wednesday (zero)
        ('2025-05-29', 180.0),  # Thursday
        ('2025-05-30', 600.0),  # Friday
        ('2025-05-31', 750.0),  # Saturday
        ('2025-06-01', 950.0),  # Sunday
    ]
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("🎮 Generating and inserting game history data...")
        
        all_games = []
        
        # Generate games for each day
        for date_str, earnings in daily_earnings:
            day_games = generate_games_for_date(date_str, earnings)
            all_games.extend(day_games)
        
        # Insert all games into database
        print(f"\n📊 Inserting {len(all_games)} game records...")
        
        for game in all_games:
            cursor.execute('''
            INSERT INTO game_history
            (date_time, username, house, stake, players, total_calls,
             commission_percent, fee, total_prize, details, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game['date_time'], game['username'], game['house'], 
                game['stake'], game['players'], game['total_calls'],
                game['commission_percent'], game['fee'], game['total_prize'],
                game['details'], game['status']
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ Game history data inserted successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error inserting game history: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def verify_game_history():
    """Verify the inserted game history data."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        print("\n📋 Verifying game history data:")
        
        # Get summary by date
        cursor.execute('''
        SELECT 
            DATE(date_time) as game_date,
            COUNT(*) as games_count,
            SUM(fee) as total_commission,
            SUM(total_prize) as total_prizes,
            AVG(players) as avg_players,
            AVG(total_calls) as avg_calls
        FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        GROUP BY DATE(date_time)
        ORDER BY game_date
        ''')
        
        results = cursor.fetchall()
        
        if results:
            print("   Date       | Games | Commission | Prizes   | Avg Players | Avg Calls")
            print("   -----------|-------|------------|----------|-------------|----------")
            
            for date, games, commission, prizes, avg_players, avg_calls in results:
                dt = datetime.strptime(date, '%Y-%m-%d')
                day_name = dt.strftime('%a')
                print(f"   {date} ({day_name}) | {games:>5} | {commission:>10.1f} | {prizes:>8.1f} | {avg_players:>11.1f} | {avg_calls:>9.1f}")
        
        # Get total counts
        cursor.execute('''
        SELECT COUNT(*), SUM(fee), SUM(total_prize)
        FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        
        total_games, total_commission, total_prizes = cursor.fetchone()
        
        print("   -----------|-------|------------|----------|-------------|----------")
        print(f"   TOTAL      | {total_games:>5} | {total_commission:>10.1f} | {total_prizes:>8.1f} |             |")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying game history: {e}")

def main():
    """Main function to generate game history."""
    print("🎯 Game History Data Generation")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists(STATS_DB_PATH):
        print(f"❌ Database not found: {STATS_DB_PATH}")
        print("Please run the stats data insertion script first.")
        return
    
    # Generate and insert game history
    success = insert_game_history()
    
    if success:
        # Verify the data
        verify_game_history()
        print("\n🎉 Game history generation completed successfully!")
        print("💡 The stats page will now show detailed game history matching the daily earnings.")
    else:
        print("\n❌ Game history generation failed!")

if __name__ == "__main__":
    main()
