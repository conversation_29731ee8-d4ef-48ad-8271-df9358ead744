#!/usr/bin/env python3
"""
Quick test to verify stats page performance improvements.
"""

import sys
import os
import time

def test_stats_performance():
    """Test stats page loading performance."""
    print("=" * 60)
    print("TESTING STATS PAGE PERFORMANCE")
    print("=" * 60)
    
    try:
        # Test the stats provider directly
        print("1. Testing CentralizedStatsProvider...")
        start_time = time.time()
        
        from stats_page import CentralizedStatsProvider
        provider = CentralizedStatsProvider()
        
        # Test key operations
        today = time.strftime('%Y-%m-%d')
        earnings = provider.get_daily_earnings(today)
        games = provider._get_daily_games_played(today)
        
        load_time = time.time() - start_time
        print(f"   ✅ Provider loaded in {load_time:.2f} seconds")
        print(f"   ✅ Daily earnings: {earnings} ETB")
        print(f"   ✅ Daily games: {games}")
        
        if load_time > 5.0:
            print(f"   ⚠️  Still slow ({load_time:.2f}s), but should be faster than before")
        elif load_time > 2.0:
            print(f"   ⚠️  Moderate speed ({load_time:.2f}s), improvement needed")
        else:
            print(f"   🎉 Good performance ({load_time:.2f}s)!")
        
        # Test cache performance
        print("\n2. Testing cache performance...")
        start_time = time.time()
        
        # Second call should be much faster (cached)
        earnings2 = provider.get_daily_earnings(today)
        games2 = provider._get_daily_games_played(today)
        
        cache_time = time.time() - start_time
        print(f"   ✅ Cached call in {cache_time:.3f} seconds")
        print(f"   ✅ Data consistency: earnings={earnings==earnings2}, games={games==games2}")
        
        if cache_time < 0.1:
            print(f"   🎉 Excellent cache performance!")
        else:
            print(f"   ⚠️  Cache could be faster")
        
        # Test startup optimization
        print("\n3. Testing startup optimization...")
        provider2 = CentralizedStatsProvider()
        start_time = time.time()
        
        # This should use startup optimization
        earnings3 = provider2.get_daily_earnings(today)
        startup_time = time.time() - start_time
        
        print(f"   ✅ Startup-optimized call in {startup_time:.3f} seconds")
        print(f"   ✅ Earnings during startup: {earnings3} ETB")
        
        if startup_time < 0.05:
            print(f"   🎉 Excellent startup optimization!")
        else:
            print(f"   ⚠️  Startup optimization needs improvement")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing performance: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_performance():
    """Test database performance specifically."""
    print("\n" + "=" * 60)
    print("TESTING DATABASE PERFORMANCE")
    print("=" * 60)
    
    try:
        import sqlite3
        
        # Test database connection speed
        print("1. Testing database connection...")
        start_time = time.time()
        
        db_path = os.path.join('data', 'stats.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        connect_time = time.time() - start_time
        print(f"   ✅ Database connected in {connect_time:.3f} seconds")
        
        # Test query performance
        print("2. Testing query performance...")
        start_time = time.time()
        
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_games = cursor.fetchone()[0]
        
        query_time = time.time() - start_time
        print(f"   ✅ Count query in {query_time:.3f} seconds")
        print(f"   ✅ Total games: {total_games}")
        
        # Test complex query
        start_time = time.time()
        
        cursor.execute('''
            SELECT date_time, username, stake, players, fee 
            FROM game_history 
            WHERE username NOT LIKE "TestPlayer" 
            ORDER BY date_time DESC 
            LIMIT 10
        ''')
        recent_games = cursor.fetchall()
        
        complex_query_time = time.time() - start_time
        print(f"   ✅ Complex query in {complex_query_time:.3f} seconds")
        print(f"   ✅ Recent games found: {len(recent_games)}")
        
        conn.close()
        
        # Performance assessment
        total_db_time = connect_time + query_time + complex_query_time
        print(f"\n   📊 Total database time: {total_db_time:.3f} seconds")
        
        if total_db_time < 0.1:
            print("   🎉 Excellent database performance!")
        elif total_db_time < 0.5:
            print("   ✅ Good database performance")
        else:
            print("   ⚠️  Database performance needs optimization")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing database: {e}")
        return False

if __name__ == "__main__":
    print("🚀 STATS PAGE PERFORMANCE TEST")
    print("Testing the fixes applied to reduce 40+ second load times...")
    
    success1 = test_stats_performance()
    success2 = test_database_performance()
    
    print("\n" + "=" * 60)
    print("PERFORMANCE TEST SUMMARY")
    print("=" * 60)
    
    if success1 and success2:
        print("✅ Performance tests completed successfully!")
        print("\nExpected improvements:")
        print("• Stats page should load in under 5 seconds (vs 40+ seconds before)")
        print("• Cached operations should be nearly instant")
        print("• Database queries should complete in milliseconds")
        print("• UI should be responsive during loading")
        
        print("\nTo test the actual stats page:")
        print("1. Run the main game")
        print("2. Navigate to the stats page")
        print("3. It should load much faster now")
        
    else:
        print("❌ Some performance tests failed")
        print("The stats page may still have performance issues")
    
    sys.exit(0 if (success1 and success2) else 1)
