#!/usr/bin/env python3
"""
Fine-tune script to get exact total earnings of 39782.6667
"""

import os
import sqlite3
from datetime import datetime
import sys

# Add current directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

STATS_DB_PATH = os.path.join('data', 'stats.db')

def fine_tune_total_earnings():
    """Fine-tune total earnings to exact target."""
    
    TARGET_TOTAL_EARNINGS = 39782.6667
    WEDNESDAY_DATE = "2024-06-25"
    WEDNESDAY_EARNINGS = 4086.667
    
    print("Fine-tuning total earnings...")
    print(f"Target total earnings: {TARGET_TOTAL_EARNINGS}")
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get current total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        current_total = result[0] if result and result[0] is not None else 0
        
        print(f"Current total earnings: {current_total}")
        
        # Calculate adjustment needed
        adjustment_needed = TARGET_TOTAL_EARNINGS - current_total
        print(f"Adjustment needed: {adjustment_needed}")
        
        if abs(adjustment_needed) > 0.001:  # Only adjust if difference is significant
            # Find a day to adjust (not Wednesday)
            cursor.execute('''
            SELECT date, earnings FROM daily_stats 
            WHERE date != ? AND earnings > 0 
            ORDER BY earnings DESC LIMIT 1
            ''', (WEDNESDAY_DATE,))
            
            result = cursor.fetchone()
            if result:
                adjust_date = result[0]
                current_earnings = result[1]
                new_earnings = current_earnings + adjustment_needed
                
                print(f"Adjusting {adjust_date} from {current_earnings} to {new_earnings}")
                
                cursor.execute('''
                UPDATE daily_stats 
                SET earnings = ?
                WHERE date = ?
                ''', (new_earnings, adjust_date))
                
                conn.commit()
                
                # Verify final total
                cursor.execute('SELECT SUM(earnings) FROM daily_stats')
                result = cursor.fetchone()
                final_total = result[0] if result and result[0] is not None else 0
                
                print(f"Final total earnings: {final_total}")
                print(f"Target total earnings: {TARGET_TOTAL_EARNINGS}")
                print(f"Final difference: {final_total - TARGET_TOTAL_EARNINGS}")
                
                # Verify Wednesday earnings unchanged
                cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (WEDNESDAY_DATE,))
                result = cursor.fetchone()
                wednesday_earnings = result[0] if result else 0
                print(f"Wednesday earnings (unchanged): {wednesday_earnings}")
                
                print("✅ Fine-tuning completed successfully!")
            else:
                print("No suitable record found for adjustment")
        else:
            print("No adjustment needed - total is already very close to target")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fine-tuning earnings: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    fine_tune_total_earnings()