#!/usr/bin/env python3
"""
Diagnostic script to test game recording functionality.
This script simulates a game completion and tests all recording paths.
"""

import sys
import os
import time
from datetime import datetime

def test_game_recording():
    """Test the complete game recording flow."""
    print("=" * 60)
    print("DIAGNOSING GAME RECORDING ISSUE")
    print("=" * 60)
    
    # Test data for a sample game
    test_game_data = {
        'winner_name': 'TestPlayer',
        'winner_cartella': 1,
        'claim_type': 'Full House',
        'game_duration': 120,
        'player_count': 3,
        'prize_amount': 150,
        'commission_percentage': 20,
        'called_numbers': [1, 2, 3, 4, 5, 10, 15, 20, 25, 30],
        'is_demo_mode': False,
        'date_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'stake': 50,
        'bet_amount': 50
    }
    
    print(f"Test game data: {test_game_data}")
    print()
    
    # Test 1: Check if modules can be imported
    print("1. TESTING MODULE IMPORTS:")
    try:
        import thread_safe_db
        print("   ✓ thread_safe_db imported successfully")
    except ImportError as e:
        print(f"   ❌ thread_safe_db import failed: {e}")
        return False
    
    try:
        from game_stats_integration import GameStatsIntegration
        print("   ✓ GameStatsIntegration imported successfully")
    except ImportError as e:
        print(f"   ❌ GameStatsIntegration import failed: {e}")
    
    try:
        from stats_event_hooks import get_stats_event_hooks
        hooks = get_stats_event_hooks()
        print("   ✓ stats_event_hooks imported successfully")
    except ImportError as e:
        print(f"   ❌ stats_event_hooks import failed: {e}")
    
    # Test 2: Test direct database recording
    print("\n2. TESTING DIRECT DATABASE RECORDING:")
    try:
        result = thread_safe_db.record_game_completed(test_game_data)
        print(f"   Direct database recording result: {result}")
        if result:
            print("   ✅ Direct database recording SUCCESSFUL")
        else:
            print("   ❌ Direct database recording FAILED")
    except Exception as e:
        print(f"   ❌ Direct database recording ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Test GameStatsIntegration recording
    print("\n3. TESTING GAMESTATSINTEGRATION RECORDING:")
    try:
        result = GameStatsIntegration.record_game_completed(test_game_data)
        print(f"   GameStatsIntegration recording result: {result}")
        if result:
            print("   ✅ GameStatsIntegration recording SUCCESSFUL")
        else:
            print("   ❌ GameStatsIntegration recording FAILED")
    except Exception as e:
        print(f"   ❌ GameStatsIntegration recording ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 4: Test event hooks recording
    print("\n4. TESTING EVENT HOOKS RECORDING:")
    try:
        hooks = get_stats_event_hooks()
        result = hooks.on_game_completed(test_game_data)
        print(f"   Event hooks recording result: {result}")
        if result:
            print("   ✅ Event hooks recording SUCCESSFUL")
        else:
            print("   ❌ Event hooks recording FAILED")
    except Exception as e:
        print(f"   ❌ Event hooks recording ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 5: Check if game was actually recorded
    print("\n5. VERIFYING GAME WAS RECORDED:")
    try:
        import sqlite3
        db_path = os.path.join('data', 'stats.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check total count
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f"   Total games in database: {total_count}")
        
        # Check for our test game
        cursor.execute('SELECT COUNT(*) FROM game_history WHERE username = ?', ('TestPlayer',))
        test_count = cursor.fetchone()[0]
        print(f"   Test games found: {test_count}")
        
        if test_count > 0:
            cursor.execute('SELECT date_time, username, stake, players FROM game_history WHERE username = ? ORDER BY date_time DESC LIMIT 1', ('TestPlayer',))
            latest = cursor.fetchone()
            print(f"   Latest test game: {latest}")
            print("   ✅ Test game was recorded successfully!")
        else:
            print("   ❌ Test game was NOT recorded")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
    
    # Test 6: Check usage tracker
    print("\n6. CHECKING USAGE TRACKER:")
    try:
        from payment.usage_tracker import UsageTracker
        tracker = UsageTracker()
        print(f"   Usage tracker active: {tracker.active}")
        print(f"   Current game ID: {tracker.current_game_id}")
        print(f"   Credits used: {tracker.credits_used}")
        
        # Check usage log
        import json
        usage_log_path = os.path.join('data', 'usage_log.json')
        if os.path.exists(usage_log_path):
            with open(usage_log_path, 'r') as f:
                usage_data = json.load(f)
                total_usage = usage_data.get('total_usage', 0)
                usage_count = len(usage_data.get('usage', []))
                print(f"   Total credit usage: {total_usage}")
                print(f"   Number of usage entries: {usage_count}")
        
    except Exception as e:
        print(f"   ❌ Error checking usage tracker: {e}")
    
    print("\n" + "=" * 60)
    print("DIAGNOSIS COMPLETE")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_game_recording()
    if success:
        print("\n✅ Diagnostic completed successfully")
        print("\nNext steps:")
        print("1. Check if any test games were recorded")
        print("2. If recording works here but not in actual games, check game completion triggers")
        print("3. If recording fails, check database permissions and connection issues")
    else:
        print("\n❌ Diagnostic failed")
    
    sys.exit(0 if success else 1)
