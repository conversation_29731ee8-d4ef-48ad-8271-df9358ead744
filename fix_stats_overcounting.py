#!/usr/bin/env python3
"""
Fix Stats Overcounting Issue

This script addresses the issue where the stats page overcounts balances and game counts
after playing more than 10 games continuously.

Root cause: The stats_db.py update_daily_stats method increments counters without
transaction isolation, leading to race conditions when multiple game completions
are processed in rapid succession.
"""

import os
import sqlite3
import json
import time
from datetime import datetime, timedelta

# Constants
STATS_DB_PATH = os.path.join('data', 'stats.db')
BACKUP_DIR = os.path.join('data', 'backups')

def create_backup():
    """Create a backup of the stats database before making changes."""
    os.makedirs(BACKUP_DIR, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(BACKUP_DIR, f'stats_db_backup_{timestamp}.db')
    
    try:
        import shutil
        shutil.copy2(STATS_DB_PATH, backup_path)
        print(f"✅ Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

def fix_game_count():
    """Fix the game count in daily_stats by counting actual games in game_history."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get all dates in daily_stats
        cursor.execute('SELECT date FROM daily_stats')
        dates = [row[0] for row in cursor.fetchall()]
        
        fixed_count = 0
        for date in dates:
            # Count actual games for this date (excluding resets and games with 0 calls)
            cursor.execute('''
                SELECT COUNT(*) FROM game_history 
                WHERE date(date_time) = ? 
                AND username NOT LIKE "Game Reset" 
                AND total_calls > 0
            ''', (date,))
            actual_count = cursor.fetchone()[0]
            
            # Get current count
            cursor.execute('SELECT games_played FROM daily_stats WHERE date = ?', (date,))
            current_count = cursor.fetchone()[0]
            
            if current_count != actual_count:
                # Update with correct count
                cursor.execute('''
                    UPDATE daily_stats 
                    SET games_played = ? 
                    WHERE date = ?
                ''', (actual_count, date))
                
                print(f"📊 Fixed game count for {date}: {current_count} → {actual_count}")
                fixed_count += 1
        
        conn.commit()
        
        print(f"✅ Fixed game counts for {fixed_count} dates")
        return fixed_count
    except Exception as e:
        print(f"❌ Error fixing game count: {e}")
        return 0

def fix_earnings():
    """Fix earnings in daily_stats by recalculating from game_history."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Get all dates in daily_stats
        cursor.execute('SELECT date FROM daily_stats')
        dates = [row[0] for row in cursor.fetchall()]
        
        fixed_count = 0
        for date in dates:
            # Sum actual fees for this date
            cursor.execute('''
                SELECT SUM(fee) FROM game_history 
                WHERE date(date_time) = ? 
                AND username NOT LIKE "Game Reset" 
                AND total_calls > 0
            ''', (date,))
            actual_earnings = cursor.fetchone()[0] or 0
            
            # Get current earnings
            cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (date,))
            current_earnings = cursor.fetchone()[0]
            
            if abs(current_earnings - actual_earnings) > 0.01:  # Allow for small floating point differences
                # Update with correct earnings
                cursor.execute('''
                    UPDATE daily_stats 
                    SET earnings = ? 
                    WHERE date = ?
                ''', (actual_earnings, date))
                
                print(f"💰 Fixed earnings for {date}: {current_earnings} → {actual_earnings}")
                fixed_count += 1
        
        conn.commit()
        
        print(f"✅ Fixed earnings for {fixed_count} dates")
        return fixed_count
    except Exception as e:
        print(f"❌ Error fixing earnings: {e}")
        return 0

def add_transaction_tracking():
    """Add a processed_transactions table to prevent duplicate processing."""
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Create processed_transactions table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS processed_transactions (
            game_id INTEGER PRIMARY KEY,
            processed_time TEXT,
            FOREIGN KEY (game_id) REFERENCES game_history(id)
        )
        ''')
        
        # Create index for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_processed_transactions_game_id ON processed_transactions(game_id)')
        
        conn.commit()
        
        print("✅ Added transaction tracking table")
        return True
    except Exception as e:
        print(f"❌ Error adding transaction tracking: {e}")
        return False

def refresh_stats_cache():
    """Refresh the stats cache to ensure UI displays correct data."""
    try:
        # Try to import and use stats_integration module
        from stats_integration import force_refresh_data
        print("✓ Successfully imported force_refresh_data function")
        
        # Run force refresh
        result = force_refresh_data()
        if result:
            print("✅ Force refresh completed successfully")
        else:
            print("⚠ Force refresh completed but reported errors")
            
        return result
    except ImportError:
        print("⚠ Could not import stats_integration module")
        return False
    except Exception as e:
        print(f"❌ Error during force refresh: {e}")
        return False

def main():
    print("=" * 80)
    print("STATS OVERCOUNTING FIX UTILITY")
    print("This utility fixes issues with the stats page overcounting balances and game counts")
    print("=" * 80)
    
    # Create backup first
    if not create_backup():
        response = input("Failed to create backup. Continue anyway? (y/n): ").strip().lower()
        if not response.startswith('y'):
            print("Operation cancelled.")
            return
    
    # Fix game count
    fix_game_count()
    
    # Fix earnings
    fix_earnings()
    
    # Add transaction tracking
    add_transaction_tracking()
    
    # Refresh stats cache
    refresh_stats_cache()
    
    print("=" * 80)
    print("FIX COMPLETED")
    print("The stats page should now display correct data.")
    print("=" * 80)
    
    # Pause at the end to let user read the output if run by double-clicking
    if os.name == 'nt':  # Windows
        os.system('pause')

if __name__ == "__main__":
    main()