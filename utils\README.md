# Stats Data Utilities

This folder contains utility scripts for managing historical statistics and game history data.

## Main Scripts (Production Ready)

### `insert_stats_data.py`
- **Purpose**: Insert daily earnings data into the statistics database
- **Usage**: `python insert_stats_data.py`
- **Features**: 
  - Creates database if it doesn't exist
  - Inserts 7 days of historical earnings data
  - Handles existing records (updates vs inserts)
  - Verification and logging

### `generate_perfect_game_history.py`
- **Purpose**: Generate detailed game session records that exactly match daily earnings
- **Usage**: `python generate_perfect_game_history.py`
- **Features**:
  - Creates 14 realistic game sessions
  - Perfect financial calculations (exact commission matching)
  - Authentic Ethiopian player names
  - Realistic game patterns and durations
  - Clears existing history before inserting new data

### `sync_all_databases.py`
- **Purpose**: Synchronize game history data between different database files
- **Usage**: `python sync_all_databases.py`
- **Features**:
  - Handles different database schemas
  - Syncs from stats.db to stats_new.db
  - Verification of synchronization

### `final_verification_complete.py`
- **Purpose**: Comprehensive verification of all inserted data
- **Usage**: `python final_verification_complete.py`
- **Features**:
  - Verifies daily stats accuracy
  - Verifies game history consistency
  - Checks database synchronization
  - Tests stats page compatibility
  - Complete summary report

## Development Scripts

### `check_db_structure.py`
- **Purpose**: Analyze database structure and insert data to multiple databases
- **Features**: Schema detection, multi-database support

### `verify_stats_display.py`
- **Purpose**: Simulate how stats page will display the data
- **Features**: Weekly stats simulation, display format testing

### `generate_game_history.py`
- **Purpose**: Initial attempt at game history generation (superseded)

### `generate_exact_game_history.py`
- **Purpose**: Improved game history generation (superseded by perfect version)

## Data Summary

### Daily Earnings Inserted
- **2025-05-26 (Mon)**: 500.0 ETB (2 games)
- **2025-05-27 (Tue)**: 500.0 ETB (2 games)
- **2025-05-28 (Wed)**: 0.0 ETB (0 games)
- **2025-05-29 (Thu)**: 180.0 ETB (1 game)
- **2025-05-30 (Fri)**: 600.0 ETB (3 games)
- **2025-05-31 (Sat)**: 750.0 ETB (3 games)
- **2025-06-01 (Sun)**: 950.0 ETB (3 games)

**Total: 3,480.0 ETB across 14 game sessions**

### Game History Features
- Realistic player counts (4-7 players per game)
- Varied stake amounts (142.9 - 375.0 ETB per player)
- Authentic game patterns (Full House, Line, Four Corners, X Pattern, T Pattern)
- Ethiopian player names for authenticity
- Detailed JSON game data including called numbers and durations
- Perfect 20% commission calculations

## Usage Instructions

### To Insert Fresh Data
1. Run `python utils/insert_stats_data.py` to insert daily earnings
2. Run `python utils/generate_perfect_game_history.py` to create game sessions
3. Run `python utils/sync_all_databases.py` to sync all databases
4. Run `python utils/final_verification_complete.py` to verify everything

### To Verify Existing Data
- Run `python utils/final_verification_complete.py` for complete verification
- Run `python utils/verify_stats_display.py` for stats page simulation

## Database Files Updated
- `data/stats.db` - Primary statistics database
- `data/stats_new.db` - Secondary statistics database

Both databases now contain identical historical data that will be displayed on the stats page with proper dates, earnings, and detailed game session information.
