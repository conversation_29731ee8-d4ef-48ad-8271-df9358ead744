#!/usr/bin/env python3
"""
Final comprehensive verification of all inserted stats and game history data.
"""

import sqlite3
import os
from datetime import datetime, timed<PERSON>ta

def verify_daily_stats():
    """Verify daily stats data."""
    print("📊 DAILY STATS VERIFICATION")
    print("-" * 40)
    
    conn = sqlite3.connect('data/stats.db')
    cursor = conn.cursor()
    
    cursor.execute('''
    SELECT date, earnings, games_played 
    FROM daily_stats 
    WHERE date BETWEEN '2025-05-26' AND '2025-06-01'
    ORDER BY date
    ''')
    
    results = cursor.fetchall()
    total_earnings = 0
    total_games = 0
    
    print("Date       | Day | Earnings | Games")
    print("-----------|-----|----------|------")
    
    for date_str, earnings, games in results:
        dt = datetime.strptime(date_str, '%Y-%m-%d')
        day_name = dt.strftime('%a')
        total_earnings += earnings
        total_games += games
        print(f"{date_str} | {day_name} | {earnings:>8.1f} | {games:>5}")
    
    print("-----------|-----|----------|------")
    print(f"TOTAL      |     | {total_earnings:>8.1f} | {total_games:>5}")
    
    conn.close()
    return total_earnings, total_games

def verify_game_history():
    """Verify game history data."""
    print("\n🎮 GAME HISTORY VERIFICATION")
    print("-" * 40)
    
    conn = sqlite3.connect('data/stats.db')
    cursor = conn.cursor()
    
    # Summary by date
    cursor.execute('''
    SELECT 
        DATE(date_time) as game_date,
        COUNT(*) as games_count,
        SUM(fee) as total_commission,
        SUM(total_prize) as total_prizes,
        AVG(players) as avg_players
    FROM game_history 
    WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
    GROUP BY DATE(date_time)
    ORDER BY game_date
    ''')
    
    results = cursor.fetchall()
    total_commission = 0
    total_games = 0
    
    print("Date       | Games | Commission | Prizes   | Avg Players")
    print("-----------|-------|------------|----------|------------")
    
    for date, games, commission, prizes, avg_players in results:
        dt = datetime.strptime(date, '%Y-%m-%d')
        day_name = dt.strftime('%a')
        total_commission += commission
        total_games += games
        print(f"{date} ({day_name}) | {games:>5} | {commission:>10.1f} | {prizes:>8.1f} | {avg_players:>11.1f}")
    
    print("-----------|-------|------------|----------|------------")
    print(f"TOTAL      | {total_games:>5} | {total_commission:>10.1f} |          |")
    
    conn.close()
    return total_commission, total_games

def verify_database_consistency():
    """Verify consistency between databases."""
    print("\n🔄 DATABASE CONSISTENCY CHECK")
    print("-" * 40)
    
    databases = [
        ('stats.db', 'data/stats.db'),
        ('stats_new.db', 'data/stats_new.db')
    ]
    
    for db_name, db_path in databases:
        if not os.path.exists(db_path):
            print(f"❌ {db_name} not found")
            continue
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check daily stats
        cursor.execute('''
        SELECT COUNT(*), SUM(earnings) 
        FROM daily_stats 
        WHERE date BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        daily_count, daily_earnings = cursor.fetchone()
        
        # Check game history
        cursor.execute('''
        SELECT COUNT(*), SUM(fee) 
        FROM game_history 
        WHERE DATE(date_time) BETWEEN '2025-05-26' AND '2025-06-01'
        ''')
        game_count, game_commission = cursor.fetchone()
        
        print(f"{db_name:<12} | Daily: {daily_count:>2} records, {daily_earnings:>7.1f} ETB | Games: {game_count:>2} records, {game_commission:>7.1f} ETB")
        
        conn.close()

def verify_stats_page_compatibility():
    """Verify data will display correctly on stats page."""
    print("\n📈 STATS PAGE COMPATIBILITY CHECK")
    print("-" * 40)
    
    conn = sqlite3.connect('data/stats.db')
    cursor = conn.cursor()
    
    # Test weekly stats retrieval (last 7 days from today)
    today = datetime.now()
    print(f"Testing weekly stats from stats page perspective (today: {today.strftime('%Y-%m-%d')}):")
    
    weekly_total = 0
    for i in range(7):
        day = today - timedelta(days=6-i)
        day_str = day.strftime('%Y-%m-%d')
        day_name = day.strftime('%a %m/%d')
        
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (day_str,))
        result = cursor.fetchone()
        earnings = result[0] if result else 0
        weekly_total += earnings
        
        print(f"  {day_name}: {earnings:>6.1f} ETB")
    
    print(f"  Weekly Total: {weekly_total:>6.1f} ETB")
    
    # Test total earnings
    cursor.execute('SELECT SUM(earnings) FROM daily_stats')
    total_all_time = cursor.fetchone()[0] or 0
    print(f"  All-time Total: {total_all_time:>6.1f} ETB")
    
    conn.close()

def main():
    """Main verification function."""
    print("🎯 FINAL COMPREHENSIVE VERIFICATION")
    print("=" * 50)
    
    # Verify daily stats
    daily_earnings, daily_games = verify_daily_stats()
    
    # Verify game history
    game_commission, game_count = verify_game_history()
    
    # Verify database consistency
    verify_database_consistency()
    
    # Verify stats page compatibility
    verify_stats_page_compatibility()
    
    # Final summary
    print("\n✅ VERIFICATION SUMMARY")
    print("=" * 30)
    print(f"Daily Stats: {daily_earnings:>7.1f} ETB from {daily_games:>2} game days")
    print(f"Game History: {game_commission:>7.1f} ETB from {game_count:>2} game sessions")
    print(f"Data Match: {'✅ PERFECT' if abs(daily_earnings - game_commission) < 0.01 else '❌ MISMATCH'}")
    print(f"Expected Total: 3,480.0 ETB")
    print(f"Actual Total: {daily_earnings:>7.1f} ETB")
    print(f"Accuracy: {'✅ EXACT' if abs(daily_earnings - 3480.0) < 0.01 else '❌ INCORRECT'}")
    
    print("\n🎉 All data successfully inserted and verified!")
    print("💡 The stats page is ready to display the historical data.")

if __name__ == "__main__":
    main()
