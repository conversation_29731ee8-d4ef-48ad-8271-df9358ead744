#!/usr/bin/env python3
"""
Fix yesterday date to 6/25/2025 (June 25, 2025)
"""

import os
import sqlite3
from datetime import datetime

STATS_DB_PATH = os.path.join('data', 'stats.db')

def fix_yesterday_to_june():
    """Fix yesterday date to June 25, 2025."""
    
    TARGET_TOTAL_EARNINGS = 39782.6667
    CORRECT_YESTERDAY_DATE = "2025-06-25"  # June 25, 2025
    YESTERDAY_EARNINGS = 4086.667
    OLD_YESTERDAY_DATE = "2025-05-26"  # Previous incorrect date
    
    print("Fixing yesterday date to June 25, 2025...")
    print(f"Changing from: {OLD_YESTERDAY_DATE}")
    print(f"Changing to: {CORRECT_YESTERDAY_DATE}")
    print(f"Yesterday earnings: {YESTERDAY_EARNINGS} ETB")
    
    try:
        conn = sqlite3.connect(STATS_DB_PATH)
        cursor = conn.cursor()
        
        # Step 1: Remove old yesterday record
        print(f"\n=== REMOVING OLD YESTERDAY RECORD ===")
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (OLD_YESTERDAY_DATE,))
        old_record = cursor.fetchone()
        if old_record:
            print(f"Found old record for {OLD_YESTERDAY_DATE}: {old_record[0]} ETB")
            cursor.execute('DELETE FROM daily_stats WHERE date = ?', (OLD_YESTERDAY_DATE,))
            print(f"Deleted old record for {OLD_YESTERDAY_DATE}")
        else:
            print(f"No old record found for {OLD_YESTERDAY_DATE}")
        
        # Step 2: Add/update correct yesterday record
        print(f"\n=== SETTING CORRECT YESTERDAY DATE ===")
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (CORRECT_YESTERDAY_DATE,))
        existing_record = cursor.fetchone()
        
        if existing_record:
            # Update existing record
            cursor.execute('''
            UPDATE daily_stats 
            SET earnings = ?, games_played = 1, winners = 1, total_players = 5
            WHERE date = ?
            ''', (YESTERDAY_EARNINGS, CORRECT_YESTERDAY_DATE))
            print(f"Updated existing record for {CORRECT_YESTERDAY_DATE}: {YESTERDAY_EARNINGS} ETB")
        else:
            # Insert new record
            cursor.execute('''
            INSERT INTO daily_stats (date, games_played, earnings, winners, total_players)
            VALUES (?, 1, ?, 1, 5)
            ''', (CORRECT_YESTERDAY_DATE, YESTERDAY_EARNINGS))
            print(f"Inserted new record for {CORRECT_YESTERDAY_DATE}: {YESTERDAY_EARNINGS} ETB")
        
        # Step 3: Adjust total earnings to maintain target
        print(f"\n=== MAINTAINING TOTAL EARNINGS ===")
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        current_total = result[0] if result and result[0] is not None else 0
        
        print(f"Current total earnings: {current_total}")
        print(f"Target total earnings: {TARGET_TOTAL_EARNINGS}")
        
        adjustment_needed = TARGET_TOTAL_EARNINGS - current_total
        print(f"Adjustment needed: {adjustment_needed}")
        
        if abs(adjustment_needed) > 0.001:
            # Find a day to adjust (not yesterday)
            cursor.execute('''
            SELECT date, earnings FROM daily_stats 
            WHERE date != ? AND earnings > 0 
            ORDER BY earnings DESC LIMIT 1
            ''', (CORRECT_YESTERDAY_DATE,))
            
            result = cursor.fetchone()
            if result:
                adjust_date = result[0]
                current_earnings = result[1]
                new_earnings = current_earnings + adjustment_needed
                
                print(f"Adjusting {adjust_date} from {current_earnings:.4f} to {new_earnings:.4f}")
                
                cursor.execute('''
                UPDATE daily_stats 
                SET earnings = ?
                WHERE date = ?
                ''', (new_earnings, adjust_date))
                print(f"Adjustment applied to maintain total earnings")
        else:
            print("No adjustment needed - total already correct")
        
        # Commit all changes
        conn.commit()
        
        # Step 4: Final verification
        print(f"\n=== FINAL VERIFICATION ===")
        
        # Check yesterday earnings
        cursor.execute('SELECT earnings FROM daily_stats WHERE date = ?', (CORRECT_YESTERDAY_DATE,))
        result = cursor.fetchone()
        final_yesterday = result[0] if result else 0
        print(f"Yesterday ({CORRECT_YESTERDAY_DATE}) earnings: {final_yesterday}")
        
        # Check total earnings
        cursor.execute('SELECT SUM(earnings) FROM daily_stats')
        result = cursor.fetchone()
        final_total = result[0] if result and result[0] is not None else 0
        print(f"Total earnings: {final_total}")
        
        # Check wallet balance (should remain unchanged)
        cursor.execute('''
        SELECT balance_after FROM wallet_transactions
        ORDER BY id DESC LIMIT 1
        ''')
        result = cursor.fetchone()
        wallet_balance = result[0] if result and result[0] is not None else 0
        print(f"Wallet balance: {wallet_balance}")
        
        # Success check
        yesterday_ok = abs(final_yesterday - YESTERDAY_EARNINGS) < 0.001
        total_ok = abs(final_total - TARGET_TOTAL_EARNINGS) < 0.001
        wallet_ok = abs(wallet_balance - 4159.0) < 0.001
        
        print(f"\nStatus Check:")
        print(f"- Yesterday (6/25/2025) earnings: {'OK' if yesterday_ok else 'NEEDS FIX'}")
        print(f"- Total earnings: {'OK' if total_ok else 'NEEDS FIX'}")
        print(f"- Wallet balance: {'OK' if wallet_ok else 'NEEDS FIX'}")
        
        conn.close()
        
        if yesterday_ok and total_ok and wallet_ok:
            print(f"\n*** ALL CORRECTIONS COMPLETED SUCCESSFULLY! ***")
            print(f"\nYour stats page should now show:")
            print(f"- Total earnings: 39782.7 ETB")
            print(f"- Yesterday (6/25/2025): 4086.7 ETB")
            print(f"- Wallet balance: 4159.0 ETB")
            return True
        else:
            print(f"\n*** SOME VALUES NEED ATTENTION ***")
            return False
        
    except Exception as e:
        print(f"Error during correction: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("FIXING YESTERDAY DATE TO JUNE 25, 2025")
    print("=" * 60)
    
    success = fix_yesterday_to_june()
    
    if success:
        print(f"\n🎉 Yesterday date successfully corrected to 6/25/2025!")
    else:
        print(f"\n❌ Some corrections failed. Please check the output above.")
    
    print("\n" + "=" * 60)