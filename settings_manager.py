import os
import json
import pygame
from announcer_language_manager import AnnouncerLanguageManager

class SettingsManager:
    """
    Manages application settings, including loading, saving, and providing defaults.
    Acts as a central repository for all configurable options in the application.
    """

    # Class variable for singleton pattern
    _instance = None
    _initialized = False

    def __new__(cls):
        """Implement singleton pattern for settings manager"""
        if cls._instance is None:
            cls._instance = super(SettingsManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the settings manager with default values"""
        # Only initialize once (singleton pattern)
        if SettingsManager._initialized:
            return
        SettingsManager._initialized = True

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)

        # Path to settings file
        self.settings_file = os.path.join('data', 'settings.json')

        # Cache for theme colors to avoid recalculation
        self._theme_color_cache = {}

        # Define default settings
        self.default_settings = {
            # Game settings
            "game": {
                "number_call_delay": 3.0,  # Seconds between automatic number calls
                "strict_claim_timing": True,  # Enforce claim timing rules (must claim before next number)
                "shuffle_duration": 3.0,  # Duration of shuffle animation in seconds
                "commission_percentage": 20.0,  # Commission percentage for prize pool calculation (20%)
                "show_total_selected": True,  # Show/hide the Total selected section in board selection
                "remember_cartella_numbers": True,  # Remember previously selected cartella numbers (enabled by default)
                "show_game_info": True,  # Show/hide game info footer (credits, commission, share)
            },
            
            # Pattern bonus settings
            "pattern_bonuses": {
                # Enable/disable pattern bonuses
                "enable_inner_square": True,  # Enable Inner Square/3x3 Square pattern bonus
                "enable_diamond": True,       # Enable Diamond pattern bonus
                "enable_cross": True,         # Enable Cross pattern bonus
                "enable_four_corners": True,  # Enable Four Corners pattern bonus
                "enable_rows": True,          # Enable Row pattern bonuses
                "enable_columns": True,       # Enable Column pattern bonuses
                "enable_diagonals": True,     # Enable Diagonal pattern bonuses
                
                # Bonus amounts for each pattern type
                "bonus_amount_inner_square": 300,  # Bonus amount for Inner Square pattern
                "bonus_amount_diamond": 300,       # Bonus amount for Diamond pattern
                "bonus_amount_cross": 300,         # Bonus amount for Cross pattern
                "bonus_amount_four_corners": 200,  # Bonus amount for Four Corners pattern
                "bonus_amount_rows": 100,          # Bonus amount for Row patterns
                "bonus_amount_columns": 100,       # Bonus amount for Column patterns
                "bonus_amount_diagonals": 200,     # Bonus amount for Diagonal patterns
                
                # Call thresholds for each pattern type (0 = no limit)
                "call_threshold_inner_square": 0,  # No call limit for Inner Square pattern
                "call_threshold_diamond": 0,       # No call limit for Diamond pattern
                "call_threshold_cross": 0,         # No call limit for Cross pattern
                "call_threshold_four_corners": 5,  # Four Corners must be claimed within 5 calls
                "call_threshold_rows": 5,          # Rows must be claimed within 5 calls
                "call_threshold_columns": 5,       # Columns must be claimed within 5 calls
                "call_threshold_diagonals": 5,     # Diagonals must be claimed within 5 calls
            },

            # Board settings
            "boards": {
                "presets": [],  # List of board preset names
                "current_preset": "",  # Currently selected preset
            },

            # Display settings
            "display": {
                "fullscreen": False,  # Fullscreen mode
                "resolution": "1280x720",  # Default resolution
                "animations_enabled": True,  # Enable animations
                "show_recent_calls": True,  # Show recent calls in footer
                "recent_calls_count": 5,  # Number of recent calls to show
                "ui_theme": "dark",  # UI theme (dark, light, midnight, ocean)
                "ui_accent_color": "blue",  # Accent color for UI elements (blue, teal, purple, orange, green, red, gold)
            },

            # Animation settings
            "animations": {
                "transition_animation_enabled": True,  # Enable game start transition animation
                "transition_animation_duration": 13,  # Duration of game start transition in seconds
            },

            # Audio settings
            "audio": {
                "sound_effects_enabled": True,  # Enable sound effects
                "sound_effects_volume": 0.7,  # Sound effects volume (0.0 to 1.0)
                "music_enabled": True,  # Enable background music
                "music_volume": 0.5,  # Music volume (0.0 to 1.0)
                "voice_enabled": True,  # Enable voice announcements
                "voice_volume": 0.8,  # Voice volume (0.0 to 1.0)
                "cartella_announcements_enabled": True,  # Enable cartella registration announcements
                "announcer_language": "Default (English)",  # Default announcer language
            },

            # Language settings
            "language": {
                "current_language": "English",  # Current language
                "available_languages": ["English", "Oromo", "Amharic", "Tigrinya", "Somali", "Agew"],  # Available languages
                "custom_language_path": "",  # Path to custom language file
            },

            # Import/Export settings
            "import_export": {
                "last_import_path": "",  # Last path used for importing boards
                "last_export_path": "",  # Last path used for exporting boards
                "auto_backup": True,  # Automatically backup data files
                "backup_interval": 30,  # Minutes between automatic backups
                "default_export_format": "pdf",  # Default export format (pdf, html)
                "export_location": "",  # Default export location (empty = user documents)
                "include_game_history": True,  # Include game history in reports
                "include_credit_history": True,  # Include credit history in reports
                "include_summary_data": True,  # Include summary cards data in reports
                "include_notifications": True,  # Include current notifications in reports
                "auto_open_exported_file": True,  # Automatically open exported file after creation
            },

            # Notification settings
            "notifications": {
                "enabled": True,  # Enable/disable notification system
                "low_credit_threshold": 500,  # Credit threshold for low balance warning
                "expiry_warning_days": 5,  # Days before expiry to show warning
                "show_low_credit_warning": True,  # Show low credit balance warnings
                "show_expiry_warning": True,  # Show voucher expiry warnings
            },

            # Advertising settings
            "advertising": {
                "enabled": True,  # Enable advertising text
                "hidden": False,  # Whether the section is hidden by user click
                "text": "WOW Games - Premium Bingo Experience",  # Default advertising text
                "font": "Arial",  # Font for advertising text
                "font_size": 32,  # Font size for advertising text
                "text_color": "#FFD700",  # Text color (golden yellow)
                "scroll_speed": 2.0,  # Scrolling speed (pixels per frame)
                "bold": True,  # Bold text
                "italic": False,  # Italic text
                "led_style": True,  # Use LED-style background
                "led_pixel_size": 4,  # Size of LED pixels
                "rainbow_text": True,  # Use golden gradient colors for text by default
                "text_glow": True,  # Add glow effect to text

                # Modern advertising settings
                "modern_mode": True,  # Use modern GPU-accelerated advertising
                "animation_mode": "scroll",  # Animation mode: scroll, fade, pulse, wave, typewriter, particle
                "visual_quality": "auto",  # Visual quality: auto, ultra, high, medium, low
                "particles_enabled": True,  # Enable particle effects
                "glass_morphism": True,  # Use glass morphism background
                "auto_quality": True,  # Automatically adjust quality based on performance
                "gpu_acceleration": True,  # Enable GPU acceleration when available
            },

            # Power management settings
            "power_management": {
                "enabled": True,  # Enable power management system
                "prevent_screen_sleep": True,  # Prevent screen from going to sleep
                "keep_window_active": True,  # Keep application window active
                "simulate_user_activity": True,  # Simulate user activity to prevent screensaver
                "check_interval": 300,  # Check interval in seconds (5 minutes)
                "auto_start": True,  # Automatically start power management on app startup
            }
        }

        # Current settings (will be loaded from file or defaults)
        self.settings = {}

        # Load settings from file or use defaults
        self.load_settings()

    def load_settings(self):
        """Load settings from JSON file or use defaults if file doesn't exist"""
        try:
            if os.path.exists(self.settings_file):
                # Use explicit UTF-8 encoding to ensure Amharic characters are loaded correctly
                with open(self.settings_file, 'r', encoding='utf-8') as file:
                    loaded_settings = json.load(file)

                # Print the loaded advertising text for debugging
                if 'advertising' in loaded_settings and 'text' in loaded_settings['advertising']:
                    ad_text = loaded_settings['advertising']['text']
                    print(f"Loaded advertising text: {ad_text}")
                    print(f"Hex representation: {ad_text.encode('utf-8').hex()}")

                # Merge loaded settings with defaults to ensure all keys exist
                self.settings = self.merge_settings(self.default_settings, loaded_settings)
            else:
                # Use defaults if file doesn't exist
                self.settings = self.default_settings.copy()
                # Save defaults to create the file
                self.save_settings()

            return True
        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            # Fall back to defaults on error
            self.settings = self.default_settings.copy()
            return False

    def save_settings(self):
        """Save current settings to JSON file with proper UTF-8 encoding"""
        try:
            # Use explicit UTF-8 encoding to ensure Amharic characters are saved correctly
            with open(self.settings_file, 'w', encoding='utf-8') as file:
                json.dump(self.settings, file, indent=4, ensure_ascii=False)
            print(f"Settings saved to {self.settings_file} with UTF-8 encoding")
            return True
        except Exception as e:
            print(f"Error saving settings: {str(e)}")
            return False

    def get_setting(self, category, key, default=None):
        """
        Get a specific setting value

        Args:
            category: The settings category (e.g., 'game', 'display')
            key: The specific setting key
            default: Default value if setting doesn't exist

        Returns:
            The setting value or default if not found
        """
        try:
            return self.settings[category][key]
        except KeyError:
            # If the setting doesn't exist, try to get it from defaults
            try:
                return self.default_settings[category][key]
            except KeyError:
                # If it's not in defaults either, return the provided default
                return default

    def set_setting(self, category, key, value):
        """
        Set a specific setting value and save settings to disk

        Args:
            category: The settings category (e.g., 'game', 'display')
            key: The specific setting key
            value: The value to set

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure category exists
            if category not in self.settings:
                self.settings[category] = {}

            # Set the value
            self.settings[category][key] = value

            # Print debug information for Amharic text
            if category == 'advertising' and key == 'text':
                print(f"Setting advertising text to: {value}")
                print(f"Hex representation: {value.encode('utf-8').hex()}")

            # Special handling for board presets - automatically apply when selected
            if category == 'boards' and key == 'current_preset':
                try:
                    # Import here to avoid circular imports
                    from board_manager import BoardManager
                    board_manager = BoardManager(self)

                    # Apply the preset
                    board_manager.apply_preset(value)
                    print(f"Automatically applied board preset: {value}")
                except Exception as e:
                    print(f"Error automatically applying preset: {e}")

            # Save settings to disk immediately
            success = self.save_settings()
            if success:
                print(f"Settings saved: {category}.{key} = {value}")

            return True
        except Exception as e:
            print(f"Error setting {category}.{key}: {str(e)}")
            return False

    def reset_to_defaults(self, category=None):
        """
        Reset settings to defaults

        Args:
            category: Optional category to reset. If None, resets all settings.

        Returns:
            True if successful, False otherwise
        """
        try:
            if category:
                # Reset only the specified category
                if category in self.default_settings:
                    self.settings[category] = self.default_settings[category].copy()
            else:
                # Reset all settings
                self.settings = self.default_settings.copy()

            # Save the changes
            self.save_settings()
            return True
        except Exception as e:
            print(f"Error resetting settings: {str(e)}")
            return False

    def merge_settings(self, defaults, loaded):
        """
        Merge loaded settings with defaults to ensure all keys exist

        Args:
            defaults: Default settings dictionary
            loaded: Loaded settings dictionary

        Returns:
            Merged settings dictionary
        """
        result = defaults.copy()

        # Update with loaded values, preserving structure
        for category in loaded:
            if category in result:
                if isinstance(loaded[category], dict) and isinstance(result[category], dict):
                    # Merge category dictionaries
                    for key in loaded[category]:
                        if key in result[category]:
                            result[category][key] = loaded[category][key]

        return result

    def get_resolution_options(self):
        """
        Get a list of available screen resolutions

        Returns:
            List of resolution strings (e.g., ["1280x720", "1920x1080"])
        """
        # Common resolutions
        resolutions = [
            "1280x720",   # HD
            "1366x768",   # Common laptop resolution
            "1600x900",   # HD+
            "1920x1080",  # Full HD
            "2560x1440",  # QHD
            "3840x2160"   # 4K UHD
        ]

        # Add current display resolution if not in the list
        info = pygame.display.Info()
        current = f"{info.current_w}x{info.current_h}"
        if current not in resolutions:
            resolutions.append(current)

        return sorted(resolutions, key=lambda x: int(x.split('x')[0]))

    def get_language_options(self):
        """
        Get a list of available languages

        Returns:
            List of language strings
        """
        return self.get_setting('language', 'available_languages',
                              ["English", "Oromo", "Amharic", "Tigrinya", "Somali", "Agew"])

    def get_theme_options(self):
        """
        Get a list of available UI themes

        Returns:
            List of theme strings
        """
        return ["dark", "light", "midnight", "ocean"]

    def get_accent_color_options(self):
        """
        Get a list of available accent colors

        Returns:
            List of accent color strings
        """
        return ["blue", "teal", "purple", "orange", "green", "red", "gold"]

    def get_announcer_language_options(self):
        """
        Get a list of available announcer languages

        Returns:
            List of announcer language strings
        """
        # Use the AnnouncerLanguageManager to get the available languages
        return AnnouncerLanguageManager.get_available_languages()

    def get_font_options(self):
        """
        Get a list of available fonts

        Returns:
            List of font names
        """
        # Common fonts that should be available on most systems
        return ["Arial", "Verdana", "Tahoma", "Times New Roman", "Georgia", "Courier New", "Impact"]

    # Cache for setting options
    _options_cache = {}

    def clear_options_cache(self, setting_key=None):
        """Clear the options cache for a specific setting or all settings"""
        if setting_key:
            if setting_key in self._options_cache:
                del self._options_cache[setting_key]
        else:
            self._options_cache.clear()

    def get_board_preset_options(self):
        """
        Get a list of available board presets

        Returns:
            List of preset names
        """
        # Get presets from settings
        presets = self.get_setting('boards', 'presets', [])

        # If no presets, return a default
        if not presets:
            return ["Default"]

        return presets

    def get_options_for_setting(self, setting_key):
        """Get available options for a setting with caching for performance"""
        # Check if options are already in cache
        if setting_key in self._options_cache:
            return self._options_cache[setting_key]

        # Get options based on setting key
        if setting_key == "resolution":
            options = self.get_resolution_options()
        elif setting_key == "ui_theme":
            options = self.get_theme_options()
        elif setting_key == "ui_accent_color":
            options = self.get_accent_color_options()
        elif setting_key == "current_language":
            options = self.get_language_options()
        elif setting_key == "font":
            options = self.get_font_options()
        elif setting_key == "current_preset":
            options = self.get_board_preset_options()
        elif setting_key == "announcer_language":
            options = self.get_announcer_language_options()
        elif setting_key == "tts_language":
            options = ["System Default", "English (US)", "English (UK)", "Spanish", "French", "German", "Italian", "Portuguese", "Russian", "Japanese", "Chinese"]
        elif setting_key == "voice_gender":
            options = ["Male", "Female"]
        elif setting_key == "backup_format":
            options = ["ZIP", "JSON", "CSV"]
        elif setting_key == "cloud_service":
            options = ["Google Drive", "Dropbox", "OneDrive", "iCloud"]
        elif setting_key == "sound_quality":
            options = ["Low", "Medium", "High"]
        elif setting_key == "difficulty":
            options = ["Easy", "Normal", "Hard", "Expert"]
        elif setting_key == "font_size":
            options = ["Small", "Medium", "Large", "Extra Large"]
        elif setting_key == "colorblind_mode":
            options = ["None", "Protanopia", "Deuteranopia", "Tritanopia"]
        else:
            options = None

        # Cache the options
        if options is not None:
            self._options_cache[setting_key] = options

        return options

    def get_theme_colors(self, theme=None, accent_color=None):
        """
        Get color palette for the specified theme and accent color with caching for performance

        Args:
            theme: Theme name (if None, uses current setting)
            accent_color: Accent color name (if None, uses current setting)

        Returns:
            Dictionary of color values for the theme
        """
        # Use current settings if not specified
        if theme is None:
            theme = self.get_setting('display', 'ui_theme', 'dark')
        if accent_color is None:
            accent_color = self.get_setting('display', 'ui_accent_color', 'blue')

        # Check if this theme/accent combination is in the cache
        cache_key = (theme, accent_color)
        if cache_key in self._theme_color_cache:
            return self._theme_color_cache[cache_key]

        # Define accent color RGB values - simplified for better readability
        accent_colors = {
            "blue": (0, 120, 215),     # Standard blue
            "teal": (0, 150, 150),     # Simplified teal
            "purple": (120, 80, 200),   # Simplified purple
            "orange": (220, 120, 40),   # Simplified orange
            "green": (40, 160, 80),     # Simplified green
            "red": (200, 60, 60),       # Simplified red
            "gold": (220, 180, 40)      # Simplified gold
        }

        # Get the selected accent color or default to blue
        selected_accent = accent_colors.get(accent_color, accent_colors["blue"])

        # Define theme color palettes - optimized for readability
        if theme == "light":
            result = {
                # Primary colors - simplified
                "accent": selected_accent,
                "accent_hover": self._lighten_color(selected_accent, 30),
                "accent_active": self._lighten_color(selected_accent, 50),

                # Background colors - light theme with better contrast
                "panel_bg": (245, 245, 245),       # Very light gray background
                "panel_header": (230, 230, 230),   # Light gray header
                "panel_content": (240, 240, 240),   # Content area

                # Control colors - improved contrast
                "control_bg": (225, 225, 225),      # Control background
                "control_border": (180, 180, 180),  # Control border
                "control_highlight": selected_accent,

                # Status colors - high visibility
                "success": (0, 150, 50),            # Green
                "warning": (200, 150, 0),           # Yellow
                "danger": (200, 30, 30),            # Red

                # Text colors - high contrast for readability
                "light_text": (0, 0, 0),            # Black text for maximum contrast
                "medium_text": (40, 40, 40),        # Very dark gray (for normal text)
                "dark_text": (0, 0, 0),             # Black (for emphasis)

                # Navigation - improved contrast
                "nav_bar_bg": (210, 210, 210),      # Light gray nav bar
                "nav_bar_active": selected_accent,

                # Misc - reduced effects
                "shadow": (0, 0, 0, 20),            # Minimal shadow
                "overlay": (255, 255, 255, 160),    # Simplified overlay
                "glossy_start": (255, 255, 255, 40),# Minimal glossy effect
                "glossy_end": (255, 255, 255, 0),

                # Tab colors - improved contrast
                "tab_active_bg": (255, 255, 255),   # White active tab
                "tab_inactive_bg": (220, 220, 220), # Light gray inactive tab
                "tab_hover_bg": (235, 235, 235)     # Hover tab
            }

            # Store in cache and return
            self._theme_color_cache[cache_key] = result
            return result
        elif theme == "midnight":
            result = {
                # Primary colors - simplified
                "accent": selected_accent,
                "accent_hover": self._lighten_color(selected_accent, 30),
                "accent_active": self._lighten_color(selected_accent, 50),

                # Background colors - simplified midnight theme
                "panel_bg": (20, 20, 40),           # Deep purple background
                "panel_header": (30, 30, 50),       # Slightly lighter header
                "panel_content": (25, 25, 45),       # Content area

                # Control colors - simplified
                "control_bg": (35, 35, 60),          # Control background
                "control_border": (50, 50, 80),      # Control border
                "control_highlight": selected_accent,

                # Status colors - simplified (same as other themes)
                "success": (40, 160, 80),           # Green
                "warning": (220, 180, 40),          # Yellow
                "danger": (200, 60, 60),            # Red

                # Text colors - simplified for readability
                "light_text": (240, 240, 240),       # White text
                "medium_text": (180, 180, 200),      # Light purple-gray text
                "dark_text": (120, 120, 150),        # Dark purple-gray text

                # Navigation - simplified
                "nav_bar_bg": (15, 15, 35),          # Dark nav bar
                "nav_bar_active": selected_accent,

                # Misc - reduced effects
                "shadow": (0, 0, 0, 40),             # Minimal shadow
                "overlay": (15, 15, 35, 180),       # Simplified overlay
                "glossy_start": (255, 255, 255, 30),# Minimal glossy effect
                "glossy_end": (255, 255, 255, 0),

                # Tab colors - simplified
                "tab_active_bg": (35, 35, 65),       # Active tab
                "tab_inactive_bg": (25, 25, 45),     # Inactive tab
                "tab_hover_bg": (30, 30, 55)         # Hover tab
            }

            # Store in cache and return
            self._theme_color_cache[cache_key] = result
            return result
        elif theme == "ocean":
            result = {
                # Primary colors - simplified
                "accent": selected_accent,
                "accent_hover": self._lighten_color(selected_accent, 30),
                "accent_active": self._lighten_color(selected_accent, 50),

                # Background colors - simplified ocean theme
                "panel_bg": (20, 40, 60),           # Ocean blue background
                "panel_header": (25, 50, 70),       # Slightly lighter header
                "panel_content": (30, 45, 65),       # Content area

                # Control colors - simplified
                "control_bg": (35, 55, 75),          # Control background
                "control_border": (45, 65, 85),      # Control border
                "control_highlight": selected_accent,

                # Status colors - simplified (same as other themes)
                "success": (40, 160, 80),           # Green
                "warning": (220, 180, 40),          # Yellow
                "danger": (200, 60, 60),            # Red

                # Text colors - simplified for readability
                "light_text": (240, 240, 240),       # White text
                "medium_text": (180, 200, 220),      # Light blue-gray text
                "dark_text": (120, 140, 160),        # Dark blue-gray text

                # Navigation - simplified
                "nav_bar_bg": (15, 35, 55),          # Dark nav bar
                "nav_bar_active": selected_accent,

                # Misc - reduced effects
                "shadow": (0, 0, 0, 40),             # Minimal shadow
                "overlay": (10, 30, 50, 180),       # Simplified overlay
                "glossy_start": (255, 255, 255, 30),# Minimal glossy effect
                "glossy_end": (255, 255, 255, 0),

                # Tab colors - simplified
                "tab_active_bg": (30, 60, 80),       # Active tab
                "tab_inactive_bg": (25, 45, 65),     # Inactive tab
                "tab_hover_bg": (30, 50, 70)         # Hover tab
            }

            # Store in cache and return
            self._theme_color_cache[cache_key] = result
            return result
        else:  # Default dark theme
            result = {
                # Primary colors - simplified
                "accent": selected_accent,
                "accent_hover": self._lighten_color(selected_accent, 30),
                "accent_active": self._lighten_color(selected_accent, 50),

                # Background colors - darker for better contrast with text
                "panel_bg": (15, 25, 40),           # Dark blue background
                "panel_header": (25, 35, 50),       # Slightly lighter header
                "panel_content": (20, 30, 45),       # Content area

                # Control colors - better contrast
                "control_bg": (35, 45, 65),          # Control background
                "control_border": (55, 65, 85),      # Control border
                "control_highlight": selected_accent,

                # Status colors - high visibility
                "success": (50, 200, 100),           # Bright green
                "warning": (255, 200, 50),           # Bright yellow
                "danger": (255, 70, 70),             # Bright red

                # Text colors - high contrast for readability
                "light_text": (255, 255, 255),        # Pure white text
                "medium_text": (220, 220, 220),       # Very light gray text
                "dark_text": (180, 180, 180),         # Light gray text

                # Navigation - simplified
                "nav_bar_bg": (15, 25, 40),           # Dark nav bar
                "nav_bar_active": selected_accent,

                # Misc - reduced effects
                "shadow": (0, 0, 0, 40),              # Minimal shadow
                "overlay": (10, 20, 30, 180),        # Simplified overlay
                "glossy_start": (255, 255, 255, 30), # Minimal glossy effect
                "glossy_end": (255, 255, 255, 0),

                # Tab colors - better contrast
                "tab_active_bg": (45, 65, 90),        # Brighter active tab
                "tab_inactive_bg": (25, 35, 50),      # Darker inactive tab
                "tab_hover_bg": (35, 50, 70)          # Medium hover tab
            }

            # Store in cache and return
            self._theme_color_cache[cache_key] = result
            return result

    def _lighten_color(self, color, amount):
        """Helper method to lighten a color by a specified amount"""
        r, g, b = color
        return (min(255, r + amount), min(255, g + amount), min(255, b + amount))

    def _darken_color(self, color, amount):
        """Helper method to darken a color by a specified amount"""
        r, g, b = color
        return (max(0, r - amount), max(0, g - amount), max(0, b - amount))

    def hex_to_rgb(self, hex_color):
        """Convert hex color string to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        if len(hex_color) == 3:
            # Convert shorthand (e.g. #ABC) to full form (e.g. #AABBCC)
            hex_color = ''.join([c*2 for c in hex_color])
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def import_language_from_json(self, file_path):
        """
        Import language settings from a JSON file

        Args:
            file_path: Path to the JSON file

        Returns:
            True if successful, False otherwise
        """
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as file:
                    language_data = json.load(file)

                # Validate the language data
                if 'language_name' in language_data and 'translations' in language_data:
                    # Add the language to available languages if not already present
                    available_languages = self.get_language_options()
                    language_name = language_data['language_name']

                    if language_name not in available_languages:
                        available_languages.append(language_name)
                        self.set_setting('language', 'available_languages', available_languages)

                    # Set as current language
                    self.set_setting('language', 'current_language', language_name)

                    # Save the language file to the data directory
                    language_file = os.path.join('data', f"language_{language_name.lower()}.json")
                    with open(language_file, 'w', encoding='utf-8') as outfile:
                        json.dump(language_data, outfile, indent=4, ensure_ascii=False)

                    # Update custom language path
                    self.set_setting('language', 'custom_language_path', language_file)

                    return True
                else:
                    print("Invalid language file format")
                    return False
            else:
                print(f"File not found: {file_path}")
                return False
        except Exception as e:
            print(f"Error importing language: {str(e)}")
            return False

    def apply_display_settings(self, screen):
        """
        Apply display settings to the screen with external display support

        Args:
            screen: Pygame screen surface

        Returns:
            Updated screen surface
        """
        fullscreen = self.get_setting('display', 'fullscreen', False)
        resolution_str = self.get_setting('display', 'resolution', '1280x720')

        try:
            width, height = map(int, resolution_str.split('x'))

            # Try to use external display manager for better compatibility
            try:
                from external_display_manager import get_external_display_manager
                display_manager = get_external_display_manager()

                # Create display with external display support
                screen, actual_width, actual_height = display_manager.create_compatible_display(
                    preferred_width=width,
                    preferred_height=height,
                    fullscreen=fullscreen
                )

                print(f"Applied display settings with external support: {actual_width}x{actual_height}")
                return screen

            except Exception as e:
                print(f"External display manager failed, using fallback: {e}")
                # Fallback to original logic
                if fullscreen:
                    return pygame.display.set_mode((width, height), pygame.FULLSCREEN)
                else:
                    return pygame.display.set_mode((width, height))

        except Exception as e:
            print(f"Error applying display settings: {str(e)}")
            # Return original screen on error
            return screen

    def get_current_screen_mode(self):
        """
        Get the current screen mode setting

        Returns:
            bool: True if fullscreen, False if windowed
        """
        return self.get_setting('display', 'fullscreen', False)

    def set_screen_mode(self, fullscreen):
        """
        Set the screen mode setting and save it

        Args:
            fullscreen (bool): True for fullscreen, False for windowed
        """
        self.set_setting('display', 'fullscreen', fullscreen)
        self.save_settings()
        print(f"Screen mode set to: {'fullscreen' if fullscreen else 'windowed'}")

    def toggle_screen_mode(self):
        """
        Toggle between fullscreen and windowed mode

        Returns:
            bool: New screen mode (True if fullscreen, False if windowed)
        """
        current_mode = self.get_current_screen_mode()
        new_mode = not current_mode
        self.set_screen_mode(new_mode)
        return new_mode

    def get_setting_category_map(self):
        """
        Returns a dictionary mapping each setting key to its category.
        This allows looking up the category for a specific setting key.

        Returns:
            dict: Dictionary mapping setting keys to their categories
        """
        category_map = {}

        # Build the map from default settings
        for category, settings in self.default_settings.items():
            for key in settings.keys():
                category_map[key] = category

        # Also include any additional settings not in defaults
        for category, settings in self.settings.items():
            for key in settings.keys():
                category_map[key] = category

        return category_map
