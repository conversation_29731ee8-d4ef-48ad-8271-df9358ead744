# Stats Overcounting Fix

This document explains the fix for the issue where the stats page overcounts balances and game counts after playing more than 10 games continuously.

## Problem Description

After playing more than 10 games continuously, the stats page shows incorrect (inflated) values for:
- Game count
- Balance/earnings

This issue does not occur:
- In demo mode (games are correctly not counted)
- When playing fewer than 10 games continuously

## Root Cause Analysis

The root cause is a race condition in the database operations combined with multiple recording paths:

1. **Multiple Recording Paths**:
   - Direct database recording via `thread_safe_db.record_game_completed()`
   - Event hooks system via `stats_event_hooks.py`
   - Stats integration via `stats_integration.py`

2. **Race Condition**:
   - In `stats_event_hooks.py`, a game completion event:
     * Gets queued for processing in a background thread
     * ALSO posts a direct pygame event to refresh stats
   - This creates a race condition where the same game can be processed twice

3. **No Duplicate Detection**:
   - There's no mechanism to detect if a game has already been recorded
   - Each recording path operates independently without coordination

4. **Cumulative Probability Effect**:
   - Each game has a small chance of triggering this race condition
   - With fewer than 10 games, the probability is low enough that you might not see it
   - After 10+ games, the probability becomes high enough that it consistently manifests

## The Fix

The `fix_stats_overcounting_targeted.py` script implements a comprehensive solution:

1. **Duplicate Detection**:
   - Adds a `processed_games` table to track which games have been recorded
   - Creates a unique signature for each game based on player, stake, etc.
   - Checks this table before recording a game to prevent duplicates

2. **Single Source of Truth**:
   - Ensures each game is only recorded once, regardless of which path processes it first
   - Records which path processed the game for debugging purposes

3. **Data Correction**:
   - Fixes existing data by recounting games from the `game_history` table
   - Recalculates earnings based on actual fee records

4. **Code Patching**:
   - Modifies `thread_safe_db.py` to add duplicate detection
   - Modifies `stats_event_hooks.py` to add duplicate detection
   - Ensures both paths check for duplicates before processing

## How to Apply the Fix

1. **Backup Your Data**:
   - The script automatically creates backups, but you may want to manually backup your data first

2. **Run the Fix Script**:
   ```
   python fix_stats_overcounting_targeted.py
   ```

3. **Verify the Fix**:
   - Play more than 10 games continuously
   - Check that the stats page shows the correct game count and earnings

## Technical Details

### Duplicate Detection Logic

The fix creates a unique signature for each game using:
```python
signature_str = f"{username}:{stake}:{players}:{total_calls}:{timestamp}"
game_signature = hashlib.md5(signature_str.encode()).hexdigest()
```

This signature is checked against the `processed_games` table before recording a game:
```sql
SELECT COUNT(*) FROM processed_games WHERE game_signature = ?
```

### Database Schema Changes

The fix adds a new table to the database:
```sql
CREATE TABLE IF NOT EXISTS processed_games (
    game_signature TEXT PRIMARY KEY,
    date_time TEXT,
    username TEXT,
    stake REAL,
    players INTEGER,
    total_calls INTEGER,
    processed_time TEXT,
    recording_path TEXT
)
```

## Troubleshooting

If you encounter issues after applying the fix:

1. **Check the logs**:
   - Look for messages about duplicate games being detected
   - Check which recording path is processing games

2. **Restore from backup**:
   - Backups are stored in the `data/backups` directory
   - You can restore the original files if needed

3. **Manual data correction**:
   - You can manually correct the data using SQL:
   ```sql
   UPDATE daily_stats SET games_played = X, earnings = Y WHERE date = 'YYYY-MM-DD'
   ```

## Contact

If you have any questions or need further assistance, please contact the development team.