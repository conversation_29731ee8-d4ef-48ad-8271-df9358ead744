"""
Admin UI Module for the WOW Games application.

This module provides a graphical user interface for admin operations.
"""

import os
import sys
import pygame
import time
import json
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from admin_auth import authenticate_admin, validate_admin_session, end_admin_session, create_default_admin
from stats_db import get_stats_db_manager
from admin_cache import get_admin_cache, get_background_loader

# Import stats export module if available
try:
    from stats_export import export_to_csv, export_to_pdf
    STATS_EXPORT_AVAILABLE = True
    print("Stats export module imported successfully")
except ImportError as e:
    STATS_EXPORT_AVAILABLE = False
    print(f"Stats export module not available: {e}")
    print("Export functionality will be disabled.")

# Set up logging
logging.basicConfig(
    filename=os.path.join('data', 'admin_ui.log'),
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Define colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (50, 50, 50)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
LIGHT_BLUE = (100, 150, 255)
GOLD = (255, 215, 0)
ORANGE = (255, 165, 0)
NAV_BAR_BG = (30, 40, 50)

# Admin UI colors
ADMIN_BG = (25, 25, 35)
ADMIN_PANEL_BG = (35, 35, 45)
ADMIN_HEADER_BG = (45, 45, 60)
ADMIN_BUTTON_BG = (60, 80, 120)
ADMIN_BUTTON_HOVER = (80, 100, 150)
ADMIN_SUCCESS = (50, 180, 50)
ADMIN_ERROR = (180, 50, 50)
ADMIN_WARNING = (180, 150, 50)
ADMIN_INFO = (50, 150, 180)

def show_admin_ui(screen):
    """
    Show the admin UI and handle interactions.

    Args:
        screen: Pygame screen surface

    Returns:
        None
    """
    # Create default admin if needed
    create_default_admin()

    # Create admin UI instance
    admin_ui = AdminUI(screen)

    # Run the admin UI
    admin_ui.run()

class AdminUI:
    """Admin UI class for the WOW Games application."""

    def __init__(self, screen):
        """
        Initialize the admin UI.

        Args:
            screen: Pygame screen surface
        """
        self.screen = screen
        self.running = True
        self.scale_x = screen.get_width() / 1024
        self.scale_y = screen.get_height() / 768

        # Authentication state
        self.authenticated = False
        self.session_token = None
        self.user_data = None

        # UI state
        self.active_section = "login"  # login, dashboard, users, games, wallet, export
        self.active_dialog = None  # edit_user, etc.
        self.message = ""
        self.message_timer = 0
        self.message_type = "info"

        # Input fields
        self.input_fields = {}
        self.active_input = None

        # Initialize font cache
        self._font_cache = {}

        # Initialize UI elements
        self.hit_areas = {}
        self.button_states = {}
        self.init_button_animations()

        # Initialize login form
        self.init_login_form()

        # Load sound effects
        self.load_sound_effects()

        # Initialize caching system
        self.cache = get_admin_cache()
        self.background_loader = get_background_loader()

        # Data loading state
        self.data_loading = {
            "dashboard": False,
            "users": False,
            "games": False,
            "wallet": False
        }

        # Cached data
        self.cached_data = {
            "weekly_stats": [],
            "total_earnings": 0,
            "daily_earnings": 0,
            "daily_games": 0,
            "wallet_balance": 0,
            "admin_users": [],
            "game_history": [],
            "wallet_summary": {
                "total_deposits": 0,
                "total_withdrawals": 0,
                "transaction_count": 0,
                "current_balance": 0
            },
            "wallet_transactions": []
        }

    def init_button_animations(self):
        """Initialize button animation states."""
        # Navigation buttons
        nav_items = ["dashboard", "users", "games", "wallet", "export", "logout"]
        for item in nav_items:
            self.button_states[f"nav_{item}"] = {
                "hover": False,
                "click": False,
                "click_time": 0
            }

        # Action buttons
        action_buttons = ["login", "add_user", "edit_user", "delete_user",
                         "export_csv", "export_pdf", "add_transaction",
                         "save_user", "cancel_edit"]
        for button in action_buttons:
            self.button_states[button] = {
                "hover": False,
                "click": False,
                "click_time": 0
            }

    def init_login_form(self):
        """Initialize the login form."""
        # Create input fields for username and password
        self.input_fields["username"] = {
            "rect": pygame.Rect(0, 0, 0, 0),  # Will be positioned in draw_login_form
            "text": "",
            "placeholder": "Username",
            "password": False
        }

        self.input_fields["password"] = {
            "rect": pygame.Rect(0, 0, 0, 0),  # Will be positioned in draw_login_form
            "text": "",
            "placeholder": "Password",
            "password": True
        }

    def load_sound_effects(self):
        """Load sound effects."""
        try:
            # Button click sound
            self.button_click_sound = pygame.mixer.Sound("assets/audio-effects/button_click.mp3")

            # Success sound
            self.success_sound = pygame.mixer.Sound("assets/audio-effects/success.mp3")

            # Error sound
            self.error_sound = pygame.mixer.Sound("assets/audio-effects/error.mp3")
        except Exception as e:
            logging.error(f"Error loading sound effects: {e}")
            self.button_click_sound = None
            self.success_sound = None
            self.error_sound = None

    def run(self):
        """Run the admin UI loop."""
        clock = pygame.time.Clock()

        while self.running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    self.handle_mouse_down(event.pos)
                elif event.type == pygame.MOUSEMOTION:
                    self.handle_mouse_motion(event.pos)
                elif event.type == pygame.KEYDOWN:
                    self.handle_key_down(event)

            # Process background loading tasks
            self.process_background_tasks()

            # Draw the UI
            self.draw()

            # Update the display
            pygame.display.flip()

            # Cap the frame rate
            clock.tick(60)

            # Update message timer
            if self.message_timer > 0:
                self.message_timer -= 1

    def process_background_tasks(self):
        """Process background loading tasks."""
        # Check if we need to load data for the active section
        if self.authenticated:
            if self.active_section == "dashboard" and not self.data_loading["dashboard"]:
                self.load_dashboard_data()
            elif self.active_section == "users" and not self.data_loading["users"]:
                self.load_users_data()
            elif self.active_section == "games" and not self.data_loading["games"]:
                self.load_games_data()
            elif self.active_section == "wallet" and not self.data_loading["wallet"]:
                self.load_wallet_data()

    def load_dashboard_data(self):
        """Load dashboard data in the background."""
        # Mark as loading
        self.data_loading["dashboard"] = True

        # Check cache first
        cached_weekly_stats = self.cache.get("weekly_stats", max_age=300)  # 5 minutes
        cached_total_earnings = self.cache.get("total_earnings", max_age=300)
        cached_daily_earnings = self.cache.get("daily_earnings", max_age=300)
        cached_daily_games = self.cache.get("daily_games", max_age=300)
        cached_wallet_balance = self.cache.get("wallet_balance", max_age=300)
        cached_recent_games = self.cache.get("recent_games", max_age=300)

        # Use cached data if available
        if cached_weekly_stats:
            self.cached_data["weekly_stats"] = cached_weekly_stats
        if cached_total_earnings is not None:
            self.cached_data["total_earnings"] = cached_total_earnings
        if cached_daily_earnings is not None:
            self.cached_data["daily_earnings"] = cached_daily_earnings
        if cached_daily_games is not None:
            self.cached_data["daily_games"] = cached_daily_games
        if cached_wallet_balance is not None:
            self.cached_data["wallet_balance"] = cached_wallet_balance
        if cached_recent_games:
            self.cached_data["game_history"] = cached_recent_games

        # Queue background loading tasks
        def load_weekly_stats():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_weekly_stats()
            except Exception as e:
                logging.error(f"Error loading weekly stats: {e}")
                return []

        def load_summary_data():
            try:
                stats_db = get_stats_db_manager()

                # Get summary data
                total_earnings = stats_db.get_total_earnings()
                wallet_balance = stats_db.get_wallet_balance()

                # Get today's stats
                today = datetime.now().strftime('%Y-%m-%d')
                daily_stats = stats_db.get_daily_stats(today)
                daily_games = daily_stats['games_played']
                daily_earnings = daily_stats['earnings']

                return {
                    "total_earnings": total_earnings,
                    "wallet_balance": wallet_balance,
                    "daily_games": daily_games,
                    "daily_earnings": daily_earnings
                }
            except Exception as e:
                logging.error(f"Error loading summary data: {e}")
                return {}

        def load_recent_games():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_game_history(limit=5)
            except Exception as e:
                logging.error(f"Error loading recent games: {e}")
                return []

        # Queue tasks with callbacks
        def weekly_stats_callback(result, error):
            if not error and result:
                self.cached_data["weekly_stats"] = result
                self.cache.set("weekly_stats", result)

        def summary_data_callback(result, error):
            if not error and result:
                for key, value in result.items():
                    self.cached_data[key] = value
                    self.cache.set(key, value)

        def recent_games_callback(result, error):
            if not error and result:
                self.cached_data["game_history"] = result
                self.cache.set("recent_games", result)

        # Queue tasks
        self.background_loader.queue_task("weekly_stats", load_weekly_stats, weekly_stats_callback)
        self.background_loader.queue_task("summary_data", load_summary_data, summary_data_callback)
        self.background_loader.queue_task("recent_games", load_recent_games, recent_games_callback)

    def load_users_data(self):
        """Load users data in the background."""
        # Mark as loading
        self.data_loading["users"] = True

        # Check cache first
        cached_admin_users = self.cache.get("admin_users", max_age=300)  # 5 minutes

        # Use cached data if available
        if cached_admin_users:
            self.cached_data["admin_users"] = cached_admin_users

        # Queue background loading task
        def load_admin_users():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_admin_users()
            except Exception as e:
                logging.error(f"Error loading admin users: {e}")
                return []

        # Queue task with callback
        def admin_users_callback(result, error):
            if not error and result:
                self.cached_data["admin_users"] = result
                self.cache.set("admin_users", result)

        # Queue task
        self.background_loader.queue_task("admin_users", load_admin_users, admin_users_callback)

    def load_games_data(self):
        """Load games data in the background."""
        # Mark as loading
        self.data_loading["games"] = True

        # Check cache first
        cached_game_history = self.cache.get("game_history_full", max_age=300)  # 5 minutes

        # Use cached data if available
        if cached_game_history:
            self.cached_data["game_history"] = cached_game_history

        # Queue background loading task
        def load_game_history():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_game_history(limit=20)
            except Exception as e:
                logging.error(f"Error loading game history: {e}")
                return []

        # Queue task with callback
        def game_history_callback(result, error):
            if not error and result:
                self.cached_data["game_history"] = result
                self.cache.set("game_history_full", result)

        # Queue task
        self.background_loader.queue_task("game_history", load_game_history, game_history_callback)

    def load_wallet_data(self):
        """Load wallet data in the background."""
        # Mark as loading
        self.data_loading["wallet"] = True

        # Check cache first
        cached_wallet_summary = self.cache.get("wallet_summary", max_age=300)  # 5 minutes
        cached_wallet_transactions = self.cache.get("wallet_transactions", max_age=300)

        # Use cached data if available
        if cached_wallet_summary:
            self.cached_data["wallet_summary"] = cached_wallet_summary
        if cached_wallet_transactions:
            self.cached_data["wallet_transactions"] = cached_wallet_transactions

        # Queue background loading tasks
        def load_wallet_summary():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_wallet_summary()
            except Exception as e:
                logging.error(f"Error loading wallet summary: {e}")
                return {
                    "total_deposits": 0,
                    "total_withdrawals": 0,
                    "transaction_count": 0,
                    "current_balance": 0
                }

        def load_wallet_transactions():
            try:
                stats_db = get_stats_db_manager()
                return stats_db.get_wallet_transactions(limit=10)
            except Exception as e:
                logging.error(f"Error loading wallet transactions: {e}")
                return []

        # Queue tasks with callbacks
        def wallet_summary_callback(result, error):
            if not error and result:
                self.cached_data["wallet_summary"] = result
                self.cache.set("wallet_summary", result)

        def wallet_transactions_callback(result, error):
            if not error and result:
                self.cached_data["wallet_transactions"] = result
                self.cache.set("wallet_transactions", result)

        # Queue tasks
        self.background_loader.queue_task("wallet_summary", load_wallet_summary, wallet_summary_callback)
        self.background_loader.queue_task("wallet_transactions", load_wallet_transactions, wallet_transactions_callback)

    def draw(self):
        """Draw the admin UI."""
        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Draw background
        self.screen.fill(ADMIN_BG)

        # Draw appropriate section based on authentication state
        if not self.authenticated:
            self.draw_login_form(screen_width, screen_height)
        else:
            # Draw navigation bar
            self.draw_navigation_bar(screen_width)

            # Draw active section
            if self.active_section == "dashboard":
                self.draw_dashboard(screen_width, screen_height)
            elif self.active_section == "users":
                self.draw_users_section(screen_width, screen_height)
            elif self.active_section == "games":
                self.draw_games_section(screen_width, screen_height)
            elif self.active_section == "wallet":
                self.draw_wallet_section(screen_width, screen_height)
            elif self.active_section == "export":
                self.draw_export_section(screen_width, screen_height)

        # Draw active dialog if any
        if hasattr(self, 'active_dialog') and self.active_dialog:
            if self.active_dialog == "edit_user":
                self.draw_edit_user_dialog()
            elif self.active_dialog == "game_details":
                self.draw_game_details_dialog()

        # Draw message if active
        if self.message_timer > 0:
            self.draw_message(screen_width)

    def draw_login_form(self, screen_width, screen_height):
        """
        Draw the login form.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Calculate form dimensions
        form_width = int(400 * self.scale_x)
        form_height = int(300 * self.scale_y)
        form_x = (screen_width - form_width) // 2
        form_y = (screen_height - form_height) // 2

        # Draw form background
        form_rect = pygame.Rect(form_x, form_y, form_width, form_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, form_rect, border_radius=10)

        # Draw form header
        header_height = int(50 * self.scale_y)
        header_rect = pygame.Rect(form_x, form_y, form_width, header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=10)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG,
                       pygame.Rect(form_x, form_y + header_height - 10, form_width, 10))

        # Draw header text
        header_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        header_text = header_font.render("Admin Login", True, WHITE)
        header_text_rect = header_text.get_rect(center=(form_x + form_width // 2, form_y + header_height // 2))
        self.screen.blit(header_text, header_text_rect)

        # Draw input fields
        input_width = int(300 * self.scale_x)
        input_height = int(40 * self.scale_y)
        input_x = form_x + (form_width - input_width) // 2

        # Username field
        username_y = form_y + header_height + int(40 * self.scale_y)
        username_rect = pygame.Rect(input_x, username_y, input_width, input_height)
        self.input_fields["username"]["rect"] = username_rect

        # Draw username field
        self.draw_input_field(
            self.input_fields["username"],
            is_active=self.active_input == "username"
        )

        # Password field
        password_y = username_y + input_height + int(20 * self.scale_y)
        password_rect = pygame.Rect(input_x, password_y, input_width, input_height)
        self.input_fields["password"]["rect"] = password_rect

        # Draw password field
        self.draw_input_field(
            self.input_fields["password"],
            is_active=self.active_input == "password"
        )

        # Draw login button
        button_width = int(150 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_x = form_x + (form_width - button_width) // 2
        button_y = password_y + input_height + int(30 * self.scale_y)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        button_color = ADMIN_BUTTON_HOVER if self.button_states["login"]["hover"] else ADMIN_BUTTON_BG

        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=5)

        # Draw button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Login", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Store hit area for login button
        self.hit_areas["login"] = button_rect

    def draw_input_field(self, field, is_active=False):
        """
        Draw an input field.

        Args:
            field: Input field data
            is_active: Whether the field is active
        """
        # Draw field background
        border_color = LIGHT_BLUE if is_active else GRAY
        pygame.draw.rect(self.screen, WHITE, field["rect"], border_radius=5)
        pygame.draw.rect(self.screen, border_color, field["rect"], border_radius=5, width=2)

        # Draw field text or placeholder
        text_font = self.get_font("Arial", self.scaled_font_size(16))

        if field["text"]:
            # Show asterisks for password fields
            display_text = "*" * len(field["text"]) if field["password"] else field["text"]
            text_color = BLACK
        else:
            display_text = field["placeholder"]
            text_color = GRAY

        text_surface = text_font.render(display_text, True, text_color)
        text_rect = text_surface.get_rect(
            midleft=(field["rect"].left + 10, field["rect"].centery)
        )
        self.screen.blit(text_surface, text_rect)

    def draw_navigation_bar(self, screen_width):
        """
        Draw the navigation bar.

        Args:
            screen_width: Screen width
        """
        # Navigation bar dimensions
        nav_height = int(50 * self.scale_y)

        # Draw navigation bar background
        nav_rect = pygame.Rect(0, 0, screen_width, nav_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, nav_rect)

        # Draw navigation items
        nav_items = [
            {"id": "dashboard", "text": "Dashboard", "icon": "📊"},
            {"id": "users", "text": "Users", "icon": "👥"},
            {"id": "games", "text": "Games", "icon": "🎮"},
            {"id": "wallet", "text": "Wallet", "icon": "💰"},
            {"id": "export", "text": "Export", "icon": "📤"},
            {"id": "logout", "text": "Logout", "icon": "🚪"}
        ]

        # Calculate item width
        item_width = int(screen_width / len(nav_items))

        # Draw each item
        for i, item in enumerate(nav_items):
            item_x = i * item_width
            item_rect = pygame.Rect(item_x, 0, item_width, nav_height)

            # Highlight active section
            if item["id"] == self.active_section:
                pygame.draw.rect(self.screen, ADMIN_BUTTON_BG, item_rect)

            # Highlight on hover
            elif self.button_states[f"nav_{item['id']}"]["hover"]:
                pygame.draw.rect(self.screen, ADMIN_BUTTON_HOVER, item_rect)

            # Draw item text
            text_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
            text = f"{item['icon']} {item['text']}"
            text_surface = text_font.render(text, True, WHITE)
            text_rect = text_surface.get_rect(center=item_rect.center)
            self.screen.blit(text_surface, text_rect)

            # Store hit area
            self.hit_areas[f"nav_{item['id']}"] = item_rect

    def draw_dashboard(self, screen_width, screen_height):
        """
        Draw the dashboard section.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)
        content_y = nav_height + int(20 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Dashboard", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=content_y
        )
        self.screen.blit(title_text, title_rect)

        try:
            # Use cached data
            total_earnings = self.cached_data["total_earnings"]
            wallet_balance = self.cached_data["wallet_balance"]
            daily_games = self.cached_data["daily_games"]
            daily_earnings = self.cached_data["daily_earnings"]
            weekly_stats = self.cached_data["weekly_stats"]
            recent_games = self.cached_data["game_history"]

            # Draw summary cards
            self.draw_dashboard_summary(
                screen_width, content_y + title_rect.height + int(20 * self.scale_y),
                total_earnings, daily_earnings, daily_games, wallet_balance
            )

            # Draw weekly chart
            chart_y = content_y + title_rect.height + int(150 * self.scale_y)
            self.draw_weekly_chart(screen_width, chart_y, weekly_stats)

            # Draw recent games table
            table_y = chart_y + int(200 * self.scale_y)
            self.draw_recent_games_table(screen_width, table_y, recent_games)

            # Draw loading indicator if data is being loaded
            if self.data_loading["dashboard"]:
                self.draw_loading_indicator(screen_width, screen_height)

        except Exception as e:
            logging.error(f"Error drawing dashboard: {e}")

            # Draw error message
            error_font = self.get_font("Arial", self.scaled_font_size(16))
            error_text = error_font.render(f"Error loading dashboard data: {str(e)}", True, RED)
            error_rect = error_text.get_rect(
                center=(screen_width // 2, content_y + int(100 * self.scale_y))
            )
            self.screen.blit(error_text, error_rect)

    def draw_dashboard_summary(self, screen_width, y, total_earnings, daily_earnings, daily_games, wallet_balance):
        """
        Draw dashboard summary cards.

        Args:
            screen_width: Screen width
            y: Y position
            total_earnings: Total earnings
            daily_earnings: Daily earnings
            daily_games: Daily games played
            wallet_balance: Wallet balance
        """
        # Card dimensions
        card_width = int((screen_width - int(100 * self.scale_x)) / 4)
        card_height = int(100 * self.scale_y)
        card_spacing = int(20 * self.scale_x)

        # Card data
        cards = [
            {
                "title": "Total Earnings",
                "value": f"{total_earnings:.2f} ETB",
                "color": (100, 200, 100)
            },
            {
                "title": "Today's Earnings",
                "value": f"{daily_earnings:.2f} ETB",
                "color": (100, 150, 200)
            },
            {
                "title": "Today's Games",
                "value": str(daily_games),
                "color": (200, 150, 100)
            },
            {
                "title": "Wallet Balance",
                "value": f"{wallet_balance:.2f} ETB",
                "color": (150, 100, 200)
            }
        ]

        # Draw each card
        for i, card in enumerate(cards):
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            self.draw_summary_card(card_x, y, card_width, card_height, card["title"], card["value"], card["color"])

    def draw_summary_card(self, x, y, width, height, title, value, color):
        """
        Draw a summary card.

        Args:
            x: X position
            y: Y position
            width: Card width
            height: Card height
            title: Card title
            value: Card value
            color: Card color
        """
        # Draw card background
        card_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, card_rect, border_radius=10)

        # Draw card header
        header_height = int(30 * self.scale_y)
        header_rect = pygame.Rect(x, y, width, header_height)
        pygame.draw.rect(self.screen, color, header_rect, border_radius=10)
        pygame.draw.rect(self.screen, color,
                       pygame.Rect(x, y + header_height - 10, width, 10))

        # Draw title
        title_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        title_text = title_font.render(title, True, WHITE)
        title_rect = title_text.get_rect(
            centerx=x + width // 2,
            centery=y + header_height // 2
        )
        self.screen.blit(title_text, title_rect)

        # Draw value
        value_font = self.get_font("Arial", self.scaled_font_size(20), bold=True)
        value_text = value_font.render(value, True, WHITE)
        value_rect = value_text.get_rect(
            centerx=x + width // 2,
            centery=y + header_height + (height - header_height) // 2
        )
        self.screen.blit(value_text, value_rect)

    def draw_weekly_chart(self, screen_width, y, weekly_stats):
        """
        Draw weekly earnings chart.

        Args:
            screen_width: Screen width
            y: Y position
            weekly_stats: Weekly statistics
        """
        # Chart dimensions
        chart_width = screen_width - int(40 * self.scale_x)
        chart_height = int(180 * self.scale_y)
        chart_x = int(20 * self.scale_x)

        # Draw chart background
        chart_rect = pygame.Rect(chart_x, y, chart_width, chart_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, chart_rect, border_radius=10)

        # Draw chart title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Weekly Earnings", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=chart_x + chart_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Calculate chart area
        chart_area_x = chart_x + int(50 * self.scale_x)
        chart_area_y = y + title_rect.height + int(20 * self.scale_y)
        chart_area_width = chart_width - int(70 * self.scale_x)
        chart_area_height = chart_height - title_rect.height - int(50 * self.scale_y)

        # Draw chart area background
        chart_area_rect = pygame.Rect(chart_area_x, chart_area_y, chart_area_width, chart_area_height)
        pygame.draw.rect(self.screen, DARK_GRAY, chart_area_rect)

        # Draw grid lines
        grid_color = GRAY
        for i in range(5):
            # Horizontal grid lines
            y_pos = chart_area_y + i * chart_area_height // 4
            pygame.draw.line(
                self.screen, grid_color,
                (chart_area_x, y_pos),
                (chart_area_x + chart_area_width, y_pos),
                1
            )

        # Get max earnings for scaling
        max_earnings = max([day['earnings'] for day in weekly_stats]) if weekly_stats else 1000
        max_earnings = max(max_earnings, 1)  # Avoid division by zero

        # Day names
        day_names = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

        # Bar width
        bar_width = chart_area_width // 7 - int(10 * self.scale_x)

        # Draw bars
        for i, day in enumerate(weekly_stats):
            # Calculate bar height
            earnings = day['earnings']
            bar_height = int((earnings / max_earnings) * chart_area_height)

            # Calculate bar position
            bar_x = chart_area_x + i * (chart_area_width // 7) + int(5 * self.scale_x)
            bar_y = chart_area_y + chart_area_height - bar_height

            # Draw bar
            bar_color = (100, 150, 250)
            bar_rect = pygame.Rect(bar_x, bar_y, bar_width, bar_height)
            pygame.draw.rect(self.screen, bar_color, bar_rect, border_radius=5)

            # Draw day name
            day_font = self.get_font("Arial", self.scaled_font_size(12))
            day_text = day_font.render(day_names[i], True, WHITE)
            day_rect = day_text.get_rect(
                centerx=bar_x + bar_width // 2,
                y=chart_area_y + chart_area_height + int(5 * self.scale_y)
            )
            self.screen.blit(day_text, day_rect)

            # Draw earnings value
            value_font = self.get_font("Arial", self.scaled_font_size(10))
            value_text = value_font.render(f"{earnings:.0f}", True, WHITE)
            value_rect = value_text.get_rect(
                centerx=bar_x + bar_width // 2,
                bottom=bar_y - int(5 * self.scale_y)
            )
            self.screen.blit(value_text, value_rect)

    def draw_recent_games_table(self, screen_width, y, games):
        """
        Draw recent games table.

        Args:
            screen_width: Screen width
            y: Y position
            games: Recent games data
        """
        # Table dimensions
        table_width = screen_width - int(40 * self.scale_x)
        table_height = int(200 * self.scale_y)
        table_x = int(20 * self.scale_x)

        # Draw table background
        table_rect = pygame.Rect(table_x, y, table_width, table_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, table_rect, border_radius=10)

        # Draw table title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Recent Games", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=table_x + table_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Define columns
        columns = [
            {"id": "id", "name": "ID", "width": 0.05},
            {"id": "date_time", "name": "Date/Time", "width": 0.15},
            {"id": "username", "name": "Winner", "width": 0.15},
            {"id": "players", "name": "Players", "width": 0.08},
            {"id": "total_calls", "name": "Calls", "width": 0.08},
            {"id": "fee", "name": "Fee", "width": 0.08},
            {"id": "tips", "name": "Tips", "width": 0.08},
            {"id": "total_prize", "name": "Prize", "width": 0.13},
            {"id": "status", "name": "Status", "width": 0.1}
        ]

        # Calculate table content area
        content_y = y + title_rect.height + int(20 * self.scale_y)
        content_height = table_height - title_rect.height - int(30 * self.scale_y)

        # Draw table header
        header_height = int(30 * self.scale_y)
        header_rect = pygame.Rect(table_x + int(10 * self.scale_x), content_y,
                                table_width - int(20 * self.scale_x), header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=5)

        # Draw column headers
        header_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        x_offset = table_x + int(20 * self.scale_x)

        for column in columns:
            col_width = int(table_width * column["width"])
            header_text = header_font.render(column["name"], True, WHITE)
            header_rect = header_text.get_rect(
                midleft=(x_offset, content_y + header_height // 2)
            )
            self.screen.blit(header_text, header_rect)
            x_offset += col_width

        # Draw table rows
        row_height = int(30 * self.scale_y)
        row_font = self.get_font("Arial", self.scaled_font_size(12))

        if games:
            for i, game in enumerate(games):
                row_y = content_y + header_height + i * row_height

                # Draw row background (alternating colors)
                row_color = DARK_GRAY if i % 2 == 0 else ADMIN_PANEL_BG
                row_rect = pygame.Rect(
                    table_x + int(10 * self.scale_x),
                    row_y,
                    table_width - int(20 * self.scale_x),
                    row_height
                )
                pygame.draw.rect(self.screen, row_color, row_rect)

                # Draw row data
                x_offset = table_x + int(20 * self.scale_x)

                for column in columns:
                    col_width = int(table_width * column["width"])
                    col_id = column["id"]

                    # Get value for this column
                    value = str(game.get(col_id, ""))

                    # Format specific columns
                    if col_id == "fee" or col_id == "total_prize" or col_id == "tips":
                        value = f"{float(value):.2f} ETB"
                    elif col_id == "date_time":
                        # Shorten date/time format
                        try:
                            dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                            value = dt.strftime('%m/%d %H:%M')
                        except:
                            pass

                    # Truncate long values
                    if len(value) > 15:
                        value = value[:12] + "..."

                    # Draw cell text
                    text_color = GREEN if col_id == "status" and value.lower() == "won" else WHITE
                    cell_text = row_font.render(value, True, text_color)
                    cell_rect = cell_text.get_rect(
                        midleft=(x_offset, row_y + row_height // 2)
                    )
                    self.screen.blit(cell_text, cell_rect)

                    x_offset += col_width
        else:
            # Draw "No data" message
            no_data_font = self.get_font("Arial", self.scaled_font_size(14))
            no_data_text = no_data_font.render("No recent games found", True, LIGHT_GRAY)
            no_data_rect = no_data_text.get_rect(
                center=(table_x + table_width // 2, content_y + header_height + int(50 * self.scale_y))
            )
            self.screen.blit(no_data_text, no_data_rect)

    def draw_loading_indicator(self, screen_width, screen_height):
        """
        Draw a loading indicator.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Calculate animation time
        anim_time = time.time() % 2  # 2-second cycle

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 64))  # Semi-transparent black
        self.screen.blit(overlay, (0, 0))

        # Draw loading text
        font = self.get_font("Arial", self.scaled_font_size(16), bold=True)

        # Determine loading text with animated dots
        dots = "." * (int(anim_time * 3) % 4)
        text = f"Loading{dots}"

        text_surface = font.render(text, True, WHITE)
        text_rect = text_surface.get_rect(center=(screen_width // 2, screen_height - int(50 * self.scale_y)))

        # Draw text with glow effect
        glow_size = int(4 * self.scale_x)
        for dx in range(-glow_size, glow_size + 1, 2):
            for dy in range(-glow_size, glow_size + 1, 2):
                if dx*dx + dy*dy <= glow_size*glow_size:
                    glow_rect = text_rect.copy()
                    glow_rect.x += dx
                    glow_rect.y += dy
                    self.screen.blit(text_surface, glow_rect)

        # Draw final text
        self.screen.blit(text_surface, text_rect)

    def draw_users_section(self, screen_width, screen_height):
        """
        Draw the users section.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)
        content_y = nav_height + int(20 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Users Management", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=content_y
        )
        self.screen.blit(title_text, title_rect)

        try:
            # Use cached data
            admin_users = self.cached_data["admin_users"]

            # Draw users table
            table_y = content_y + title_rect.height + int(20 * self.scale_y)
            self.draw_users_table(screen_width, table_y, admin_users)

            # Draw add user form
            form_y = table_y + int(300 * self.scale_y)
            self.draw_add_user_form(screen_width, form_y)

            # Draw loading indicator if data is being loaded
            if self.data_loading["users"]:
                self.draw_loading_indicator(screen_width, screen_height)

        except Exception as e:
            logging.error(f"Error drawing users section: {e}")

            # Draw error message
            error_font = self.get_font("Arial", self.scaled_font_size(16))
            error_text = error_font.render(f"Error loading users data: {str(e)}", True, RED)
            error_rect = error_text.get_rect(
                center=(screen_width // 2, content_y + int(100 * self.scale_y))
            )
            self.screen.blit(error_text, error_rect)

    def draw_users_table(self, screen_width, y, users):
        """
        Draw users table.

        Args:
            screen_width: Screen width
            y: Y position
            users: Admin users data
        """
        # Table dimensions
        table_width = screen_width - int(40 * self.scale_x)
        table_height = int(250 * self.scale_y)
        table_x = int(20 * self.scale_x)

        # Draw table background
        table_rect = pygame.Rect(table_x, y, table_width, table_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, table_rect, border_radius=10)

        # Draw table title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Admin Users", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=table_x + table_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Define columns
        columns = [
            {"id": "id", "name": "ID", "width": 0.1},
            {"id": "username", "name": "Username", "width": 0.3},
            {"id": "access_level", "name": "Access Level", "width": 0.2},
            {"id": "last_login", "name": "Last Login", "width": 0.3},
            {"id": "actions", "name": "Actions", "width": 0.1}
        ]

        # Calculate table content area
        content_y = y + title_rect.height + int(20 * self.scale_y)
        content_height = table_height - title_rect.height - int(30 * self.scale_y)

        # Draw table header
        header_height = int(30 * self.scale_y)
        header_rect = pygame.Rect(table_x + int(10 * self.scale_x), content_y,
                                table_width - int(20 * self.scale_x), header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=5)

        # Draw column headers
        header_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        x_offset = table_x + int(20 * self.scale_x)

        for column in columns:
            col_width = int(table_width * column["width"])
            header_text = header_font.render(column["name"], True, WHITE)
            header_rect = header_text.get_rect(
                midleft=(x_offset, content_y + header_height // 2)
            )
            self.screen.blit(header_text, header_rect)
            x_offset += col_width

        # Draw table rows
        row_height = int(40 * self.scale_y)
        row_font = self.get_font("Arial", self.scaled_font_size(12))

        if users:
            for i, user in enumerate(users):
                row_y = content_y + header_height + i * row_height

                # Draw row background (alternating colors)
                row_color = DARK_GRAY if i % 2 == 0 else ADMIN_PANEL_BG
                row_rect = pygame.Rect(
                    table_x + int(10 * self.scale_x),
                    row_y,
                    table_width - int(20 * self.scale_x),
                    row_height
                )
                pygame.draw.rect(self.screen, row_color, row_rect)

                # Draw row data
                x_offset = table_x + int(20 * self.scale_x)

                for column in columns:
                    col_width = int(table_width * column["width"])
                    col_id = column["id"]

                    if col_id == "actions":
                        # Draw action buttons
                        button_width = int(30 * self.scale_x)
                        button_height = int(30 * self.scale_y)
                        button_y = row_y + (row_height - button_height) // 2

                        # Edit button
                        edit_button_x = x_offset
                        edit_button_rect = pygame.Rect(edit_button_x, button_y, button_width, button_height)
                        pygame.draw.rect(self.screen, ADMIN_BUTTON_BG, edit_button_rect, border_radius=5)

                        # Draw edit icon
                        edit_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
                        edit_text = edit_font.render("✏️", True, WHITE)
                        edit_rect = edit_text.get_rect(center=edit_button_rect.center)
                        self.screen.blit(edit_text, edit_rect)

                        # Store hit area for edit button
                        self.hit_areas[f"edit_user_{user['id']}"] = edit_button_rect

                        # Delete button
                        delete_button_x = edit_button_x + button_width + int(5 * self.scale_x)
                        delete_button_rect = pygame.Rect(delete_button_x, button_y, button_width, button_height)
                        pygame.draw.rect(self.screen, ADMIN_ERROR, delete_button_rect, border_radius=5)

                        # Draw delete icon
                        delete_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
                        delete_text = delete_font.render("❌", True, WHITE)
                        delete_rect = delete_text.get_rect(center=delete_button_rect.center)
                        self.screen.blit(delete_text, delete_rect)

                        # Store hit area for delete button
                        self.hit_areas[f"delete_user_{user['id']}"] = delete_button_rect
                    else:
                        # Get value for this column
                        value = str(user.get(col_id, ""))

                        # Format specific columns
                        if col_id == "access_level":
                            # Convert access level to text
                            access_levels = {
                                1: "Read Only",
                                2: "Read/Write",
                                3: "Admin"
                            }
                            value = access_levels.get(user.get(col_id, 1), "Unknown")
                        elif col_id == "last_login":
                            # Format date/time
                            try:
                                dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                                value = dt.strftime('%Y-%m-%d %H:%M')
                            except:
                                pass

                        # Truncate long values
                        if len(value) > 25:
                            value = value[:22] + "..."

                        # Draw cell text
                        cell_text = row_font.render(value, True, WHITE)
                        cell_rect = cell_text.get_rect(
                            midleft=(x_offset, row_y + row_height // 2)
                        )
                        self.screen.blit(cell_text, cell_rect)

                    x_offset += col_width
        else:
            # Draw "No data" message
            no_data_font = self.get_font("Arial", self.scaled_font_size(14))
            no_data_text = no_data_font.render("No admin users found", True, LIGHT_GRAY)
            no_data_rect = no_data_text.get_rect(
                center=(table_x + table_width // 2, content_y + header_height + int(50 * self.scale_y))
            )
            self.screen.blit(no_data_text, no_data_rect)

    def draw_add_user_form(self, screen_width, y):
        """
        Draw add user form.

        Args:
            screen_width: Screen width
            y: Y position
        """
        # Form dimensions
        form_width = screen_width - int(40 * self.scale_x)
        form_height = int(200 * self.scale_y)
        form_x = int(20 * self.scale_x)

        # Draw form background
        form_rect = pygame.Rect(form_x, y, form_width, form_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, form_rect, border_radius=10)

        # Draw form title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Add New Admin User", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=form_x + form_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Initialize input fields if they don't exist
        if "new_username" not in self.input_fields:
            self.input_fields["new_username"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Username",
                "password": False
            }

        if "new_password" not in self.input_fields:
            self.input_fields["new_password"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Password",
                "password": True
            }

        if "new_access_level" not in self.input_fields:
            self.input_fields["new_access_level"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "1",  # Default to read-only
                "placeholder": "Access Level (1-3)",
                "password": False
            }

        # Calculate input field dimensions
        field_width = int(200 * self.scale_x)
        field_height = int(40 * self.scale_y)
        field_y = y + title_rect.height + int(20 * self.scale_y)

        # Username field
        username_x = form_x + int(20 * self.scale_x)
        username_rect = pygame.Rect(username_x, field_y, field_width, field_height)
        self.input_fields["new_username"]["rect"] = username_rect

        # Draw username field
        self.draw_input_field(
            self.input_fields["new_username"],
            is_active=self.active_input == "new_username"
        )

        # Draw username label
        label_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        username_label = label_font.render("Username:", True, WHITE)
        username_label_rect = username_label.get_rect(
            midright=(username_x - int(10 * self.scale_x), field_y + field_height // 2)
        )
        self.screen.blit(username_label, username_label_rect)

        # Password field
        password_x = username_x + field_width + int(100 * self.scale_x)
        password_rect = pygame.Rect(password_x, field_y, field_width, field_height)
        self.input_fields["new_password"]["rect"] = password_rect

        # Draw password field
        self.draw_input_field(
            self.input_fields["new_password"],
            is_active=self.active_input == "new_password"
        )

        # Draw password label
        password_label = label_font.render("Password:", True, WHITE)
        password_label_rect = password_label.get_rect(
            midright=(password_x - int(10 * self.scale_x), field_y + field_height // 2)
        )
        self.screen.blit(password_label, password_label_rect)

        # Access level field
        access_x = form_x + int(20 * self.scale_x)
        access_y = field_y + field_height + int(20 * self.scale_y)
        access_rect = pygame.Rect(access_x, access_y, field_width, field_height)
        self.input_fields["new_access_level"]["rect"] = access_rect

        # Draw access level field
        self.draw_input_field(
            self.input_fields["new_access_level"],
            is_active=self.active_input == "new_access_level"
        )

        # Draw access level label
        access_label = label_font.render("Access Level:", True, WHITE)
        access_label_rect = access_label.get_rect(
            midright=(access_x - int(10 * self.scale_x), access_y + field_height // 2)
        )
        self.screen.blit(access_label, access_label_rect)

        # Draw access level help text
        help_font = self.get_font("Arial", self.scaled_font_size(12))
        help_text = help_font.render("1 = Read Only, 2 = Read/Write, 3 = Admin", True, LIGHT_GRAY)
        help_rect = help_text.get_rect(
            midleft=(access_x + field_width + int(20 * self.scale_x), access_y + field_height // 2)
        )
        self.screen.blit(help_text, help_rect)

        # Add user button
        button_width = int(150 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_x = form_x + form_width - button_width - int(20 * self.scale_x)
        button_y = access_y + field_height + int(20 * self.scale_y)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        button_color = ADMIN_BUTTON_HOVER if self.button_states["add_user"]["hover"] else ADMIN_BUTTON_BG

        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=5)

        # Draw button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Add User", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Store hit area for add user button
        self.hit_areas["add_user"] = button_rect

    def draw_games_section(self, screen_width, screen_height):
        """
        Draw the games section.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)
        content_y = nav_height + int(20 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Games History", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=content_y
        )
        self.screen.blit(title_text, title_rect)

        try:
            # Use cached data
            games = self.cached_data["game_history"]

            # Draw games table
            table_y = content_y + title_rect.height + int(20 * self.scale_y)
            self.draw_games_table(screen_width, table_y, games)

            # Draw loading indicator if data is being loaded
            if self.data_loading["games"]:
                self.draw_loading_indicator(screen_width, screen_height)

        except Exception as e:
            logging.error(f"Error drawing games section: {e}")

            # Draw error message
            error_font = self.get_font("Arial", self.scaled_font_size(16))
            error_text = error_font.render(f"Error loading games data: {str(e)}", True, RED)
            error_rect = error_text.get_rect(
                center=(screen_width // 2, content_y + int(100 * self.scale_y))
            )
            self.screen.blit(error_text, error_rect)

    def draw_games_table(self, screen_width, y, games):
        """
        Draw games table.

        Args:
            screen_width: Screen width
            y: Y position
            games: Games data
        """
        # Table dimensions
        table_width = screen_width - int(40 * self.scale_x)
        table_height = int(600 * self.scale_y)
        table_x = int(20 * self.scale_x)

        # Draw table background
        table_rect = pygame.Rect(table_x, y, table_width, table_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, table_rect, border_radius=10)

        # Draw table title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Game History", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=table_x + table_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Define columns
        columns = [
            {"id": "id", "name": "ID", "width": 0.05},
            {"id": "date_time", "name": "Date/Time", "width": 0.15},
            {"id": "username", "name": "Winner", "width": 0.15},
            {"id": "house", "name": "House", "width": 0.1},
            {"id": "stake", "name": "Stake", "width": 0.08},
            {"id": "players", "name": "Players", "width": 0.05},
            {"id": "total_calls", "name": "Calls", "width": 0.05},
            {"id": "commission_percent", "name": "Comm %", "width": 0.05},
            {"id": "fee", "name": "Fee", "width": 0.08},
            {"id": "tips", "name": "Tips", "width": 0.08},
            {"id": "total_prize", "name": "Prize", "width": 0.08},
            {"id": "status", "name": "Status", "width": 0.08}
        ]

        # Calculate table content area
        content_y = y + title_rect.height + int(20 * self.scale_y)
        content_height = table_height - title_rect.height - int(30 * self.scale_y)

        # Draw table header
        header_height = int(30 * self.scale_y)
        header_rect = pygame.Rect(table_x + int(10 * self.scale_x), content_y,
                                table_width - int(20 * self.scale_x), header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=5)

        # Draw column headers
        header_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        x_offset = table_x + int(20 * self.scale_x)

        for column in columns:
            col_width = int(table_width * column["width"])
            header_text = header_font.render(column["name"], True, WHITE)
            header_rect = header_text.get_rect(
                midleft=(x_offset, content_y + header_height // 2)
            )
            self.screen.blit(header_text, header_rect)
            x_offset += col_width

        # Draw table rows
        row_height = int(30 * self.scale_y)
        row_font = self.get_font("Arial", self.scaled_font_size(12))

        if games:
            for i, game in enumerate(games):
                row_y = content_y + header_height + i * row_height

                # Draw row background (alternating colors)
                row_color = DARK_GRAY if i % 2 == 0 else ADMIN_PANEL_BG
                row_rect = pygame.Rect(
                    table_x + int(10 * self.scale_x),
                    row_y,
                    table_width - int(20 * self.scale_x),
                    row_height
                )
                pygame.draw.rect(self.screen, row_color, row_rect)

                # Draw row data
                x_offset = table_x + int(20 * self.scale_x)

                for column in columns:
                    col_width = int(table_width * column["width"])
                    col_id = column["id"]

                    # Get value for this column
                    value = str(game.get(col_id, ""))

                    # Format specific columns
                    if col_id == "fee" or col_id == "total_prize" or col_id == "stake" or col_id == "tips":
                        value = f"{float(value):.2f} ETB"
                        text_color = WHITE
                    elif col_id == "commission_percent":
                        value = f"{float(value):.0f}%"
                        text_color = WHITE
                    elif col_id == "date_time":
                        # Shorten date/time format
                        try:
                            dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                            value = dt.strftime('%m/%d %H:%M')
                        except:
                            pass
                        text_color = WHITE
                    elif col_id == "status":
                        text_color = GREEN if value.lower() == "won" else WHITE
                    else:
                        text_color = WHITE

                    # Truncate long values
                    if len(value) > 15:
                        value = value[:12] + "..."

                    # Draw cell text
                    cell_text = row_font.render(value, True, text_color)
                    cell_rect = cell_text.get_rect(
                        midleft=(x_offset, row_y + row_height // 2)
                    )
                    self.screen.blit(cell_text, cell_rect)

                    x_offset += col_width

                # Add view details button
                button_width = int(20 * self.scale_x)
                button_height = int(20 * self.scale_y)
                button_x = table_x + table_width - button_width - int(20 * self.scale_x)
                button_y = row_y + (row_height - button_height) // 2

                button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
                pygame.draw.rect(self.screen, ADMIN_BUTTON_BG, button_rect, border_radius=5)

                # Draw button icon
                button_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)
                button_text = button_font.render("👁️", True, WHITE)
                button_text_rect = button_text.get_rect(center=button_rect.center)
                self.screen.blit(button_text, button_text_rect)

                # Store hit area for view details button
                self.hit_areas[f"view_game_{game['id']}"] = button_rect
        else:
            # Draw "No data" message
            no_data_font = self.get_font("Arial", self.scaled_font_size(14))
            no_data_text = no_data_font.render("No games found", True, LIGHT_GRAY)
            no_data_rect = no_data_text.get_rect(
                center=(table_x + table_width // 2, content_y + header_height + int(50 * self.scale_y))
            )
            self.screen.blit(no_data_text, no_data_rect)

    def draw_wallet_section(self, screen_width, screen_height):
        """
        Draw the wallet section.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)
        content_y = nav_height + int(20 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Wallet Management", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=content_y
        )
        self.screen.blit(title_text, title_rect)

        try:
            # Use cached data
            wallet_summary = self.cached_data["wallet_summary"]
            wallet_transactions = self.cached_data["wallet_transactions"]

            # Draw wallet summary
            summary_y = content_y + title_rect.height + int(20 * self.scale_y)
            self.draw_wallet_summary(screen_width, summary_y, wallet_summary)

            # Draw add transaction form
            form_y = summary_y + int(150 * self.scale_y)
            self.draw_add_transaction_form(screen_width, form_y)

            # Draw transactions table
            table_y = form_y + int(200 * self.scale_y)
            self.draw_transactions_table(screen_width, table_y, wallet_transactions)

            # Draw loading indicator if data is being loaded
            if self.data_loading["wallet"]:
                self.draw_loading_indicator(screen_width, screen_height)

        except Exception as e:
            logging.error(f"Error drawing wallet section: {e}")

            # Draw error message
            error_font = self.get_font("Arial", self.scaled_font_size(16))
            error_text = error_font.render(f"Error loading wallet data: {str(e)}", True, RED)
            error_rect = error_text.get_rect(
                center=(screen_width // 2, content_y + int(100 * self.scale_y))
            )
            self.screen.blit(error_text, error_rect)

    def draw_wallet_summary(self, screen_width, y, wallet_summary):
        """
        Draw wallet summary.

        Args:
            screen_width: Screen width
            y: Y position
            wallet_summary: Wallet summary data
        """
        # Card dimensions
        card_width = int((screen_width - int(100 * self.scale_x)) / 4)
        card_height = int(100 * self.scale_y)
        card_spacing = int(20 * self.scale_x)

        # Card data
        cards = [
            {
                "title": "Current Balance",
                "value": f"{wallet_summary['current_balance']:.2f} ETB",
                "color": (100, 200, 100)
            },
            {
                "title": "Total Deposits",
                "value": f"{wallet_summary['total_deposits']:.2f} ETB",
                "color": (100, 150, 200)
            },
            {
                "title": "Total Withdrawals",
                "value": f"{wallet_summary['total_withdrawals']:.2f} ETB",
                "color": (200, 150, 100)
            },
            {
                "title": "Transaction Count",
                "value": str(wallet_summary['transaction_count']),
                "color": (150, 100, 200)
            }
        ]

        # Draw each card
        for i, card in enumerate(cards):
            card_x = int(20 * self.scale_x) + i * (card_width + card_spacing)
            self.draw_summary_card(card_x, y, card_width, card_height, card["title"], card["value"], card["color"])

    def draw_add_transaction_form(self, screen_width, y):
        """
        Draw add transaction form.

        Args:
            screen_width: Screen width
            y: Y position
        """
        # Form dimensions
        form_width = screen_width - int(40 * self.scale_x)
        form_height = int(180 * self.scale_y)
        form_x = int(20 * self.scale_x)

        # Draw form background
        form_rect = pygame.Rect(form_x, y, form_width, form_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, form_rect, border_radius=10)

        # Draw form title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Add Transaction", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=form_x + form_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Initialize input fields if they don't exist
        if "transaction_amount" not in self.input_fields:
            self.input_fields["transaction_amount"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Amount (ETB)",
                "password": False
            }

        if "transaction_type" not in self.input_fields:
            self.input_fields["transaction_type"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "deposit",  # Default to deposit
                "placeholder": "Transaction Type",
                "password": False
            }

        if "transaction_description" not in self.input_fields:
            self.input_fields["transaction_description"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Description",
                "password": False
            }

        # Calculate input field dimensions
        field_width = int(200 * self.scale_x)
        field_height = int(40 * self.scale_y)
        field_y = y + title_rect.height + int(20 * self.scale_y)

        # Amount field
        amount_x = form_x + int(20 * self.scale_x)
        amount_rect = pygame.Rect(amount_x, field_y, field_width, field_height)
        self.input_fields["transaction_amount"]["rect"] = amount_rect

        # Draw amount field
        self.draw_input_field(
            self.input_fields["transaction_amount"],
            is_active=self.active_input == "transaction_amount"
        )

        # Draw amount label
        label_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        amount_label = label_font.render("Amount:", True, WHITE)
        amount_label_rect = amount_label.get_rect(
            midright=(amount_x - int(10 * self.scale_x), field_y + field_height // 2)
        )
        self.screen.blit(amount_label, amount_label_rect)

        # Transaction type field (dropdown)
        type_x = amount_x + field_width + int(100 * self.scale_x)
        type_rect = pygame.Rect(type_x, field_y, field_width, field_height)
        self.input_fields["transaction_type"]["rect"] = type_rect

        # Draw transaction type field
        self.draw_dropdown_field(
            self.input_fields["transaction_type"],
            is_active=self.active_input == "transaction_type",
            options=["deposit", "withdrawal", "fee", "other"]
        )

        # Draw transaction type label
        type_label = label_font.render("Type:", True, WHITE)
        type_label_rect = type_label.get_rect(
            midright=(type_x - int(10 * self.scale_x), field_y + field_height // 2)
        )
        self.screen.blit(type_label, type_label_rect)

        # Description field
        desc_x = form_x + int(20 * self.scale_x)
        desc_y = field_y + field_height + int(20 * self.scale_y)
        desc_width = int(500 * self.scale_x)
        desc_rect = pygame.Rect(desc_x, desc_y, desc_width, field_height)
        self.input_fields["transaction_description"]["rect"] = desc_rect

        # Draw description field
        self.draw_input_field(
            self.input_fields["transaction_description"],
            is_active=self.active_input == "transaction_description"
        )

        # Draw description label
        desc_label = label_font.render("Description:", True, WHITE)
        desc_label_rect = desc_label.get_rect(
            midright=(desc_x - int(10 * self.scale_x), desc_y + field_height // 2)
        )
        self.screen.blit(desc_label, desc_label_rect)

        # Add transaction button
        button_width = int(180 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_x = form_x + form_width - button_width - int(20 * self.scale_x)
        button_y = desc_y + field_height + int(20 * self.scale_y)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        button_color = ADMIN_BUTTON_HOVER if self.button_states["add_transaction"]["hover"] else ADMIN_BUTTON_BG

        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=5)

        # Draw button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Add Transaction", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Store hit area for add transaction button
        self.hit_areas["add_transaction"] = button_rect

    def draw_dropdown_field(self, field, is_active=False, options=None):
        """
        Draw a dropdown field.

        Args:
            field: Field data
            is_active: Whether the field is active
            options: List of options
        """
        # Draw field background
        border_color = LIGHT_BLUE if is_active else GRAY
        pygame.draw.rect(self.screen, WHITE, field["rect"], border_radius=5)
        pygame.draw.rect(self.screen, border_color, field["rect"], border_radius=5, width=2)

        # Draw field text
        text_font = self.get_font("Arial", self.scaled_font_size(16))

        if field["text"]:
            display_text = field["text"]
            text_color = BLACK
        else:
            display_text = field["placeholder"]
            text_color = GRAY

        text_surface = text_font.render(display_text, True, text_color)
        text_rect = text_surface.get_rect(
            midleft=(field["rect"].left + 10, field["rect"].centery)
        )
        self.screen.blit(text_surface, text_rect)

        # Draw dropdown arrow
        arrow_color = LIGHT_BLUE if is_active else GRAY
        arrow_x = field["rect"].right - 20
        arrow_y = field["rect"].centery

        pygame.draw.polygon(self.screen, arrow_color, [
            (arrow_x - 5, arrow_y - 5),
            (arrow_x + 5, arrow_y - 5),
            (arrow_x, arrow_y + 5)
        ])

    def draw_transactions_table(self, screen_width, y, transactions):
        """
        Draw transactions table.

        Args:
            screen_width: Screen width
            y: Y position
            transactions: Wallet transactions data
        """
        # Table dimensions
        table_width = screen_width - int(40 * self.scale_x)
        table_height = int(300 * self.scale_y)
        table_x = int(20 * self.scale_x)

        # Draw table background
        table_rect = pygame.Rect(table_x, y, table_width, table_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, table_rect, border_radius=10)

        # Draw table title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Recent Transactions", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=table_x + table_width // 2,
            y=y + int(10 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Define columns
        columns = [
            {"id": "id", "name": "ID", "width": 0.05},
            {"id": "date_time", "name": "Date/Time", "width": 0.15},
            {"id": "amount", "name": "Amount", "width": 0.15},
            {"id": "transaction_type", "name": "Type", "width": 0.15},
            {"id": "description", "name": "Description", "width": 0.35},
            {"id": "balance_after", "name": "Balance", "width": 0.15}
        ]

        # Calculate table content area
        content_y = y + title_rect.height + int(20 * self.scale_y)
        content_height = table_height - title_rect.height - int(30 * self.scale_y)

        # Draw table header
        header_height = int(30 * self.scale_y)
        header_rect = pygame.Rect(table_x + int(10 * self.scale_x), content_y,
                                table_width - int(20 * self.scale_x), header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=5)

        # Draw column headers
        header_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        x_offset = table_x + int(20 * self.scale_x)

        for column in columns:
            col_width = int(table_width * column["width"])
            header_text = header_font.render(column["name"], True, WHITE)
            header_rect = header_text.get_rect(
                midleft=(x_offset, content_y + header_height // 2)
            )
            self.screen.blit(header_text, header_rect)
            x_offset += col_width

        # Draw table rows
        row_height = int(30 * self.scale_y)
        row_font = self.get_font("Arial", self.scaled_font_size(12))

        if transactions:
            for i, transaction in enumerate(transactions):
                row_y = content_y + header_height + i * row_height

                # Draw row background (alternating colors)
                row_color = DARK_GRAY if i % 2 == 0 else ADMIN_PANEL_BG
                row_rect = pygame.Rect(
                    table_x + int(10 * self.scale_x),
                    row_y,
                    table_width - int(20 * self.scale_x),
                    row_height
                )
                pygame.draw.rect(self.screen, row_color, row_rect)

                # Draw row data
                x_offset = table_x + int(20 * self.scale_x)

                for column in columns:
                    col_width = int(table_width * column["width"])
                    col_id = column["id"]

                    # Get value for this column
                    value = str(transaction.get(col_id, ""))

                    # Format specific columns
                    if col_id == "amount":
                        # Format amount with sign and color
                        amount = float(value)
                        value = f"{amount:+.2f} ETB"
                        text_color = GREEN if amount > 0 else RED if amount < 0 else WHITE
                    elif col_id == "balance_after":
                        value = f"{float(value):.2f} ETB"
                        text_color = WHITE
                    elif col_id == "date_time":
                        # Shorten date/time format
                        try:
                            dt = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                            value = dt.strftime('%m/%d %H:%M')
                        except:
                            pass
                        text_color = WHITE
                    else:
                        text_color = WHITE

                    # Truncate long values
                    if len(value) > 30:
                        value = value[:27] + "..."

                    # Draw cell text
                    cell_text = row_font.render(value, True, text_color)
                    cell_rect = cell_text.get_rect(
                        midleft=(x_offset, row_y + row_height // 2)
                    )
                    self.screen.blit(cell_text, cell_rect)

                    x_offset += col_width
        else:
            # Draw "No data" message
            no_data_font = self.get_font("Arial", self.scaled_font_size(14))
            no_data_text = no_data_font.render("No transactions found", True, LIGHT_GRAY)
            no_data_rect = no_data_text.get_rect(
                center=(table_x + table_width // 2, content_y + header_height + int(50 * self.scale_y))
            )
            self.screen.blit(no_data_text, no_data_rect)

    def draw_export_section(self, screen_width, screen_height):
        """
        Draw the export section.

        Args:
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)
        content_y = nav_height + int(20 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render("Export Data", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=content_y
        )
        self.screen.blit(title_text, title_rect)

        # Draw export options
        options_y = content_y + title_rect.height + int(40 * self.scale_y)
        self.draw_export_options(screen_width, options_y)

    def draw_export_options(self, screen_width, y):
        """
        Draw export options.

        Args:
            screen_width: Screen width
            y: Y position
        """
        # Panel dimensions
        panel_width = screen_width - int(40 * self.scale_x)
        panel_height = int(400 * self.scale_y)
        panel_x = int(20 * self.scale_x)

        # Draw panel background
        panel_rect = pygame.Rect(panel_x, y, panel_width, panel_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, panel_rect, border_radius=10)

        # Draw panel title
        title_font = self.get_font("Arial", self.scaled_font_size(18), bold=True)
        title_text = title_font.render("Export Options", True, WHITE)
        title_rect = title_text.get_rect(
            centerx=panel_x + panel_width // 2,
            y=y + int(20 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Draw export options
        options_y = y + title_rect.height + int(40 * self.scale_y)

        # Daily Stats Export
        self.draw_export_option(
            panel_x + int(40 * self.scale_x),
            options_y,
            "Daily Statistics",
            "Export daily statistics data",
            "daily_stats"
        )

        # Game History Export
        self.draw_export_option(
            panel_x + int(40 * self.scale_x),
            options_y + int(120 * self.scale_y),
            "Game History",
            "Export game history data",
            "game_history"
        )

        # Wallet Transactions Export
        self.draw_export_option(
            panel_x + int(40 * self.scale_x),
            options_y + int(240 * self.scale_y),
            "Wallet Transactions",
            "Export wallet transaction data",
            "wallet_transactions"
        )

    def draw_export_option(self, x, y, title, description, data_type):
        """
        Draw an export option.

        Args:
            x: X position
            y: Y position
            title: Option title
            description: Option description
            data_type: Data type for export
        """
        # Option dimensions
        option_width = int(600 * self.scale_x)
        option_height = int(100 * self.scale_y)

        # Draw option background
        option_rect = pygame.Rect(x, y, option_width, option_height)
        pygame.draw.rect(self.screen, DARK_GRAY, option_rect, border_radius=5)

        # Draw option title
        title_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        title_text = title_font.render(title, True, WHITE)
        title_rect = title_text.get_rect(
            x=x + int(20 * self.scale_x),
            y=y + int(15 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Draw option description
        desc_font = self.get_font("Arial", self.scaled_font_size(12))
        desc_text = desc_font.render(description, True, LIGHT_GRAY)
        desc_rect = desc_text.get_rect(
            x=x + int(20 * self.scale_x),
            y=y + int(45 * self.scale_y)
        )
        self.screen.blit(desc_text, desc_rect)

        # Draw export buttons
        button_width = int(100 * self.scale_x)
        button_height = int(30 * self.scale_y)
        button_y = y + int(60 * self.scale_y)

        # CSV button
        csv_button_x = x + option_width - button_width * 2 - int(30 * self.scale_x)
        csv_button_rect = pygame.Rect(csv_button_x, button_y, button_width, button_height)
        csv_button_color = ADMIN_BUTTON_HOVER if self.button_states.get(f"export_csv_{data_type}", {}).get("hover", False) else ADMIN_BUTTON_BG
        pygame.draw.rect(self.screen, csv_button_color, csv_button_rect, border_radius=5)

        # Draw CSV button text
        button_font = self.get_font("Arial", self.scaled_font_size(12), bold=True)
        csv_text = button_font.render("Export CSV", True, WHITE)
        csv_rect = csv_text.get_rect(center=csv_button_rect.center)
        self.screen.blit(csv_text, csv_rect)

        # Store hit area for CSV button
        self.hit_areas[f"export_csv_{data_type}"] = csv_button_rect

        # PDF button
        pdf_button_x = x + option_width - button_width - int(10 * self.scale_x)
        pdf_button_rect = pygame.Rect(pdf_button_x, button_y, button_width, button_height)
        pdf_button_color = ADMIN_BUTTON_HOVER if self.button_states.get(f"export_pdf_{data_type}", {}).get("hover", False) else ADMIN_BUTTON_BG
        pygame.draw.rect(self.screen, pdf_button_color, pdf_button_rect, border_radius=5)

        # Draw PDF button text
        pdf_text = button_font.render("Export PDF", True, WHITE)
        pdf_rect = pdf_text.get_rect(center=pdf_button_rect.center)
        self.screen.blit(pdf_text, pdf_rect)

        # Store hit area for PDF button
        self.hit_areas[f"export_pdf_{data_type}"] = pdf_button_rect

    def draw_section_placeholder(self, title, screen_width, screen_height):
        """
        Draw a placeholder for a section.

        Args:
            title: Section title
            screen_width: Screen width
            screen_height: Screen height
        """
        # Navigation bar height
        nav_height = int(50 * self.scale_y)

        # Draw section title
        title_font = self.get_font("Arial", self.scaled_font_size(24), bold=True)
        title_text = title_font.render(title, True, WHITE)
        title_rect = title_text.get_rect(
            centerx=screen_width // 2,
            y=nav_height + int(20 * self.scale_y)
        )
        self.screen.blit(title_text, title_rect)

        # Draw coming soon message
        message_font = self.get_font("Arial", self.scaled_font_size(18))
        message_text = message_font.render("This section will be implemented soon", True, LIGHT_GRAY)
        message_rect = message_text.get_rect(
            center=(screen_width // 2, screen_height // 2)
        )
        self.screen.blit(message_text, message_rect)

    def draw_message(self, screen_width):
        """
        Draw a message at the bottom of the screen.

        Args:
            screen_width: Screen width
        """
        if not self.message:
            return

        # Message colors based on type
        message_colors = {
            "info": ADMIN_INFO,
            "success": ADMIN_SUCCESS,
            "error": ADMIN_ERROR,
            "warning": ADMIN_WARNING
        }

        color = message_colors.get(self.message_type, ADMIN_INFO)

        # Calculate message dimensions
        message_height = int(40 * self.scale_y)
        message_y = self.screen.get_height() - message_height

        # Draw message background
        message_rect = pygame.Rect(0, message_y, screen_width, message_height)
        pygame.draw.rect(self.screen, color, message_rect)

        # Draw message text
        message_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        message_text = message_font.render(self.message, True, WHITE)
        message_text_rect = message_text.get_rect(center=(screen_width // 2, message_y + message_height // 2))
        self.screen.blit(message_text, message_text_rect)

    def handle_mouse_down(self, pos):
        """
        Handle mouse button down events.

        Args:
            pos: Mouse position
        """
        # Check for input field clicks
        for field_name, field in self.input_fields.items():
            if field["rect"].collidepoint(pos):
                self.active_input = field_name
                return

        # Clear active input if clicked elsewhere
        self.active_input = None

        # If a dialog is active, only handle clicks within the dialog
        if hasattr(self, 'active_dialog') and self.active_dialog:
            # Only process button clicks for now
            pass

        # Check for button clicks
        for button_id, rect in self.hit_areas.items():
            if rect.collidepoint(pos):
                # Play button click sound
                if self.button_click_sound:
                    self.button_click_sound.play()

                # Set button click animation
                if button_id in self.button_states:
                    self.button_states[button_id]["click"] = True
                    self.button_states[button_id]["click_time"] = pygame.time.get_ticks()

                # Handle button click
                if button_id == "login":
                    self.handle_login()
                elif button_id == "add_user":
                    self.handle_add_user()
                elif button_id == "add_transaction":
                    self.handle_add_transaction()
                elif button_id == "save_user":
                    self.handle_save_user()
                elif button_id == "cancel_edit":
                    self.handle_cancel_edit()
                elif button_id == "close_dialog":
                    self.handle_close_dialog()
                elif button_id.startswith("edit_user_"):
                    user_id = int(button_id.split("_")[-1])
                    self.handle_edit_user(user_id)
                elif button_id.startswith("delete_user_"):
                    user_id = int(button_id.split("_")[-1])
                    self.handle_delete_user(user_id)
                elif button_id.startswith("view_game_"):
                    game_id = int(button_id.split("_")[-1])
                    self.handle_view_game(game_id)
                elif button_id.startswith("export_csv_"):
                    data_type = button_id[11:]  # Remove "export_csv_" prefix
                    self.handle_export(data_type, "csv")
                elif button_id.startswith("export_pdf_"):
                    data_type = button_id[11:]  # Remove "export_pdf_" prefix
                    self.handle_export(data_type, "pdf")
                elif button_id.startswith("nav_"):
                    self.handle_navigation(button_id[4:])  # Remove "nav_" prefix

                return

    def handle_mouse_motion(self, pos):
        """
        Handle mouse motion events.

        Args:
            pos: Mouse position
        """
        # Update button hover states
        for button_id, rect in self.hit_areas.items():
            if button_id in self.button_states:
                self.button_states[button_id]["hover"] = rect.collidepoint(pos)

    def handle_key_down(self, event):
        """
        Handle key down events.

        Args:
            event: Pygame event
        """
        # Handle input field text input
        if self.active_input:
            field = self.input_fields[self.active_input]

            if event.key == pygame.K_BACKSPACE:
                # Remove last character
                field["text"] = field["text"][:-1]
            elif event.key == pygame.K_RETURN:
                # Submit login form if on password field
                if self.active_input == "password":
                    self.handle_login()
                else:
                    # Move to next field
                    if self.active_input == "username":
                        self.active_input = "password"
            elif event.unicode and len(event.unicode) == 1:
                # Add character to field
                field["text"] += event.unicode

    def handle_login(self):
        """Handle login button click."""
        username = self.input_fields["username"]["text"]
        password = self.input_fields["password"]["text"]

        if not username or not password:
            self.show_message("Please enter username and password", "error")
            return

        # Authenticate user
        success, result = authenticate_admin(username, password)

        if success:
            # Store session token
            self.session_token = result

            # Get session data
            success, session_data = validate_admin_session(self.session_token)

            if success:
                # Set authenticated state
                self.authenticated = True
                self.user_data = session_data

                # Set active section to dashboard
                self.active_section = "dashboard"

                # Show success message
                self.show_message(f"Welcome, {session_data['username']}!", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()
            else:
                # Show error message
                self.show_message(result, "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        else:
            # Show error message
            self.show_message(result, "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_add_user(self):
        """Handle add user button click."""
        # Get input values
        username = self.input_fields["new_username"]["text"]
        password = self.input_fields["new_password"]["text"]

        try:
            access_level = int(self.input_fields["new_access_level"]["text"])
        except ValueError:
            self.show_message("Access level must be a number (1-3)", "error")
            return

        # Validate input
        if not username:
            self.show_message("Username is required", "error")
            return

        if not password:
            self.show_message("Password is required", "error")
            return

        if access_level < 1 or access_level > 3:
            self.show_message("Access level must be between 1 and 3", "error")
            return

        # Add user
        try:
            stats_db = get_stats_db_manager()
            user_id = stats_db.add_admin_user(username, password, access_level)

            if user_id > 0:
                # Show success message
                self.show_message(f"User '{username}' added successfully", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()

                # Clear input fields
                self.input_fields["new_username"]["text"] = ""
                self.input_fields["new_password"]["text"] = ""
                self.input_fields["new_access_level"]["text"] = "1"
            else:
                # Show error message
                self.show_message("Failed to add user", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        except Exception as e:
            # Show error message
            self.show_message(f"Error adding user: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_edit_user(self, user_id):
        """
        Handle edit user button click.

        Args:
            user_id: User ID to edit
        """
        try:
            # Get user data
            stats_db = get_stats_db_manager()
            user = stats_db.get_admin_user(user_id)

            if not user:
                self.show_message(f"User with ID {user_id} not found", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Show edit user dialog
            self.show_edit_user_dialog(user)
        except Exception as e:
            # Show error message
            self.show_message(f"Error editing user: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def show_edit_user_dialog(self, user):
        """
        Show edit user dialog.

        Args:
            user: User data
        """
        # Store user data for later use
        self.edit_user_data = user

        # Create input fields if they don't exist
        if "edit_username" not in self.input_fields:
            self.input_fields["edit_username"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Username",
                "password": False
            }

        if "edit_password" not in self.input_fields:
            self.input_fields["edit_password"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "New Password (leave blank to keep current)",
                "password": True
            }

        if "edit_access_level" not in self.input_fields:
            self.input_fields["edit_access_level"] = {
                "rect": pygame.Rect(0, 0, 0, 0),
                "text": "",
                "placeholder": "Access Level (1-3)",
                "password": False
            }

        # Set initial values
        self.input_fields["edit_username"]["text"] = user["username"]
        self.input_fields["edit_password"]["text"] = ""
        self.input_fields["edit_access_level"]["text"] = str(user["access_level"])

        # Show dialog
        self.active_dialog = "edit_user"

    def draw_edit_user_dialog(self):
        """Draw edit user dialog."""
        if not hasattr(self, 'edit_user_data') or not self.edit_user_data:
            return

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Calculate dialog dimensions
        dialog_width = int(500 * self.scale_x)
        dialog_height = int(300 * self.scale_y)
        dialog_x = (screen_width - dialog_width) // 2
        dialog_y = (screen_height - dialog_height) // 2

        # Draw dialog background with semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 192))  # Semi-transparent black
        self.screen.blit(overlay, (0, 0))

        # Draw dialog background
        dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, dialog_rect, border_radius=10)

        # Draw dialog header
        header_height = int(50 * self.scale_y)
        header_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=10)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG,
                       pygame.Rect(dialog_x, dialog_y + header_height - 10, dialog_width, 10))

        # Draw header text
        header_font = self.get_font("Arial", self.scaled_font_size(20), bold=True)
        header_text = header_font.render(f"Edit User: {self.edit_user_data['username']}", True, WHITE)
        header_text_rect = header_text.get_rect(
            centerx=dialog_x + dialog_width // 2,
            centery=dialog_y + header_height // 2
        )
        self.screen.blit(header_text, header_text_rect)

        # Calculate input field dimensions
        field_width = int(300 * self.scale_x)
        field_height = int(40 * self.scale_y)
        field_x = dialog_x + (dialog_width - field_width) // 2

        # Username field
        username_y = dialog_y + header_height + int(20 * self.scale_y)
        username_rect = pygame.Rect(field_x, username_y, field_width, field_height)
        self.input_fields["edit_username"]["rect"] = username_rect

        # Draw username field
        self.draw_input_field(
            self.input_fields["edit_username"],
            is_active=self.active_input == "edit_username"
        )

        # Draw username label
        label_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
        username_label = label_font.render("Username:", True, WHITE)
        username_label_rect = username_label.get_rect(
            midright=(field_x - int(10 * self.scale_x), username_y + field_height // 2)
        )
        self.screen.blit(username_label, username_label_rect)

        # Password field
        password_y = username_y + field_height + int(20 * self.scale_y)
        password_rect = pygame.Rect(field_x, password_y, field_width, field_height)
        self.input_fields["edit_password"]["rect"] = password_rect

        # Draw password field
        self.draw_input_field(
            self.input_fields["edit_password"],
            is_active=self.active_input == "edit_password"
        )

        # Draw password label
        password_label = label_font.render("Password:", True, WHITE)
        password_label_rect = password_label.get_rect(
            midright=(field_x - int(10 * self.scale_x), password_y + field_height // 2)
        )
        self.screen.blit(password_label, password_label_rect)

        # Access level field
        access_y = password_y + field_height + int(20 * self.scale_y)
        access_rect = pygame.Rect(field_x, access_y, field_width, field_height)
        self.input_fields["edit_access_level"]["rect"] = access_rect

        # Draw access level field
        self.draw_input_field(
            self.input_fields["edit_access_level"],
            is_active=self.active_input == "edit_access_level"
        )

        # Draw access level label
        access_label = label_font.render("Access Level:", True, WHITE)
        access_label_rect = access_label.get_rect(
            midright=(field_x - int(10 * self.scale_x), access_y + field_height // 2)
        )
        self.screen.blit(access_label, access_label_rect)

        # Draw access level help text
        help_font = self.get_font("Arial", self.scaled_font_size(12))
        help_text = help_font.render("1 = Read Only, 2 = Read/Write, 3 = Admin", True, LIGHT_GRAY)
        help_rect = help_text.get_rect(
            midleft=(field_x + field_width + int(10 * self.scale_x), access_y + field_height // 2)
        )
        self.screen.blit(help_text, help_rect)

        # Draw buttons
        button_width = int(120 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_y = dialog_y + dialog_height - button_height - int(20 * self.scale_y)

        # Save button
        save_button_x = dialog_x + dialog_width - button_width * 2 - int(30 * self.scale_x)
        save_button_rect = pygame.Rect(save_button_x, button_y, button_width, button_height)
        save_button_color = ADMIN_BUTTON_HOVER if self.button_states.get("save_user", {}).get("hover", False) else ADMIN_BUTTON_BG
        pygame.draw.rect(self.screen, save_button_color, save_button_rect, border_radius=5)

        # Draw save button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        save_text = button_font.render("Save", True, WHITE)
        save_rect = save_text.get_rect(center=save_button_rect.center)
        self.screen.blit(save_text, save_rect)

        # Store hit area for save button
        self.hit_areas["save_user"] = save_button_rect

        # Cancel button
        cancel_button_x = dialog_x + dialog_width - button_width - int(10 * self.scale_x)
        cancel_button_rect = pygame.Rect(cancel_button_x, button_y, button_width, button_height)
        cancel_button_color = ADMIN_BUTTON_HOVER if self.button_states.get("cancel_edit", {}).get("hover", False) else ADMIN_ERROR
        pygame.draw.rect(self.screen, cancel_button_color, cancel_button_rect, border_radius=5)

        # Draw cancel button text
        cancel_text = button_font.render("Cancel", True, WHITE)
        cancel_rect = cancel_text.get_rect(center=cancel_button_rect.center)
        self.screen.blit(cancel_text, cancel_rect)

        # Store hit area for cancel button
        self.hit_areas["cancel_edit"] = cancel_button_rect

    def handle_delete_user(self, user_id):
        """
        Handle delete user button click.

        Args:
            user_id: User ID to delete
        """
        try:
            # Get user data
            stats_db = get_stats_db_manager()
            user = stats_db.get_admin_user(user_id)

            if not user:
                self.show_message(f"User with ID {user_id} not found", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Check if this is the current user
            if self.user_data and self.user_data.get('user_id') == user_id:
                self.show_message("Cannot delete your own account", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Delete user
            success = stats_db.delete_admin_user(user_id)

            if success:
                # Show success message
                self.show_message(f"User '{user['username']}' deleted successfully", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()
            else:
                # Show error message
                self.show_message(f"Failed to delete user '{user['username']}'", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        except Exception as e:
            # Show error message
            self.show_message(f"Error deleting user: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_save_user(self):
        """Handle save user button click."""
        if not hasattr(self, 'edit_user_data') or not self.edit_user_data:
            return

        try:
            # Get input values
            username = self.input_fields["edit_username"]["text"]
            password = self.input_fields["edit_password"]["text"]

            try:
                access_level = int(self.input_fields["edit_access_level"]["text"])
            except ValueError:
                self.show_message("Access level must be a number (1-3)", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Validate input
            if not username:
                self.show_message("Username is required", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            if access_level < 1 or access_level > 3:
                self.show_message("Access level must be between 1 and 3", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Update user
            stats_db = get_stats_db_manager()

            # Only update password if it's not empty
            if password:
                success = stats_db.update_admin_user(
                    self.edit_user_data["id"],
                    username=username,
                    password=password,
                    access_level=access_level
                )
            else:
                success = stats_db.update_admin_user(
                    self.edit_user_data["id"],
                    username=username,
                    access_level=access_level
                )

            if success:
                # Show success message
                self.show_message(f"User '{username}' updated successfully", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()

                # Close dialog
                self.active_dialog = None
                self.edit_user_data = None
            else:
                # Show error message
                self.show_message("Failed to update user", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        except Exception as e:
            # Show error message
            self.show_message(f"Error updating user: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_cancel_edit(self):
        """Handle cancel edit button click."""
        # Close dialog
        self.active_dialog = None
        self.edit_user_data = None

    def handle_close_dialog(self):
        """Handle close dialog button click."""
        # Close any active dialog
        self.active_dialog = None
        self.edit_user_data = None
        self.game_details_data = None

    def handle_view_game(self, game_id):
        """
        Handle view game button click.

        Args:
            game_id: Game ID to view
        """
        try:
            # Get game data
            stats_db = get_stats_db_manager()
            games = stats_db.get_game_history(limit=1000)

            # Find the game with the specified ID
            game_data = None
            for game in games:
                if game['id'] == game_id:
                    game_data = game
                    break

            if not game_data:
                self.show_message(f"Game with ID {game_id} not found", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Store game data for later use
            self.game_details_data = game_data

            # Show game details dialog
            self.active_dialog = "game_details"
        except Exception as e:
            # Show error message
            self.show_message(f"Error viewing game: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def draw_game_details_dialog(self):
        """Draw game details dialog."""
        if not hasattr(self, 'game_details_data') or not self.game_details_data:
            return

        # Get screen dimensions
        screen_width, screen_height = self.screen.get_size()

        # Calculate dialog dimensions
        dialog_width = int(600 * self.scale_x)
        dialog_height = int(500 * self.scale_y)
        dialog_x = (screen_width - dialog_width) // 2
        dialog_y = (screen_height - dialog_height) // 2

        # Draw dialog background with semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 192))  # Semi-transparent black
        self.screen.blit(overlay, (0, 0))

        # Draw dialog background
        dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
        pygame.draw.rect(self.screen, ADMIN_PANEL_BG, dialog_rect, border_radius=10)

        # Draw dialog header
        header_height = int(50 * self.scale_y)
        header_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, header_height)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG, header_rect, border_radius=10)
        pygame.draw.rect(self.screen, ADMIN_HEADER_BG,
                       pygame.Rect(dialog_x, dialog_y + header_height - 10, dialog_width, 10))

        # Draw header text
        header_font = self.get_font("Arial", self.scaled_font_size(20), bold=True)
        header_text = header_font.render(f"Game Details: ID {self.game_details_data['id']}", True, WHITE)
        header_text_rect = header_text.get_rect(
            centerx=dialog_x + dialog_width // 2,
            centery=dialog_y + header_height // 2
        )
        self.screen.blit(header_text, header_text_rect)

        # Draw game details
        content_y = dialog_y + header_height + int(20 * self.scale_y)
        self.draw_game_details_content(dialog_x, content_y, dialog_width, dialog_height - header_height - int(80 * self.scale_y))

        # Draw close button
        button_width = int(120 * self.scale_x)
        button_height = int(40 * self.scale_y)
        button_x = dialog_x + dialog_width - button_width - int(20 * self.scale_x)
        button_y = dialog_y + dialog_height - button_height - int(20 * self.scale_y)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        button_color = ADMIN_BUTTON_HOVER if self.button_states.get("close_dialog", {}).get("hover", False) else ADMIN_ERROR

        pygame.draw.rect(self.screen, button_color, button_rect, border_radius=5)

        # Draw button text
        button_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        button_text = button_font.render("Close", True, WHITE)
        button_text_rect = button_text.get_rect(center=button_rect.center)
        self.screen.blit(button_text, button_text_rect)

        # Store hit area for close button
        self.hit_areas["close_dialog"] = button_rect

    def draw_game_details_content(self, x, y, width, height):
        """
        Draw game details content.

        Args:
            x: X position
            y: Y position
            width: Content width
            height: Content height
        """
        game = self.game_details_data

        # Define fields to display
        fields = [
            {"label": "Date/Time", "value": game.get('date_time', '')},
            {"label": "Winner", "value": game.get('username', '')},
            {"label": "House", "value": game.get('house', '')},
            {"label": "Stake", "value": f"{float(game.get('stake', 0)):.2f} ETB"},
            {"label": "Players", "value": str(game.get('players', 0))},
            {"label": "Total Calls", "value": str(game.get('total_calls', 0))},
            {"label": "Commission %", "value": f"{float(game.get('commission_percent', 0)):.0f}%"},
            {"label": "Fee", "value": f"{float(game.get('fee', 0)):.2f} ETB"},
            {"label": "Tips", "value": f"{float(game.get('tips', 0)):.2f} ETB"},
            {"label": "Total Prize", "value": f"{float(game.get('total_prize', 0)):.2f} ETB"},
            {"label": "Status", "value": game.get('status', '')},
        ]

        # Calculate field dimensions
        field_height = int(30 * self.scale_y)
        label_width = int(150 * self.scale_x)
        value_width = width - label_width - int(40 * self.scale_x)

        # Draw fields
        for i, field in enumerate(fields):
            field_y = y + i * field_height

            # Draw label
            label_font = self.get_font("Arial", self.scaled_font_size(14), bold=True)
            label_text = label_font.render(field["label"] + ":", True, WHITE)
            label_rect = label_text.get_rect(
                midright=(x + label_width, field_y + field_height // 2)
            )
            self.screen.blit(label_text, label_rect)

            # Draw value
            value_font = self.get_font("Arial", self.scaled_font_size(14))

            # Special formatting for status
            if field["label"] == "Status":
                text_color = GREEN if field["value"].lower() == "won" else WHITE
            else:
                text_color = WHITE

            value_text = value_font.render(field["value"], True, text_color)
            value_rect = value_text.get_rect(
                midleft=(x + label_width + int(20 * self.scale_x), field_y + field_height // 2)
            )
            self.screen.blit(value_text, value_rect)

        # Draw details section
        details_y = y + len(fields) * field_height + int(20 * self.scale_y)

        # Draw details label
        details_label_font = self.get_font("Arial", self.scaled_font_size(16), bold=True)
        details_label_text = details_label_font.render("Additional Details:", True, WHITE)
        details_label_rect = details_label_text.get_rect(
            x=x + int(20 * self.scale_x),
            y=details_y
        )
        self.screen.blit(details_label_text, details_label_rect)

        # Draw details content
        details_content_y = details_y + details_label_rect.height + int(10 * self.scale_y)
        details_content_height = height - (details_content_y - y)

        # Parse details JSON
        try:
            details_json = game.get('details', '{}')
            details = json.loads(details_json) if details_json else {}

            # Format details as string
            details_str = json.dumps(details, indent=2)
        except:
            details_str = "Error parsing details JSON"

        # Draw details content
        details_content_font = self.get_font("Arial", self.scaled_font_size(12))
        details_content_rect = pygame.Rect(
            x + int(20 * self.scale_x),
            details_content_y,
            width - int(40 * self.scale_x),
            details_content_height
        )

        # Draw details background
        pygame.draw.rect(self.screen, DARK_GRAY, details_content_rect, border_radius=5)

        # Draw details text
        details_lines = details_str.split('\n')
        line_height = int(20 * self.scale_y)

        for i, line in enumerate(details_lines):
            if i * line_height > details_content_height - line_height:
                # Draw ellipsis if there are more lines
                ellipsis_text = details_content_font.render("...", True, LIGHT_GRAY)
                ellipsis_rect = ellipsis_text.get_rect(
                    x=details_content_rect.x + int(10 * self.scale_x),
                    y=details_content_rect.y + details_content_height - line_height
                )
                self.screen.blit(ellipsis_text, ellipsis_rect)
                break

            line_text = details_content_font.render(line, True, LIGHT_GRAY)
            line_rect = line_text.get_rect(
                x=details_content_rect.x + int(10 * self.scale_x),
                y=details_content_rect.y + i * line_height + int(5 * self.scale_y)
            )
            self.screen.blit(line_text, line_rect)

    def handle_add_transaction(self):
        """Handle add transaction button click."""
        try:
            # Get input values
            amount_str = self.input_fields["transaction_amount"]["text"]
            transaction_type = self.input_fields["transaction_type"]["text"]
            description = self.input_fields["transaction_description"]["text"]

            # Validate input
            if not amount_str:
                self.show_message("Amount is required", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            try:
                amount = float(amount_str)
            except ValueError:
                self.show_message("Amount must be a number", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            if not transaction_type:
                self.show_message("Transaction type is required", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            if not description:
                self.show_message("Description is required", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()

                return

            # Adjust amount based on transaction type
            if transaction_type == "withdrawal":
                amount = -abs(amount)  # Make sure it's negative
            elif transaction_type == "deposit":
                amount = abs(amount)  # Make sure it's positive
            elif transaction_type == "fee":
                amount = -abs(amount)  # Make sure it's negative

            # Add transaction
            stats_db = get_stats_db_manager()
            transaction_id = stats_db.add_wallet_transaction(amount, transaction_type, description)

            if transaction_id > 0:
                # Show success message
                self.show_message(f"Transaction added successfully", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()

                # Clear input fields
                self.input_fields["transaction_amount"]["text"] = ""
                self.input_fields["transaction_description"]["text"] = ""
            else:
                # Show error message
                self.show_message("Failed to add transaction", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        except Exception as e:
            # Show error message
            self.show_message(f"Error adding transaction: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_export(self, data_type, format_type):
        """
        Handle export button click.

        Args:
            data_type: Type of data to export
            format_type: Export format (csv or pdf)
        """
        if not STATS_EXPORT_AVAILABLE:
            self.show_message("Export functionality not available", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

            return

        # Show exporting message
        self.show_message(f"Exporting {data_type} to {format_type.upper()}...", "info")

        try:
            # Export data
            if format_type.lower() == "csv":
                success, result = export_to_csv(data_type)
            elif format_type.lower() == "pdf":
                success, result = export_to_pdf(data_type)
            else:
                success = False
                result = f"Unknown format type: {format_type}"

            if success:
                # Show success message
                self.show_message(f"Export successful: {os.path.basename(result)}", "success")

                # Play success sound
                if self.success_sound:
                    self.success_sound.play()

                # Open the file
                try:
                    if os.path.exists(result):
                        # Open the file with the default application
                        if sys.platform == "win32":
                            os.startfile(result)
                        elif sys.platform == "darwin":
                            subprocess.call(["open", result])
                        else:
                            subprocess.call(["xdg-open", result])
                except Exception as e:
                    logging.error(f"Error opening exported file: {e}")
            else:
                # Show error message
                self.show_message(f"Export failed: {result}", "error")

                # Play error sound
                if self.error_sound:
                    self.error_sound.play()
        except Exception as e:
            # Show error message
            self.show_message(f"Export error: {str(e)}", "error")

            # Play error sound
            if self.error_sound:
                self.error_sound.play()

    def handle_navigation(self, section):
        """
        Handle navigation button click.

        Args:
            section: Section ID
        """
        if section == "logout":
            # End session
            if self.session_token:
                end_admin_session(self.session_token)

            # Reset authentication state
            self.authenticated = False
            self.session_token = None
            self.user_data = None

            # Clear input fields
            self.input_fields["username"]["text"] = ""
            self.input_fields["password"]["text"] = ""

            # Show message
            self.show_message("Logged out successfully", "info")
        else:
            # Set active section
            if section != self.active_section:
                self.active_section = section

                # Reset data loading state for the new section
                if section in self.data_loading:
                    self.data_loading[section] = False

    def show_message(self, message, message_type="info"):
        """
        Show a message.

        Args:
            message: Message text
            message_type: Message type (info, success, error, warning)
        """
        self.message = message
        self.message_type = message_type
        self.message_timer = 180  # Display for 3 seconds (60 FPS)

    def get_font(self, font_name, size, bold=False):
        """
        Get a font from the cache or create a new one.

        Args:
            font_name: Font name
            size: Font size
            bold: Whether the font should be bold

        Returns:
            pygame.font.Font: Font object
        """
        key = (font_name, size, bold)

        if key not in self._font_cache:
            self._font_cache[key] = pygame.font.SysFont(font_name, size, bold=bold)

        return self._font_cache[key]

    def scaled_font_size(self, base_size):
        """
        Scale a font size based on screen dimensions.

        Args:
            base_size: Base font size

        Returns:
            int: Scaled font size
        """
        return int(base_size * min(self.scale_x, self.scale_y))

if __name__ == "__main__":
    # Initialize pygame
    pygame.init()

    # Create screen
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("WOW Games Admin")

    # Show admin UI
    show_admin_ui(screen)

    # Quit pygame
    pygame.quit()
    sys.exit()
